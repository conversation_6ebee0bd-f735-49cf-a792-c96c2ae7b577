Initializing deployment
Clonning Repo github.com/rhingonekar/rplwebapp.git to /etc/dokploy/applications/rpl-frontend-3bqqpj/code: ✅
Cloning into '/etc/dokploy/applications/rpl-frontend-3bqqpj/code'...
remote: Enumerating objects: 1369, done.
Cloned github.com/rhingonekar/rplwebapp.git: ✅
Build heroku_buildpacks: ✅
Source Type: github: ✅
24: Pulling from heroku/builder
Digest: sha256:63ca96df234c499d2e1844f4b96750ad46394d32673f886d98ba7311ca49a5b4
Status: Image is up to date for heroku/builder:24
24: Pulling from heroku/heroku
Digest: sha256:5325f68c40385890717edac26dc3afd54d3e34921204671a461b4fb18600086a
Status: Image is up to date for heroku/heroku:24
===> ANALYZING
Restoring data for SBOM from previous image
===> DETECTING
of 6 buildpacks participating
heroku/nodejs-engine      4.0.0
heroku/nodejs-npm-install 4.0.0
heroku/procfile           4.2.1
===> RESTORING
Restoring metadata for "heroku/nodejs-engine:dist" from app image
Restoring metadata for "heroku/nodejs-engine:web_env" from app image
Restoring metadata for "heroku/nodejs-npm-install:npm_runtime_config" from app image
Restoring metadata for "heroku/nodejs-npm-install:npm_cache" from cache
Restoring data for "heroku/nodejs-engine:dist" from cache
Restoring data for "heroku/nodejs-npm-install:npm_cache" from cache
===> BUILDING
## Heroku Node.js Engine
- Checking Node.js version
- Node.js version not specified, using `22.x`
- Resolved Node.js version: `22.17.0`
- Installing Node.js distribution
- Reusing Node.js 22.17.0 (linux-amd64)
- Done (finished in < 0.1s)
## Heroku Node.js npm Install
- Installing node modules
- Using npm version `10.9.2`
- Restoring npm cache
- Configuring npm cache directory
- Running `npm ci`
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated stable@0.1.8: Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility
npm warn deprecated @babel/plugin-proposal-private-methods@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
npm warn deprecated @babel/plugin-proposal-numeric-separator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-numeric-separator instead.
npm warn deprecated @babel/plugin-proposal-nullish-coalescing-operator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
npm warn deprecated @babel/plugin-proposal-class-properties@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
npm warn deprecated rollup-plugin-terser@7.0.2: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-terser
npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
npm warn deprecated @babel/plugin-proposal-optional-chaining@7.21.0: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-chaining instead.
npm warn deprecated @babel/plugin-proposal-private-property-in-object@7.21.11: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
npm warn deprecated domexception@2.0.1: Use your platform's native DOMException instead
npm warn deprecated w3c-hr-time@1.0.2: Use your platform's native performance.now() and performance.timeOrigin.
npm warn deprecated q@1.5.1: You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.
npm warn deprecated
npm warn deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)
npm warn deprecated sourcemap-codec@1.4.8: Please use @jridgewell/sourcemap-codec instead
npm warn deprecated workbox-cacheable-response@6.6.0: workbox-background-sync@6.6.0
npm warn deprecated workbox-google-analytics@6.6.0: It is not compatible with newer versions of GA starting with v4, as long as you are using GAv3 it should be ok, but the package is not longer being maintained
npm warn deprecated svgo@1.3.2: This SVGO version is no longer supported. Upgrade to v2.x.x.
npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
added 1406 packages, and audited 1407 packages in 23s
packages are looking for funding
run `npm fund` for details
vulnerabilities (3 moderate, 6 high)
To address all issues (including breaking changes), run:
npm audit fix --force
Run `npm audit` for details.
- Done (23.4s)
- Running scripts
- Running `npm run build`
> client@0.1.0 build
> react-scripts build
Creating an optimized production build...
Compiled with warnings.
[eslint]
src/App.js
Line 28:8:  'SimpleCameraTest' is defined but never used  no-unused-vars
src/components/AdminMatchManagement.js
Line 18:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/components/CricketPlayerCard.js
Line 2:15:  'Typography' is defined but never used                   no-unused-vars
Line 48:9:  'getNationalityCode' is assigned a value but never used  no-unused-vars
src/components/ExportIplPlayers.js
Line 10:3:  'Link' is defined but never used  no-unused-vars
src/components/ImportIplPlayers.js
Line 9:3:   'Card' is defined but never used         no-unused-vars
Line 10:3:  'CardContent' is defined but never used  no-unused-vars
src/components/Tournaments/EnhancedMatchForm.js
Line 151:6:    React Hook useEffect has a missing dependency: 'mapExtractedDataToForm'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
Line 437:9:    'prepareMatchDataForApi' is assigned a value but never used                                                                no-unused-vars
Line 734:9:    'markFieldValidated' is assigned a value but never used                                                                    no-unused-vars
Line 781:11:   'team1Wickets' is assigned a value but never used                                                                          no-unused-vars
Line 1011:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1011:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1016:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1016:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
src/components/Tournaments/MatchDetail.js
Line 10:3:   'Grid' is defined but never used              no-unused-vars
Line 11:3:   'Divider' is defined but never used           no-unused-vars
Line 18:3:   'Tooltip' is defined but never used           no-unused-vars
Line 26:3:   'Accordion' is defined but never used         no-unused-vars
Line 27:3:   'AccordionSummary' is defined but never used  no-unused-vars
Line 28:3:   'AccordionDetails' is defined but never used  no-unused-vars
Line 40:17:  'ExpandMoreIcon' is defined but never used    no-unused-vars
src/components/Tournaments/MatchFormStep.js
Line 110:11:  'isOcrFailed' is assigned a value but never used   no-unused-vars
Line 389:13:  'playerIssues' is assigned a value but never used  no-unused-vars
src/components/Tournaments/OCRDataViewer.js
Line 18:3:  'TextField' is defined but never used  no-unused-vars
src/components/Tournaments/PlayerMatchingViewer.js
Line 165:57:  'candidates' is assigned a value but never used  no-unused-vars
src/components/Tournaments/ScorecardUploadStep.js
Line 23:12:  'ImageIcon' is defined but never used  no-unused-vars
src/components/Training/ImageSelectionModal.js
Line 13:3:   'FormControlLabel' is defined but never used  no-unused-vars
Line 26:12:  'ImageIcon' is defined but never used         no-unused-vars
src/components/Training/ScorecardLabeler.js
Line 2:10:    'API_URL' is defined but never used                    no-unused-vars
Line 1041:9:  'getCategoryColor' is assigned a value but never used  no-unused-vars
src/components/admin/TemplateBuilder.js
Line 6:9:    'imageRef' is assigned a value but never used                                                                    no-unused-vars
Line 16:10:  'isResizing' is assigned a value but never used                                                                  no-unused-vars
Line 16:22:  'setIsResizing' is assigned a value but never used                                                               no-unused-vars
Line 872:7:  Expected a default case                                                                                          default-case
Line 960:6:  React Hook useEffect has a missing dependency: 'redrawCanvas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/components/auction/AuctionPlayerCard.js
Line 3:3:  'Box' is defined but never used         no-unused-vars
Line 6:3:  'Typography' is defined but never used  no-unused-vars
src/components/auction/BidActivityFeed.js
Line 13:3:   'IconButton' is defined but never used         no-unused-vars
Line 20:18:  'ArrowUpwardIcon' is defined but never used    no-unused-vars
Line 21:20:  'ArrowDownwardIcon' is defined but never used  no-unused-vars
Line 22:17:  'AccessTimeIcon' is defined but never used     no-unused-vars
Line 24:18:  'TrophyIcon' is defined but never used         no-unused-vars
src/components/auction/PlayerAuctionStatus.js
Line 1:38:   'useRef' is defined but never used                  no-unused-vars
Line 10:3:   'LinearProgress' is defined but never used          no-unused-vars
Line 17:3:   'Tooltip' is defined but never used                 no-unused-vars
Line 22:18:  'ArrowUpwardIcon' is defined but never used         no-unused-vars
Line 24:18:  'TrophyIcon' is defined but never used              no-unused-vars
Line 151:9:  'theme' is assigned a value but never used          no-unused-vars
Line 202:9:  'isSmallScreen' is assigned a value but never used  no-unused-vars
src/components/dashboard/ResponsiveDashboardCard.js
Line 3:3:   'Box' is defined but never used                no-unused-vars
Line 47:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/components/layout/NewLayout.js
Line 41:8:   'Brightness4Icon' is defined but never used           no-unused-vars
Line 42:8:   'Brightness7Icon' is defined but never used           no-unused-vars
Line 45:8:   'AccountBalanceWalletIcon' is defined but never used  no-unused-vars
Line 171:9:  'isDark' is assigned a value but never used           no-unused-vars
src/components/players/CardGallery.js
Line 5:3:  'Typography' is defined but never used  no-unused-vars
src/components/players/ImportPlayersDialog.js
Line 21:3:  'Stack' is defined but never used  no-unused-vars
src/components/ui/ResponsiveCard.js
Line 9:3:   'Box' is defined but never used                no-unused-vars
Line 40:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/pages/Admin/OcrSettings.js
Line 8:3:   'Card' is defined but never used            no-unused-vars
Line 9:3:   'CardHeader' is defined but never used      no-unused-vars
Line 10:3:  'CardContent' is defined but never used     no-unused-vars
Line 11:3:  'TextField' is defined but never used       no-unused-vars
Line 13:3:  'Divider' is defined but never used         no-unused-vars
Line 48:9:  'theme' is assigned a value but never used  no-unused-vars
src/pages/Admin/PlayerManagement.js
Line 8:3:    'Card' is defined but never used                      no-unused-vars
Line 9:3:    'CardContent' is defined but never used               no-unused-vars
Line 21:3:   'IconButton' is defined but never used                no-unused-vars
Line 30:11:  'EditIcon' is defined but never used                  no-unused-vars
Line 35:15:  'CheckBoxIcon' is defined but never used              no-unused-vars
Line 36:27:  'CheckBoxOutlineBlankIcon' is defined but never used  no-unused-vars
Line 53:10:  'filteredPlayers' is assigned a value but never used  no-unused-vars
src/pages/Auction/AuctionListings.js
Line 32:8:   'MoneyIcon' is defined but never used                                                                                      no-unused-vars
Line 42:3:   'deleteAuction' is defined but never used                                                                                  no-unused-vars
Line 49:7:   'AuctionCard' is assigned a value but never used                                                                           no-unused-vars
Line 443:6:  React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Auction/LiveAuctionDashboard.js
Line 15:3:    'Button' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 21:3:    'useMediaQuery' is defined but never used                                                                                                                                                                                                                          no-unused-vars
Line 28:12:   'GavelIcon' is defined but never used                                                                                                                                                                                                                              no-unused-vars
Line 29:17:   'AccessTimeIcon' is defined but never used                                                                                                                                                                                                                         no-unused-vars
Line 36:10:   'format' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 38:10:   'motion' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 65:10:   'bidLoading' is assigned a value but never used                                                                                                                                                                                                                    no-unused-vars
Line 149:32:  The ref value 'socketRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'socketRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
Line 157:6:   React Hook useEffect has a missing dependency: 'selectedAuction'. Either include it or remove the dependency array                                                                                                                                                 react-hooks/exhaustive-deps
Line 217:9:   'handleNewBid' is assigned a value but never used                                                                                                                                                                                                                  no-unused-vars
Line 423:6:   React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array                                                                                                                                          react-hooks/exhaustive-deps
src/pages/Dashboard/EnhancedDashboard.js
Line 27:9:  'isMobile' is assigned a value but never used  no-unused-vars
Line 28:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/pages/Players/MyTeam.js
Line 6:3:   'Box' is defined but never used                                                                                  no-unused-vars
Line 28:6:  React Hook useEffect has a missing dependency: 'fetchPlayers'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/TeamManagement/TeamDashboard.js
Line 16:3:  'CardActions' is defined but never used     no-unused-vars
Line 20:3:  'ListItemAvatar' is defined but never used  no-unused-vars
Line 21:3:  'Avatar' is defined but never used          no-unused-vars
src/pages/TeamManagement/TeamRoster.js
Line 17:3:  'IconButton' is defined but never used         no-unused-vars
Line 28:3:  'Tooltip' is defined but never used            no-unused-vars
Line 33:8:  'RemoveIcon' is defined but never used         no-unused-vars
Line 35:8:  'InfoIcon' is defined but never used           no-unused-vars
Line 45:9:  'isMobile' is assigned a value but never used  no-unused-vars
src/pages/TeamManagement/TeamSettings.js
Line 16:3:  'CardContent' is defined but never used  no-unused-vars
Line 24:3:  'Chip' is defined but never used         no-unused-vars
Line 30:8:  'EditIcon' is defined but never used     no-unused-vars
src/pages/Tournaments/SimpleCameraTest.js
Line 21:10:  'cameraPermission' is assigned a value but never used                                                          no-unused-vars
Line 125:6:  React Hook useEffect has a missing dependency: 'stopCamera'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/SimplifiedGuidedCapture.js
Line 611:6:   React Hook useCallback has a missing dependency: 'debugMode'. Either include it or remove the dependency array                                                                                                                                                                                 react-hooks/exhaustive-deps
Line 756:26:  Assignments to the 'tournamentId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect  react-hooks/exhaustive-deps
Line 757:21:  Assignments to the 'matchId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect       react-hooks/exhaustive-deps
Line 778:6:   React Hook useEffect has a missing dependency: 'initCamera'. Either include it or remove the dependency array                                                                                                                                                                                  react-hooks/exhaustive-deps
src/pages/Tournaments/TestGuidedCapture.js
Line 22:10:  'Link' is defined but never used                        no-unused-vars
Line 56:9:   'handleUploadError' is assigned a value but never used  no-unused-vars
src/pages/Tournaments/TournamentDetail.js
Line 31:3:   'Tooltip' is defined but never used                                                                                 no-unused-vars
Line 32:3:   'IconButton' is defined but never used                                                                              no-unused-vars
Line 46:13:  'PersonIcon' is defined but never used                                                                              no-unused-vars
Line 74:6:   React Hook useEffect has a missing dependency: 'fetchTournament'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/TournamentForm.js
Line 18:3:  'IconButton' is defined but never used                                                                                                 no-unused-vars
Line 73:6:  React Hook useEffect has missing dependencies: 'fetchTournament' and 'isEditMode'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/TournamentList.js
Line 21:3:  'IconButton' is defined but never used                                                                               no-unused-vars
Line 22:3:  'Tooltip' is defined but never used                                                                                  no-unused-vars
Line 54:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Training/ScorecardTrainingPage.js
Line 46:6:  React Hook useEffect has missing dependencies: 'fetchModelStatus' and 'fetchServerStatus'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/pages/TransferMarket/TransferMarket.js
Line 10:3:  'CircularProgress' is defined but never used   no-unused-vars
Line 13:3:  'CardContent' is defined but never used        no-unused-vars
Line 14:3:  'CardMedia' is defined but never used          no-unused-vars
Line 15:3:  'CardActions' is defined but never used        no-unused-vars
Line 16:3:  'Chip' is defined but never used               no-unused-vars
Line 22:3:  'Tooltip' is defined but never used            no-unused-vars
Line 36:8:  'InfoIcon' is defined but never used           no-unused-vars
Line 44:9:  'isMobile' is assigned a value but never used  no-unused-vars
src/services/exportService.js
Line 46:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/mlTrainingService.js
Line 103:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/ocrService.js
Line 106:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerMatchingService.js
Line 271:11:  'noMatch' is assigned a value but never used                    no-unused-vars
Line 335:1:   Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerPhotoService.js
Line 42:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerService.js
Line 194:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/scorecardService.js
Line 60:7:   Expected an error object to be thrown                           no-throw-literal
Line 65:7:   Expected an error object to be thrown                           no-throw-literal
Line 67:7:   Expected an error object to be thrown                           no-throw-literal
Line 69:7:   Expected an error object to be thrown                           no-throw-literal
Line 71:7:   Expected an error object to be thrown                           no-throw-literal
Line 112:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/teamService.js
Line 189:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/tournamentService.js
Line 147:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/trainingService.js
Line 528:5:    Unreachable code                      no-unreachable
Line 1248:22:  'box' was used before it was defined  no-use-before-define
src/services/uploadService.js
Line 153:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/theme/EnhancedThemeProvider.js
Line 49:6:  React Hook useMemo has a missing dependency: 'themeOptions'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
Line 65:7:  React Hook useMemo has missing dependencies: 'handleSetTheme', 'themeNames', and 'themeOptions'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/theme/ThemeOptions.js
Line 1:10:  'alpha' is defined but never used  no-unused-vars
src/utils/auth.js
Line 60:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.
File sizes after gzip:
457.02 kB  build/static/js/main.537510a3.js
7.08 kB    build/static/css/main.796a8c97.css
1.76 kB    build/static/js/453.a3c1d153.chunk.js
The project was built assuming it is hosted at /.
You can control this with the homepage field in your package.json.
The build folder is ready to be deployed.
You may serve it with a static server:
npm install -g serve
serve -s build
Find out more about deployment here:
https://cra.link/deployment
- Done (1m 32s)
- Pruning dev dependencies
- Running `npm prune`
up to date, audited 1406 packages in 3s
packages are looking for funding
run `npm fund` for details
vulnerabilities (3 moderate, 6 high)
To address all issues (including breaking changes), run:
npm audit fix --force
Run `npm audit` for details.
- Done (3.1s)
- Configuring default processes
- Skipping default web process (Procfile detected)
- Done (finished in 1m 59s)
## Procfile Buildpack
- Processes from `Procfile`
- web: `npm install && npm start`
- Done (finished in < 0.1s)
===> EXPORTING
Reusing layer 'heroku/nodejs-engine:available_parallelism'
Reusing layer 'heroku/nodejs-engine:dist'
Reusing layer 'heroku/nodejs-engine:web_env'
Reusing layer 'heroku/nodejs-npm-install:npm_runtime_config'
Reusing layer 'buildpacksio/lifecycle:launch.sbom'
Added 1/1 app layer(s)
Reusing layer 'buildpacksio/lifecycle:launcher'
Reusing layer 'buildpacksio/lifecycle:config'
Reusing layer 'buildpacksio/lifecycle:process-types'
Adding label 'io.buildpacks.lifecycle.metadata'
Adding label 'io.buildpacks.build.metadata'
Adding label 'io.buildpacks.project.metadata'
Setting default process type 'web'
Saving rpl-frontend-3bqqpj...
*** Images (a5967f5d97b3):
rpl-frontend-3bqqpj
ERROR: failed to write export report: failed to write report file: write /layers/report.toml: no space left on device
ERROR: failed to build: executing lifecycle: failed with status code: 62
Error ❌
## Heroku Node.js Engine
- Checking Node.js version
- Node.js version not specified, using `22.x`
- Resolved Node.js version: `22.17.0`
- Installing Node.js distribution
- Reusing Node.js 22.17.0 (linux-amd64)
- Done (finished in < 0.1s)
## Heroku Node.js npm Install
- Installing node modules
- Using npm version `10.9.2`
- Restoring npm cache
- Configuring npm cache directory
- Running `npm ci`
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated stable@0.1.8: Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility
npm warn deprecated @babel/plugin-proposal-private-methods@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
npm warn deprecated @babel/plugin-proposal-numeric-separator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-numeric-separator instead.
npm warn deprecated @babel/plugin-proposal-nullish-coalescing-operator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
npm warn deprecated @babel/plugin-proposal-class-properties@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
npm warn deprecated rollup-plugin-terser@7.0.2: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-terser
npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
npm warn deprecated @babel/plugin-proposal-optional-chaining@7.21.0: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-chaining instead.
npm warn deprecated @babel/plugin-proposal-private-property-in-object@7.21.11: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
npm warn deprecated domexception@2.0.1: Use your platform's native DOMException instead
npm warn deprecated w3c-hr-time@1.0.2: Use your platform's native performance.now() and performance.timeOrigin.
npm warn deprecated q@1.5.1: You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.
npm warn deprecated
npm warn deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)
npm warn deprecated sourcemap-codec@1.4.8: Please use @jridgewell/sourcemap-codec instead
npm warn deprecated workbox-cacheable-response@6.6.0: workbox-background-sync@6.6.0
npm warn deprecated workbox-google-analytics@6.6.0: It is not compatible with newer versions of GA starting with v4, as long as you are using GAv3 it should be ok, but the package is not longer being maintained
npm warn deprecated svgo@1.3.2: This SVGO version is no longer supported. Upgrade to v2.x.x.
npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
added 1406 packages, and audited 1407 packages in 23s
packages are looking for funding
run `npm fund` for details
vulnerabilities (3 moderate, 6 high)
To address all issues (including breaking changes), run:
npm audit fix --force
Run `npm audit` for details.
- Done (23.4s)
- Running scripts
- Running `npm run build`
> client@0.1.0 build
> react-scripts build
Creating an optimized production build...
Compiled with warnings.
[eslint]
src/App.js
Line 28:8:  'SimpleCameraTest' is defined but never used  no-unused-vars
src/components/AdminMatchManagement.js
Line 18:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/components/CricketPlayerCard.js
Line 2:15:  'Typography' is defined but never used                   no-unused-vars
Line 48:9:  'getNationalityCode' is assigned a value but never used  no-unused-vars
src/components/ExportIplPlayers.js
Line 10:3:  'Link' is defined but never used  no-unused-vars
src/components/ImportIplPlayers.js
Line 9:3:   'Card' is defined but never used         no-unused-vars
Line 10:3:  'CardContent' is defined but never used  no-unused-vars
src/components/Tournaments/EnhancedMatchForm.js
Line 151:6:    React Hook useEffect has a missing dependency: 'mapExtractedDataToForm'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
Line 437:9:    'prepareMatchDataForApi' is assigned a value but never used                                                                no-unused-vars
Line 734:9:    'markFieldValidated' is assigned a value but never used                                                                    no-unused-vars
Line 781:11:   'team1Wickets' is assigned a value but never used                                                                          no-unused-vars
Line 1011:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1011:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1016:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
Line 1016:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
src/components/Tournaments/MatchDetail.js
Line 10:3:   'Grid' is defined but never used              no-unused-vars
Line 11:3:   'Divider' is defined but never used           no-unused-vars
Line 18:3:   'Tooltip' is defined but never used           no-unused-vars
Line 26:3:   'Accordion' is defined but never used         no-unused-vars
Line 27:3:   'AccordionSummary' is defined but never used  no-unused-vars
Line 28:3:   'AccordionDetails' is defined but never used  no-unused-vars
Line 40:17:  'ExpandMoreIcon' is defined but never used    no-unused-vars
src/components/Tournaments/MatchFormStep.js
Line 110:11:  'isOcrFailed' is assigned a value but never used   no-unused-vars
Line 389:13:  'playerIssues' is assigned a value but never used  no-unused-vars
src/components/Tournaments/OCRDataViewer.js
Line 18:3:  'TextField' is defined but never used  no-unused-vars
src/components/Tournaments/PlayerMatchingViewer.js
Line 165:57:  'candidates' is assigned a value but never used  no-unused-vars
src/components/Tournaments/ScorecardUploadStep.js
Line 23:12:  'ImageIcon' is defined but never used  no-unused-vars
src/components/Training/ImageSelectionModal.js
Line 13:3:   'FormControlLabel' is defined but never used  no-unused-vars
Line 26:12:  'ImageIcon' is defined but never used         no-unused-vars
src/components/Training/ScorecardLabeler.js
Line 2:10:    'API_URL' is defined but never used                    no-unused-vars
Line 1041:9:  'getCategoryColor' is assigned a value but never used  no-unused-vars
src/components/admin/TemplateBuilder.js
Line 6:9:    'imageRef' is assigned a value but never used                                                                    no-unused-vars
Line 16:10:  'isResizing' is assigned a value but never used                                                                  no-unused-vars
Line 16:22:  'setIsResizing' is assigned a value but never used                                                               no-unused-vars
Line 872:7:  Expected a default case                                                                                          default-case
Line 960:6:  React Hook useEffect has a missing dependency: 'redrawCanvas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/components/auction/AuctionPlayerCard.js
Line 3:3:  'Box' is defined but never used         no-unused-vars
Line 6:3:  'Typography' is defined but never used  no-unused-vars
src/components/auction/BidActivityFeed.js
Line 13:3:   'IconButton' is defined but never used         no-unused-vars
Line 20:18:  'ArrowUpwardIcon' is defined but never used    no-unused-vars
Line 21:20:  'ArrowDownwardIcon' is defined but never used  no-unused-vars
Line 22:17:  'AccessTimeIcon' is defined but never used     no-unused-vars
Line 24:18:  'TrophyIcon' is defined but never used         no-unused-vars
src/components/auction/PlayerAuctionStatus.js
Line 1:38:   'useRef' is defined but never used                  no-unused-vars
Line 10:3:   'LinearProgress' is defined but never used          no-unused-vars
Line 17:3:   'Tooltip' is defined but never used                 no-unused-vars
Line 22:18:  'ArrowUpwardIcon' is defined but never used         no-unused-vars
Line 24:18:  'TrophyIcon' is defined but never used              no-unused-vars
Line 151:9:  'theme' is assigned a value but never used          no-unused-vars
Line 202:9:  'isSmallScreen' is assigned a value but never used  no-unused-vars
src/components/dashboard/ResponsiveDashboardCard.js
Line 3:3:   'Box' is defined but never used                no-unused-vars
Line 47:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/components/layout/NewLayout.js
Line 41:8:   'Brightness4Icon' is defined but never used           no-unused-vars
Line 42:8:   'Brightness7Icon' is defined but never used           no-unused-vars
Line 45:8:   'AccountBalanceWalletIcon' is defined but never used  no-unused-vars
Line 171:9:  'isDark' is assigned a value but never used           no-unused-vars
src/components/players/CardGallery.js
Line 5:3:  'Typography' is defined but never used  no-unused-vars
src/components/players/ImportPlayersDialog.js
Line 21:3:  'Stack' is defined but never used  no-unused-vars
src/components/ui/ResponsiveCard.js
Line 9:3:   'Box' is defined but never used                no-unused-vars
Line 40:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/pages/Admin/OcrSettings.js
Line 8:3:   'Card' is defined but never used            no-unused-vars
Line 9:3:   'CardHeader' is defined but never used      no-unused-vars
Line 10:3:  'CardContent' is defined but never used     no-unused-vars
Line 11:3:  'TextField' is defined but never used       no-unused-vars
Line 13:3:  'Divider' is defined but never used         no-unused-vars
Line 48:9:  'theme' is assigned a value but never used  no-unused-vars
src/pages/Admin/PlayerManagement.js
Line 8:3:    'Card' is defined but never used                      no-unused-vars
Line 9:3:    'CardContent' is defined but never used               no-unused-vars
Line 21:3:   'IconButton' is defined but never used                no-unused-vars
Line 30:11:  'EditIcon' is defined but never used                  no-unused-vars
Line 35:15:  'CheckBoxIcon' is defined but never used              no-unused-vars
Line 36:27:  'CheckBoxOutlineBlankIcon' is defined but never used  no-unused-vars
Line 53:10:  'filteredPlayers' is assigned a value but never used  no-unused-vars
src/pages/Auction/AuctionListings.js
Line 32:8:   'MoneyIcon' is defined but never used                                                                                      no-unused-vars
Line 42:3:   'deleteAuction' is defined but never used                                                                                  no-unused-vars
Line 49:7:   'AuctionCard' is assigned a value but never used                                                                           no-unused-vars
Line 443:6:  React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Auction/LiveAuctionDashboard.js
Line 15:3:    'Button' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 21:3:    'useMediaQuery' is defined but never used                                                                                                                                                                                                                          no-unused-vars
Line 28:12:   'GavelIcon' is defined but never used                                                                                                                                                                                                                              no-unused-vars
Line 29:17:   'AccessTimeIcon' is defined but never used                                                                                                                                                                                                                         no-unused-vars
Line 36:10:   'format' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 38:10:   'motion' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
Line 65:10:   'bidLoading' is assigned a value but never used                                                                                                                                                                                                                    no-unused-vars
Line 149:32:  The ref value 'socketRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'socketRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
Line 157:6:   React Hook useEffect has a missing dependency: 'selectedAuction'. Either include it or remove the dependency array                                                                                                                                                 react-hooks/exhaustive-deps
Line 217:9:   'handleNewBid' is assigned a value but never used                                                                                                                                                                                                                  no-unused-vars
Line 423:6:   React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array                                                                                                                                          react-hooks/exhaustive-deps
src/pages/Dashboard/EnhancedDashboard.js
Line 27:9:  'isMobile' is assigned a value but never used  no-unused-vars
Line 28:9:  'isTablet' is assigned a value but never used  no-unused-vars
src/pages/Players/MyTeam.js
Line 6:3:   'Box' is defined but never used                                                                                  no-unused-vars
Line 28:6:  React Hook useEffect has a missing dependency: 'fetchPlayers'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/TeamManagement/TeamDashboard.js
Line 16:3:  'CardActions' is defined but never used     no-unused-vars
Line 20:3:  'ListItemAvatar' is defined but never used  no-unused-vars
Line 21:3:  'Avatar' is defined but never used          no-unused-vars
src/pages/TeamManagement/TeamRoster.js
Line 17:3:  'IconButton' is defined but never used         no-unused-vars
Line 28:3:  'Tooltip' is defined but never used            no-unused-vars
Line 33:8:  'RemoveIcon' is defined but never used         no-unused-vars
Line 35:8:  'InfoIcon' is defined but never used           no-unused-vars
Line 45:9:  'isMobile' is assigned a value but never used  no-unused-vars
src/pages/TeamManagement/TeamSettings.js
Line 16:3:  'CardContent' is defined but never used  no-unused-vars
Line 24:3:  'Chip' is defined but never used         no-unused-vars
Line 30:8:  'EditIcon' is defined but never used     no-unused-vars
src/pages/Tournaments/SimpleCameraTest.js
Line 21:10:  'cameraPermission' is assigned a value but never used                                                          no-unused-vars
Line 125:6:  React Hook useEffect has a missing dependency: 'stopCamera'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/SimplifiedGuidedCapture.js
Line 611:6:   React Hook useCallback has a missing dependency: 'debugMode'. Either include it or remove the dependency array                                                                                                                                                                                 react-hooks/exhaustive-deps
Line 756:26:  Assignments to the 'tournamentId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect  react-hooks/exhaustive-deps
Line 757:21:  Assignments to the 'matchId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect       react-hooks/exhaustive-deps
Line 778:6:   React Hook useEffect has a missing dependency: 'initCamera'. Either include it or remove the dependency array                                                                                                                                                                                  react-hooks/exhaustive-deps
src/pages/Tournaments/TestGuidedCapture.js
Line 22:10:  'Link' is defined but never used                        no-unused-vars
Line 56:9:   'handleUploadError' is assigned a value but never used  no-unused-vars
src/pages/Tournaments/TournamentDetail.js
Line 31:3:   'Tooltip' is defined but never used                                                                                 no-unused-vars
Line 32:3:   'IconButton' is defined but never used                                                                              no-unused-vars
Line 46:13:  'PersonIcon' is defined but never used                                                                              no-unused-vars
Line 74:6:   React Hook useEffect has a missing dependency: 'fetchTournament'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/TournamentForm.js
Line 18:3:  'IconButton' is defined but never used                                                                                                 no-unused-vars
Line 73:6:  React Hook useEffect has missing dependencies: 'fetchTournament' and 'isEditMode'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Tournaments/TournamentList.js
Line 21:3:  'IconButton' is defined but never used                                                                               no-unused-vars
Line 22:3:  'Tooltip' is defined but never used                                                                                  no-unused-vars
Line 54:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
src/pages/Training/ScorecardTrainingPage.js
Line 46:6:  React Hook useEffect has missing dependencies: 'fetchModelStatus' and 'fetchServerStatus'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/pages/TransferMarket/TransferMarket.js
Line 10:3:  'CircularProgress' is defined but never used   no-unused-vars
Line 13:3:  'CardContent' is defined but never used        no-unused-vars
Line 14:3:  'CardMedia' is defined but never used          no-unused-vars
Line 15:3:  'CardActions' is defined but never used        no-unused-vars
Line 16:3:  'Chip' is defined but never used               no-unused-vars
Line 22:3:  'Tooltip' is defined but never used            no-unused-vars
Line 36:8:  'InfoIcon' is defined but never used           no-unused-vars
Line 44:9:  'isMobile' is assigned a value but never used  no-unused-vars
src/services/exportService.js
Line 46:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/mlTrainingService.js
Line 103:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/ocrService.js
Line 106:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerMatchingService.js
Line 271:11:  'noMatch' is assigned a value but never used                    no-unused-vars
Line 335:1:   Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerPhotoService.js
Line 42:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/playerService.js
Line 194:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/scorecardService.js
Line 60:7:   Expected an error object to be thrown                           no-throw-literal
Line 65:7:   Expected an error object to be thrown                           no-throw-literal
Line 67:7:   Expected an error object to be thrown                           no-throw-literal
Line 69:7:   Expected an error object to be thrown                           no-throw-literal
Line 71:7:   Expected an error object to be thrown                           no-throw-literal
Line 112:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/teamService.js
Line 189:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/tournamentService.js
Line 147:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/services/trainingService.js
Line 528:5:    Unreachable code                      no-unreachable
Line 1248:22:  'box' was used before it was defined  no-use-before-define
src/services/uploadService.js
Line 153:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
src/theme/EnhancedThemeProvider.js
Line 49:6:  React Hook useMemo has a missing dependency: 'themeOptions'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
Line 65:7:  React Hook useMemo has missing dependencies: 'handleSetTheme', 'themeNames', and 'themeOptions'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
src/theme/ThemeOptions.js
Line 1:10:  'alpha' is defined but never used  no-unused-vars
src/utils/auth.js
Line 60:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.
File sizes after gzip:
457.02 kB  build/static/js/main.537510a3.js
7.08 kB    build/static/css/main.796a8c97.css
1.76 kB    build/static/js/453.a3c1d153.chunk.js
The project was built assuming it is hosted at /.
You can control this with the homepage field in your package.json.
The build folder is ready to be deployed.
You may serve it with a static server:
npm install -g serve
serve -s build
Find out more about deployment here:
https://cra.link/deployment
- Done (1m 32s)
- Pruning dev dependencies
- Running `npm prune`
up to date, audited 1406 packages in 3s
packages are looking for funding
run `npm fund` for details
vulnerabilities (3 moderate, 6 high)
To address all issues (including breaking changes), run:
npm audit fix --force
Run `npm audit` for details.
- Done (3.1s)
- Configuring default processes
- Skipping default web process (Procfile detected)
- Done (finished in 1m 59s)
ERROR: failed to build: executing lifecycle: failed with status code: 62