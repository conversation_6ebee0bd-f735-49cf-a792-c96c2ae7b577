#!/bin/bash

# GitHub Issues Creation Script for RPL Cricket Application
# 
# This script creates all the GitHub issues based on our detailed task breakdown
# and sets up proper labels, milestones, and project board integration.
#
# Prerequisites:
# 1. Install GitHub CLI: https://cli.github.com/
# 2. Authenticate: gh auth login
#
# Usage:
# chmod +x scripts/create-github-issues.sh
# ./scripts/create-github-issues.sh

set -e

echo "🚀 Creating GitHub Issues for RPL Cricket Application"
echo "===================================================="

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI is not installed. Please install it first:"
    echo "   https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub. Please run:"
    echo "   gh auth login"
    exit 1
fi

echo "✅ GitHub CLI is ready"
echo ""

# Function to create CRITICAL priority issues
create_critical_issues() {
    echo "🔴 Creating CRITICAL priority issues..."
    
    # Issue 1: Transfer Market System
    gh issue create \
        --title "🔄 Complete Transfer Market System" \
        --body "## Description
Complete the player trading system between teams with market value calculations and transfer history.

## Acceptance Criteria
- [ ] Player trading between teams
- [ ] Market value calculations  
- [ ] Transfer history tracking
- [ ] Transaction validation

## Files
- \`client/src/pages/TransferMarket/\`
- \`server/controllers/\` (needs completion)

## Remaining Work
Complete trading logic, market value algorithms, transaction history

## Phase
2.6: Player & Team Management

## Priority
🔴 Critical

## Estimated Effort
1 week" \
        --label "🔴 Critical,✨ Feature,🏆 Auction,🎨 Frontend" \
        --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    # Issue 2: Skill Points & Rating System
    gh issue create \
        --title "🎮 Implement Advanced Skill Points & Rating System" \
        --body "## Description
Implement automatic rating increases based on skill points (5000 points = +1 rating), configurable thresholds.

## Acceptance Criteria
- [ ] 1 run = 1 skill point
- [ ] 1 wicket = 10 skill points  
- [ ] 5000 skill points = +1 rating increase
- [ ] Admin configurable thresholds
- [ ] Automatic rating updates after each match

## Big Ant Cricket 24 Alignment
This is a core feature from the original vision where player ratings increase based on performance.

## Phase
7.1: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical

## Estimated Effort
1 week" \
        --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,⚙️ Backend" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    # Issue 3: Performance Milestone Bonuses
    gh issue create \
        --title "🎮 Add Performance Milestone Bonuses" \
        --body "## Description
Implement milestone bonus system: 30's (+60 points), 50's (+90 points), 100's (+150 points), 3W hauls (+60 points), 5W hauls (+90 points).

## Acceptance Criteria
- [ ] Batting milestones: 30 (+60), 50 (+90), 100 (+150) bonus points
- [ ] Bowling milestones: 3W (+60), 5W (+90) bonus points
- [ ] Automatic detection from scorecard OCR
- [ ] Historical milestone tracking

## Big Ant Cricket 24 Alignment
Essential for the original vision where milestones provide bonus skill points.

## Phase
7.2: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical

## Estimated Effort
1 week" \
        --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,🔍 OCR" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    echo "✅ Critical issues created"
}

# Function to create HIGH priority issues
create_high_priority_issues() {
    echo "🟠 Creating HIGH priority issues..."
    
    # Issue 4: Match Result Processing
    gh issue create \
        --title "🔄 Refine Match Result Processing" \
        --body "## Description
Complete score validation, winner determination, player statistics updates, and match verification.

## Acceptance Criteria
- [ ] Refine score validation logic
- [ ] Complete player statistics updates
- [ ] Improve match verification
- [ ] Winner determination accuracy

## Files
- \`server/controllers/matchOutcomeController.js\`
- \`server/controllers/scorecardController.js\`

## Phase
3.5: Tournament & Match Management

## Priority
🟠 High

## Estimated Effort
1 week" \
        --label "🟠 High,🔧 Enhancement,🏟️ Tournament,⚙️ Backend" \
        --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    # Issue 5: Post-Auction Processing
    gh issue create \
        --title "🔄 Complete Post-Auction Processing" \
        --body "## Description
Finalize player assignment to teams, payment processing, and auction result finalization.

## Acceptance Criteria
- [ ] Complete player assignment logic
- [ ] Finalize payment processing
- [ ] Auction result notifications
- [ ] Post-auction cleanup

## Files
- Auction controller (needs completion)

## Phase
4.6: Auction System

## Priority
🟠 High

## Estimated Effort
1 week" \
        --label "🟠 High,🔧 Enhancement,🏆 Auction,⚙️ Backend" \
        --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    # Issue 6: Comprehensive Leaderboards
    gh issue create \
        --title "🎮 Build Comprehensive Leaderboards" \
        --body "## Description
Create comprehensive leaderboards for Most Runs, 30's, 50's, 100's, Wickets, 3W/5W Hauls, MOM - format and tournament wise.

## Acceptance Criteria
- [ ] Multiple leaderboard categories
- [ ] Format-wise filtering (T10, T20, ODI, Test)
- [ ] Tournament-wise and overall statistics
- [ ] Real-time updates after each match

## Big Ant Cricket 24 Alignment
Leaderboards are essential for competitive gaming experience.

## Phase
7.3: Big Ant Cricket 24 Integration Features

## Priority
🟠 High

## Estimated Effort
2 weeks" \
        --label "🟠 High,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    # Issue 7: Testing & Quality Assurance
    gh issue create \
        --title "🧪 Complete Testing & Quality Assurance" \
        --body "## Description
Add comprehensive test coverage, set up automated testing pipeline.

## Acceptance Criteria
- [ ] Unit tests for all major components
- [ ] Integration tests for API endpoints
- [ ] End-to-end testing for user workflows
- [ ] Automated testing pipeline

## Phase
6.4: Production & Deployment

## Priority
🟠 High

## Estimated Effort
2 weeks" \
        --label "🟠 High,🧪 Testing,🔧 Enhancement,🚀 Deployment" \
        --milestone "Production Optimization" || echo "⚠️  Issue may already exist"

    echo "✅ High priority issues created"
}

# Function to create MEDIUM priority issues
create_medium_priority_issues() {
    echo "🟡 Creating MEDIUM priority issues..."
    
    # Issue 8: Strike Rate & Economy Calculations
    gh issue create \
        --title "🎮 Add Strike Rate & Economy Calculations" \
        --body "## Description
Auto-calculate strike rates (runs/balls) and economy rates (runs/overs) from scorecard OCR.

## Acceptance Criteria
- [ ] Strike rate calculation: (runs/balls) * 100
- [ ] Economy rate calculation: runs conceded/overs bowled
- [ ] Automatic calculation from OCR data
- [ ] Historical tracking and trends

## Phase
7.4: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week" \
        --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" \
        --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    # Issue 9: Enhanced Match Validation
    gh issue create \
        --title "🎮 Enhanced Match Validation" \
        --body "## Description
Auto-detect chase/defend from scorecard, improved team name validation.

## Acceptance Criteria
- [ ] Automatic chase/defend detection
- [ ] Enhanced team name validation
- [ ] Match format auto-detection
- [ ] Improved error handling and user feedback

## Phase
7.8: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week" \
        --label "🟡 Medium,🔧 Enhancement,🎮 Big Ant Cricket 24,🔍 OCR" \
        --milestone "Enhanced Features" || echo "⚠️  Issue may already exist"

    # Issue 10: Database Optimization
    gh issue create \
        --title "🗄️ Database Optimization" \
        --body "## Description
MongoDB indexing, query optimization, connection pooling, performance tuning.

## Acceptance Criteria
- [ ] Add database indexes for frequently queried fields
- [ ] Optimize slow queries
- [ ] Implement connection pooling
- [ ] Performance monitoring and tuning

## Phase
6.2: Production & Deployment

## Priority
🟡 Medium

## Estimated Effort
1 week" \
        --label "🟡 Medium,🔧 Enhancement,🗄️ Database,⚙️ Backend" \
        --milestone "Production Optimization" || echo "⚠️  Issue may already exist"

    echo "✅ Medium priority issues created"
}

# Function to create additional Big Ant Cricket 24 features
create_additional_features() {
    echo "🎮 Creating additional Big Ant Cricket 24 features..."
    
    # Issue 11: Fastest Milestones Tracking
    gh issue create \
        --title "🎮 Fastest Milestones Tracking" \
        --body "## Description
Track fastest 50's, 100's with Top 5 rankings, dynamic updates when records are broken.

## Acceptance Criteria
- [ ] Track fastest 50's and 100's (balls faced)
- [ ] Top 5 rankings for each milestone
- [ ] Dynamic updates when records are broken
- [ ] Personal best tracking

## Phase
7.5: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1.5 weeks" \
        --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" \
        --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    # Issue 12: Venue-based Performance Analytics
    gh issue create \
        --title "🎮 Venue-based Performance Analytics" \
        --body "## Description
Track performance by venue with tournament filtering capabilities.

## Acceptance Criteria
- [ ] Performance statistics by venue
- [ ] Tournament filtering
- [ ] Venue-specific leaderboards
- [ ] Historical venue performance

## Phase
7.6: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week" \
        --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" \
        --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    # Issue 13: Test Match Support
    gh issue create \
        --title "🎮 Test Match Support" \
        --body "## Description
4-innings match processing, extended scorecard handling for Test format.

## Acceptance Criteria
- [ ] Support for 4-innings matches
- [ ] Extended scorecard processing
- [ ] Test-specific statistics
- [ ] Multi-day match handling

## Phase
7.7: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
2 weeks" \
        --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,🔍 OCR" \
        --milestone "Enhanced Features" || echo "⚠️  Issue may already exist"

    echo "✅ Additional features created"
}

# Main execution
main() {
    echo "Starting GitHub issues creation..."
    echo ""
    
    create_critical_issues
    echo ""
    
    create_high_priority_issues
    echo ""
    
    create_medium_priority_issues
    echo ""
    
    create_additional_features
    echo ""
    
    echo "🎉 All GitHub issues created successfully!"
    echo ""
    echo "📋 Summary:"
    echo "• 3 Critical priority issues (Complete first)"
    echo "• 4 High priority issues (Complete next)"
    echo "• 3 Medium priority issues (Complete after)"
    echo "• 3 Additional Big Ant Cricket 24 features"
    echo ""
    echo "📝 Next steps:"
    echo "1. Go to your GitHub repository issues tab"
    echo "2. Review and organize the created issues"
    echo "3. Create a project board and add these issues"
    echo "4. Start working on the Critical priority tasks first"
    echo ""
    echo "🎯 Priority order:"
    echo "1. 🔄 Complete Transfer Market System"
    echo "2. 🎮 Implement Advanced Skill Points & Rating System"
    echo "3. 🎮 Add Performance Milestone Bonuses"
}

# Run main function
main
