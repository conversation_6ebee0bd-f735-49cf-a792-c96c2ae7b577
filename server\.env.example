# Server Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/rpl-cricket
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/rpl-cricket

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Google Cloud Vision API (for OCR)
GOOGLE_APPLICATION_CREDENTIALS=./config/google-cloud-credentials.json

# CORS Configuration
FRONTEND_URL=https://your-frontend-domain.com

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# OCR Configuration
OCR_SPACE_API_KEY=your-ocr-space-api-key
PADDLE_OCR_ENABLED=true

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true

# Auction Configuration
AUCTION_CHECK_INTERVAL=60000

# Email Configuration (if needed)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Redis Configuration
# For Dokploy built-in Redis, use the Internal Connection URL from Dokploy dashboard:
# REDIS_URL=redis://default:your-password@rpl-redis-xxxxx:6379
# OR use individual variables (REDIS_URL takes priority if both are set):
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
