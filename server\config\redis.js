const Redis = require('ioredis');

class RedisService {
  constructor() {
    this.client = null;
    this.subscriber = null;
    this.publisher = null;
    this.isConnected = false;
    this.isEnabled = process.env.REDIS_ENABLED !== 'false';
    this.connectionAttempted = false;
    this.lastConnectionAttempt = 0;
    this.reconnectDelay = 30000; // 30 seconds between reconnection attempts
  }

  async connect() {
    if (!this.isEnabled) {
      console.log('⚠️ Redis is disabled. Running without Redis caching.');
      return false;
    }

    // Prevent excessive connection attempts
    const now = Date.now();
    if (this.connectionAttempted && (now - this.lastConnectionAttempt) < this.reconnectDelay) {
      return false;
    }

    this.lastConnectionAttempt = now;
    this.connectionAttempted = true;

    try {
      console.log('🔍 Redis Environment Variables:');
      console.log('REDIS_URL:', process.env.REDIS_URL ? '[SET]' : '[NOT SET]');
      console.log('REDIS_HOST:', process.env.REDIS_HOST || '[NOT SET]');
      console.log('REDIS_PORT:', process.env.REDIS_PORT || '[NOT SET]');
      console.log('REDIS_PASSWORD:', process.env.REDIS_PASSWORD ? '[SET]' : '[NOT SET]');

      let redisConfig;

      // Priority 1: Use REDIS_URL if provided
      if (process.env.REDIS_URL) {
        console.log('📡 Using REDIS_URL for connection');
        redisConfig = process.env.REDIS_URL;
      } else {
        // Priority 2: Use individual environment variables
        console.log('🔧 Using individual Redis environment variables');
        redisConfig = {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT) || 6379,
          password: process.env.REDIS_PASSWORD || undefined,
          db: parseInt(process.env.REDIS_DB) || 0,
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: 0,
          lazyConnect: true,
          connectTimeout: 5000,
          commandTimeout: 5000,
          retryDelayOnClusterDown: 300,
          retryDelayOnFailover: 100,
          enableOfflineQueue: false,
          autoResubscribe: false,
          autoResendUnfulfilledCommands: false,
          maxRetriesPerRequest: 0
        };
      }

      console.log('🚀 Attempting Redis connection with config:', 
        typeof redisConfig === 'string' ? 'REDIS_URL' : 
        `${redisConfig.host}:${redisConfig.port}`);

      // For REDIS_URL, add lazyConnect to prevent auto-connection
      if (typeof redisConfig === 'string') {
        // Parse URL and add lazyConnect option
        const urlConfig = new URL(redisConfig);
        redisConfig = {
          host: urlConfig.hostname,
          port: parseInt(urlConfig.port) || 6379,
          password: urlConfig.password || undefined,
          db: parseInt(urlConfig.pathname.slice(1)) || 0,
          username: urlConfig.username || undefined,
          lazyConnect: true,
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: 0,
          connectTimeout: 5000,
          commandTimeout: 5000,
          enableOfflineQueue: false,
          autoResubscribe: false,
          autoResendUnfulfilledCommands: false
        };
        console.log('🔧 Parsed Redis URL to config:', `${redisConfig.host}:${redisConfig.port}`);
      }

      // Main Redis client
      this.client = new Redis(redisConfig);
      
      // Separate clients for pub/sub
      this.subscriber = new Redis(redisConfig);
      this.publisher = new Redis(redisConfig);

      // Event handlers
      this.client.on('connect', () => {
        console.log('✅ Redis client connected');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        if (!this.isConnected) {
          // Only log the first error to avoid spam
          console.error('❌ Redis client error:', err.message);
        }
        this.isConnected = false;
      });

      this.subscriber.on('connect', () => {
        console.log('✅ Redis subscriber connected');
      });

      this.subscriber.on('error', (err) => {
        if (!this.isConnected) {
          // Only log the first error to avoid spam
          console.error('❌ Redis subscriber error:', err.message);
        }
      });

      this.publisher.on('connect', () => {
        console.log('✅ Redis publisher connected');
      });

      this.publisher.on('error', (err) => {
        if (!this.isConnected) {
          // Only log the first error to avoid spam
          console.error('❌ Redis publisher error:', err.message);
        }
      });

      // Connect all clients with timeout and proper error handling
      const connectPromises = [];
      
      // Only connect if not already connected/connecting
      if (this.client.status !== 'ready' && this.client.status !== 'connecting') {
        connectPromises.push(this.client.connect());
      }
      if (this.subscriber.status !== 'ready' && this.subscriber.status !== 'connecting') {
        connectPromises.push(this.subscriber.connect());
      }
      if (this.publisher.status !== 'ready' && this.publisher.status !== 'connecting') {
        connectPromises.push(this.publisher.connect());
      }

      if (connectPromises.length > 0) {
        await Promise.race([
          Promise.all(connectPromises),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Redis connection timeout')), 8000)
          )
        ]);
      } else {
        console.log('🔄 Redis clients already connected or connecting');
      }

      // Verify connections are actually ready
      const clientReady = this.client.status === 'ready';
      const subscriberReady = this.subscriber.status === 'ready';
      const publisherReady = this.publisher.status === 'ready';
      
      console.log('🔍 Connection Status Check:');
      console.log('  Client:', this.client.status);
      console.log('  Subscriber:', this.subscriber.status);
      console.log('  Publisher:', this.publisher.status);
      
      if (clientReady && subscriberReady && publisherReady) {
        // Disable automatic reconnection after successful connection
        this.client.options.retryDelayOnFailover = 0;
        this.subscriber.options.retryDelayOnFailover = 0;
        this.publisher.options.retryDelayOnFailover = 0;
        
        this.isConnected = true;
        console.log('🚀 Redis service initialized successfully');
        return true;
      } else {
        throw new Error(`Redis connection verification failed - Client: ${this.client.status}, Subscriber: ${this.subscriber.status}, Publisher: ${this.publisher.status}`);
      }
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error.message);
      console.log('⚠️ Continuing without Redis. Real-time features will be limited.');
      this.isConnected = false;
      this.isEnabled = false;
      
      // Disconnect any partially connected clients to prevent retry attempts
      if (this.client) {
        this.client.disconnect();
        this.client = null;
      }
      if (this.subscriber) {
        this.subscriber.disconnect();
        this.subscriber = null;
      }
      if (this.publisher) {
        this.publisher.disconnect();
        this.publisher = null;
      }
      
      return false;
    }
  }

  async disconnect() {
    try {
      if (this.client) await this.client.disconnect();
      if (this.subscriber) await this.subscriber.disconnect();
      if (this.publisher) await this.publisher.disconnect();
      
      this.isConnected = false;
      console.log('✅ Redis disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting Redis:', error);
    }
  }

  getClient() {
    return this.isConnected ? this.client : null;
  }

  getSubscriber() {
    return this.isConnected ? this.subscriber : null;
  }

  getPublisher() {
    return this.isConnected ? this.publisher : null;
  }

  isRedisConnected() {
    return this.isConnected;
  }

  isRedisEnabled() {
    return this.isEnabled;
  }
}

module.exports = new RedisService();