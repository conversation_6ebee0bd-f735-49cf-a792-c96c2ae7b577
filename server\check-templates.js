const mongoose = require('mongoose');
const Template = require('./models/Template');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cricket24', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function checkTemplates() {
  try {
    console.log('🔍 Checking templates in database...');
    
    // Get all templates (active and inactive)
    const allTemplates = await Template.find({});
    console.log(`\n📊 Total templates in database: ${allTemplates.length}`);
    
    if (allTemplates.length > 0) {
      console.log('\n📋 Template details:');
      allTemplates.forEach((template, index) => {
        console.log(`${index + 1}. Name: "${template.name}"`);
        console.log(`   - ID: ${template._id}`);
        console.log(`   - Active: ${template.isActive}`);
        console.log(`   - Regions: ${template.regions?.length || 0}`);
        console.log(`   - Created: ${template.createdAt}`);
        console.log(`   - Has Image: ${!!template.originalImageData}`);
        console.log('   ---');
      });
      
      // Check for specific names
      const ps5Templates = allTemplates.filter(t => t.name.includes('PS5'));
      if (ps5Templates.length > 0) {
        console.log('\n🎯 PS5 related templates found:');
        ps5Templates.forEach(t => {
          console.log(`   - "${t.name}" (Active: ${t.isActive})`);
        });
      }
    } else {
      console.log('\n✅ Database is clean - no templates found');
    }
    
    // Check active templates only
    const activeTemplates = await Template.find({ isActive: true });
    console.log(`\n🟢 Active templates: ${activeTemplates.length}`);
    
    // Check inactive templates only
    const inactiveTemplates = await Template.find({ isActive: false });
    console.log(`🔴 Inactive templates: ${inactiveTemplates.length}`);
    
    if (inactiveTemplates.length > 0) {
      console.log('\n🗑️ Inactive templates (causing duplicate issues):');
      inactiveTemplates.forEach(t => {
        console.log(`   - "${t.name}" (ID: ${t._id})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking templates:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

checkTemplates();
