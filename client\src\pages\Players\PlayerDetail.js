import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Box,
  Button,
  Chip,
  LinearProgress,
  Card,
  CardMedia,
  CardContent,
  CircularProgress,
  Divider,
  Alert,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ArrowBack,
  SportsCricket,
  ShoppingCart,
  Star,
  BarChart,
  AttachMoney,
  Person
} from '@mui/icons-material';

import { getPlayerById, buyPlayer } from '../../services/playerService';
import { useAuth } from '../../hooks/useAuth';

const PlayerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [player, setPlayer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [buyDialogOpen, setBuyDialogOpen] = useState(false);
  const [buyLoading, setBuyLoading] = useState(false);
  const [buyError, setBuyError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Fetch player data
  useEffect(() => {
    const fetchPlayer = async () => {
      setLoading(true);
      try {
        const data = await getPlayerById(id);
        setPlayer(data);
      } catch (err) {
        console.error('Error fetching player:', err);
        setError('Failed to load player details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchPlayer();
  }, [id]);
  
  // Handle buy player
  const handleBuyPlayer = async () => {
    setBuyLoading(true);
    setBuyError(null);
    
    try {
      await buyPlayer(id);
      
      // Update player to reflect new ownership
      const updatedPlayer = await getPlayerById(id);
      setPlayer(updatedPlayer);
      
      // Show success message
      setSuccessMessage(`You've successfully acquired ${player.name}!`);
      
      // Close dialog
      setBuyDialogOpen(false);
    } catch (err) {
      console.error('Error buying player:', err);
      setBuyError(err.response?.data?.message || 'Failed to purchase player. Please try again.');
    } finally {
      setBuyLoading(false);
    }
  };
  
  // Get color based on rating
  const getRatingColor = (rating) => {
    if (rating >= 90) return '#009900'; // Green
    if (rating >= 80) return '#59b300'; // Light Green
    if (rating >= 70) return '#e6ac00'; // Yellow
    if (rating >= 60) return '#ff8c1a'; // Orange
    return '#cc2900'; // Red
  };
  
  // Get background color based on rarity
  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'Common':
        return 'linear-gradient(135deg, #f0f0f0 0%, #d0d0d0 100%)';
      case 'Rare':
        return 'linear-gradient(135deg, #95ccff 0%, #2986cc 100%)';
      case 'Epic':
        return 'linear-gradient(135deg, #c58af9 0%, #7c21d9 100%)';
      case 'Legendary':
        return 'linear-gradient(135deg, #ffd966 0%, #e69138 100%)';
      default:
        return 'linear-gradient(135deg, #f0f0f0 0%, #d0d0d0 100%)';
    }
  };
  
  // Check if player is available for purchase
  const isPlayerAvailable = player && !player.owner;
  
  // Check if current user is owner
  const isOwner = player && user && player.owner?._id === user.id;
  
  // Determine if user can afford the player
  const canAffordPlayer = user && player && user.coins >= player.marketValue;
  
  // Stats section
  const renderStatsSection = () => {
    if (!player) return null;
    
    return (
      <Paper sx={{ p: 3, mt: 4 }} elevation={2}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />
          Detailed Stats
        </Typography>
        
        <Grid container spacing={2}>
          {/* Batting Stats */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>Batting Stats</Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={1}>
              {Object.entries(player.stats.batting || {}).map(([key, value]) => (
                <Grid item xs={6} key={`batting-${key}`}>
                  <Box sx={{ mb: 1.5 }}>
                    <Typography variant="body2" color="text.secondary">
                      {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {key.includes('rate') || key.includes('average') 
                        ? parseFloat(value).toFixed(2) 
                        : value}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>
          
          {/* Bowling Stats */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>Bowling Stats</Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={1}>
              {Object.entries(player.stats.bowling || {}).map(([key, value]) => (
                <Grid item xs={6} key={`bowling-${key}`}>
                  <Box sx={{ mb: 1.5 }}>
                    <Typography variant="body2" color="text.secondary">
                      {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {key.includes('rate') || key.includes('average') || key.includes('economy')
                        ? parseFloat(value).toFixed(2) 
                        : value}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Grid>
        </Grid>
        
        {/* Special Abilities */}
        {player.specialAbilities && player.specialAbilities.length > 0 && (
          <Box sx={{ mt: 4 }}>
            <Typography variant="subtitle1" gutterBottom>Special Abilities</Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {player.specialAbilities.map((ability) => (
                <Chip
                  key={ability}
                  icon={<Star />}
                  label={ability}
                  color="secondary"
                />
              ))}
            </Box>
          </Box>
        )}
      </Paper>
    );
  };
  
  // Back to players list
  const handleBack = () => {
    navigate('/players');
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Back button */}
      <Button 
        startIcon={<ArrowBack />} 
        onClick={handleBack} 
        sx={{ mb: 3 }}
      >
        Back to Players
      </Button>
      
      {/* Success message */}
      {successMessage && (
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          onClose={() => setSuccessMessage(null)}
        >
          {successMessage}
        </Alert>
      )}
      
      {/* Loading spinner */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Player details */}
      {!loading && player && (
        <>
          <Card 
            sx={{ 
              overflow: 'hidden',
              mb: 4,
              borderRadius: 2,
              background: getRarityColor(player.rarity)
            }}
          >
            <Grid container>
              {/* Player image */}
              <Grid item xs={12} md={4}>
                <Box
                  sx={{
                    position: 'relative',
                    height: '100%',
                    minHeight: 300,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    p: 2,
                    backgroundColor: 'rgba(255,255,255,0.9)',
                  }}
                >
                  <img 
                    src={player.image || '/placeholder-player.png'} 
                    alt={player.name}
                    style={{
                      maxWidth: '100%',
                      maxHeight: '300px',
                      objectFit: 'contain'
                    }}
                  />
                  
                  {/* Rarity badge */}
                  <Chip
                    label={player.rarity}
                    color="primary"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      left: 16,
                      fontWeight: 'bold'
                    }}
                  />
                  
                  {/* Overall rating */}
                  <Box
                    sx={{
                      position: 'absolute',
                      right: 16,
                      top: 16,
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      borderRadius: '50%',
                      width: 60,
                      height: 60,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: 2,
                    }}
                  >
                    <Typography
                      variant="h4"
                      sx={{
                        color: getRatingColor(player.ratings.overall),
                        fontWeight: 'bold'
                      }}
                    >
                      {player.ratings.overall}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
              
              {/* Player info */}
              <Grid item xs={12} md={8}>
                <CardContent sx={{ p: 4, backgroundColor: 'rgba(255,255,255,0.9)' }}>
                  <Typography variant="h4" component="h1" gutterBottom>
                    {player.name}
                  </Typography>
                  
                  {/* Basic info chips */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    <Chip 
                      icon={<SportsCricket />} 
                      label={player.type} 
                      variant="outlined" 
                    />
                    <Chip 
                      label={player.nationality} 
                      variant="outlined" 
                    />
                    <Chip 
                      label={`${player.battingStyle} | ${player.bowlingStyle !== 'None' ? player.bowlingStyle : 'Does not bowl'}`}
                      variant="outlined" 
                    />
                  </Box>
                  
                  {/* Status */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Status
                    </Typography>
                    {player.owner ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Person sx={{ mr: 1 }} />
                        <Typography>
                          {isOwner 
                            ? "This player is in your team" 
                            : `Owned by ${player.owner.username} (${player.owner.teamName})`}
                        </Typography>
                      </Box>
                    ) : (
                      <Chip 
                        label="Available for purchase" 
                        color="success" 
                        variant="outlined" 
                      />
                    )}
                  </Box>
                  
                  {/* Market value */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Market Value
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AttachMoney sx={{ mr: 1, color: '#4caf50' }} />
                      <Typography variant="h5" color="#4caf50" fontWeight="bold">
                        {player.marketValue.toLocaleString()} coins
                      </Typography>
                    </Box>
                  </Box>
                  
                  {/* Player ratings */}
                  <Typography variant="subtitle1" gutterBottom>
                    Ratings
                  </Typography>
                  
                  {/* Batting rating */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 80 }}>Batting:</Typography>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={(player.ratings.batting / 99) * 100}
                        sx={{
                          height: 8,
                          borderRadius: 5,
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getRatingColor(player.ratings.batting)
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body1" fontWeight="medium">
                      {player.ratings.batting}
                    </Typography>
                  </Box>
                  
                  {/* Bowling rating */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" sx={{ minWidth: 80 }}>Bowling:</Typography>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={(player.ratings.bowling / 99) * 100}
                        sx={{
                          height: 8,
                          borderRadius: 5,
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getRatingColor(player.ratings.bowling)
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body1" fontWeight="medium">
                      {player.ratings.bowling}
                    </Typography>
                  </Box>
                  
                  {/* Fielding rating */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Typography variant="body2" sx={{ minWidth: 80 }}>Fielding:</Typography>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={(player.ratings.fielding / 99) * 100}
                        sx={{
                          height: 8,
                          borderRadius: 5,
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getRatingColor(player.ratings.fielding)
                          }
                        }}
                      />
                    </Box>
                    <Typography variant="body1" fontWeight="medium">
                      {player.ratings.fielding}
                    </Typography>
                  </Box>
                  
                  {/* Buy button */}
                  {isPlayerAvailable && user && (
                    <Button
                      variant="contained"
                      color="primary"
                      size="large"
                      startIcon={<ShoppingCart />}
                      onClick={() => setBuyDialogOpen(true)}
                      disabled={!canAffordPlayer}
                      fullWidth
                    >
                      {canAffordPlayer 
                        ? `Buy for ${player.marketValue.toLocaleString()} coins` 
                        : `Need ${(player.marketValue - user.coins).toLocaleString()} more coins`}
                    </Button>
                  )}
                </CardContent>
              </Grid>
            </Grid>
          </Card>
          
          {/* Stats section */}
          {renderStatsSection()}
          
          {/* Buy confirmation dialog */}
          <Dialog
            open={buyDialogOpen}
            onClose={() => setBuyDialogOpen(false)}
          >
            <DialogTitle>Confirm Purchase</DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                Are you sure you want to buy {player.name} for {player.marketValue.toLocaleString()} coins?
              </Typography>
              
              {user && (
                <Typography variant="body2">
                  Your balance after purchase: <strong>{(user.coins - player.marketValue).toLocaleString()} coins</strong>
                </Typography>
              )}
              
              {buyError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {buyError}
                </Alert>
              )}
            </DialogContent>
            <DialogActions>
              <Button 
                onClick={() => setBuyDialogOpen(false)} 
                disabled={buyLoading}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={handleBuyPlayer}
                disabled={buyLoading}
                color="primary"
                autoFocus
              >
                {buyLoading ? <CircularProgress size={24} /> : 'Confirm Purchase'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}
    </Container>
  );
};

export default PlayerDetail;