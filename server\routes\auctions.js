const express = require('express');
const router = express.Router();
const auctionController = require('../controllers/auctionController');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');

/**
 * @route   GET api/auctions
 * @desc    Get all auctions with filtering options
 * @access  Public
 */
router.get('/', auctionController.getAllAuctions);

/**
 * @route   GET api/auctions/my-bids
 * @desc    Get auctions the current user has bid on
 * @access  Private
 */
router.get('/my-bids', authenticateToken, auctionController.getMyBids);

/**
 * @route   GET api/auctions/:id
 * @desc    Get a specific auction by ID
 * @access  Public
 */
router.get('/:id', auctionController.getAuctionById);

/**
 * @route   POST api/auctions
 * @desc    Create a new auction
 * @access  Private (Admin only)
 */
router.post(
  '/',
  [authenticateToken, roleAuth(['admin'])],
  auctionController.createAuction
);

/**
 * @route   PUT api/auctions/:id
 * @desc    Update an auction
 * @access  Private (Admin only)
 */
router.put(
  '/:id',
  [authenticateToken, roleAuth(['admin'])],
  auctionController.updateAuction
);

/**
 * @route   DELETE api/auctions/:id
 * @desc    Delete an auction
 * @access  Private (Admin only)
 */
router.delete(
  '/:id',
  [authenticateToken, roleAuth(['admin'])],
  auctionController.deleteAuction
);

/**
 * @route   POST api/auctions/test-bid
 * @desc    Test endpoint for bidding
 * @access  Public
 */
router.post('/test-bid', (req, res) => {
  console.log('Test bid route hit!');
  console.log('Request body:', req.body);
  res.json({ success: true, message: 'Test bid received', data: req.body });
});

/**
 * @route   POST api/auctions/:id/bid
 * @desc    Place a bid on an auction
 * @access  Private (Team Owner only)
 */
router.post(
  '/:id/bid',
  authenticateToken,
  (req, res, next) => {
    console.log('Bid route hit!');
    console.log('Auction ID:', req.params.id);
    console.log('Bid amount:', req.body.amount);
    next();
  },
  auctionController.placeBid
);

/**
 * @route   POST api/auctions/process-completed
 * @desc    Process completed auctions and assign players to winners
 * @access  Private (Admin only)
 */
router.post(
  '/process-completed',
  [authenticateToken, roleAuth(['admin'])],
  auctionController.processCompletedAuctions
);

module.exports = router;
