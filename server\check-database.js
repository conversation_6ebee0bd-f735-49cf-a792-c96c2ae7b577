const mongoose = require('mongoose');

async function checkDatabase() {
  try {
    await mongoose.connect('mongodb://localhost:27017/rpl-new');
    console.log('Connected to database: rpl-new');
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(col => {
      console.log(`  - ${col.name}`);
    });
    
    // Check if there are any documents in tournaments collection
    const tournamentsCount = await mongoose.connection.db.collection('tournaments').countDocuments();
    console.log(`\nDocuments in tournaments collection: ${tournamentsCount}`);
    
    // Check other possible tournament-related collections
    const possibleCollections = ['tournament', 'Tournament', 'tournaments', 'Tournaments'];
    for (const colName of possibleCollections) {
      try {
        const count = await mongoose.connection.db.collection(colName).countDocuments();
        if (count > 0) {
          console.log(`Found ${count} documents in collection: ${colName}`);
          const sample = await mongoose.connection.db.collection(colName).findOne();
          console.log('Sample document:', JSON.stringify(sample, null, 2));
        }
      } catch (err) {
        // Collection doesn't exist, ignore
      }
    }
    
    // Check if we're connected to the right database
    console.log(`\nCurrent database: ${mongoose.connection.name}`);
    
    // List all databases
    const admin = mongoose.connection.db.admin();
    const dbs = await admin.listDatabases();
    console.log('\nAll databases:');
    dbs.databases.forEach(db => {
      console.log(`  - ${db.name}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkDatabase();