const mongoose = require('mongoose');
const Tournament = require('./models/Tournament');

async function debugTournamentData() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/rpl-new', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('Connected to MongoDB');
    
    // Check all databases
    const admin = mongoose.connection.db.admin();
    const dbs = await admin.listDatabases();
    console.log('Available databases:', dbs.databases.map(db => db.name));
    
    // Check current database name
    console.log('Current database:', mongoose.connection.name);
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('Collections in current database:', collections.map(c => c.name));
    
    // Check tournaments collection specifically
    const tournamentsCollection = mongoose.connection.db.collection('tournaments');
    const tournamentCount = await tournamentsCollection.countDocuments();
    console.log('Tournament documents count:', tournamentCount);
    
    if (tournamentCount > 0) {
      const tournaments = await tournamentsCollection.find({}).toArray();
      console.log('\nTournaments found:');
      tournaments.forEach((t, index) => {
        console.log(`${index + 1}. ${t.name} (ID: ${t._id})`);
        console.log(`   Status: ${t.status}`);
        console.log(`   Phases: ${t.phases ? t.phases.length : 0}`);
        if (t.phases && t.phases.length > 0) {
          t.phases.forEach((phase, pIndex) => {
            console.log(`   Phase ${pIndex + 1}: ${phase.name}`);
            console.log(`     Matches: ${phase.matches ? phase.matches.length : 0}`);
            console.log(`     Standings: ${phase.standings ? phase.standings.length : 0}`);
            if (phase.matches && phase.matches.length > 0) {
              phase.matches.forEach((match, mIndex) => {
                console.log(`     Match ${mIndex + 1}: ${match._id}`);
                console.log(`       Status: ${match.status}`);
                console.log(`       Home: ${match.homeTeam}, Away: ${match.awayTeam}`);
              });
            }
          });
        }
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

debugTournamentData();