const mongoose = require('mongoose');
const Template = require('./models/Template');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cricket24', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function cleanupTemplates() {
  try {
    console.log('🧹 Starting template cleanup...');
    
    // Get all inactive templates first
    const inactiveTemplates = await Template.find({ isActive: false });
    console.log(`\n🔴 Found ${inactiveTemplates.length} inactive templates to delete:`);
    
    inactiveTemplates.forEach(t => {
      console.log(`   - "${t.name}" (ID: ${t._id})`);
    });
    
    // Delete all inactive templates permanently
    const deleteResult = await Template.deleteMany({ isActive: false });
    console.log(`\n✅ Permanently deleted ${deleteResult.deletedCount} inactive templates`);
    
    // Check remaining templates
    const remainingTemplates = await Template.find({});
    console.log(`\n📊 Remaining templates in database: ${remainingTemplates.length}`);
    
    if (remainingTemplates.length > 0) {
      console.log('\n📋 Remaining templates:');
      remainingTemplates.forEach(t => {
        console.log(`   - "${t.name}" (Active: ${t.isActive})`);
      });
    } else {
      console.log('\n🎉 Database is now clean! No templates remaining.');
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    console.log('\n🎯 You can now try saving "PS5-T20" template again!');
  }
}

cleanupTemplates();
