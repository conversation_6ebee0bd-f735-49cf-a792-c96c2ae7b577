import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  LinearProgress
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import GroupIcon from '@mui/icons-material/Group';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import GavelIcon from '@mui/icons-material/Gavel';
import { useAuth } from '../../hooks/useAuth';
import {
  getTeamSettings,
  getTeamRoster,
  getTeamBudget,
  getTeamStatistics
} from '../../services/teamService';

const TeamDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const [teamData, setTeamData] = useState({
    teamName: user?.teamName || '',
    description: '',
    logo: '/uploads/teams/default-logo.png',
    primaryColor: '#1e88e5',
    secondaryColor: '#bbdefb'
  });

  const [rosterData, setRosterData] = useState({
    players: [],
    totalPlayers: 0
  });

  const [budgetData, setBudgetData] = useState({
    totalBudget: 0,
    allocations: {}
  });

  const [statsData, setStatsData] = useState({
    totalMatches: 0,
    wins: 0,
    losses: 0,
    draws: 0,
    winPercentage: 0
  });

  // Load team data from server
  useEffect(() => {
    if (!user || user.role !== 'team_owner') return;

    setLoading(true);

    const fetchTeamData = async () => {
      try {
        // Fetch team settings from server
        let settings;
        try {
          settings = await getTeamSettings();
          console.log('Loaded team settings from server:', settings);
        } catch (settingsErr) {
          console.error('Error fetching team settings from server:', settingsErr);

          // Fallback to localStorage if server request fails
          const savedSettings = localStorage.getItem(`teamSettings_${user.id}`);
          if (savedSettings) {
            settings = JSON.parse(savedSettings);
            console.log('Fallback: Loaded saved team settings from localStorage:', settings);
          } else {
            // Use default settings if nothing is saved
            settings = {
              teamName: user.teamName || 'My Team',
              description: 'This is a placeholder for your team description. The full team management features are coming soon!',
              logo: '/uploads/teams/default-logo.png',
              primaryColor: '#1e88e5',
              secondaryColor: '#bbdefb'
            };
            console.log('Using default team settings');
          }
        }

        // Ensure teamName is set correctly from user data if not in settings
        if (!settings.teamName) {
          settings.teamName = user.teamName || 'My Team';
        }

        setTeamData(settings);

        // Fetch roster data from server
        let roster;
        try {
          roster = await getTeamRoster();
          console.log('Loaded team roster from server:', roster);
          setRosterData({
            players: roster.players?.slice(0, 5) || [], // Show only first 5 players on dashboard
            totalPlayers: roster.players?.length || 0
          });
        } catch (rosterErr) {
          console.error('Error fetching team roster from server:', rosterErr);

          // Fallback to localStorage if server request fails
          const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);
          if (savedRoster) {
            const rosterData = JSON.parse(savedRoster);
            setRosterData({
              players: rosterData.slice(0, 5), // Show only first 5 players on dashboard
              totalPlayers: rosterData.length
            });
            console.log('Fallback: Loaded saved team roster from localStorage:', rosterData);
          } else {
            setRosterData({
              players: [],
              totalPlayers: 0
            });
            console.log('No roster data available');
          }
        }

        // Fetch budget data from server
        let budget;
        try {
          budget = await getTeamBudget();
          console.log('Loaded team budget from server:', budget);
          setBudgetData(budget);
        } catch (budgetErr) {
          console.error('Error fetching team budget from server:', budgetErr);

          // Use placeholder budget data if server request fails
          setBudgetData({
            totalBudget: 10000,
            allocations: {
              playerAcquisition: { percentage: 60, amount: 6000 },
              playerDevelopment: { percentage: 20, amount: 2000 },
              teamOperations: { percentage: 10, amount: 1000 },
              marketing: { percentage: 5, amount: 500 },
              reserve: { percentage: 5, amount: 500 }
            }
          });
        }

        // Fetch statistics data from server
        let stats;
        try {
          stats = await getTeamStatistics();
          console.log('Loaded team statistics from server:', stats);
          setStatsData(stats);
        } catch (statsErr) {
          console.error('Error fetching team statistics from server:', statsErr);

          // Use placeholder statistics if server request fails
          setStatsData({
            totalMatches: 0,
            wins: 0,
            losses: 0,
            draws: 0,
            winPercentage: 0
          });
        }
      } catch (err) {
        console.error('Error loading team data:', err);
        setError('Failed to load team data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTeamData();
  }, [user]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Team Header */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(to right, ${teamData.primaryColor}, ${teamData.secondaryColor})`,
          color: theme.palette.getContrastText(teamData.primaryColor)
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={2}>
            {teamData.logo !== '/uploads/teams/default-logo.png' && teamData.logoUrl ? (
              <Box
                component="img"
                src={teamData.logoUrl}
                alt={teamData.teamName}
                sx={{
                  width: 100,
                  height: 100,
                  borderRadius: '50%',
                  objectFit: 'cover',
                  border: `4px solid ${teamData.primaryColor}`,
                  bgcolor: 'white',
                  mx: 'auto'
                }}
              />
            ) : (
              <Box
                sx={{
                  width: 100,
                  height: 100,
                  borderRadius: '50%',
                  bgcolor: teamData.secondaryColor,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: `4px solid ${teamData.primaryColor}`,
                  mx: 'auto'
                }}
              >
                <Typography
                  variant="h3"
                  sx={{
                    color: teamData.primaryColor,
                    fontWeight: 'bold'
                  }}
                >
                  {teamData.teamName.charAt(0).toUpperCase()}
                </Typography>
              </Box>
            )}
          </Grid>
          <Grid item xs={12} sm={7}>
            <Typography variant="h4" gutterBottom>
              {teamData.teamName}
            </Typography>
            <Typography variant="subtitle1">
              {teamData.description || 'No team description available.'}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={3} sx={{ textAlign: { xs: 'center', sm: 'right' } }}>
            <Button
              component={RouterLink}
              to="/team/settings"
              variant="contained"
              startIcon={<SettingsIcon />}
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.9)',
                color: teamData.primaryColor,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.7)'
                }
              }}
            >
              Team Settings
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mb: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          Team Management Features
        </Typography>
        <Typography variant="body1" paragraph>
          You can now customize your team's appearance in the Team Settings page and manage your players in the Team Roster page!
        </Typography>
        <Typography variant="body1">
          We're still working on implementing budget allocation features. Stay tuned for updates!
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            component={RouterLink}
            to="/team/settings"
            variant="contained"
            color="secondary"
            startIcon={<SettingsIcon />}
          >
            Team Settings
          </Button>
          <Button
            component={RouterLink}
            to="/team/roster"
            variant="contained"
            color="primary"
            startIcon={<GroupIcon />}
          >
            Manage Roster
          </Button>
        </Box>
      </Paper>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={3}>
          <Button
            component={RouterLink}
            to="/team/roster"
            variant="outlined"
            fullWidth
            startIcon={<GroupIcon />}
            sx={{ py: 1.5 }}
          >
            Manage Roster
          </Button>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Button
            component={RouterLink}
            to="/market"
            variant="outlined"
            fullWidth
            startIcon={<AccountBalanceWalletIcon />}
            sx={{ py: 1.5 }}
          >
            Transfer Market
          </Button>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Button
            component={RouterLink}
            to="/auctions"
            variant="outlined"
            fullWidth
            startIcon={<GavelIcon />}
            sx={{ py: 1.5 }}
          >
            Player Auctions
          </Button>
        </Grid>
        <Grid item xs={12} sm={3}>
          <Button
            variant="outlined"
            fullWidth
            startIcon={<PersonAddIcon />}
            sx={{ py: 1.5 }}
            disabled
          >
            Add Players (Coming Soon)
          </Button>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Team Statistics */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Team Statistics
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {statsData.totalMatches}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Matches
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">
                    {statsData.wins}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Wins
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="error.main">
                    {statsData.losses}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Losses
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="text.secondary">
                    {statsData.draws}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Draws
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Win Rate</Typography>
                <Typography variant="body2">{statsData.winPercentage}%</Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={statsData.winPercentage}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Budget Overview */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Budget Overview
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Total Budget
              </Typography>
              <Typography variant="h4" color="primary">
                {budgetData.totalBudget.toLocaleString()} Credits
              </Typography>
            </Box>

            {Object.entries(budgetData.allocations).length > 0 ? (
              <List dense>
                {Object.entries(budgetData.allocations).map(([id, allocation]) => (
                  <ListItem key={id}>
                    <ListItemText
                      primary={id.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      secondary={`${allocation.percentage}% (${allocation.amount.toLocaleString()} Credits)`}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No budget allocations set up yet.
              </Typography>
            )}

            <Box sx={{ mt: 2, textAlign: 'right' }}>
              <Button
                component={RouterLink}
                to="/team/budget"
                color="primary"
              >
                Manage Budget
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Team Roster */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Team Roster
              </Typography>
              <Button
                component={RouterLink}
                to="/team/roster"
                color="primary"
              >
                View All ({rosterData.totalPlayers})
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />

            {rosterData.players.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  Your team doesn't have any players yet.
                </Typography>
                <Button
                  component={RouterLink}
                  to="/team/roster"
                  variant="outlined"
                  startIcon={<PersonAddIcon />}
                  sx={{ mt: 2 }}
                  state={{ openAddDialog: true }}
                >
                  Add Your First Player
                </Button>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {rosterData.players.map((player) => (
                  <Grid item xs={12} sm={6} md={4} lg={2.4} key={player._id}>
                    <Card sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4
                      }
                    }}>
                      <CardMedia
                        component="img"
                        height={isMobile ? 120 : 140}
                        image={player.image}
                        alt={player.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                        <Typography variant="subtitle1" gutterBottom noWrap>
                          {player.name}
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          <Chip
                            label={player.type}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                          <Chip
                            label={`OVR ${player.ratings.overall}`}
                            size="small"
                            color="secondary"
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default TeamDashboard;
