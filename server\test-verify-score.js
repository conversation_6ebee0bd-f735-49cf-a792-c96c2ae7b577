const GoogleVisionService = require('./services/googleVisionService');
const path = require('path');

async function testVerifyScore() {
  console.log('🚀 Testing Score Verification with Google Cloud Vision...\n');

  try {
    // Test image path
    const imagePath = path.join(__dirname, 'uploads/scorecards/scorecard9.png');
    
    console.log(`📸 Testing with image: ${imagePath}`);

    // Initialize Google Vision service
    const visionService = new GoogleVisionService();

    // Verify JOHN MORTIMORE's score
    console.log('\n🔍 Verifying JOHN MORTIMORE\'s score:');
    const johnResult = await visionService.verifyPlayerScore(imagePath, 'JOHN MORTIMORE');
    
    if (johnResult.found) {
      console.log(`✅ Found JOHN MORTIMORE with score: ${johnResult.runs}(${johnResult.balls})`);
      console.log('\nElements in the same row:');
      johnResult.elements.forEach(el => console.log(el));
    } else {
      console.log('❌ JOHN MORTIMORE not found');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testVerifyScore();