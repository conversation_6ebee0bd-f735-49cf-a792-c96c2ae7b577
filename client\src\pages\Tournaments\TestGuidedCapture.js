import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Alert,
  Divider
} from '@mui/material';
import {
  CameraAlt as CameraIcon,
  CloudUpload as UploadIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import ScorecardUpload from '../../components/Tournaments/ScorecardUpload';
import { Link, useNavigate } from 'react-router-dom';

/**
 * Test page for the Scorecard Capture feature
 * This page demonstrates both the traditional upload and the simplified guided capture approaches
 */
const TestGuidedCapture = () => {
  const navigate = useNavigate();
  const [traditionalUploadOpen, setTraditionalUploadOpen] = useState(false);
  const [uploadedScorecard, setUploadedScorecard] = useState(null);
  const [error, setError] = useState(null);

  // Mock tournament and match IDs for testing
  const mockTournamentId = '60d21b4667d0d8992e610c85';
  const mockMatchId = '60d21b4667d0d8992e610c86';

  // Mock tournament object for testing
  const mockTournament = {
    _id: mockTournamentId,
    name: 'Test Tournament',
    registeredTeams: [
      { _id: '60d21b4667d0d8992e610c87', teamName: 'Team 1' },
      { _id: '60d21b4667d0d8992e610c88', teamName: 'Team 2' },
      { _id: '60d21b4667d0d8992e610c89', teamName: 'Team 3' }
    ]
  };

  // Handle successful upload
  const handleUploadSuccess = (result) => {
    setUploadedScorecard(result.scorecard);
    setError(null);
  };

  // Handle upload error
  const handleUploadError = (err) => {
    setError(err.message || 'Failed to upload scorecard');
    setUploadedScorecard(null);
  };

  // Navigate to simplified guided capture
  const goToGuidedCapture = () => {
    navigate('/scorecard-capture');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Scorecard Capture
      </Typography>

      <Typography variant="body1" paragraph>
        This page provides options for uploading match scorecards. You can use the camera-based guided capture
        or the traditional file upload method.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Capture methods */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h5" gutterBottom>
              Capture Methods
            </Typography>

            <Typography variant="body2" color="text.secondary" paragraph>
              Choose how you want to upload your match scorecard.
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 3 }}>
              <Button
                variant="contained"
                color="success"
                startIcon={<CameraIcon />}
                onClick={goToGuidedCapture}
                fullWidth
                size="large"
                sx={{ py: 2 }}
              >
                Camera Guided Capture
              </Button>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                px: 1,
                py: 1,
                bgcolor: 'success.light',
                color: 'success.contrastText',
                borderRadius: 1
              }}>
                <CheckCircleIcon color="inherit" />
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  Recommended: Uses your device camera with real-time guidance to capture the scorecard directly from your TV/monitor.
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }}>OR</Divider>

              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => setTraditionalUploadOpen(true)}
                fullWidth
                size="large"
              >
                Traditional File Upload
              </Button>

              <Typography variant="body2" color="text.secondary" sx={{ px: 1 }}>
                Upload an existing screenshot or photo from your device.
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h5" gutterBottom>
              Uploaded Scorecard
            </Typography>

            {uploadedScorecard ? (
              <Box>
                <Card>
                  <CardMedia
                    component="img"
                    image={uploadedScorecard.url}
                    alt="Uploaded scorecard"
                    sx={{ maxHeight: 300, objectFit: 'contain' }}
                  />
                  <CardContent>
                    <Typography variant="body2" color="text.secondary">
                      Uploaded at: {new Date(uploadedScorecard.uploadedAt).toLocaleString()}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button size="small" color="primary">
                      View Details
                    </Button>
                  </CardActions>
                </Card>
              </Box>
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: 300,
                bgcolor: 'action.hover',
                borderRadius: 1
              }}>
                <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  No scorecard uploaded yet
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Use one of the methods on the left to upload a scorecard
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Traditional Upload Dialog */}
      <ScorecardUpload
        open={traditionalUploadOpen}
        onClose={() => setTraditionalUploadOpen(false)}
        tournamentId={mockTournamentId}
        matchId={mockMatchId}
        onUploadSuccess={handleUploadSuccess}
        tournament={mockTournament} // Pass the mock tournament object
      />
    </Container>
  );
};

export default TestGuidedCapture;
