const express = require('express');
const router = express.Router();
const tournamentController = require('../controllers/tournamentController');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');

/**
 * @route   GET api/tournaments
 * @desc    Get all tournaments with filtering options
 * @access  Public
 */
router.get('/', tournamentController.getAllTournaments);

/**
 * @route   GET api/tournaments/:id
 * @desc    Get a specific tournament by ID
 * @access  Public
 */
router.get('/:id', tournamentController.getTournamentById);

/**
 * @route   POST api/tournaments
 * @desc    Create a new tournament
 * @access  Private (Admin only)
 */
router.post(
  '/',
  [authenticateToken, roleAuth(['admin'])],
  tournamentController.createTournament
);

/**
 * @route   PUT api/tournaments/:id
 * @desc    Update a tournament
 * @access  Private (Admin only)
 */
router.put(
  '/:id',
  [authenticateToken, roleAuth(['admin'])],
  tournamentController.updateTournament
);

/**
 * @route   DELETE api/tournaments/:id
 * @desc    Delete a tournament
 * @access  Private (Admin only)
 */
router.delete(
  '/:id',
  [authenticateToken, roleAuth(['admin'])],
  tournamentController.deleteTournament
);

/**
 * @route   POST api/tournaments/:id/register
 * @desc    Register team for tournament
 * @access  Private (Team Owner only)
 */
router.post(
  '/:id/register',
  [authenticateToken, roleAuth(['team_owner'])],
  tournamentController.registerTeam
);

/**
 * @route   POST api/tournaments/:id/unregister
 * @desc    Unregister team from tournament
 * @access  Private (Team Owner only)
 */
router.post(
  '/:id/unregister',
  [authenticateToken, roleAuth(['team_owner'])],
  tournamentController.unregisterTeam
);

/**
 * @route   GET api/tournaments/:id/next-round/:team1Id/:team2Id
 * @desc    Get next round number for a match between two teams
 * @access  Private (Team Owner only)
 */
router.get(
  '/:id/next-round/:team1Id/:team2Id',
  [authenticateToken, roleAuth(['team_owner'])],
  tournamentController.getNextRoundNumber
);

/**
 * @route   POST api/tournaments/:id/matches
 * @desc    Add a match result
 * @access  Private (Team Owner only)
 */
router.post(
  '/:id/matches',
  [authenticateToken, roleAuth(['team_owner'])],
  tournamentController.addMatch
);

/**
 * @route   GET api/tournaments/:id/matches/:team1Id/:team2Id
 * @desc    Get matches between two teams
 * @access  Private (Team Owner only)
 */
router.get(
  '/:id/matches/:team1Id/:team2Id',
  [authenticateToken, roleAuth(['team_owner'])],
  tournamentController.getMatchesBetweenTeams
);

module.exports = router;
