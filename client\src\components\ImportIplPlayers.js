import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Divider,
  Alert,
  Snackbar,
  FormControlLabel,
  Switch,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  MenuItem,
  Select,
  InputLabel,
  FormControl
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { scrapeIplPlayers } from '../services/playerService';
import CricketPlayerCard from './CricketPlayerCard';

const ImportIplPlayers = ({ onImportPlayers }) => {
  const [url, setUrl] = useState('https://www.iplt20.com/teams/delhi-capitals/squad/2025');
  const [loading, setLoading] = useState(false);
  const [players, setPlayers] = useState([]);
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [useMock, setUseMock] = useState(true); // Default to mock data for reliability
  const [editingPlayer, setEditingPlayer] = useState(null);
  const [editingPlayerIndex, setEditingPlayerIndex] = useState(null);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
  };

  const handleFetchPlayers = async () => {
    if (!url.includes('iplt20.com/teams')) {
      setError('Please enter a valid IPL team URL');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Pass the useMock parameter to the API
      const result = await scrapeIplPlayers(url, useMock);

      if (!result.data || result.data.length === 0) {
        setError('No players found. Please check the URL and try again.');
      } else {
        setPlayers(result.data);
        // Players are unselected by default now
        setSelectedPlayers([]);
        setSuccess(`Successfully fetched ${result.data.length} players using ${useMock ? 'mock data' : 'headless browser'}!`);
      }
    } catch (err) {
      console.error('Error fetching players:', err);
      setError('Error fetching players: ' + (typeof err === 'string' ? err : err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Add select all and deselect all functions
  const handleSelectAll = () => {
    setSelectedPlayers(players.map((_, index) => index));
  };

  const handleDeselectAll = () => {
    setSelectedPlayers([]);
  };

  const togglePlayerSelection = (index) => {
    setSelectedPlayers(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  const handleImportSelected = () => {
    if (selectedPlayers.length === 0) {
      setError('Please select at least one player to import');
      return;
    }

    const playersToImport = selectedPlayers.map(index => {
      const player = players[index];
      return {
        ...player,
        // Convert imageUrl to image property expected by the app
        image: player.imageUrl,
        // Add any other necessary transformations
      };
    });

    onImportPlayers(playersToImport);
    setSuccess(`${playersToImport.length} players imported successfully!`);
  };

  const handleCloseSnackbar = () => {
    setError('');
    setSuccess('');
  };

  // Edit player functionality
  const handleEditPlayer = (index) => {
    setEditingPlayerIndex(index);
    setEditingPlayer({...players[index]});
    setOpenEditDialog(true);
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
    setEditingPlayer(null);
    setEditingPlayerIndex(null);
  };

  const handleSaveEditedPlayer = () => {
    if (editingPlayerIndex !== null && editingPlayer) {
      const updatedPlayers = [...players];
      updatedPlayers[editingPlayerIndex] = editingPlayer;
      setPlayers(updatedPlayers);
      setOpenEditDialog(false);
      setEditingPlayer(null);
      setEditingPlayerIndex(null);
      setSuccess('Player updated successfully');
    }
  };

  const handleEditPlayerChange = (field, value) => {
    if (editingPlayer) {
      setEditingPlayer({
        ...editingPlayer,
        [field]: value
      });
    }
  };

  // Delete player functionality
  const handleDeletePlayer = (index) => {
    if (window.confirm('Are you sure you want to remove this player?')) {
      // Remove the player from the players array
      const updatedPlayers = players.filter((_, i) => i !== index);
      setPlayers(updatedPlayers);

      // Update selected players indices
      setSelectedPlayers(prevSelected => {
        return prevSelected
          .filter(i => i !== index) // Remove the deleted index
          .map(i => i > index ? i - 1 : i); // Adjust indices for players after the deleted one
      });

      setSuccess('Player removed successfully');
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Import IPL Players
      </Typography>

      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          label="IPL Team URL"
          variant="outlined"
          value={url}
          onChange={handleUrlChange}
          placeholder="https://www.iplt20.com/teams/team-name/squad/year"
          sx={{ mb: 2 }}
        />

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={useMock}
                onChange={(e) => setUseMock(e.target.checked)}
                color="primary"
              />
            }
            label="Use Mock Data"
          />
          <Tooltip title="Mock data provides reliable player information for testing. Headless browser attempts to scrape real data from the IPL website but requires Puppeteer to be installed on the server.">
            <InfoIcon color="action" fontSize="small" sx={{ ml: 1 }} />
          </Tooltip>
        </Box>

        <Button
          variant="contained"
          color="primary"
          onClick={handleFetchPlayers}
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
        >
          {loading ? 'Fetching Players...' : 'Fetch Players'}
        </Button>
      </Box>

      {players.length > 0 && (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h6">
                {players.length} Players Found ({selectedPlayers.length} selected)
              </Typography>
              <Box sx={{ mt: 1 }}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleSelectAll}
                  sx={{ mr: 1 }}
                >
                  Select All
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleDeselectAll}
                >
                  Deselect All
                </Button>
              </Box>
            </Box>

            <Button
              variant="contained"
              color="success"
              onClick={handleImportSelected}
              disabled={selectedPlayers.length === 0}
            >
              Import Selected Players
            </Button>
          </Box>

          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {players.map((player, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Box
                  onClick={() => togglePlayerSelection(index)}
                  sx={{ cursor: 'pointer' }}
                >
                  <CricketPlayerCard
                    player={{
                      _id: `temp-${index}`,
                      ...player,
                      image: player.imageUrl,
                      ratings: { overall: Math.floor(Math.random() * 30) + 70 }, // Random rating between 70-99
                      stats: {
                        battingAverage: (Math.random() * 50 + 10).toFixed(2),
                        strikeRate: (Math.random() * 150 + 50).toFixed(2),
                        wickets: Math.floor(Math.random() * 300),
                        economy: (Math.random() * 8 + 3).toFixed(2),
                        highScore: Math.floor(Math.random() * 150 + 50)
                      },
                      battingHand: 'Right',
                      bowlingHand: player.type.includes('Bowl') ? 'Right' : 'None'
                    }}
                    selected={selectedPlayers.includes(index)}
                    onSelect={() => togglePlayerSelection(index)}
                    onEdit={() => handleEditPlayer(index)}
                    onDelete={() => handleDeletePlayer(index)}
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </>
      )}

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>

      {/* Edit Player Dialog */}
      <Dialog open={openEditDialog} onClose={handleCloseEditDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Player</DialogTitle>
        <DialogContent>
          {editingPlayer && (
            <Box sx={{ pt: 2 }}>
              <TextField
                fullWidth
                label="Name"
                value={editingPlayer.name || ''}
                onChange={(e) => handleEditPlayerChange('name', e.target.value)}
                margin="normal"
              />

              <FormControl fullWidth margin="normal">
                <InputLabel>Player Type</InputLabel>
                <Select
                  value={editingPlayer.type || 'Batsman'}
                  onChange={(e) => handleEditPlayerChange('type', e.target.value)}
                  label="Player Type"
                >
                  <MenuItem value="Batsman">Batsman</MenuItem>
                  <MenuItem value="Bowler">Bowler</MenuItem>
                  <MenuItem value="Allrounder">Allrounder</MenuItem>
                  <MenuItem value="Wicket Keeper">Wicket Keeper</MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Nationality</InputLabel>
                <Select
                  value={editingPlayer.nationality || 'India'}
                  onChange={(e) => handleEditPlayerChange('nationality', e.target.value)}
                  label="Nationality"
                >
                  <MenuItem value="India">India</MenuItem>
                  <MenuItem value="Australia">Australia</MenuItem>
                  <MenuItem value="England">England</MenuItem>
                  <MenuItem value="South Africa">South Africa</MenuItem>
                  <MenuItem value="New Zealand">New Zealand</MenuItem>
                  <MenuItem value="West Indies">West Indies</MenuItem>
                  <MenuItem value="Pakistan">Pakistan</MenuItem>
                  <MenuItem value="Sri Lanka">Sri Lanka</MenuItem>
                  <MenuItem value="Bangladesh">Bangladesh</MenuItem>
                  <MenuItem value="Afghanistan">Afghanistan</MenuItem>
                  <MenuItem value="Zimbabwe">Zimbabwe</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="Image URL"
                value={editingPlayer.imageUrl || ''}
                onChange={(e) => handleEditPlayerChange('imageUrl', e.target.value)}
                margin="normal"
              />

              {editingPlayer.imageUrl && (
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <img
                    src={editingPlayer.imageUrl}
                    alt={editingPlayer.name}
                    style={{ maxHeight: '150px', maxWidth: '100%' }}
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = `${process.env.PUBLIC_URL}/uploads/players/default.png`;
                    }}
                  />
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button onClick={handleSaveEditedPlayer} variant="contained" color="primary">
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ImportIplPlayers;
