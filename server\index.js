const express = require('express');
const http = require('http');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const connectDB = require('./config/db');
const { processCompletedAuctions } = require('./utils/auctionScheduler');
const socketService = require('./services/socketService');
const auctionService = require('./services/auctionService');
const redisService = require('./config/redis');

// Enhanced console.log that also writes to file
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

// Ensure logs directory exists
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// File logging function with immediate flush
const logToFile = (message, type = 'INFO') => {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${type}] ${message}\n`;
  const logFile = path.join(logsDir, `server-${new Date().toISOString().split('T')[0]}.log`);

  try {
    fs.appendFileSync(logFile, logEntry);
  } catch (err) {
    originalConsoleError('Failed to write to log file:', err);
  }
};

// Override console.log with improved flushing
console.log = (...args) => {
  const message = args.join(' ');
  originalConsoleLog(...args);
  logToFile(message, 'LOG');
  if (process.stdout.writable) {
    process.stdout.write(''); // Force flush
  }
};

// Override console.error with improved flushing
console.error = (...args) => {
  const message = args.join(' ');
  originalConsoleError(...args);
  logToFile(message, 'ERROR');
  if (process.stderr.writable) {
    process.stderr.write(''); // Force flush
  }
};

// Load environment variables early
dotenv.config();

// Initialize Express app
const app = express();

// Create HTTP server for Socket.io
const server = http.createServer(app);

// Request logging middleware - MUST BE FIRST
app.use((req, res, next) => {
  const start = Date.now();
  const logMessage = `🌐 REQUEST: ${req.method} ${req.url} - [${new Date().toISOString()}]`;
  console.log(logMessage);
  
  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    const responseLog = `✅ RESPONSE: ${req.method} ${req.url} - Status ${res.statusCode} - ${duration}ms`;
    console.log(responseLog);
  });

  next();
});

// CORS setup
const corsOptions = {
  origin: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-auth-token', 'Origin'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Body parsers
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files from uploads folder
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Add a logging test endpoint BEFORE startServer
app.get('/api/test-logs-early', (req, res) => {
  console.log('🚀 EARLY TEST LOG: This is an early test log message!');
  console.log('🚀 EARLY TEST LOG: Current time:', new Date().toISOString());
  console.log('🚀 EARLY TEST LOG: Request received from:', req.ip);
  console.error('🚀 EARLY TEST ERROR: This is an early test error message!');

  res.json({
    message: 'Early test endpoint hit successfully!',
    timestamp: new Date().toISOString(),
    logs: 'Check server terminal for EARLY log messages with 🚀 prefix'
  });
});

// Add direct registration route for debugging
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('Registration request received directly in index.js');
    const authController = require('./controllers/authController');
    return await authController.register(req, res);
  } catch (err) {
    console.error('Error in direct registration handler:', err);
    return res.status(500).json({
      msg: 'Server error during registration',
      error: err.message
    });
  }
});

// Connect to MongoDB and start server
const startServer = async () => {
  try {
    console.log('Attempting to connect to MongoDB...');
    const dbConnected = await connectDB();



    if (!dbConnected) {
      console.error('Failed to connect to MongoDB. Check your connection string and ensure MongoDB is running.');
      console.log('Starting server, but MongoDB features will not work correctly.');
    } else {
      console.log('MongoDB connected successfully.');
    }

    // Initialize Socket.io service
    console.log('🔌 Initializing Socket.io service...');
    socketService.initialize(server);
    
    // Start auction timer service
    console.log('⏰ Starting auction timer service...');
    auctionService.startTimerService();

    // *** CRITICAL FIX: Register request logging middleware BEFORE routes ***
    console.log('🔧 REGISTERING REQUEST LOGGING MIDDLEWARE');
    app.use((req, res, next) => {
      const logMessage = `[${new Date().toISOString()}] ${req.method} ${req.url}`;
      console.log(logMessage);
      process.stdout.write(''); // Force flush

      // Also log to stderr to see if that appears
      console.error(`STDERR LOG: ${logMessage}`);

      next();
    });
    console.log('✅ REQUEST LOGGING MIDDLEWARE REGISTERED');

    // Define Routes (except for register which we defined directly)
    app.use('/api/auth', require('./routes/auth'));
    app.use('/api/upload', require('./routes/upload'));
    app.use('/api/players', require('./routes/players'));
    app.use('/api/player-photos', require('./routes/playerPhotoRoutes'));
    app.use('/api/export', require('./routes/exportRoutes'));
    app.use('/api/teams', require('./routes/teams'));
    app.use('/api/auctions', require('./routes/auctions'));
    app.use('/api/player-matching', require('./routes/playerMatching'));

    // TEMPLATE ROUTES MOVED TO AFTER LOGGING SETUP
    console.log('🚀 REGISTERING TEMPLATE ROUTES at /api/admin/templates');
    app.use('/api/admin/templates', require('./routes/templates'));
    console.log('✅ TEMPLATE ROUTES REGISTERED SUCCESSFULLY');

    // === FRESH START: MINIMAL ROUTE REGISTRATION ===
    console.log('🚀 STARTING FRESH ROUTE REGISTRATION');

    // Only register essential routes first
    console.log('🚀 REGISTERING TOURNAMENT ROUTES at /api/tournaments');
    try {
      app.use('/api/tournaments', require('./routes/tournaments'));
      console.log('✅ TOURNAMENT ROUTES REGISTERED SUCCESSFULLY');
    } catch (error) {
      console.error('❌ ERROR REGISTERING TOURNAMENT ROUTES:', error);
    }

    // OCR settings routes removed - file does not exist
    // OCR functionality is handled by existing ocr.js and ocrComparisonRoutes.js

    // Register OCR comparison routes
    console.log('🚀 REGISTERING OCR COMPARISON ROUTES');
    try {
      app.use('/api/ocr-comparison', require('./routes/ocr-comparison'));
      console.log('✅ OCR COMPARISON ROUTES REGISTERED SUCCESSFULLY');
    } catch (error) {
      console.error('❌ ERROR REGISTERING OCR COMPARISON ROUTES:', error);
    }

    // Register Match Outcome routes
    console.log('🚀 REGISTERING MATCH OUTCOME ROUTES at /api/match-outcome');
    try {
      app.use('/api/match-outcome', require('./routes/matchOutcome'));
      console.log('✅ MATCH OUTCOME ROUTES REGISTERED SUCCESSFULLY');
    } catch (error) {
      console.error('❌ ERROR REGISTERING MATCH OUTCOME ROUTES:', error);
    }

    // Serve exported files
    app.use('/exports', express.static(path.join(__dirname, 'exports')));

    // Add a test endpoint
    app.get('/api/test', (req, res) => {
      console.log('🧪 TEST ENDPOINT HIT - This should appear in logs!');
      res.json({ message: 'Backend API is working' });
    });

    // Add health check endpoint for Docker health checks
    app.get('/api/health', (req, res) => {
      console.log('🏥 HEALTH CHECK ENDPOINT HIT');
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      });
    });

    // REMOVED: Direct template route that was overriding the template router
    // This was causing the template router routes to not work

    // REMOVE THE INTERCEPTOR - IT WAS BLOCKING THE REAL DELETE ROUTE
    // The interceptor was responding instead of letting the request reach the template router

    // Add template debug endpoint directly
    app.get('/api/debug-templates', (req, res) => {
      console.log('Direct template debug endpoint hit');

      const Template = require('./models/Template');

      Template.find({})
        .then(allTemplates => {
          return Template.find({ isActive: true })
            .then(activeTemplates => {
              console.log('Direct Debug - Total templates in DB:', allTemplates.length);
              console.log('Direct Debug - Active templates in DB:', activeTemplates.length);

              if (allTemplates.length > 0) {
                console.log('Template details:');
                allTemplates.forEach((t, i) => {
                  console.log(`${i + 1}. ${t.name} - Active: ${t.isActive} - Regions: ${t.regions?.length || 0} - HasImage: ${!!t.originalImageData}`);
                });
              }

              res.json({
                message: 'Direct template debug info',
                totalTemplates: allTemplates.length,
                activeTemplates: activeTemplates.length,
                templates: allTemplates.map(t => ({
                  id: t._id,
                  name: t.name,
                  isActive: t.isActive,
                  regionsCount: t.regions?.length || 0,
                  hasImage: !!t.originalImageData,
                  createdAt: t.createdAt,
                  createdBy: t.createdBy
                }))
              });
            });
        })
        .catch(error => {
          console.error('Direct debug endpoint error:', error);
          res.status(500).json({
            message: 'Direct debug endpoint error',
            error: error.message
          });
        });
    });

    // Add template cleanup endpoint
    app.delete('/api/cleanup-templates', (req, res) => {
      console.log('Template cleanup endpoint hit');

      const Template = require('./models/Template');

      // Delete all inactive templates permanently
      Template.deleteMany({ isActive: false })
        .then(result => {
          console.log('Cleanup - Deleted inactive templates:', result.deletedCount);

          // Also get remaining templates
          return Template.find({});
        })
        .then(remainingTemplates => {
          console.log('Cleanup - Remaining templates:', remainingTemplates.length);

          res.json({
            message: 'Template cleanup completed',
            deletedInactive: true,
            remainingTemplates: remainingTemplates.map(t => ({
              id: t._id,
              name: t.name,
              isActive: t.isActive
            }))
          });
        })
        .catch(error => {
          console.error('Cleanup endpoint error:', error);
          res.status(500).json({
            message: 'Cleanup endpoint error',
            error: error.message
          });
        });
    });

    // Add step1 route right next to the working route
    app.get('/api/step1', (req, res) => {
      console.log('=== STEP 1 ENDPOINT HIT ===');
      res.json({ message: 'Step 1 working', timestamp: new Date().toISOString() });
    });

    // Step 2: Add simple scorecard routes
    app.get('/api/scorecards/test', (req, res) => {
      console.log('=== SCORECARD TEST ENDPOINT HIT ===');
      res.json({ message: 'Scorecard test working', timestamp: new Date().toISOString() });
    });

    app.get('/api/scorecards/:tournamentId/debug', (req, res) => {
      console.log('=== SCORECARD DEBUG ENDPOINT HIT ===');
      res.json({
        message: 'Scorecard debug working',
        tournamentId: req.params.tournamentId,
        timestamp: new Date().toISOString()
      });
    });

    // Step 4: Add scorecard POST route with REAL OCR processing
    app.post('/api/scorecards/:tournamentId', async (req, res) => {
      console.log('=== SCORECARD POST ENDPOINT HIT ===');
      console.log('Tournament ID:', req.params.tournamentId);
      console.log('Request body keys:', Object.keys(req.body || {}));
      console.log('Has file:', !!req.file);

      try {
        // Import the scorecard controller for real processing
        const scorecardController = require('./controllers/scorecardController');

        // Call the real scorecard processing function
        await scorecardController.processScorecard(req, res);

      } catch (error) {
        console.error('Error in scorecard processing:', error);

        // Return fallback data with error information
        res.json({
          msg: 'Scorecard uploaded but OCR processing failed',
          scorecard: {
            url: '/uploads/scorecards/fallback-scorecard.png',
            uploadedBy: 'test-user',
            uploadedAt: new Date().toISOString(),
            isVerified: false
          },
          ocrData: {
            success: false,
            error: error.message,
            errorType: 'OCR_PROCESSING_FAILED',
            team1: 'Team 1 (Please Edit)',
            team2: 'Team 2 (Please Edit)',
            venue: 'Venue (Please Edit)',
            team1Score: { runs: 0, wickets: 0, overs: 0 },
            team2Score: { runs: 0, wickets: 0, overs: 0 },
            playerOfMatch: 'Player of Match (Please Edit)',
            resultText: 'Result (Please Edit)',
            ocrStatus: 'failed',
            ocrMessage: `OCR failed: ${error.message}. Please enter match details manually.`,
            fieldsNeedingCorrection: ['team1', 'team2', 'venue', 'team1Score', 'team2Score', 'playerOfMatch', 'resultText']
          }
        });
      }
    });

    // Add a logging test endpoint
    app.get('/api/test-logs', (req, res) => {
      const logger = require('./utils/fileLogger');

      console.log('🚀 TEST LOG: This is a test log message!');
      logger.info('test', 'Test log endpoint hit', { ip: req.ip, timestamp: new Date().toISOString() });

      console.log('🚀 TEST LOG: Current time:', new Date().toISOString());
      console.error('🚀 TEST ERROR: This is a test error message!');
      console.warn('🚀 TEST WARNING: This is a test warning message!');

      res.json({
        message: 'Test endpoint hit successfully!',
        timestamp: new Date().toISOString(),
        logs: 'Check server terminal for log messages with 🚀 prefix',
        fileLogged: 'Also logged to file - check /api/view-logs'
      });
    });

    // Add real-time log viewer endpoint (with type parameter)
    app.get('/api/view-logs/:type', (req, res) => {
      const logger = require('./utils/fileLogger');
      const logType = req.params.type || 'general';
      const lines = parseInt(req.query.lines) || 100;

      logger.info('log-viewer', `Log viewer accessed for type: ${logType}`, { lines, ip: req.ip });

      const logs = logger.getRecentLogs(logType, lines);

      res.setHeader('Content-Type', 'text/plain');
      res.send(`=== RECENT LOGS (${logType}) - Last ${lines} lines ===\n\n${logs}`);
    });

    // Add real-time log viewer endpoint (default to general)
    app.get('/api/view-logs', (req, res) => {
      const logger = require('./utils/fileLogger');
      const lines = parseInt(req.query.lines) || 100;

      logger.info('log-viewer', 'Log viewer accessed for general logs', { lines, ip: req.ip });

      const logs = logger.getRecentLogs('general', lines);

      res.setHeader('Content-Type', 'text/plain');
      res.send(`=== RECENT LOGS (general) - Last ${lines} lines ===\n\n${logs}`);
    });

    // Add log viewer HTML page
    app.get('/logs', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Server Logs Viewer</title>
          <style>
            body { font-family: monospace; margin: 20px; background: #1e1e1e; color: #fff; }
            .controls { margin-bottom: 20px; padding: 15px; background: #2d2d2d; border-radius: 5px; }
            .controls button { margin: 5px; padding: 8px 15px; background: #007acc; color: white; border: none; border-radius: 3px; cursor: pointer; }
            .controls button:hover { background: #005a9e; }
            .controls select, .controls input { margin: 5px; padding: 5px; background: #3c3c3c; color: white; border: 1px solid #555; }
            .log-container { background: #000; padding: 15px; border-radius: 5px; height: 70vh; overflow-y: auto; white-space: pre-wrap; font-size: 12px; }
            .refresh-info { color: #888; margin-top: 10px; }
          </style>
        </head>
        <body>
          <h1>🔍 Server Logs Viewer</h1>
          <div class="controls">
            <label>Log Type:</label>
            <select id="logType">
              <option value="general">General</option>
              <option value="template-save">Template Save</option>
              <option value="requests">Requests</option>
              <option value="test">Test</option>
            </select>

            <label>Lines:</label>
            <input type="number" id="lines" value="100" min="10" max="1000">

            <button onclick="refreshLogs()">🔄 Refresh</button>
            <button onclick="autoRefresh()">⏰ Auto Refresh (5s)</button>
            <button onclick="clearAutoRefresh()">⏹️ Stop Auto</button>
          </div>

          <div id="logContainer" class="log-container">Loading logs...</div>
          <div class="refresh-info" id="refreshInfo">Click Refresh to load logs</div>

          <script>
            let autoRefreshInterval = null;

            function refreshLogs() {
              const logType = document.getElementById('logType').value;
              const lines = document.getElementById('lines').value;
              const url = '/api/view-logs/' + logType + '?lines=' + lines;

              fetch(url)
                .then(response => response.text())
                .then(data => {
                  document.getElementById('logContainer').textContent = data;
                  document.getElementById('refreshInfo').textContent = 'Last refreshed: ' + new Date().toLocaleTimeString();
                })
                .catch(error => {
                  document.getElementById('logContainer').textContent = 'Error loading logs: ' + error.message;
                });
            }

            function autoRefresh() {
              clearAutoRefresh();
              refreshLogs();
              autoRefreshInterval = setInterval(refreshLogs, 5000);
              document.getElementById('refreshInfo').textContent = 'Auto-refreshing every 5 seconds...';
            }

            function clearAutoRefresh() {
              if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
              }
            }

            // Load logs on page load
            refreshLogs();
          </script>
        </body>
        </html>
      `);
    });

    // Add comprehensive route analysis endpoint
    app.get('/api/analyze-routes', (req, res) => {
      console.log('🔍 ROUTE ANALYSIS ENDPOINT HIT');

      const routes = [];

      // Function to extract route info from layer
      function extractRoutes(layer, basePath = '') {
        if (layer.route) {
          // Direct route
          const methods = Object.keys(layer.route.methods).map(m => m.toUpperCase()).join(', ');
          routes.push({
            type: 'DIRECT',
            methods: methods,
            path: basePath + layer.route.path,
            source: 'Direct route in index.js',
            regexp: layer.regexp ? layer.regexp.source : 'N/A'
          });
        } else if (layer.name === 'router' && layer.handle && layer.handle.stack) {
          // Router middleware
          let routerPath = '';

          // Try to extract router path from regexp
          if (layer.regexp && layer.regexp.source) {
            routerPath = layer.regexp.source
              .replace(/\\\//g, '/')
              .replace(/\$.*$/, '')
              .replace(/^\^/, '')
              .replace(/\?\(\?\=/g, '')
              .replace(/\|.*$/, '');
          }

          // Recursively analyze router routes
          layer.handle.stack.forEach(subLayer => {
            extractRoutes(subLayer, basePath + routerPath);
          });
        } else if (layer.name === 'serveStatic') {
          // Static file middleware
          routes.push({
            type: 'STATIC',
            methods: 'GET',
            path: basePath + '/*',
            source: 'Static file middleware',
            regexp: layer.regexp ? layer.regexp.source : 'N/A'
          });
        } else {
          // Other middleware
          routes.push({
            type: 'MIDDLEWARE',
            methods: 'ALL',
            path: basePath + (layer.regexp ? layer.regexp.source : '/*'),
            source: `Middleware: ${layer.name || 'anonymous'}`,
            regexp: layer.regexp ? layer.regexp.source : 'N/A'
          });
        }
      }

      // Analyze all routes
      if (app._router && app._router.stack) {
        app._router.stack.forEach(layer => {
          extractRoutes(layer);
        });
      }

      // Sort routes by path
      routes.sort((a, b) => a.path.localeCompare(b.path));

      // Find conflicts
      const routeMap = new Map();
      const conflicts = [];

      routes.forEach(route => {
        if (route.type === 'DIRECT') {
          route.methods.split(', ').forEach(method => {
            const key = `${method} ${route.path}`;
            if (!routeMap.has(key)) {
              routeMap.set(key, []);
            }
            routeMap.get(key).push(route);
          });
        }
      });

      routeMap.forEach((routeList, key) => {
        if (routeList.length > 1) {
          conflicts.push({
            route: key,
            count: routeList.length,
            sources: routeList.map(r => r.source)
          });
        }
      });

      // Filter template routes
      const templateRoutes = routes.filter(r =>
        r.path.includes('/api/admin/templates') ||
        r.path.includes('template') ||
        r.source.includes('template')
      );

      // Filter DELETE routes
      const deleteRoutes = routes.filter(r => r.methods.includes('DELETE'));

      console.log(`🔍 Found ${routes.length} total routes`);
      console.log(`🎯 Found ${templateRoutes.length} template routes`);
      console.log(`🗑️ Found ${deleteRoutes.length} delete routes`);
      console.log(`🚨 Found ${conflicts.length} conflicts`);

      res.json({
        message: 'Route analysis complete',
        summary: {
          totalRoutes: routes.length,
          templateRoutes: templateRoutes.length,
          deleteRoutes: deleteRoutes.length,
          conflicts: conflicts.length
        },
        allRoutes: routes,
        templateRoutes: templateRoutes,
        deleteRoutes: deleteRoutes,
        conflicts: conflicts,
        timestamp: new Date().toISOString()
      });
    });

    // Add a direct OCR test endpoint
    app.get('/direct-ocr', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Direct OCR Test</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              max-width: 1200px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f5f5f5;
            }
            h1 {
              color: #333;
              text-align: center;
            }
            .container {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
            }
            .form-container {
              flex: 1;
              min-width: 300px;
              background-color: #fff;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .results-container {
              flex: 2;
              min-width: 500px;
              background-color: #fff;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .form-group {
              margin-bottom: 15px;
            }
            label {
              display: block;
              margin-bottom: 5px;
              font-weight: bold;
            }
            input, select, textarea {
              width: 100%;
              padding: 8px;
              border: 1px solid #ddd;
              border-radius: 4px;
              box-sizing: border-box;
            }
            button {
              background-color: #4CAF50;
              color: white;
              padding: 10px 15px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-size: 16px;
            }
            button:hover {
              background-color: #45a049;
            }
            .image-preview {
              margin-top: 20px;
              text-align: center;
            }
            .image-preview img {
              max-width: 100%;
              max-height: 300px;
              border: 1px solid #ddd;
            }
            .results {
              margin-top: 20px;
            }
            .results pre {
              background-color: #f8f8f8;
              padding: 15px;
              border-radius: 4px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-wrap: break-word;
            }
            .terminal {
              background-color: #000;
              color: #0f0;
              font-family: monospace;
              padding: 15px;
              border-radius: 4px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-wrap: break-word;
              height: 300px;
              overflow-y: auto;
            }
            .settings-group {
              margin-top: 15px;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
            }
            .settings-title {
              font-weight: bold;
              margin-bottom: 10px;
            }
            .settings-row {
              display: flex;
              margin-bottom: 10px;
            }
            .settings-row label {
              flex: 1;
              margin-right: 10px;
            }
            .settings-row input {
              flex: 2;
            }
            .note {
              background-color: #fff3cd;
              padding: 15px;
              border-radius: 4px;
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <h1>Direct OCR Test</h1>

          <div class="note">
            <strong>Note:</strong> This page allows you to test the OCR functionality directly without using templates.
            <p>Upload an image, adjust the settings, and click "Process Image" to see the raw OCR results.</p>
          </div>

          <div class="container">
            <div class="form-container">
              <form id="ocrForm">
                <div class="form-group">
                  <label for="imageFile">Upload Image:</label>
                  <input type="file" id="imageFile" name="image" accept="image/*">
                </div>

                <div class="settings-group">
                  <div class="settings-title">PaddleOCR Settings:</div>

                  <div class="settings-row">
                    <label for="det_db_thresh">det_db_thresh:</label>
                    <input type="number" id="det_db_thresh" name="det_db_thresh" value="0.3" step="0.05" min="0.05" max="0.95">
                  </div>

                  <div class="settings-row">
                    <label for="det_db_box_thresh">det_db_box_thresh:</label>
                    <input type="number" id="det_db_box_thresh" name="det_db_box_thresh" value="0.4" step="0.05" min="0.05" max="0.95">
                  </div>

                  <div class="settings-row">
                    <label for="det_db_unclip_ratio">det_db_unclip_ratio:</label>
                    <input type="number" id="det_db_unclip_ratio" name="det_db_unclip_ratio" value="1.5" step="0.1" min="0.5" max="3.0">
                  </div>

                  <div class="settings-row">
                    <label for="drop_score">drop_score:</label>
                    <input type="number" id="drop_score" name="drop_score" value="0.5" step="0.05" min="0.05" max="0.95">
                  </div>
                </div>

                <button type="submit">Process Image</button>
              </form>

              <div class="image-preview">
                <h3>Image Preview</h3>
                <img id="preview" src="" alt="Preview will appear here">
              </div>
            </div>

            <div class="results-container">
              <h2>OCR Results</h2>

              <div class="results">
                <h3>Terminal Output</h3>
                <div class="terminal" id="terminalOutput">Waiting for processing...</div>

                <h3>Detected Text</h3>
                <pre id="detectedText">Waiting for processing...</pre>

                <h3>JSON Response</h3>
                <pre id="jsonResponse">Waiting for processing...</pre>
              </div>
            </div>
          </div>

          <script>
            document.addEventListener('DOMContentLoaded', function() {
              const ocrForm = document.getElementById('ocrForm');
              const imageFile = document.getElementById('imageFile');
              const preview = document.getElementById('preview');
              const terminalOutput = document.getElementById('terminalOutput');
              const detectedText = document.getElementById('detectedText');
              const jsonResponse = document.getElementById('jsonResponse');

              // Handle image file selection
              imageFile.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                  const reader = new FileReader();
                  reader.onload = function(e) {
                    preview.src = e.target.result;
                  };
                  reader.readAsDataURL(this.files[0]);
                }
              });

              // Handle form submission
              ocrForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Show loading state
                terminalOutput.textContent = 'Processing...';
                detectedText.textContent = 'Processing...';
                jsonResponse.textContent = 'Processing...';

                // Create form data
                const formData = new FormData();

                // Add image file
                if (imageFile.files && imageFile.files[0]) {
                  formData.append('image', imageFile.files[0]);
                } else {
                  alert('Please select an image');
                  return;
                }

                // Add settings
                const settings = {
                  det_db_thresh: parseFloat(document.getElementById('det_db_thresh').value),
                  det_db_box_thresh: parseFloat(document.getElementById('det_db_box_thresh').value),
                  det_db_unclip_ratio: parseFloat(document.getElementById('det_db_unclip_ratio').value),
                  drop_score: parseFloat(document.getElementById('drop_score').value)
                };

                formData.append('settings', JSON.stringify(settings));
                formData.append('methods', JSON.stringify(['direct']));

                // Send request
                fetch('/api/ocr-comparison/compare', {
                  method: 'POST',
                  body: formData
                })
                .then(response => response.json())
                .then(data => {
                  // Display results
                  if (data.results && data.results.direct) {
                    const directResult = data.results.direct;

                    terminalOutput.textContent = directResult.rawOutput || 'No terminal output';

                    if (directResult.lines && directResult.lines.length > 0) {
                      detectedText.textContent = directResult.lines.join('\\n');
                    } else {
                      detectedText.textContent = 'No text detected';
                    }

                    jsonResponse.textContent = JSON.stringify(directResult, null, 2);
                  } else {
                    terminalOutput.textContent = 'No direct OCR results found';
                    detectedText.textContent = 'No direct OCR results found';
                    jsonResponse.textContent = JSON.stringify(data, null, 2);
                  }
                })
                .catch(error => {
                  console.error('Error:', error);
                  terminalOutput.textContent = 'Error: ' + error.message;
                  detectedText.textContent = 'Error: ' + error.message;
                  jsonResponse.textContent = 'Error: ' + error.message;
                });
              });
            });
          </script>
        </body>
        </html>
      `);
    });

    // === STEP-BY-STEP ROUTE TESTING ===
    console.log('🚀 STEP 1 ROUTE MOVED TO EARLY POSITION');

    // Debug endpoint to check file paths
    app.get('/api/debug/paths', (req, res) => {
      const buildPath = path.join(process.cwd(), 'client/build');
      const indexPath = path.join(process.cwd(), 'client/build/index.html');
      
      res.json({
        __dirname,
        buildPath,
        indexPath,
        buildExists: fs.existsSync(buildPath),
        indexExists: fs.existsSync(indexPath),
        NODE_ENV: process.env.NODE_ENV,
        cwd: process.cwd(),
        buildContents: fs.existsSync(buildPath) ? fs.readdirSync(buildPath) : 'Directory does not exist'
      });
    });

    // --- DEPLOYMENT CONFIGURATION ---
    // Serve static assets from the React app for production
    if (process.env.NODE_ENV === 'production') {
      const buildPath = path.join(process.cwd(), 'client/build');
      console.log('🔍 Production build path:', buildPath);
      console.log('🔍 Build directory exists:', fs.existsSync(buildPath));
      
      if (fs.existsSync(buildPath)) {
        console.log('🔍 Build directory contents:', fs.readdirSync(buildPath));
        // Set static folder
        app.use(express.static(buildPath));
        
        // Catch-all route to serve the frontend's index.html for any non-API requests
        app.get(/^(?!\/api).*/, (req, res) => {
          const indexPath = path.join(buildPath, 'index.html');
          console.log('🔍 Serving index.html from:', indexPath);
          console.log('🔍 Index.html exists:', fs.existsSync(indexPath));
          
          if (fs.existsSync(indexPath)) {
            res.sendFile(indexPath);
          } else {
            res.status(404).json({ error: 'index.html not found', path: indexPath });
          }
        });
      } else {
        console.error('❌ Build directory not found:', buildPath);
        app.get(/^(?!\/api).*/, (req, res) => {
          res.status(404).json({ error: 'Build directory not found', path: buildPath });
        });
      }
    } else {
      app.get('/', (req, res) => {
        res.send('API is running in development mode...');
      });
    }


    // Error handling middleware (moved after routes)
    app.use((err, req, res, next) => {
      console.error('Global error handler caught:', err);
      console.error('Error stack:', err.stack);
      console.error('Request path:', req.path);
      console.error('Request method:', req.method);
      console.error('Request body:', JSON.stringify(req.body, null, 2));
      console.error('Request headers:', req.headers);

      res.status(500).json({
        error: 'Server error',
        details: err.message,
        stack: process.env.NODE_ENV === 'production' ? null : err.stack
      });
    });

    // Start server
    const PORT = process.env.PORT || 5000;
    server.listen(PORT, async () => {
      console.log(`🚀🚀🚀 SERVER STARTUP COMPLETE 🚀🚀🚀`);
      console.log(`Server running on port ${PORT}`);
      console.log(`API available at http://localhost:${PORT}`);
      console.log(`🔌 Socket.io enabled for real-time features`);
      console.log(`🔄 Redis caching ${redisService.getClient() ? 'enabled' : 'disabled'}`);
      console.log(`✅ React build files deployed - SPA routing fixed!`);

      // Test logging functionality
      console.log('🧪 TESTING CONSOLE.LOG - This should appear in terminal');
      console.error('🧪 TESTING CONSOLE.ERROR - This should appear in terminal');

      // Test file logging
      logToFile('🧪 TESTING FILE LOGGING - Server started successfully', 'TEST');
      console.log('📁 Log files are being written to:', logsDir);

      // Set up auction scheduler to run every minute
      console.log('Setting up auction scheduler...');
      setInterval(async () => {
        try {
          console.log('Running scheduled auction processing...');
          const results = await processCompletedAuctions();
          console.log(`Processed ${results.processed} auctions with ${results.errors} errors`);
        } catch (err) {
          console.error('Error in scheduled auction processing:', err);
        }
      }, 60000); // Run every minute

      // Training server startup disabled - will be reinstalled fresh
      console.log('Training server startup disabled during OCR cleanup');
    });

    // Set up graceful shutdown
    const gracefulShutdown = async (signal) => {
      console.log(`Received ${signal}. Shutting down gracefully...`);

      // Stop the training server
      try {
        console.log('Stopping training server...');
        const result = await trainingServerManager.stopServer();
        if (result.success) {
          console.log('Training server stopped successfully');
        } else {
          console.error('Failed to stop training server:', result.message);
        }
      } catch (err) {
        console.error('Error stopping training server:', err.message);
      }

      // Close the HTTP server
      server.close(() => {
        console.log('HTTP server closed');
        process.exit(0);
      });

      // Force close if it takes too long
      setTimeout(() => {
        console.error('Forcing shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (err) {
    console.error('Failed to start the server:', err);
  }
};

startServer();
