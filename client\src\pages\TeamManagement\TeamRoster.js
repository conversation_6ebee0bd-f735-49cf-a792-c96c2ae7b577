import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  InputAdornment,
  Pagination,
  useTheme,
  useMediaQuery,
  Tooltip,
  Snackbar
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import InfoIcon from '@mui/icons-material/Info';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import { useAuth } from '../../hooks/useAuth';
import TeamRosterPlayerCard from '../../components/TeamRosterPlayerCard';
// Import API services
import { getTeamRoster, addPlayerToTeam, removePlayerFromTeam } from '../../services/teamService';
import { getPlayers } from '../../services/playerService';

const TeamRoster = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [roster, setRoster] = useState([]);
  const [totalPlayers, setTotalPlayers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [availablePlayers, setAvailablePlayers] = useState([]);
  const [availablePlayersLoading, setAvailablePlayersLoading] = useState(false);
  const [availablePlayersError, setAvailablePlayersError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [availableCurrentPage, setAvailableCurrentPage] = useState(1);
  const [availableTotalPages, setAvailableTotalPages] = useState(1);

  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [playerToRemove, setPlayerToRemove] = useState(null);

  // Load team roster from server on component mount and when page changes
  useEffect(() => {
    if (!user || user.role !== 'team_owner') return;

    setLoading(true);

    const fetchRoster = async () => {
      try {
        // Fetch roster from server
        const rosterData = await getTeamRoster({
          page: currentPage,
          limit: 12
        });

        console.log('Loaded team roster from server:', rosterData);

        setRoster(rosterData.players || []);
        setTotalPlayers(rosterData.totalPlayers || 0);
        setTotalPages(rosterData.totalPages || 1);

      } catch (err) {
        console.error('Error loading team roster from server:', err);

        // Fallback to localStorage if server request fails
        try {
          const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);

          if (savedRoster) {
            const rosterData = JSON.parse(savedRoster);
            console.log('Fallback: Loaded saved team roster from localStorage:', rosterData);

            // Calculate pagination
            const startIndex = (currentPage - 1) * 12;
            const endIndex = startIndex + 12;
            const paginatedPlayers = rosterData.slice(startIndex, endIndex);

            setRoster(paginatedPlayers);
            setTotalPlayers(rosterData.length);
            setTotalPages(Math.ceil(rosterData.length / 12));
          } else {
            // No saved roster, initialize with empty array
            setRoster([]);
            setTotalPlayers(0);
            setTotalPages(1);
            console.log('No roster data available');
          }
        } catch (localErr) {
          console.error('Error loading team roster from localStorage:', localErr);
          setError('Failed to load team roster. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRoster();
  }, [user, currentPage]);

  // Load available players when dialog opens or search/page changes
  useEffect(() => {
    if (!dialogOpen) return;

    setAvailablePlayersLoading(true);

    const fetchAvailablePlayers = async () => {
      try {
        // Fetch available players from server
        const playersData = await getPlayers({
          page: availableCurrentPage,
          limit: 8,
          search: searchTerm,
          available: true // Only get players that are available (not owned by any team)
        });

        console.log('Loaded available players from server:', playersData);

        setAvailablePlayers(playersData.players || []);
        setAvailableTotalPages(playersData.totalPages || 1);

      } catch (err) {
        console.error('Error loading available players from server:', err);

        // Fallback to generating placeholder players if server request fails
        try {
          // Generate some placeholder players
          const generatePlaceholderPlayers = () => {
            const playerTypes = ['Batsman', 'Bowler', 'All-Rounder', 'Wicket Keeper'];
            const nationalities = ['India', 'Australia', 'England', 'South Africa', 'New Zealand', 'West Indies'];
            const names = [
              'Virat Kohli', 'Rohit Sharma', 'Kane Williamson', 'Steve Smith', 'Joe Root',
              'Ben Stokes', 'Jasprit Bumrah', 'Pat Cummins', 'Kagiso Rabada', 'Trent Boult',
              'Babar Azam', 'Quinton de Kock', 'David Warner', 'Shakib Al Hasan', 'Rashid Khan',
              'Jos Buttler', 'Mitchell Starc', 'Jofra Archer', 'Hardik Pandya', 'Rishabh Pant'
            ];

            // Filter out players that are already in the roster
            const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);
            const rosterPlayers = savedRoster ? JSON.parse(savedRoster) : [];
            const rosterPlayerNames = rosterPlayers.map(p => p.name);

            return Array.from({ length: 20 }, (_, i) => {
              const name = names[i];
              // Skip if player is already in roster
              if (rosterPlayerNames.includes(name)) {
                return null;
              }

              const type = playerTypes[Math.floor(Math.random() * playerTypes.length)];
              const nationality = nationalities[Math.floor(Math.random() * nationalities.length)];
              const overall = Math.floor(Math.random() * 20) + 75; // 75-94

              return {
                _id: `placeholder_${i}`,
                name,
                type,
                nationality,
                image: `/uploads/players/placeholder_${i % 5 + 1}.jpg`,
                ratings: {
                  overall,
                  batting: Math.floor(Math.random() * 20) + 70,
                  bowling: Math.floor(Math.random() * 20) + 70,
                  fielding: Math.floor(Math.random() * 20) + 70
                },
                marketValue: Math.floor(Math.random() * 5000) + 5000
              };
            }).filter(Boolean); // Remove null entries (players already in roster)
          };

          const allPlayers = generatePlaceholderPlayers();

          // Filter by search term if provided
          const filteredPlayers = searchTerm
            ? allPlayers.filter(p =>
                p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                p.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                p.nationality.toLowerCase().includes(searchTerm.toLowerCase())
              )
            : allPlayers;

          // Paginate results
          const startIndex = (availableCurrentPage - 1) * 8;
          const endIndex = startIndex + 8;
          const paginatedPlayers = filteredPlayers.slice(startIndex, endIndex);

          setAvailablePlayers(paginatedPlayers);
          setAvailableTotalPages(Math.ceil(filteredPlayers.length / 8));

          console.log('Fallback: Generated placeholder players:', paginatedPlayers);
        } catch (genErr) {
          console.error('Error generating placeholder players:', genErr);
          setAvailablePlayersError('Failed to load available players.');
        }
      } finally {
        setAvailablePlayersLoading(false);
      }
    };

    fetchAvailablePlayers();
  }, [dialogOpen, searchTerm, availableCurrentPage, user]);

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };

  const handleAvailablePageChange = (event, value) => {
    setAvailableCurrentPage(value);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setAvailableCurrentPage(1); // Reset to first page when search changes
  };

  const handleAddPlayer = async (playerId) => {
    try {
      // Find the player in available players
      const playerToAdd = availablePlayers.find(p => p._id === playerId);
      if (!playerToAdd) {
        throw new Error('Player not found');
      }

      // Add player to team via API
      await addPlayerToTeam(playerId);

      // Update UI
      setSuccess('Player added to your team successfully');
      setDialogOpen(false);

      // Refresh roster from server
      try {
        const rosterData = await getTeamRoster({
          page: currentPage,
          limit: 12
        });

        setRoster(rosterData.players || []);
        setTotalPlayers(rosterData.totalPlayers || 0);
        setTotalPages(rosterData.totalPages || 1);
      } catch (refreshErr) {
        console.error('Error refreshing roster after adding player:', refreshErr);

        // Fallback to localStorage update if server refresh fails
        const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);
        const currentRoster = savedRoster ? JSON.parse(savedRoster) : [];

        // Add player to roster
        const updatedRoster = [...currentRoster, playerToAdd];

        // Save updated roster to localStorage
        localStorage.setItem(`teamRoster_${user.id}`, JSON.stringify(updatedRoster));

        // Refresh roster display from localStorage
        const startIndex = (currentPage - 1) * 12;
        const endIndex = startIndex + 12;
        const paginatedPlayers = updatedRoster.slice(startIndex, endIndex);

        setRoster(paginatedPlayers);
        setTotalPlayers(updatedRoster.length);
        setTotalPages(Math.ceil(updatedRoster.length / 12));
      }
    } catch (err) {
      console.error('Error adding player to team:', err);
      setError('Failed to add player to your team. Please try again.');
    }
  };

  const openRemoveConfirmation = (player) => {
    setPlayerToRemove(player);
    setConfirmDialogOpen(true);
  };

  const handleRemovePlayer = async () => {
    if (!playerToRemove) return;

    try {
      // Remove player from team via API
      await removePlayerFromTeam(playerToRemove._id);

      // Update UI
      setSuccess('Player removed from your team successfully');
      setConfirmDialogOpen(false);

      // Refresh roster from server
      try {
        const rosterData = await getTeamRoster({
          page: currentPage,
          limit: 12
        });

        const newRoster = rosterData.players || [];
        setRoster(newRoster);
        setTotalPlayers(rosterData.totalPlayers || 0);
        setTotalPages(rosterData.totalPages || 1);

        // If current page is now empty but not the first page, go to previous page
        if (newRoster.length === 0 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      } catch (refreshErr) {
        console.error('Error refreshing roster after removing player:', refreshErr);

        // Fallback to localStorage update if server refresh fails
        const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);
        if (!savedRoster) {
          throw new Error('Roster not found');
        }

        const currentRoster = JSON.parse(savedRoster);

        // Remove player from roster
        const updatedRoster = currentRoster.filter(p => p._id !== playerToRemove._id);

        // Save updated roster to localStorage
        localStorage.setItem(`teamRoster_${user.id}`, JSON.stringify(updatedRoster));

        // Refresh roster display from localStorage
        const startIndex = (currentPage - 1) * 12;
        const endIndex = startIndex + 12;
        const paginatedPlayers = updatedRoster.slice(startIndex, endIndex);

        setRoster(paginatedPlayers);
        setTotalPlayers(updatedRoster.length);
        setTotalPages(Math.ceil(updatedRoster.length / 12));

        // If current page is now empty but not the first page, go to previous page
        if (paginatedPlayers.length === 0 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      }
    } catch (err) {
      console.error('Error removing player from team:', err);
      setError('Failed to remove player from your team. Please try again.');
    }
  };

  if (loading && roster.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Team Roster
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Manage your team's players
            </Typography>
          </Box>

          <Button
            variant="contained"
            color="primary"
            startIcon={<PersonAddIcon />}
            onClick={() => setDialogOpen(true)}
            sx={{ mt: { xs: 2, sm: 0 } }}
          >
            Add Players
          </Button>
        </Box>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      {/* Team Roster */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Roster ({totalPlayers} players)
        </Typography>
        <Divider sx={{ mb: 2 }} />

        {roster.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your team doesn't have any players yet.
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              You can add players directly for testing purposes, or visit the Transfer Market to purchase players with your team budget.
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                startIcon={<PersonAddIcon />}
                onClick={() => setDialogOpen(true)}
              >
                Add Test Players
              </Button>
              <Button
                component={RouterLink}
                to="/market"
                variant="contained"
                color="primary"
                startIcon={<ShoppingCartIcon />}
              >
                Go to Transfer Market
              </Button>
            </Box>
          </Box>
        ) : (
          <>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {roster.map((player) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={player._id}>
                  <TeamRosterPlayerCard
                    player={player}
                    onRemove={openRemoveConfirmation}
                  />
                </Grid>
              ))}
            </Grid>

            {totalPages > 1 && (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                mt: 8, 
                mb: 3, 
                pt: 2,
                position: 'relative',
                zIndex: 1
              }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Paper>

      {/* Add Players Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Add Players to Your Team
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Search players by name, nationality, or type"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          {availablePlayersError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {availablePlayersError}
            </Alert>
          )}

          {availablePlayersLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : availablePlayers.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No available players found.
              </Typography>
            </Box>
          ) : (
            <>
              <Grid container spacing={2} sx={{ mb: 4 }}>
                {availablePlayers.map((player) => (
                  <Grid item xs={12} sm={6} md={3} key={player._id}>
                    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                      <CardMedia
                        component="img"
                        height="140"
                        image={player.image}
                        alt={player.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" gutterBottom noWrap>
                          {player.name}
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                          <Chip
                            label={player.type}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                          <Chip
                            label={`OVR ${player.ratings.overall}`}
                            size="small"
                            color="secondary"
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {player.nationality}
                        </Typography>
                      </CardContent>
                      <CardActions>
                        <Button
                          fullWidth
                          variant="contained"
                          color="primary"
                          startIcon={<AddIcon />}
                          onClick={() => handleAddPlayer(player._id)}
                          size="small"
                        >
                          Add to Team
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {availableTotalPages > 1 && (
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  mt: 6, 
                  mb: 3, 
                  pt: 2,
                  position: 'relative',
                  zIndex: 1
                }}>
                  <Pagination
                    count={availableTotalPages}
                    page={availableCurrentPage}
                    onChange={handleAvailablePageChange}
                    color="primary"
                    size="small"
                  />
                </Box>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Remove Player Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>
          Remove Player from Team
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to remove {playerToRemove?.name} from your team?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleRemovePlayer} color="error" variant="contained">
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TeamRoster;
