/**
 * Test script to verify any scorecard with Google Cloud Vision
 * 
 * Usage: node test-scorecard-verification.js <scorecard-filename>
 * Example: node test-scorecard-verification.js scorecard9.png
 */

const path = require('path');
const OCRService = require('./services/ocrService');

async function testScorecardVerification() {
  // Get the scorecard filename from command line arguments
  const scorecardName = process.argv[2];
  
  if (!scorecardName) {
    console.error('❌ Error: Please provide a scorecard filename');
    console.log('Usage: node test-scorecard-verification.js <scorecard-filename>');
    console.log('Example: node test-scorecard-verification.js scorecard9.png');
    return;
  }
  
  console.log('🧪 SCORECARD VERIFICATION TEST');
  console.log('==============================');
  console.log(`Testing scorecard: ${scorecardName}\n`);
  
  try {
    // Initialize OCR service
    const ocrService = new OCRService();
    
    // Set the image path
    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);
    
    console.log(`📸 Processing: ${imagePath}`);
    
    // Step 1: Get raw OCR results (without verification)
    console.log('\n⏳ Step 1: Getting raw OCR results...');
    ocrService.useGoogleVisionVerification = false;
    const rawResult = await ocrService.processImage(imagePath);
    
    // Step 2: Process with verification
    console.log('\n⏳ Step 2: Processing with Google Vision verification...');
    ocrService.useGoogleVisionVerification = true;
    const verifiedResult = await ocrService.processImage(imagePath);
    
    // Display RAW EXTRACTION RESULTS
    console.log('\n📊 RAW EXTRACTION RESULTS:');
    console.log('==========================');
    console.log(`Team 1: "${rawResult.team1}"`);
    console.log(`Team 2: "${rawResult.team2}"`);
    console.log(`Venue: ${rawResult.venue}`);
    console.log(`Team 1 Score: ${rawResult.team1Score.runs}-${rawResult.team1Score.wickets} (${rawResult.team1Score.overs} overs)`);
    console.log(`Team 2 Score: ${rawResult.team2Score.runs}-${rawResult.team2Score.wickets} (${rawResult.team2Score.overs} overs)`);
    console.log(`Player of Match: ${rawResult.playerOfMatch}`);
    console.log(`Extraction Method: ${rawResult.extractionMethod || 'OCR.Space API - Coordinate-Based Parser'}`);
    console.log(`Confidence: ${rawResult.confidence}\n`);
    
    // Display Team 1 Batsmen
    console.log(`🏏 ${rawResult.team1.toUpperCase()} BATTING:`);
    if (rawResult.team1Batsmen && rawResult.team1Batsmen.length > 0) {
      rawResult.team1Batsmen.forEach((player, i) => {
        const strikeRate = player.balls > 0 ? ((player.runs / player.balls) * 100).toFixed(2) : '0.00';
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }
    
    // Display Team 2 Batsmen
    console.log(`\n🏏 ${rawResult.team2.toUpperCase()} BATTING:`);
    if (rawResult.team2Batsmen && rawResult.team2Batsmen.length > 0) {
      rawResult.team2Batsmen.forEach((player, i) => {
        const strikeRate = player.balls > 0 ? ((player.runs / player.balls) * 100).toFixed(2) : '0.00';
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }
    
    // Display Team 1 Bowlers
    console.log(`\n🎳 ${rawResult.team1.toUpperCase()} BOWLING:`);
    if (rawResult.team1Bowlers && rawResult.team1Bowlers.length > 0) {
      rawResult.team1Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }
    
    // Display Team 2 Bowlers
    console.log(`\n🎳 ${rawResult.team2.toUpperCase()} BOWLING:`);
    if (rawResult.team2Bowlers && rawResult.team2Bowlers.length > 0) {
      rawResult.team2Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }
    
    // Display RAW EXTRACTION SUMMARY
    console.log('\n' + '='.repeat(50));
    console.log('📋 RAW EXTRACTION SUMMARY:');
    console.log(`✅ Team Names: "${rawResult.team1}" vs "${rawResult.team2}"`);
    console.log(`✅ Total Batsmen: ${(rawResult.team1Batsmen?.length || 0) + (rawResult.team2Batsmen?.length || 0)}`);
    console.log(`✅ Total Bowlers: ${(rawResult.team1Bowlers?.length || 0) + (rawResult.team2Bowlers?.length || 0)}`);
    console.log(`✅ Venue: ${rawResult.venue}`);
    console.log(`✅ Player of Match: ${rawResult.playerOfMatch}`);
    
    // Display VERIFICATION RESULTS
    console.log('\n' + '='.repeat(50));
    console.log('📊 VERIFICATION RESULTS:');
    console.log('=======================');
    
    // Check if verification was performed
    if (verifiedResult.verification) {
      console.log(`✅ Verification performed using: ${verifiedResult.verification.method}`);
      console.log(`✅ Players verified: ${verifiedResult.verification.playersVerified}`);
    } else {
      console.log('❌ No verification was performed');
    }
    
    // Find all verified players
    const verifiedPlayers = [
      ...(verifiedResult.team1Batsmen || []), 
      ...(verifiedResult.team2Batsmen || [])
    ].filter(p => p.verified);
    
    if (verifiedPlayers.length > 0) {
      console.log('\n🔍 VERIFIED PLAYERS:');
      verifiedPlayers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.originalRuns}(${player.originalBalls}) → ${player.runs}(${player.balls})`);
      });
    } else {
      console.log('\n❓ No players were verified or corrected');
    }
    
    // Display all players with 6 or 9 in their scores
    const playersWithSixOrNine = [
      ...(verifiedResult.team1Batsmen || []), 
      ...(verifiedResult.team2Batsmen || [])
    ].filter(player => {
      const runsStr = player.runs ? player.runs.toString() : '';
      const ballsStr = player.balls ? player.balls.toString() : '';
      return runsStr.includes('6') || runsStr.includes('9') || 
             ballsStr.includes('6') || ballsStr.includes('9');
    });
    
    if (playersWithSixOrNine.length > 0) {
      console.log('\n📊 ALL PLAYERS WITH 6 OR 9 IN THEIR SCORES:');
      playersWithSixOrNine.forEach((player, i) => {
        const verifiedMark = player.verified ? ' ✓' : '';
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls})${verifiedMark}`);
      });
    }
    
    // Display VERIFICATION SUMMARY
    console.log('\n' + '='.repeat(50));
    console.log('📋 VERIFICATION SUMMARY:');
    console.log(`✅ Total players verified: ${verifiedPlayers.length}`);
    console.log(`✅ Players corrected: ${verifiedPlayers.length}`);
    
    if (verifiedPlayers.length > 0) {
      console.log('\n🔍 CORRECTED PLAYERS:');
      verifiedPlayers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.originalRuns}(${player.originalBalls}) → ${player.runs}(${player.balls})`);
      });
    }
    
    console.log('\n🎯 CONCLUSION:');
    console.log('This test shows both the raw OCR extraction results and the verified results after Google Vision verification.');
    console.log('The verification step corrects 6/9 confusion in player scores.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testScorecardVerification();