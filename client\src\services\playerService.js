import axios from 'axios';
import { API_URL } from '../config';

const API = axios.create({
  baseURL: `${API_URL}/players`,
});

// Add auth token to requests if available
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  console.log('Token from localStorage:', token ? 'Token exists' : 'No token found');

  if (token) {
    config.headers['x-auth-token'] = token;
    console.log('Added token to request headers');
  } else {
    console.warn('No authentication token found in localStorage');
  }

  return config;
});

// Get all players with filters and pagination
export const getPlayers = async (params) => {
  try {
    const response = await API.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get player by ID
export const getPlayerById = async (id) => {
  try {
    const response = await API.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get current user's team players
export const getMyTeamPlayers = async (params) => {
  try {
    const response = await API.get('/myteam', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get team's players by user ID
export const getTeamPlayers = async (userId, params) => {
  try {
    const response = await API.get(`/team/${userId}`, { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Admin functions

// Create a new player
export const createPlayer = async (playerData) => {
  try {
    console.log('Creating player with data:', playerData);

    // Check if user is logged in by checking for token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found. User might not be logged in.');
      throw new Error('You must be logged in to create a player');
    }

    // Log the request configuration
    console.log('Request will be sent to:', `${API.defaults.baseURL}/`);
    console.log('With headers:', {
      'x-auth-token': token ? 'Token exists' : 'No token',
      'Content-Type': 'application/json'
    });

    const response = await API.post('/', playerData);
    console.log('Player created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating player:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Update a player
export const updatePlayer = async (id, playerData) => {
  try {
    const response = await API.put(`/${id}`, playerData);
    return response.data;
  } catch (error) {
    const data = error.response?.data;
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Delete a player
export const deletePlayer = async (id) => {
  try {
    console.log('Deleting player with ID:', id);

    // Check if user is logged in by checking for token
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found. User might not be logged in.');
      throw new Error('You must be logged in to delete a player');
    }

    // Log the request configuration
    console.log('Delete request will be sent to:', `${API.defaults.baseURL}/${id}`);
    console.log('With headers:', {
      'x-auth-token': token ? 'Token exists' : 'No token'
    });

    const response = await API.delete(`/${id}`);
    console.log('Player deleted successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting player:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Import players from Excel
export const importPlayers = async (formData) => {
  try {
    console.log('Import players API call to:', `${API.defaults.baseURL}/import`);
    console.log('FormData contains:', formData.get('playersFile') ? 'File attached' : 'No file',
                formData.get('singlePlayer') ? 'Single player data' : 'No single player data');

    // Check if token exists
    const token = localStorage.getItem('token');
    console.log('Token exists for import:', token ? 'Yes' : 'No');

    const response = await API.post('/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Import API response:', response.status, response.data);
    return response.data;
  } catch (error) {
    console.error('Import API error:', error);
    console.error('Error response:', error.response);
    console.error('Error status:', error.response?.status);
    console.error('Error data:', error.response?.data);
    throw error.response?.data || error.message;
  }
};

// Buy player
export const buyPlayer = async (id) => {
  try {
    const response = await API.post(`/${id}/buy`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Scrape players from IPL website
export const scrapeIplPlayers = async (url, useMock = true) => {
  try {
    console.log(`Scraping IPL players from URL: ${url} (using ${useMock ? 'mock data' : 'headless browser'})`);
    const response = await API.post('/scrape-ipl', { url, useMock });
    console.log('Scrape response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error scraping IPL players:', error);
    throw error.response?.data || error.message;
  }
};

export default {
  getPlayers,
  getPlayerById,
  getMyTeamPlayers,
  getTeamPlayers,
  createPlayer,
  updatePlayer,
  deletePlayer,
  importPlayers,
  buyPlayer,
  scrapeIplPlayers
};