/**
 * PaddleOCR Output Normalizer
 * 
 * This module normalizes PaddleOCR output to make it more similar to OCR.Space format.
 * It applies universal fixes that work across different cricket scorecards.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const logger = require('./logger');

/**
 * Normalize PaddleOCR output using the Python normalizer script
 * 
 * @param {Object} paddleOutput - The raw PaddleOCR output
 * @returns {Promise<Object>} - Normalized PaddleOCR output
 */
async function normalizePaddleOutput(paddleOutput) {
    try {
        // Create a temporary file for the input
        const tempInputPath = path.join(__dirname, '..', 'temp', `paddle_input_${Date.now()}.json`);
        const tempOutputPath = path.join(__dirname, '..', 'temp', `paddle_output_${Date.now()}.json`);
        
        // Ensure temp directory exists
        const tempDir = path.join(__dirname, '..', 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        
        // Write the input to a temporary file
        fs.writeFileSync(tempInputPath, JSON.stringify(paddleOutput, null, 2), 'utf8');
        
        // Path to the Python normalizer script
        const normalizerScript = path.join(__dirname, '..', 'scripts', 'paddle_output_normalizer.py');
        
        // Run the Python normalizer script
        const pythonPath = process.env.PYTHON_PATH || 'python';
        console.log(`Using Python path for normalizer: ${pythonPath}`);
        
        const normalized = await new Promise((resolve, reject) => {
            const pythonProcess = spawn(pythonPath, [normalizerScript, tempInputPath, '--output', tempOutputPath]);
            
            let errorOutput = '';
            
            pythonProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            pythonProcess.on('close', (code) => {
                if (code !== 0) {
                    console.error(`PaddleOCR normalizer exited with code ${code}: ${errorOutput}`);
                    console.log('Python normalizer failed, falling back to JS normalizer');
                    // If the script fails, use the JS normalizer
                    resolve(normalizeInJS(paddleOutput));
                } else {
                    try {
                        // Read the normalized output
                        const normalizedOutput = JSON.parse(fs.readFileSync(tempOutputPath, 'utf8'));
                        resolve(normalizedOutput);
                    } catch (err) {
                        logger.error(`Error reading normalized output: ${err.message}`);
                        // If reading fails, return the original output
                        resolve(paddleOutput);
                    }
                }
                
                // Clean up temporary files
                try {
                    fs.unlinkSync(tempInputPath);
                    fs.unlinkSync(tempOutputPath);
                } catch (err) {
                    console.error(`Error cleaning up temporary files: ${err.message}`);
                }
            });
        });
        
        return normalized;
    } catch (error) {
        console.error(`Error normalizing PaddleOCR output: ${error.message}`);
        console.log('Error in Python normalizer, falling back to JS normalizer');
        // If anything goes wrong, use the JS normalizer
        return normalizeInJS(paddleOutput);
    }
}

/**
 * JavaScript implementation of the normalizer for cases where Python is not available
 * 
 * @param {Object} paddleOutput - The raw PaddleOCR output
 * @returns {Object} - Normalized PaddleOCR output
 */
function normalizeInJS(paddleOutput) {
    if (!paddleOutput || typeof paddleOutput !== 'object') {
        return paddleOutput;
    }
    
    // Create a deep copy to avoid modifying the original
    const normalized = JSON.parse(JSON.stringify(paddleOutput));
    
    // Skip if no text elements
    if (!normalized.text_elements || !Array.isArray(normalized.text_elements)) {
        return normalized;
    }
    
    // Process each text element
    normalized.text_elements = normalized.text_elements.map(element => {
        if (!element.text) {
            return element;
        }
        
        let text = element.text;
        
        // 1. Fix joined player/team names using intelligent pattern recognition
        // Handle camel case (e.g., JamesFaulkner)
        if (/[A-Z][a-z]+[A-Z]/.test(text)) {
            text = text.replace(/([a-z])([A-Z])/g, '$1 $2');
        }
        
        // Handle ALL CAPS with no spaces but likely multiple words
        if (text.length > 8 && text === text.toUpperCase() && !text.includes(' ')) {
            // Common cricket first names to look for in joined text
            const commonFirstNames = [
                "JAMES", "MATT", "JOHN", "CHRIS", "DAVID", "STEVE", "MICHAEL", 
                "BRENDON", "KANE", "VIRAT", "ROHIT", "BABAR", "SHAHEEN", "JOFRA", 
                "MITCHELL", "TRENT", "NAVEEN", "ALI", "MOHAMMAD", "MOHAMMED", 
                "MOHAMMED", "MARK", "STUART", "BEN", "JOE", "JASON", "AARON", 
                "GLENN", "SHANE", "BRETT", "DALE", "KAGISO", "HASHIM", "QUINTON",
                "FAWAD", "YOUNIS", "MISBAH", "SHOAIB", "WASIM", "WAQAR", "IMRAN",
                "SACHIN", "RAHUL", "SOURAV", "ANIL", "RAVINDRA", "RAVICHANDRAN",
                "JASPRIT", "HARDIK", "RISHABH", "KUMAR", "BHUVNESHWAR", "YUZVENDRA"
            ];
            
            // Try to intelligently split based on common cricket name patterns
            for (const name of commonFirstNames) {
                if (text.includes(name) && text !== name) {
                    // Only replace if it's at the beginning or there's a clear word boundary
                    if (text.startsWith(name) || new RegExp(`[A-Z]${name}`).test(text)) {
                        text = text.replace(name, name + " ");
                        break;
                    }
                }
            }
            
            // If no first name was found but text is still long, try to split at logical points
            if (!text.includes(' ') && text.length > 10) {
                // Look for common patterns in cricket player names
                // Split before "UL" in names like "NAVEEN-UL-HAQ"
                text = text.replace(/([A-Z])(UL-)/, '$1 $2');
                
                // Split after common prefixes
                const prefixes = ["MC", "MAC", "DE", "VAN", "VON", "AL", "EL"];
                for (const prefix of prefixes) {
                    if (text.includes(prefix) && !text.startsWith(prefix)) {
                        text = text.replace(prefix, " " + prefix);
                    }
                }
            }
            
            // If still no spaces and text is very long, use a more aggressive approach
            if (!text.includes(' ') && text.length > 12) {
                // Try to split at logical points based on consonant patterns
                // This is a heuristic approach - split before a capital after 2+ consonants
                text = text.replace(/([BCDFGHJKLMNPQRSTVWXYZ]{2,})([AEIOU][A-Z])/, '$1 $2');
            }
        }
        
        // 2. Fix "Player of the Match:" format (add space after colon if missing)
        if (text.includes("Player of the Match:") && !text.includes("Player of the Match: ")) {
            text = text.replace("Player of the Match:", "Player of the Match: ");
        }
        
        // 3. Fix potential misreads in ball counts and scores using context-aware approach
        
        // Handle potential OCR confusions in cricket contexts
        
        // For ball counts (typically in parentheses and small numbers)
        if (/^\([A-Za-z0-9]\)$/.test(text)) {
            const innerChar = text.substring(1, text.length - 1);
            
            // Common OCR confusions in numbers
            if (['l', 'L', 'I'].includes(innerChar)) {  // lowercase L, uppercase L, uppercase I
                // In cricket context, these are likely to be the number 1
                text = '(1)';
            } else if (innerChar === 'O' || innerChar === 'o') {  // uppercase/lowercase O
                // In cricket context, these are likely to be the number 0
                text = '(0)';
            } else if (innerChar === 'S' || innerChar === 's') {  // can be misread as 5
                text = '(5)';
            } else if (innerChar === 'B' || innerChar === 'b') {  // can be misread as 8
                text = '(8)';
            } else if (innerChar === 'Z' || innerChar === 'z') {  // can be misread as 2
                text = '(2)';
            }
        }
        
        // For standalone numbers (not in parentheses)
        if (['I', 'l', 'L'].includes(text.trim()) && text.length === 1) {
            // These are likely to be the number 1 when they appear alone
            text = '1';
        } else if (['O', 'o'].includes(text.trim()) && text.length === 1) {
            // These are likely to be the number 0 when they appear alone
            text = '0';
        }
        
        // 4. Fix common OCR errors in cricket terms
        text = text.replace("OVERS:", "OVERS: ");
        
        // 5. Remove noise characters that are likely OCR errors
        // Single character noise that's not a number or common cricket notation
        if (text.length === 1 && !/\d/.test(text) && !['W', 'w', 'X', 'x', 'O', 'o', 'B', 'b'].includes(text)) {
            text = "";
        }
        
        // 6. Fix common misreads in team names
        text = text.replace("PHOENX", "PHOENIX");
        text = text.replace("PHOEN1X", "PHOENIX");
        
        // 7. Fix common misreads in player names
        // This is a heuristic approach - we look for common patterns in cricket player names
        if (text.includes("AKBAR") && !text.includes("ALI") && text.length < 10) {
            text = "ALI AKBAR";
        }
        
        // 8. Fix common misreads in bowling figures
        // Ensure there's a hyphen between numbers in bowling figures
        if (/^\d+\s*\d+$/.test(text)) {
            text = text.replace(/(\d+)\s*(\d+)/, '$1-$2');
        }
        
        // Update the text in the element
        element.text = text;
        return element;
    });
    
    // Filter out empty text elements
    normalized.text_elements = normalized.text_elements.filter(elem => elem.text && elem.text.trim());
    
    // Update the full_text field
    if (normalized.text_elements.length > 0) {
        normalized.full_text = normalized.text_elements.map(elem => elem.text).join('\n');
    }
    
    // Update total_elements count
    normalized.total_elements = normalized.text_elements.length;
    
    return normalized;
}

module.exports = {
    normalizePaddleOutput,
    normalizeInJS
};