const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Define upload paths
const uploadPaths = {
  profiles: path.join(__dirname, '../uploads/profiles'),
  players: path.join(__dirname, '../uploads/players'),
  scorecards: path.join(__dirname, '../uploads/scorecards'),
  temp: path.join(__dirname, '../uploads/temp'),
  others: path.join(__dirname, '../uploads/others'),
  teams: path.join(__dirname, '../uploads/teams')
};

// Create all required directories on startup
Object.values(uploadPaths).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Define storage for uploaded files
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let uploadPath;

    // Determine destination directory based on file purpose
    if (file.fieldname === 'profileImage') {
      uploadPath = uploadPaths.profiles;
    } else if (file.fieldname === 'playerImage') {
      uploadPath = uploadPaths.players;
    } else if (file.fieldname === 'scorecard') {
      uploadPath = uploadPaths.scorecards;
    } else if (file.fieldname === 'playersFile') {
      uploadPath = uploadPaths.temp;
    } else if (file.fieldname === 'teamLogo') {
      uploadPath = uploadPaths.teams;
    } else {
      uploadPath = uploadPaths.others;
    }

    // Ensure the directory exists before attempting to write to it
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
      console.log(`Created missing directory during upload: ${uploadPath}`);
    }

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// Filter to validate file types
const fileFilter = (req, file, cb) => {
  // Check file type based on purpose
  if (file.fieldname === 'profileImage' || file.fieldname === 'playerImage') {
    // Allow only images for profile and player images
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed!'), false);
    }
  } else if (file.fieldname === 'scorecard') {
    // Allow only images for scorecards
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed!'), false);
    }
  } else if (file.fieldname === 'playersFile') {
    // Allow Excel and CSV files
    const allowedMimeTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
      'application/csv',
      'text/plain'
    ];

    console.log('Uploaded file mimetype:', file.mimetype);

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error('Only Excel and CSV files are allowed!'), false);
    }
  }

  // Accept the file
  cb(null, true);
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10 MB
});

// Function to handle upload to cloud storage (placeholder for future implementation)
const uploadToCloud = async (file) => {
  if (!file) {
    console.error('No file provided to uploadToCloud');
    throw new Error('No file provided');
  }
  // Directly construct relative URL path based on destination folder and filename
  const folderName = path.basename(file.destination);
  const relativePath = `/uploads/${folderName}/${file.filename}`;
  console.log('Generated upload URL path:', relativePath);
  return relativePath;
};

// Export the necessary functions and objects
module.exports = {
  upload,
  uploadToCloud,
  uploadPaths
};