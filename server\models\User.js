const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin', 'team_owner', 'viewer'],
    default: 'team_owner'
  },
  teamName: {
    type: String,
    validate: {
      validator: function(v) {
        // If role is team_owner, teamName must be provided and non-empty
        // For other roles, teamName is optional
        return this.role !== 'team_owner' || (v && v.trim().length > 0);
      },
      message: props => `Team name is required for team owners`
    }
  },
  team: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  },
  virtualCurrency: {
    type: Number,
    default: 10000 // Initial currency amount
  },
  profilePicture: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('User', UserSchema);