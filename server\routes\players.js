const express = require('express');
const router = express.Router();
const playerController = require('../controllers/playerController');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');
const { upload } = require('../utils/fileUpload');
const fs = require('fs');
const path = require('path');

// @route   GET api/players
// @desc    Get all players with filters, sorting & pagination
// @access  Public
router.get('/', playerController.getAllPlayers);

// @route   GET api/players/myteam
// @desc    Get current user's players
// @access  Private
router.get('/myteam', authenticateToken, playerController.getMyTeamPlayers);

// @route   GET api/players/team/:userId
// @desc    Get players by user ID (team)
// @access  Public
router.get('/team/:userId', playerController.getPlayersByUserId);

// @route   POST api/players/import
// @desc    Import players from Excel file
// @access  Private (Admin only)
router.post(
  '/import',
  [authenticateToken, roleAuth(['admin'])],
  upload.single('playersFile'),
  playerController.importPlayers
);

// @route   POST api/players/scrape-ipl
// @desc    Scrape players from IPL website
// @access  Private (Admin only)
router.post(
  '/scrape-ipl',
  [authenticateToken, roleAuth(['admin'])],
  playerController.scrapeIplPlayers
);

// @route   GET api/players/:id
// @desc    Get player by ID
// @access  Public
router.get('/:id', playerController.getPlayerById);

// @route   POST api/players
// @desc    Create a new player
// @access  Private (Admin only)
router.post('/', [authenticateToken, roleAuth(['admin'])], playerController.createPlayer);

// @route   PUT api/players/:id/transfer
// @desc    Transfer player ownership to another user
// @access  Private (Admin only)
router.put('/:id/transfer', [authenticateToken, roleAuth(['admin'])], playerController.transferPlayerOwnership);

// @route   PUT api/players/:id/remove-owner
// @desc    Remove player ownership
// @access  Private (Admin only)
router.put('/:id/remove-owner', [authenticateToken, roleAuth(['admin'])], playerController.removePlayerOwnership);

// @route   POST api/players/test
// @desc    Test endpoint to create a player
// @access  Public (for testing only)
router.post('/test', async (req, res) => {
  try {
    console.log('Test player creation endpoint called');
    console.log('Request body:', JSON.stringify(req.body, null, 2));

    const Player = require('../models/Player');
    const mongoose = require('mongoose');

    // Create a test player with minimal required fields
    const testPlayer = new Player({
      name: 'Test Player',
      nationality: 'Test Country',
      age: 25,
      height: '180 cm',
      type: 'Batsman',
      battingHand: 'RHB',
      bowlingHand: 'None',
      createdBy: new mongoose.Types.ObjectId() // Create a dummy ObjectId
    });

    console.log('Test player object:', testPlayer);

    // Try to save the player
    const savedPlayer = await testPlayer.save();
    console.log('Test player saved successfully');

    res.json({
      success: true,
      message: 'Test player created successfully',
      player: savedPlayer
    });
  } catch (err) {
    console.error('Error in test player creation:', err);
    console.error('Error stack:', err.stack);

    res.status(500).json({
      success: false,
      message: 'Failed to create test player',
      error: err.message,
      stack: process.env.NODE_ENV === 'production' ? null : err.stack
    });
  }
});

// @route   PUT api/players/:id
// @desc    Update a player
// @access  Private (Admin only)
router.put('/:id', [authenticateToken, roleAuth(['admin'])], playerController.updatePlayer);

// @route   DELETE api/players/:id
// @desc    Delete a player
// @access  Private (Admin only)
router.delete('/:id', [authenticateToken, roleAuth(['admin'])], playerController.deletePlayer);

// @route   POST api/players/map
// @desc    Save player mapping (name and team) to JSON file
// @access  Public
router.post('/map', async (req, res) => {
  try {
    const { id, name, team } = req.body;
    const dataPath = path.join(__dirname, '../utils/ipl_player_data.json');

    // Read existing data
    let playerData = {};
    if (fs.existsSync(dataPath)) {
        playerData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    }

    // Update or add new player
    playerData[id] = { name, team };

    // Save back to file
    fs.writeFileSync(dataPath, JSON.stringify(playerData, null, 2));

    res.json({ success: true, message: 'Player mapping saved' });
  } catch (error) {
    console.error('Error saving player mapping:', error);
    res.status(500).json({ success: false, message: 'Error saving player mapping' });
  }
});

module.exports = router;