const express = require('express');
const router = express.Router();
const playerMatchingController = require('../controllers/playerMatchingController');
const { authenticateToken } = require('../middlewares/auth');
const { requireAdmin } = require('../middlewares/adminAuth');

/**
 * Player Matching Routes
 * Handles robust player name matching between OCR and database
 */

// Public/User routes (require authentication)
router.use(authenticateToken);

/**
 * @route POST /api/player-matching/match-single
 * @desc Match a single player name from OCR
 * @access Private (User/Admin)
 */
router.post('/match-single', playerMatchingController.matchSinglePlayer);

/**
 * @route POST /api/player-matching/match-batch
 * @desc Match multiple player names from OCR (batch processing)
 * @access Private (User/Admin)
 */
router.post('/match-batch', playerMatchingController.matchBatchPlayers);

/**
 * @route POST /api/player-matching/confirm-match
 * @desc Manually confirm a player match
 * @access Private (User/Admin)
 */
router.post('/confirm-match', playerMatchingController.confirmMatch);

/**
 * @route GET /api/player-matching/search-players
 * @desc Search players for manual matching (used in frontend dropdowns)
 * @access Private (User/Admin)
 */
router.get('/search-players', playerMatchingController.searchPlayers);

// Admin-only routes
router.use(requireAdmin);

/**
 * @route POST /api/player-matching/add-alias
 * @desc Add a custom alias for a player
 * @access Private (Admin only)
 */
router.post('/add-alias', playerMatchingController.addAlias);

/**
 * @route GET /api/player-matching/stats
 * @desc Get matching statistics
 * @access Private (Admin only)
 */
router.get('/stats', playerMatchingController.getMatchingStats);

/**
 * @route PUT /api/player-matching/thresholds
 * @desc Update matching thresholds
 * @access Private (Admin only)
 */
router.put('/thresholds', playerMatchingController.updateThresholds);

/**
 * @route POST /api/player-matching/clear-cache
 * @desc Clear the player matching cache
 * @access Private (Admin only)
 */
router.post('/clear-cache', playerMatchingController.clearCache);

module.exports = router;