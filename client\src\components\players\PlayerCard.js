import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar
} from '@mui/material';
import ReactCountryFlag from 'react-country-flag';

const getNationalityCode = (nationality) => {
  // Add a mapping of country names to ISO codes
  const countryMap = {
    'India': 'IN',
    'Australia': 'AU',
    'England': 'GB',
    'South Africa': 'ZA',
    'New Zealand': 'NZ',
    'West Indies': 'BB',
    'Pakistan': 'PK',
    'Sri Lanka': 'LK',
    'Bangladesh': 'BD',
    'Afghanistan': 'AF',
    'Zimbabwe': 'ZW',
    'Ireland': 'IE',
  };
  return countryMap[nationality] || 'UN';
};

const getInitials = (nationality) => {
  const initialsMap = {
    'India': 'IND',
    'Australia': 'AUS',
    'England': 'ENG',
    'South Africa': 'RSA',
    'New Zealand': 'NZL',
    'West Indies': 'WI',
    'Pakistan': 'PAK',
    'Sri Lanka': 'SL',
    'Bangladesh': 'BAN',
    'Afghanistan': 'AFG',
    'Zimbabwe': 'ZIM',
    'Ireland': 'IRE',
  };
  return initialsMap[nationality] || nationality.substring(0, 3).toUpperCase();
};

const PlayerCard = ({ player, onClick }) => {
  return (
    <Card 
      onClick={onClick}
      sx={{ 
        cursor: 'pointer',
        transition: '0.3s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 4
        }
      }}
    >
      <CardContent>
        {/* Nationality and Rating Section */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          {/* Nationality with flag */}
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Typography variant="h6" gutterBottom>
              {getInitials(player.nationality)}
            </Typography>
            <ReactCountryFlag
              countryCode={getNationalityCode(player.nationality)}
              svg
              style={{
                width: '24px',
                height: '24px'
              }}
            />
          </Box>
          
          {/* Rating */}
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            backgroundColor: '#f5f5f5'
          }}>
            <Typography variant="h6">
              {player.ratings.overall}
            </Typography>
          </Box>
        </Box>

        {/* Player Image */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
          <Avatar
            src={player.image || '/placeholder-player.png'}
            alt={player.name}
            sx={{ 
              width: 100, 
              height: 100,
              border: '2px solid #eee'
            }}
          />
        </Box>

        {/* Player Name */}
        <Typography variant="h6" align="center" gutterBottom>
          {player.name}
        </Typography>

        {/* Role */}
        <Typography variant="body2" align="center" color="textSecondary" gutterBottom>
          {player.type}
        </Typography>

        {/* Hand */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 1 }}>
          <Typography variant="body2" color="textSecondary">
            {player.battingHand}
            {player.bowlingHand !== 'None' && ` | ${player.bowlingHand}`}
          </Typography>
        </Box>

        {/* Height */}
        <Typography variant="body2" align="center" color="textSecondary" sx={{ mt: 1 }}>
          {player.height}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default PlayerCard;