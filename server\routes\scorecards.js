const express = require('express');
const router = express.Router();
// const scorecardController = require('../controllers/scorecardController'); // Temporarily commented out
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');

// Log that this routes file is being loaded
console.log('🚀 SCORECARD ROUTES FILE LOADED - Routes are being registered');
console.log('🚀 Current time:', new Date().toISOString());

/**
 * @route   GET api/scorecards/test
 * @desc    Test endpoint for scorecard route
 * @access  Public
 */
router.get(
  '/test',
  (req, res) => {
    console.log('=== SCORECARD TEST ENDPOINT HIT ===');
    res.json({ msg: 'Scorecard test endpoint working', timestamp: new Date().toISOString() });
  }
);

/**
 * @route   GET api/scorecards/:tournamentId/debug
 * @desc    Debug endpoint to test if route is working
 * @access  Public
 */
router.get(
  '/:tournamentId/debug',
  (req, res) => {
    console.log('=== SCORECARD DEBUG ENDPOINT HIT ===');
    console.log('Tournament ID:', req.params.tournamentId);
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);

    res.json({
      msg: 'Scorecard debug endpoint working',
      tournamentId: req.params.tournamentId,
      method: req.method,
      url: req.url,
      timestamp: new Date().toISOString()
    });
  }
);

/**
 * @route   POST api/scorecards/:tournamentId
 * @desc    Upload scorecard image without a match (for OCR processing only)
 * @access  Public (for testing)
 */
router.post(
  '/:tournamentId',
  (req, res) => {
    console.log('=== SCORECARD POST ENDPOINT HIT ===');
    res.json({ msg: 'Scorecard POST endpoint working (controller temporarily disabled)', tournamentId: req.params.tournamentId });
  }
);

// Temporarily commented out routes that use scorecardController
/*
router.post(
  '/:tournamentId/matches/:matchId/scorecard',
  [authenticateToken, roleAuth(['team_owner', 'admin'])],
  scorecardController.uploadScorecard
);

router.post(
  '/:tournamentId/matches/:matchId/verify',
  [authenticateToken, roleAuth(['admin'])],
  scorecardController.verifyMatch
);

router.post(
  '/:tournamentId/matches/:matchId/dispute',
  [authenticateToken, roleAuth(['team_owner'])],
  scorecardController.disputeMatch
);
*/

module.exports = router;
