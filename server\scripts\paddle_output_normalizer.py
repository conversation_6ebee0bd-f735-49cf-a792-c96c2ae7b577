#!/usr/bin/env python
"""
PaddleOCR Output Normalizer

This script normalizes PaddleOCR output to make it more similar to OCR.Space format.
It applies universal fixes that work across different cricket scorecards.
"""

import argparse
import json
import os
import re
import sys
from typing import Dict, List, Any

def normalize_paddle_output(paddle_output: Dict) -> Dict:
    """
    Normalize PaddleOCR output to make it more similar to OCR.Space format
    
    Args:
        paddle_output: The raw PaddleOCR output dictionary
        
    Returns:
        Normalized output dictionary
    """
    if not paddle_output or not isinstance(paddle_output, dict):
        return paddle_output
    
    # Create a copy to avoid modifying the original
    normalized = paddle_output.copy()
    
    # Skip if no text elements
    if 'text_elements' not in normalized or not normalized['text_elements']:
        return normalized
    
    # Process each text element
    for i, element in enumerate(normalized['text_elements']):
        if 'text' not in element:
            continue
            
        # Get the original text
        text = element['text']
        
        # 1. Fix joined player/team names using intelligent pattern recognition
        # Handle camel case (e.g., JamesFaulkner)
        if re.search(r'[A-Z][a-z]+[A-Z]', text):
            text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)
            
        # Handle ALL CAPS with no spaces but likely multiple words
        if len(text) > 8 and text.isupper() and ' ' not in text:
            # Common cricket first names to look for in joined text
            common_first_names = [
                "JAMES", "MATT", "JOHN", "CHRIS", "DAVID", "STEVE", "MICHAEL", 
                "BRENDON", "KANE", "VIRAT", "ROHIT", "BABAR", "SHAHEEN", "JOFRA", 
                "MITCHELL", "TRENT", "NAVEEN", "ALI", "MOHAMMAD", "MOHAMMED", 
                "MOHAMMED", "MARK", "STUART", "BEN", "JOE", "JASON", "AARON", 
                "GLENN", "SHANE", "BRETT", "DALE", "KAGISO", "HASHIM", "QUINTON",
                "FAWAD", "YOUNIS", "MISBAH", "SHOAIB", "WASIM", "WAQAR", "IMRAN",
                "SACHIN", "RAHUL", "SOURAV", "ANIL", "RAVINDRA", "RAVICHANDRAN",
                "JASPRIT", "HARDIK", "RISHABH", "KUMAR", "BHUVNESHWAR", "YUZVENDRA"
            ]
            
            # Try to intelligently split based on common cricket name patterns
            for name in common_first_names:
                if name in text and text != name:
                    # Only replace if it's at the beginning or there's a clear word boundary
                    if text.startswith(name) or re.search(rf'[A-Z]{name}', text):
                        text = text.replace(name, name + " ")
                        break
                        
            # If no first name was found but text is still long, try to split at logical points
            if ' ' not in text and len(text) > 10:
                # Look for common patterns in cricket player names
                # Split before "UL" in names like "NAVEEN-UL-HAQ"
                text = re.sub(r'([A-Z])(UL-)', r'\1 \2', text)
                
                # Split after common prefixes
                for prefix in ["MC", "MAC", "DE", "VAN", "VON", "AL", "EL"]:
                    if prefix in text and not text.startswith(prefix):
                        text = text.replace(prefix, " " + prefix)
                        
            # If still no spaces and text is very long, use a more aggressive approach
            if ' ' not in text and len(text) > 12:
                # Try to split at logical points based on consonant patterns
                # This is a heuristic approach - split before a capital after 2+ consonants
                text = re.sub(r'([BCDFGHJKLMNPQRSTVWXYZ]{2,})([AEIOU][A-Z])', r'\1 \2', text)
            
        # 2. Fix "Player of the Match:" format (add space after colon if missing)
        if "Player of the Match:" in text and not "Player of the Match: " in text:
            text = text.replace("Player of the Match:", "Player of the Match: ")
            
        # 3. Fix potential misreads in ball counts and scores using context-aware approach
        
        # Handle potential OCR confusions in cricket contexts
        
        # For ball counts (typically in parentheses and small numbers)
        if re.match(r'^\([A-Za-z0-9]\)$', text):
            inner_char = text[1:-1]
            
            # Common OCR confusions in numbers
            if inner_char in ['l', 'L', 'I']:  # lowercase L, uppercase L, uppercase I
                # In cricket context, these are likely to be the number 1
                # But only convert if it makes sense in context (ball counts are usually 1-99)
                text = '(1)'
            elif inner_char == 'O' or inner_char == 'o':  # uppercase/lowercase O
                # In cricket context, these are likely to be the number 0
                text = '(0)'
            elif inner_char == 'S' or inner_char == 's':  # can be misread as 5
                text = '(5)'
            elif inner_char == 'B' or inner_char == 'b':  # can be misread as 8
                text = '(8)'
            elif inner_char == 'Z' or inner_char == 'z':  # can be misread as 2
                text = '(2)'
                
        # For standalone numbers (not in parentheses)
        if text.strip() in ['I', 'l', 'L'] and len(text) == 1:
            # These are likely to be the number 1 when they appear alone
            text = '1'
        elif text.strip() in ['O', 'o'] and len(text) == 1:
            # These are likely to be the number 0 when they appear alone
            text = '0'
        
        # 4. Fix common OCR errors in cricket terms
        text = text.replace("OVERS:", "OVERS: ")
        
        # 5. Remove noise characters that are likely OCR errors
        # Single character noise that's not a number or common cricket notation
        if len(text) == 1 and not text.isdigit() and text not in ['W', 'w', 'X', 'x', 'O', 'o', 'B', 'b']:
            text = ""
            
        # 6. Fix common misreads in team names
        text = text.replace("PHOENX", "PHOENIX")
        text = text.replace("PHOEN1X", "PHOENIX")
        
        # 7. Fix common misreads in player names
        # This is a heuristic approach - we look for common patterns in cricket player names
        if "AKBAR" in text and "ALI" not in text and len(text) < 10:
            text = "ALI AKBAR"
            
        # 8. Fix common misreads in bowling figures
        # Ensure there's a hyphen between numbers in bowling figures
        if re.match(r'^\d+\s*\d+$', text):
            text = re.sub(r'(\d+)\s*(\d+)', r'\1-\2', text)
            
        # Update the text in the element
        element['text'] = text
        normalized['text_elements'][i] = element
    
    # Filter out empty text elements
    normalized['text_elements'] = [elem for elem in normalized['text_elements'] if elem.get('text', '').strip()]
    
    # Update the full_text field
    if normalized['text_elements']:
        normalized['full_text'] = '\n'.join([elem.get('text', '') for elem in normalized['text_elements']])
    
    # Update total_elements count
    normalized['total_elements'] = len(normalized['text_elements'])
    
    return normalized

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Normalize PaddleOCR output to make it more similar to OCR.Space format')
    parser.add_argument('input_file', help='Input JSON file with PaddleOCR output')
    parser.add_argument('--output', help='Output JSON file path (default: input_file with _normalized suffix)')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file not found: {args.input_file}")
        sys.exit(1)
    
    # Determine output file path
    output_file = args.output
    if not output_file:
        base, ext = os.path.splitext(args.input_file)
        output_file = f"{base}_normalized{ext}"
    
    # Read input file
    try:
        with open(args.input_file, 'r', encoding='utf-8') as f:
            paddle_output = json.load(f)
    except Exception as e:
        print(f"Error reading input file: {e}")
        sys.exit(1)
    
    # Normalize output
    normalized_output = normalize_paddle_output(paddle_output)
    
    # Write output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(normalized_output, f, indent=2, ensure_ascii=False)
        print(f"Normalized output saved to: {output_file}")
    except Exception as e:
        print(f"Error writing output file: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()