import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  CheckCircle as CheckCircleIcon,
  SportsCricket as CricketIcon,
  Home as HomeIcon,
  FlightTakeoff as AwayIcon
} from '@mui/icons-material';

import { getNextRoundNumber, getMatchesBetweenTeams, addMatch } from '../../services/tournamentService';
import { useAuth } from '../../hooks/useAuth';

const AddMatchForm = ({ open, onClose, tournament, onMatchAdded, initialData = null }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [teams, setTeams] = useState([]);
  const [completedRounds, setCompletedRounds] = useState([]);
  const [nextRound, setNextRound] = useState(1);
  const [formData, setFormData] = useState({
    opponentTeamId: '',
    isHomeMatch: true,
    date: new Date(),
    venue: '',
    result: {
      homeTeamScore: {
        runs: 0,
        wickets: 0,
        overs: 0
      },
      awayTeamScore: {
        runs: 0,
        wickets: 0,
        overs: 0
      },
      winnerId: '',
      isTie: false,
      manOfTheMatch: '',
      description: ''
    }
  });

  useEffect(() => {
    if (open && tournament && user) {
      // Filter out the user's team from the registered teams
      const filteredTeams = tournament.registeredTeams ? tournament.registeredTeams.filter(
        team => !user.team || team._id !== user.team
      ) : [];
      setTeams(filteredTeams);

      // Use initialData if provided, otherwise reset form data
      if (initialData) {
        console.log('Using initial data from OCR:', initialData);

        // Handle special winner ID values
        let winnerId = initialData.result?.winnerId || '';

        // Replace placeholder values with actual team IDs
        if (winnerId === 'user-team' && user.team) {
          winnerId = user.team;
        } else if (winnerId === 'opponent-team' && filteredTeams.length > 0) {
          winnerId = initialData.opponentTeamId || filteredTeams[0]._id;
        }

        // If we have extracted team names, try to match them with registered teams
        let bestMatchTeamId = '';
        if (initialData.extractedData?.homeTeamName || initialData.extractedData?.awayTeamName) {
          const extractedTeamName = initialData.isHomeMatch
            ? initialData.extractedData.awayTeamName
            : initialData.extractedData.homeTeamName;

          if (extractedTeamName && filteredTeams.length > 0) {
            // Try to find the best match for the extracted team name
            let bestMatchScore = 0;

            filteredTeams.forEach(team => {
              // Simple string similarity check
              const teamName = team.teamName.toLowerCase();
              const extractedName = extractedTeamName.toLowerCase();

              // Check if one contains the other
              if (teamName.includes(extractedName) || extractedName.includes(teamName)) {
                const score = Math.min(teamName.length, extractedName.length) /
                              Math.max(teamName.length, extractedName.length);

                if (score > bestMatchScore) {
                  bestMatchScore = score;
                  bestMatchTeamId = team._id;
                }
              }
            });

            console.log(`Best match for "${extractedTeamName}": ${bestMatchTeamId} (score: ${bestMatchScore})`);
          }
        }

        // Use the best match team ID if found, otherwise use the first team
        const opponentTeamId = bestMatchTeamId ||
                              initialData.opponentTeamId ||
                              (filteredTeams.length > 0 ? filteredTeams[0]._id : '');

        // Ensure we have valid values for all fields
        const homeTeamScore = initialData.result?.homeTeamScore || { runs: 0, wickets: 0, overs: 0 };
        const awayTeamScore = initialData.result?.awayTeamScore || { runs: 0, wickets: 0, overs: 0 };

        // Create the form data with processed values
        setFormData({
          opponentTeamId: opponentTeamId,
          isHomeMatch: initialData.isHomeMatch !== undefined ? initialData.isHomeMatch : true,
          date: initialData.date || new Date(),
          venue: initialData.venue || '',
          result: {
            homeTeamScore: {
              runs: parseInt(homeTeamScore.runs) || 0,
              wickets: parseInt(homeTeamScore.wickets) || 0,
              overs: parseFloat(homeTeamScore.overs) || 0
            },
            awayTeamScore: {
              runs: parseInt(awayTeamScore.runs) || 0,
              wickets: parseInt(awayTeamScore.wickets) || 0,
              overs: parseFloat(awayTeamScore.overs) || 0
            },
            winnerId: winnerId,
            isTie: initialData.result?.isTie || false,
            manOfTheMatch: initialData.result?.manOfTheMatch || '',
            description: initialData.result?.description ||
                        (initialData.extractedData ?
                          `Match result extracted from scorecard. ${initialData.extractedData.resultText || ''}` :
                          '')
          },
          extractedData: initialData.extractedData || null
        });
      } else {
        // Reset form data to defaults
        setFormData({
          opponentTeamId: filteredTeams.length > 0 ? filteredTeams[0]._id : '',
          isHomeMatch: true,
          date: new Date(),
          venue: '',
          result: {
            homeTeamScore: {
              runs: 0,
              wickets: 0,
              overs: 0
            },
            awayTeamScore: {
              runs: 0,
              wickets: 0,
              overs: 0
            },
            winnerId: user.team || '',
            isTie: false,
            manOfTheMatch: '',
            description: ''
          }
        });
      }

      // Reset state
      setError(null);
      setSuccess(null);
      setCompletedRounds([]);
      setNextRound(1);
    }
  }, [open, tournament, user, initialData]);

  useEffect(() => {
    // When opponent team is selected, fetch completed rounds
    if (formData.opponentTeamId && user && user.team) {
      fetchCompletedRounds();
    }
  }, [formData.opponentTeamId, user]);

  const fetchCompletedRounds = async () => {
    try {
      if (!tournament || !tournament._id || !user || !user.team || !formData.opponentTeamId) {
        return;
      }

      setLoading(true);

      // Get matches between the two teams
      const { matches, completedRounds } = await getMatchesBetweenTeams(
        tournament._id,
        user.team,
        formData.opponentTeamId
      );

      // Set completed rounds
      setCompletedRounds(Array.from({ length: completedRounds }, (_, i) => i + 1));
      setNextRound(completedRounds + 1);

    } catch (err) {
      console.error('Error fetching completed rounds:', err);
      setError('Failed to fetch completed rounds. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name.startsWith('result.')) {
      const resultField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        result: {
          ...prev.result,
          [resultField]: value
        }
      }));
    } else if (name.startsWith('result.homeTeamScore.') || name.startsWith('result.awayTeamScore.')) {
      const parts = name.split('.');
      const team = parts[1];
      const field = parts[2];

      setFormData(prev => ({
        ...prev,
        result: {
          ...prev.result,
          [team]: {
            ...prev.result[team],
            [field]: value
          }
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (!tournament || !tournament._id || !user || !user.team || !formData.opponentTeamId) {
        setError('Missing required data. Please try again.');
        return;
      }

      setSubmitting(true);
      setError(null);
      setSuccess(null);

      // Determine winner based on scores if not set
      let winnerId = formData.result.winnerId;
      let isTie = formData.result.isTie;

      if (!winnerId && !isTie) {
        const homeScore = formData.result.homeTeamScore.runs;
        const awayScore = formData.result.awayTeamScore.runs;

        // In cricket, the team with more runs wins, regardless of home/away status
        // Team 2 is always the chasing team (batting second)
        
        // Determine which team is Team 1 and Team 2 based on isHomeMatch flag
        const team1Id = formData.isHomeMatch ? user.team : formData.opponentTeamId;
        const team2Id = formData.isHomeMatch ? formData.opponentTeamId : user.team;
        
        // Team 1 is always represented by homeTeamScore in the form
        // Team 2 is always represented by awayTeamScore in the form
        
        if (homeScore > awayScore) {
          // Team 1 scored more runs
          winnerId = team1Id;
        } else if (awayScore > homeScore) {
          // Team 2 scored more runs
          winnerId = team2Id;
        } else {
          isTie = true;
        }
      }

      // Prepare data for API
      const matchData = {
        ...formData,
        result: {
          ...formData.result,
          winnerId,
          isTie
        }
      };

      // Add match
      const result = await addMatch(tournament._id, matchData);

      setSuccess('Match added successfully!');

      // Notify parent component
      if (onMatchAdded) {
        onMatchAdded(result);
      }

      // Close dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (err) {
      console.error('Error adding match:', err);
      setError(err.message || 'Failed to add match. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Check if we have all required data
  const hasRequiredData = tournament && tournament._id && user && user.team;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CricketIcon sx={{ mr: 1 }} />
          Add Match Result
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {!hasRequiredData && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Missing required data. Please make sure you have a team and are registered for this tournament.
          </Alert>
        )}

        {hasRequiredData && (
          <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            {/* Match Details */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Match Details
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Opponent Team</InputLabel>
                <Select
                  name="opponentTeamId"
                  value={formData.opponentTeamId}
                  onChange={handleChange}
                  disabled={loading}
                >
                  {teams.map(team => (
                    <MenuItem key={team._id} value={team._id}>
                      {team.teamName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Home Team Designation</InputLabel>
                <Select
                  name="isHomeMatch"
                  value={formData.isHomeMatch}
                  onChange={handleChange}
                >
                  <MenuItem value={true}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HomeIcon sx={{ mr: 1 }} />
                      Your Team is Home Team
                    </Box>
                  </MenuItem>
                  <MenuItem value={false}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AwayIcon sx={{ mr: 1 }} />
                      Opponent Team is Home Team
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Note: Home team designation is independent of batting order. Team 1 always bats first, Team 2 always chases.
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Round Information
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
                  {completedRounds.map(round => (
                    <Chip
                      key={round}
                      label={`Round ${round}`}
                      color="success"
                      icon={<CheckCircleIcon />}
                      variant="outlined"
                    />
                  ))}
                  {nextRound && (
                    <Chip
                      label={`Round ${nextRound} (Current)`}
                      color="primary"
                      variant="filled"
                    />
                  )}
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Match Date & Time"
                  value={formData.date}
                  onChange={handleDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Venue"
                name="venue"
                value={formData.venue}
                onChange={handleChange}
                placeholder="Enter match venue"
              />
            </Grid>

            {/* Match Result */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Match Result
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                {formData.isHomeMatch ? 'Your Team' : 'Opponent Team'} (Team 1 - Batting First)
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Runs"
                    name="result.homeTeamScore.runs"
                    type="number"
                    value={formData.result.homeTeamScore.runs}
                    onChange={handleChange}
                    inputProps={{ min: 0 }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Wickets"
                    name="result.homeTeamScore.wickets"
                    type="number"
                    value={formData.result.homeTeamScore.wickets}
                    onChange={handleChange}
                    inputProps={{ min: 0, max: 10 }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Overs"
                    name="result.homeTeamScore.overs"
                    type="number"
                    value={formData.result.homeTeamScore.overs}
                    onChange={handleChange}
                    inputProps={{ min: 0, step: 0.1 }}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" gutterBottom>
                {formData.isHomeMatch ? 'Opponent Team' : 'Your Team'} (Team 2 - Chasing)
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Runs"
                    name="result.awayTeamScore.runs"
                    type="number"
                    value={formData.result.awayTeamScore.runs}
                    onChange={handleChange}
                    inputProps={{ min: 0 }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Wickets"
                    name="result.awayTeamScore.wickets"
                    type="number"
                    value={formData.result.awayTeamScore.wickets}
                    onChange={handleChange}
                    inputProps={{ min: 0, max: 10 }}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    label="Overs"
                    name="result.awayTeamScore.overs"
                    type="number"
                    value={formData.result.awayTeamScore.overs}
                    onChange={handleChange}
                    inputProps={{ min: 0, step: 0.1 }}
                  />
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Match Result</InputLabel>
                <Select
                  name="result.winnerId"
                  value={formData.result.winnerId}
                  onChange={handleChange}
                >
                  <MenuItem value="">Auto-determine from scores</MenuItem>
                  {user && user.team && (
                    <MenuItem value={user.team}>
                      Your Team Won {formData.isHomeMatch ? "(Team 1)" : "(Team 2)"}
                    </MenuItem>
                  )}
                  {formData.opponentTeamId && (
                    <MenuItem value={formData.opponentTeamId}>
                      Opponent Team Won {formData.isHomeMatch ? "(Team 2)" : "(Team 1)"}
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                In cricket, the team with more runs wins, regardless of batting order.
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Tie/No Result</InputLabel>
                <Select
                  name="result.isTie"
                  value={formData.result.isTie}
                  onChange={handleChange}
                >
                  <MenuItem value={false}>Match had a winner</MenuItem>
                  <MenuItem value={true}>Match was tied/no result</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Man of the Match"
                name="result.manOfTheMatch"
                value={formData.result.manOfTheMatch}
                onChange={handleChange}
                placeholder="Enter player name"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Match Notes"
                name="result.description"
                value={formData.result.description}
                onChange={handleChange}
                multiline
                rows={2}
                placeholder="Enter any additional notes about the match"
              />
            </Grid>

            {/* Display extracted data from scorecard if available */}
            {initialData?.extractedData && (
              <Grid item xs={12}>
                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Extracted Data from Scorecard
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Home Team:</strong> {initialData.extractedData.homeTeamName || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Away Team:</strong> {initialData.extractedData.awayTeamName || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Result:</strong> {initialData.extractedData.resultText || 'Not detected'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Player of Match:</strong> {initialData.result?.manOfTheMatch || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Venue:</strong> {initialData.venue || 'Not detected'}
                      </Typography>
                    </Grid>

                    {/* Display home team score */}
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold', mt: 1 }}>
                        Home Team Score:
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {initialData.result?.homeTeamScore?.runs || 0}-{initialData.result?.homeTeamScore?.wickets || 0}
                        ({initialData.result?.homeTeamScore?.overs || 0} overs)
                      </Typography>
                    </Grid>

                    {/* Display away team score */}
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold', mt: 1 }}>
                        Away Team Score:
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {initialData.result?.awayTeamScore?.runs || 0}-{initialData.result?.awayTeamScore?.wickets || 0}
                        ({initialData.result?.awayTeamScore?.overs || 0} overs)
                      </Typography>
                    </Grid>

                    {/* Display a note about the data extraction */}
                    <Grid item xs={12}>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Note: The data above was automatically extracted from the scorecard image.
                        Please verify and correct any information before submitting.
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            )}
          </Grid>
        </form>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={submitting}>
          Cancel
        </Button>
        {hasRequiredData && (
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            disabled={submitting || !formData.opponentTeamId}
          >
            {submitting ? <CircularProgress size={24} /> : 'Add Match'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddMatchForm;
