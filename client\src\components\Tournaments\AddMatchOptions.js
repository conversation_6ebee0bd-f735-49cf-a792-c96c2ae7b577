import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  IconButton,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon
} from '@mui/icons-material';
import GuidedScorecardCapture from './GuidedScorecardCapture';
import ScorecardUpload from './ScorecardUpload';
import EnhancedMatchForm from './EnhancedMatchForm';

/**
 * Add Match Options Component
 *
 * This component provides team owners with options to add a match result:
 * 1. Capture Scorecard - Using guided camera capture
 * 2. Upload Scorecard - Direct upload from device
 *
 * @param {Object} props Component props
 * @param {boolean} props.open Whether the dialog is open
 * @param {Function} props.onClose Callback when dialog is closed
 * @param {Object} props.tournament Tournament object
 * @param {Function} props.onMatchAdded Callback when match is added
 */
const AddMatchOptions = ({ open, onClose, tournament, onMatchAdded }) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const [matchData, setMatchData] = useState(null);

  // Log tournament data on component mount
  useEffect(() => {
    console.log('AddMatchOptions component mounted with tournament:', tournament);
  }, [tournament]);

  // Handle option selection
  const handleOptionSelect = (option) => {
    setSelectedOption(option);
  };

  // Handle close
  const handleClose = () => {
    setSelectedOption(null);
    setMatchData(null);
    onClose();
  };

  // Handle scorecard upload success
  const handleScorecardSuccess = (result) => {
    console.log('Scorecard processed:', result);

    // If we have OCR data, use it to pre-fill the match form
    if (result.ocrData) {
      const ocrData = result.ocrData;
      console.log('OCR Data for match form:', ocrData);

      // Extract team names from OCR data - handle both old and new formats
      const team1Name = ocrData.team1 || '';
      const team2Name = ocrData.team2 || '';

      // For backward compatibility
      const homeTeamName = team1Name || (ocrData.homeTeam && ocrData.homeTeam.name) || '';
      const awayTeamName = team2Name || (ocrData.awayTeam && ocrData.awayTeam.name) || '';

      // Extract result text to determine winner
      const resultText = ocrData.resultText || ocrData.result || '';
      console.log('Result text:', resultText);

      // Format venue text
      let venueText = '';
      if (ocrData.venue && typeof ocrData.venue === 'string') {
        // Clean up venue text - remove any "VS" or team names
        venueText = ocrData.venue
          .replace(/\bvs\b/i, '')
          .replace(/\bv\b/i, '')
          .replace(new RegExp(team1Name, 'i'), '')
          .replace(new RegExp(team2Name, 'i'), '')
          .trim();
      }

      // Get scores from the appropriate format
      const team1Score = ocrData.team1Score || (ocrData.homeTeam && ocrData.homeTeam.score) || { runs: 0, wickets: 0, overs: 0 };
      const team2Score = ocrData.team2Score || (ocrData.awayTeam && ocrData.awayTeam.score) || { runs: 0, wickets: 0, overs: 0 };

      // Pass the raw OCR data to the form component
      // The form component will handle mapping to the appropriate fields
      const newMatchData = {
        // Team information
        team1: homeTeamName,
        team2: awayTeamName,

        // Scores
        team1Score: {
          runs: parseInt(team1Score.runs) || 0,
          wickets: parseInt(team1Score.wickets) || 0,
          overs: parseFloat(team1Score.overs) || 0
        },
        team2Score: {
          runs: parseInt(team2Score.runs) || 0,
          wickets: parseInt(team2Score.wickets) || 0,
          overs: parseFloat(team2Score.overs) || 0
        },

        // Match details
        venue: ocrData.venue || venueText || '',
        resultText: resultText,
        playerOfMatch: ocrData.playerOfMatch || '',

        // Include the original OCR data for reference
        ocrData: ocrData,

        // Include the scorecard image URL
        scorecardUrl: result.scorecard?.url
      };

      console.log('Prepared match data from OCR:', newMatchData);
      setMatchData(newMatchData);

      // Move to the match form
      setSelectedOption('form');
    }
  };

  // Render the appropriate component based on selected option
  const renderSelectedOption = () => {
    switch (selectedOption) {
      case 'capture':
        return (
          <GuidedScorecardCapture
            open={true}
            onClose={() => setSelectedOption(null)}
            tournamentId={tournament._id}
            matchId={null} // We don't have a match ID yet
            onUploadSuccess={handleScorecardSuccess}
            onSwitchToTraditional={() => setSelectedOption('upload')}
          />
        );

      case 'upload':
        return (
          <ScorecardUpload
            open={true}
            onClose={() => setSelectedOption(null)}
            tournamentId={tournament._id}
            matchId={null} // We don't have a match ID yet
            onUploadSuccess={handleScorecardSuccess}
            tournament={tournament} // Pass the full tournament object
          />
        );

      case 'form':
        return (
          <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
            <h3>Debug Form</h3>
            <p>OCR Data: {JSON.stringify(matchData, null, 2)}</p>
            <p>Tournament: {tournament?.name}</p>
            <button onClick={() => setSelectedOption(null)}>Close</button>
            <hr />
            <p>If you can see this without errors, the issue is in EnhancedMatchForm component.</p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Dialog open={open && !selectedOption} onClose={handleClose} maxWidth="md">
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Add Match Result</Typography>
            <IconButton onClick={handleClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" paragraph>
            Choose how you want to add the match result:
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardActionArea onClick={() => handleOptionSelect('capture')}>
                  <CardMedia
                    component="img"
                    height="140"
                    image="/images/guided-capture.jpg"
                    alt="Guided Capture"
                    sx={{ objectFit: 'cover', bgcolor: 'action.hover' }}
                  />
                  <CardContent>
                    <Typography gutterBottom variant="h6" component="div">
                      Capture Scorecard
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Use your camera to capture the scorecard directly from your TV or monitor.
                      Our guided system will help you align the image perfectly.
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardActionArea onClick={() => handleOptionSelect('upload')}>
                  <CardMedia
                    component="img"
                    height="140"
                    image="/images/upload-scorecard.jpg"
                    alt="Upload Scorecard"
                    sx={{ objectFit: 'cover', bgcolor: 'action.hover' }}
                  />
                  <CardContent>
                    <Typography gutterBottom variant="h6" component="div">
                      Upload Scorecard
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Upload a screenshot or photo of the match scorecard from your device.
                      Our system will extract the match data automatically.
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="body2" color="text.secondary">
            Both options will use OCR technology to extract match data from the scorecard,
            which you can review and edit before submitting.
          </Typography>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            variant="outlined"
            onClick={() => handleOptionSelect('form')}
          >
            Enter Manually
          </Button>
        </DialogActions>
      </Dialog>

      {/* Render the selected option component */}
      {renderSelectedOption()}
    </>
  );
};

export default AddMatchOptions;
