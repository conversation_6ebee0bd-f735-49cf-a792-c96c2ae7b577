import React from 'react';
import {
  Grid,
  Box,
  Typography,
  Container,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import PlayerCard from './PlayerCard';

const CardGallery = ({
  players = [],
  loading = false,
  error = null,
  totalPages = 1,
  currentPage = 1,
  filters = {},
  onPageChange,
  onFilterChange,
  onSearchChange,
  onCardClick
}) => {
  const playerTypes = ['Batsman', 'Bowler', 'Batting Allrounder', 'Bowling Allrounder', 'Allrounder', 'Wicket Keeper'];
  const rarityLevels = ['Common', 'Rare', 'Epic', 'Legendary'];

  return (
    <Container maxWidth="xl">
      {/* Filters */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          {/* Search */}
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              name="search"
              label="Search Players"
              variant="outlined"
              fullWidth
              value={filters.search || ''}
              onChange={onSearchChange}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
              }}
            />
          </Grid>

          {/* Type filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Player Type</InputLabel>
              <Select
                name="type"
                value={filters.type || ''}
                onChange={onFilterChange}
                label="Player Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {playerTypes.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Rarity filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Rarity</InputLabel>
              <Select
                name="rarity"
                value={filters.rarity || ''}
                onChange={onFilterChange}
                label="Rarity"
              >
                <MenuItem value="">All Rarities</MenuItem>
                {rarityLevels.map((rarity) => (
                  <MenuItem key={rarity} value={rarity}>{rarity}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Sort */}
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Sort By</InputLabel>
              <Select
                name="sort"
                value={filters.sort || 'ratings.overall:desc'}
                onChange={onFilterChange}
                label="Sort By"
              >
                <MenuItem value="ratings.overall:desc">Rating (High to Low)</MenuItem>
                <MenuItem value="ratings.overall:asc">Rating (Low to High)</MenuItem>
                <MenuItem value="marketValue:desc">Value (High to Low)</MenuItem>
                <MenuItem value="marketValue:asc">Value (Low to High)</MenuItem>
                <MenuItem value="name:asc">Name (A to Z)</MenuItem>
                <MenuItem value="name:desc">Name (Z to A)</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading spinner */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* No results message */}
      {!loading && players.length === 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          No players found matching the current filters.
        </Alert>
      )}

      {/* Player cards grid */}
      {!loading && players.length > 0 && (
        <Grid container spacing={3}>
          {players.map((player) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={player._id}>
              <PlayerCard
                player={player}
                onClick={() => onCardClick(player._id)}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={onPageChange}
            color="primary"
            size="large"
          />
        </Box>
      )}
    </Container>
  );
};

export default CardGallery;