const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Register a new user
 * @route POST /api/auth/register
 * @access Public
 */
exports.register = async (req, res) => {
  try {
    console.log('Registration request received:', JSON.stringify(req.body));
    const { username, email, password, role, teamName } = req.body;

    console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);

    // Validate required fields
    if (!username || !email || !password) {
      console.log('Missing required fields:', { username, email, password: !!password });
      return res.status(400).json({ msg: 'Please provide username, email and password' });
    }

    // Check if user already exists
    let user = await User.findOne({ $or: [{ email }, { username }] });
    if (user) {
      console.log('User already exists:', { email, username });
      return res.status(400).json({ msg: 'User already exists' });
    }

    // Validate team name for team_owner role
    const selectedRole = role || 'team_owner';
    if (selectedRole === 'team_owner' && (!teamName || !teamName.trim())) {
      console.log('Team owner role requires team name');
      return res.status(400).json({ msg: 'Team name is required for team owners' });
    }

    // Create new user object
    user = new User({
      username,
      email,
      password,
      role: selectedRole,
      teamName: teamName || undefined
    });

    console.log('User object created:', {
      username: user.username,
      email: user.email,
      role: user.role,
      teamName: user.teamName
    });

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);

    try {
      // Save user to database
      await user.save();
      console.log('User saved successfully');
    } catch (validationError) {
      console.error('Validation error:', JSON.stringify(validationError));
      if (validationError.name === 'ValidationError') {
        const errors = Object.values(validationError.errors).map(err => err.message);
        return res.status(400).json({ msg: errors.join(', ') });
      }
      throw validationError;
    }

    // Create JWT payload
    const payload = {
      user: {
        id: user.id,
        role: user.role
      }
    };

    // Log JWT_SECRET details (without revealing the actual secret)
    console.log('JWT_SECRET type:', typeof process.env.JWT_SECRET);
    console.log('JWT_SECRET length:', process.env.JWT_SECRET ? process.env.JWT_SECRET.length : 0);

    // Create team for team_owner users
    if (user.role === 'team_owner' && user.teamName) {
      try {
        const Team = require('../models/Team');
        const User = require('../models/User');

        // Check if team already exists for this user
        const existingTeam = await Team.findOne({ owner: user.id });

        if (!existingTeam) {
          console.log('Creating team for new team owner:', user.username);
          // Create new team with complete default settings
          const team = new Team({
            owner: user.id,
            teamName: user.teamName,
            description: '',
            logo: '/uploads/teams/default-logo.png',
            primaryColor: '#1e88e5',
            secondaryColor: '#bbdefb',
            colorScheme: 'Blue',
            homeGround: '',
            foundedYear: new Date().getFullYear(),
            slogan: '',
            budget: {
              totalBudget: 10000, // Default budget
              allocations: {
                playerAcquisition: { percentage: 60, amount: 6000 },
                playerDevelopment: { percentage: 20, amount: 2000 },
                teamOperations: { percentage: 10, amount: 1000 },
                marketing: { percentage: 5, amount: 500 },
                reserve: { percentage: 5, amount: 500 }
              },
              transactions: []
            }
          });

          await team.save();
          console.log('Team created successfully for new user with ID:', team._id);

          // Update user record with team reference
          await User.findByIdAndUpdate(user.id, {
            team: team._id
          });

          console.log('User updated with team reference');
        } else {
          console.log('Team already exists for this user:', existingTeam._id);
        }
      } catch (teamErr) {
        console.error('Error creating team during registration:', teamErr);
        // Continue with registration even if team creation fails
        // The team can be created later
      }
    }

    // Sign token
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '5d' },
      (err, token) => {
        if (err) {
          console.error('JWT sign error:', err);
          throw err;
        }
        console.log('JWT generated successfully');
        res.json({ token });
      }
    );
  } catch (err) {
    console.error('Registration error details:', {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code,
      fullError: JSON.stringify(err, Object.getOwnPropertyNames(err))
    });

    // Check for common MongoDB errors
    if (err.code === 11000) {
      return res.status(400).json({
        msg: 'Duplicate key error. Username or email already exists.',
        field: err.keyPattern ? Object.keys(err.keyPattern)[0] : undefined
      });
    }

    res.status(500).json({ msg: 'Server error during registration. Please try again later.' });
  }
};

/**
 * Login user
 * @route POST /api/auth/login
 * @access Public
 */
exports.login = async (req, res) => {
  try {
    const { email: emailOrUsername, password } = req.body;

    // Find user by email or username (case-insensitive search)
    const user = await User.findOne({
      $or: [
        { email: { $regex: `^${emailOrUsername}$`, $options: 'i' } },
        { username: { $regex: `^${emailOrUsername}$`, $options: 'i' } }
      ]
    });

    if (!user) {
      return res.status(400).json({ msg: 'Invalid credentials' });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ msg: 'Invalid credentials' });
    }

    // Create and return JWT token
    const payload = {
      user: {
        id: user.id,
        role: user.role
      }
    };

    jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '24h' }, (err, token) => {
      if (err) throw err;
      res.json({ token });
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).send('Server error');
  }
};

/**
 * Get current user
 * @route GET /api/auth/me
 * @access Private
 */
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
};

/**
 * Update user profile
 * @route PUT /api/auth/profile
 * @access Private
 */
exports.updateProfile = async (req, res) => {
  try {
    const { username, email, teamName, password, newPassword, profilePicture } = req.body;

    // Get user from database (excluding password)
    let user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // If user is changing password, verify the current password
    let passwordChanged = false;
    if (newPassword) {
      if (!password) {
        return res.status(400).json({ msg: 'Current password is required to set a new password' });
      }

      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        return res.status(400).json({ msg: 'Current password is incorrect' });
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(newPassword, salt);
      passwordChanged = true;
    }

    // Update user fields
    if (username && username !== user.username) {
      // Check if the new username is already taken
      const existingUser = await User.findOne({ username });
      if (existingUser) {
        return res.status(400).json({ msg: 'Username already taken' });
      }
      user.username = username;
    }

    if (email && email !== user.email) {
      // Check if the new email is already taken
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({ msg: 'Email already in use' });
      }
      user.email = email;
    }

    if (user.role === 'team_owner' && teamName) {
      user.teamName = teamName;
    }

    if (profilePicture) {
      user.profilePicture = profilePicture;
    }

    // Save updated user
    await user.save();

    // Return user without password
    const updatedUser = await User.findById(req.user.id).select('-password');
    res.json({
      user: updatedUser,
      passwordChanged,
      msg: passwordChanged ? 'Profile updated successfully. Please login again with your new password.' : 'Profile updated successfully.'
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
};