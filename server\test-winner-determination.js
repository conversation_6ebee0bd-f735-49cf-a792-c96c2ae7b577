const MatchOutcomeService = require('./services/matchOutcomeService');

function testWinnerDetermination() {
  try {
    console.log('Testing winner determination logic...');
    
    const matchOutcomeService = new MatchOutcomeService();
    
    // Create test data that matches the screenshot
    const team1Score = {
      runs: 164,
      wickets: 2,
      overs: 18.2,
      extras: 0,
      ballsFaced: 110
    };
    
    const team2Score = {
      runs: 162,
      wickets: 5,
      overs: 20,
      extras: 0,
      ballsFaced: 120
    };
    
    // Create match object
    const match = {
      homeTeam: '681a8a001c449f2f8180e70b', // Chennai Super Kings
      awayTeam: '681a68fcd145d1e6c9fe510b', // Some other team
      homeTeamBattedFirst: false,
      team1IsHomeTeam: false
    };
    
    console.log('\nTest Case 1: homeTeamBattedFirst=false, team1IsHomeTeam=false');
    console.log('This means: Team 1 is away team and batted first');
    console.log('Expected: Team 1 won by runs');
    
    const result1 = matchOutcomeService.determineWinner(team1Score, team2Score, '', match);
    console.log('Result:', result1);
    
    // Test case 2: Flip homeTeamBattedFirst
    match.homeTeamBattedFirst = true;
    console.log('\nTest Case 2: homeTeamBattedFirst=true, team1IsHomeTeam=false');
    console.log('This means: Team 1 is away team and batted second');
    console.log('Expected: Team 1 won by wickets');
    
    const result2 = matchOutcomeService.determineWinner(team1Score, team2Score, '', match);
    console.log('Result:', result2);
    
    // Test case 3: Flip team1IsHomeTeam
    match.homeTeamBattedFirst = false;
    match.team1IsHomeTeam = true;
    console.log('\nTest Case 3: homeTeamBattedFirst=false, team1IsHomeTeam=true');
    console.log('This means: Team 1 is home team and batted second');
    console.log('Expected: Team 1 won by wickets');
    
    const result3 = matchOutcomeService.determineWinner(team1Score, team2Score, '', match);
    console.log('Result:', result3);
    
    // Test case 4: Both true
    match.homeTeamBattedFirst = true;
    match.team1IsHomeTeam = true;
    console.log('\nTest Case 4: homeTeamBattedFirst=true, team1IsHomeTeam=true');
    console.log('This means: Team 1 is home team and batted first');
    console.log('Expected: Team 1 won by runs');
    
    const result4 = matchOutcomeService.determineWinner(team1Score, team2Score, '', match);
    console.log('Result:', result4);
    
    // Test case 5: Match from screenshot
    match.homeTeamBattedFirst = false;
    match.team1IsHomeTeam = false;
    console.log('\nTest Case 5: Match from screenshot');
    console.log('homeTeamBattedFirst:', match.homeTeamBattedFirst);
    console.log('team1IsHomeTeam:', match.team1IsHomeTeam);
    console.log('Expected: Team 1 won by runs');
    
    const result5 = matchOutcomeService.determineWinner(team1Score, team2Score, '', match);
    console.log('Result:', result5);
    
  } catch (error) {
    console.error('Error testing winner determination:', error);
  }
}

testWinnerDetermination();