import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Alert,
  Snackbar,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Chip
} from '@mui/material';
import { matchPlayerPhotos } from '../services/playerPhotoService';

/**
 * Component for matching player photos with IPL data
 * @param {Object} props - Component props
 * @param {Array} props.players - Array of players to match
 * @param {Function} props.onComplete - Callback function when matching is complete
 */
const MatchPlayerPhotos = ({ players, onComplete }) => {
  const [url, setUrl] = useState('https://www.iplt20.com/teams/delhi-capitals/squad/2025');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
  };

  const handleMatchPhotos = async () => {
    if (!url.includes('iplt20.com/teams')) {
      setError('Please enter a valid IPL team URL');
      return;
    }

    if (!players || players.length === 0) {
      setError('No players available to match');
      return;
    }

    setLoading(true);
    setError('');
    setResults(null);

    try {
      // Get player IDs
      const playerIds = players.map(player => player._id);
      
      // Call API to match player photos
      const response = await matchPlayerPhotos(playerIds, url);
      
      setResults(response.results);
      setSuccess(response.message);
      
      // Call onComplete callback if provided
      if (onComplete && typeof onComplete === 'function') {
        onComplete(response);
      }
    } catch (err) {
      console.error('Error matching player photos:', err);
      setError('Error matching player photos: ' + (typeof err === 'string' ? err : err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError('');
    setSuccess('');
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Match Player Photos with IPL Data
      </Typography>
      
      <Typography variant="body2" color="text.secondary" paragraph>
        This tool will attempt to match your imported players with players from the IPL website and update their profile photos.
        Enter the URL of an IPL team page to begin matching.
      </Typography>

      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          label="IPL Team URL"
          variant="outlined"
          value={url}
          onChange={handleUrlChange}
          placeholder="https://www.iplt20.com/teams/team-name/squad/year"
          sx={{ mb: 2 }}
        />

        <Button
          variant="contained"
          color="primary"
          onClick={handleMatchPhotos}
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
        >
          {loading ? 'Matching Photos...' : 'Match Player Photos'}
        </Button>
      </Box>

      {results && (
        <Paper sx={{ p: 2, mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Match Results
          </Typography>
          
          <List>
            {results.map((result, index) => (
              <React.Fragment key={result.playerId}>
                <ListItem alignItems="flex-start">
                  <ListItemAvatar>
                    <Avatar 
                      src={result.success ? result.imagePath : '/uploads/players/default.png'} 
                      alt={result.name}
                    />
                  </ListItemAvatar>
                  <ListItemText
                    primary={result.name}
                    secondary={result.message}
                  />
                  <Chip 
                    label={result.success ? 'Success' : 'Failed'} 
                    color={result.success ? 'success' : 'error'} 
                    size="small"
                  />
                </ListItem>
                {index < results.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MatchPlayerPhotos;
