const vision = require('@google-cloud/vision');
const fs = require('fs');
const path = require('path');

/**
 * Google Cloud Vision client for OCR
 */
class GoogleVisionClient {
  constructor() {
    // Create a client with service account credentials
    const keyFilePath = path.join(__dirname, '../credentials/vision-service-account.json');
    this.client = new vision.ImageAnnotatorClient({
      keyFilename: keyFilePath
    });
  }

  /**
   * Detect text in an image using Google Cloud Vision
   * @param {string} imagePath - Path to the image file
   * @returns {Promise<Object>} - OCR results with text and coordinates
   */
  async detectText(imagePath) {
    try {
      console.log(`Processing image with Google Cloud Vision: ${imagePath}`);
      
      // Read the image file
      const imageFile = fs.readFileSync(imagePath);
      
      // Perform text detection
      const [result] = await this.client.textDetection(imageFile);
      const detections = result.textAnnotations;
      
      // Format the results
      const formattedResults = {
        fullTextAnnotation: result.fullTextAnnotation ? result.fullTextAnnotation.text : '',
        textElements: []
      };
      
      // Skip the first element which is the entire text
      if (detections && detections.length > 1) {
        console.log(`Google Vision detected ${detections.length - 1} text elements`);
        
        for (let i = 1; i < detections.length; i++) {
          const detection = detections[i];
          const vertices = detection.boundingPoly.vertices;
          
          // Calculate the bounding box
          const x = Math.min(...vertices.map(v => v.x || 0));
          const y = Math.min(...vertices.map(v => v.y || 0));
          const width = Math.max(...vertices.map(v => v.x || 0)) - x;
          const height = Math.max(...vertices.map(v => v.y || 0)) - y;
          
          formattedResults.textElements.push({
            text: detection.description,
            x,
            y,
            width,
            height,
            confidence: 1.0 // Google Vision doesn't provide confidence per word
          });
        }
      } else {
        console.log('No text elements detected by Google Vision');
      }
      
      return formattedResults;
    } catch (error) {
      console.error('Error in Google Vision text detection:', error);
      throw error;
    }
  }
}

module.exports = GoogleVisionClient;