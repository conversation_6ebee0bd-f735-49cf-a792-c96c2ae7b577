import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  Divider,
  IconButton,
  Badge,
  useTheme,
  Tooltip
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  EmojiEvents as TrophyIcon,
  SportsKabaddi as BiddingIcon,
  MonetizationOn as MoneyIcon
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';

// Individual bid item component
const BidItem = ({ bid, currentUserId, isLatest }) => {
  const theme = useTheme();
  const isCurrentUserBid = bid.bidder._id === currentUserId;
  const isWinning = bid.isWinning || bid.isHighestBid;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <ListItem
        sx={{
          bgcolor: isLatest ? 'rgba(255, 152, 0, 0.1)' : isCurrentUserBid ? 'rgba(76, 175, 80, 0.08)' : 'transparent',
          borderRadius: 1,
          mb: { xs: 0.25, sm: 0.5 },
          py: { xs: 0.5, sm: 0.75 },
          px: { xs: 0.75, sm: 1 },
          border: isLatest ? `1px solid ${theme.palette.warning.main}` : 'none',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {isWinning && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              bgcolor: 'success.main',
              color: 'white',
              py: 0.25,
              px: 0.75,
              transform: 'rotate(45deg) translate(20%, -50%)',
              transformOrigin: 'top right',
              boxShadow: 1,
              zIndex: 1,
              fontSize: '0.55rem',
              fontWeight: 'bold',
              display: { xs: 'none', sm: 'block' }
            }}
          >
            LEADING
          </Box>
        )}

        <ListItemAvatar sx={{ minWidth: { xs: 32, sm: 40, md: 48 } }}>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            badgeContent={
              isCurrentUserBid ? (
                <Avatar sx={{ width: { xs: 12, sm: 16 }, height: { xs: 12, sm: 16 }, bgcolor: 'primary.main' }}>
                  <PersonIcon sx={{ fontSize: { xs: 8, sm: 10 } }} />
                </Avatar>
              ) : null
            }
          >
            <Avatar
              src={bid.bidder.profilePicture || '/default-avatar.png'}
              sx={{
                width: { xs: 24, sm: 28, md: 32 },
                height: { xs: 24, sm: 28, md: 32 },
                border: isWinning ? `2px solid ${theme.palette.success.main}` :
                       isCurrentUserBid ? `2px solid ${theme.palette.primary.main}` : 'none'
              }}
            />
          </Badge>
        </ListItemAvatar>

        <ListItemText
          primaryTypographyProps={{ component: 'div' }}
          primary={
            <Box component="span" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="body2" component="span" sx={{
                fontWeight: 'medium',
                fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' }
              }}>
                {isCurrentUserBid ? 'You' : bid.bidder.username}
                {isLatest &&
                  <Chip
                    size="small"
                    label="New"
                    color="warning"
                    sx={{
                      ml: 0.5,
                      height: { xs: 14, sm: 16 },
                      '& .MuiChip-label': {
                        px: { xs: 0.25, sm: 0.5 },
                        fontSize: { xs: '0.55rem', sm: '0.625rem' }
                      }
                    }}
                  />
                }
              </Typography>
              <Tooltip title={format(new Date(bid.time), 'MMM dd, yyyy HH:mm:ss')}>
                <Typography variant="caption" component="span" color="text.secondary" sx={{
                  fontSize: { xs: '0.6rem', sm: '0.65rem', md: '0.7rem' }
                }}>
                  {formatDistanceToNow(new Date(bid.time), { addSuffix: true })}
                </Typography>
              </Tooltip>
            </Box>
          }
          secondaryTypographyProps={{ component: 'div' }}
          secondary={
            <Box component="span" sx={{ display: 'flex', alignItems: 'center', mt: 0.25 }}>
              <MoneyIcon fontSize="small" sx={{
                mr: 0.25,
                color: 'primary.main',
                fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.875rem' }
              }} />
              <Typography
                variant="body2"
                fontWeight="bold"
                color="primary"
                component="span"
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' } }}
              >
                {bid.amount.toLocaleString()}
              </Typography>

              {/* Status chips - show only one based on priority */}
              {isCurrentUserBid && isWinning && (
                <Chip
                  size="small"
                  label="Winning"
                  color="success"
                  sx={{
                    ml: 0.5,
                    height: { xs: 14, sm: 16 },
                    '& .MuiChip-label': {
                      px: { xs: 0.25, sm: 0.5 },
                      fontSize: { xs: '0.55rem', sm: '0.625rem' }
                    }
                  }}
                />
              )}
              {isCurrentUserBid && !isWinning && (
                <Chip
                  size="small"
                  label="Outbid"
                  color="error"
                  sx={{
                    ml: 0.5,
                    height: { xs: 14, sm: 16 },
                    '& .MuiChip-label': {
                      px: { xs: 0.25, sm: 0.5 },
                      fontSize: { xs: '0.55rem', sm: '0.625rem' }
                    }
                  }}
                />
              )}
              {!isCurrentUserBid && isWinning && (
                <Chip
                  size="small"
                  label="Leading"
                  color="primary"
                  sx={{
                    ml: 0.5,
                    height: { xs: 14, sm: 16 },
                    '& .MuiChip-label': {
                      px: { xs: 0.25, sm: 0.5 },
                      fontSize: { xs: '0.55rem', sm: '0.625rem' }
                    }
                  }}
                />
              )}
            </Box>
          }
        />
      </ListItem>
    </motion.div>
  );
};

// Main bid activity feed component
const BidActivityFeed = ({ bids = [], currentUserId, title = "Bid Activity", maxHeight = 400 }) => {
  const scrollRef = useRef(null);
  const [newBids, setNewBids] = useState([]);

  // Auto-scroll to bottom when new bids come in
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }

    // Mark new bids
    const latestBids = bids.slice(-3);
    setNewBids(latestBids.map(bid => bid.id));

    // Clear "new" status after 5 seconds
    const timer = setTimeout(() => {
      setNewBids([]);
    }, 5000);

    return () => clearTimeout(timer);
  }, [bids]);

  return (
    <Paper elevation={3} sx={{ p: { xs: 1, sm: 1.5, md: 2 }, height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 0.75, sm: 1, md: 1.5 } }}>
        <BiddingIcon sx={{ mr: 0.75, color: 'primary.main', fontSize: { xs: 16, sm: 18, md: 24 } }} />
        <Typography variant={{ xs: 'subtitle2', sm: 'subtitle1', md: 'h6' }} sx={{ fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' } }}>
          {title}
        </Typography>
        <Badge
          badgeContent={bids.length}
          color="primary"
          sx={{ ml: 0.75 }}
          size="small"
        >
          <NotificationsIcon color="action" sx={{ fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' } }} />
        </Badge>
      </Box>

      <Divider sx={{ mb: { xs: 0.75, sm: 1, md: 1.5 } }} />

      {bids.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexGrow: 1, p: { xs: 1, sm: 1.5, md: 2 } }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' } }}>
            No bids yet. Be the first to bid!
          </Typography>
        </Box>
      ) : (
        <Box
          ref={scrollRef}
          sx={{
            overflowY: 'auto',
            maxHeight: typeof maxHeight === 'object' ? maxHeight : { xs: 250, sm: 300, md: maxHeight },
            flexGrow: 1,
            '&::-webkit-scrollbar': {
              width: { xs: '4px', sm: '6px' },
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '10px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#888',
              borderRadius: '10px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: '#555',
            },
          }}
        >
          <List disablePadding dense sx={{
            py: { xs: 0.25, sm: 0.5 },
            px: { xs: 0.25, sm: 0.5 }
          }}>
            <AnimatePresence>
              {bids.map((bid, index) => (
                <BidItem
                  key={bid.id || index}
                  bid={bid}
                  currentUserId={currentUserId}
                  isLatest={newBids.includes(bid.id)}
                />
              ))}
            </AnimatePresence>
          </List>
        </Box>
      )}
    </Paper>
  );
};

export default BidActivityFeed;
