#!/bin/bash

# Setup GitHub Project Board Columns for RPL Cricket
# This script creates all the phase columns in your GitHub project board

set -e

echo "🚀 Setting up RPL Cricket Project Board Columns"
echo "==============================================="

# Check GitHub CLI
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI not found"
    exit 1
fi

if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub CLI"
    exit 1
fi

echo "✅ GitHub CLI ready"
echo ""

# Set the project ID (from your URL: https://github.com/users/rhingonekar/projects/4)
PROJECT_ID="4"

echo ""
echo "📋 Creating project board columns for User Project ID: $PROJECT_ID"

# Function to create a column for user project
create_column() {
    local column_name="$1"
    echo "Creating column: $column_name"

    # For user projects, we use a different API endpoint
    gh api user/projects/$PROJECT_ID/columns -X POST \
        -f name="$column_name" || echo "⚠️  Column may already exist: $column_name"
}

# Create all columns
create_column "✅ Phase 1.0 - Complete"
create_column "🔄 Phase 2.0 - In Progress"
create_column "🔄 Phase 3.0 - In Progress"
create_column "🔄 Phase 4.0 - In Progress"
create_column "📋 Phase 5.0 - Planned"
create_column "🔄 Phase 6.0 - In Progress"
create_column "📋 Phase 7.0 - Big Ant Cricket 24"

echo ""
echo "🎉 Project board columns created successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Go to your project board: https://github.com/users/rhingonekar/projects/$PROJECT_ID"
echo "2. Verify the columns were created"
echo "3. Run the issue creation script: ./scripts/create-complete-project-board.sh"
echo "4. Add the created issues to the appropriate columns"
