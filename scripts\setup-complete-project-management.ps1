# Complete RPL Cricket Project Management Setup
# Runs both project board creation and issue creation scripts

param(
    [string]$RepoOwner = "rhingonekar",
    [string]$RepoName = "rplwebapp",
    [switch]$DryRun = $false
)

Write-Host "🚀 Complete RPL Cricket Project Management Setup" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Step 1: Create Project Board
Write-Host "Step 1: Creating Project Board Structure" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
try {
    if ($DryRun) {
        & "$scriptPath\setup-project-board.ps1" -RepoOwner $RepoOwner -RepoName $RepoName -DryRun
    } else {
        & "$scriptPath\setup-project-board.ps1" -RepoOwner $RepoOwner -RepoName $RepoName
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Project board creation failed"
    }
} catch {
    Write-Host "❌ Project board setup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "⏳ Waiting 5 seconds for GitHub to process..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Step 2: Create Issues
Write-Host "Step 2: Creating Project Issues" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
try {
    if ($DryRun) {
        & "$scriptPath\create-project-issues.ps1" -RepoOwner $RepoOwner -RepoName $RepoName -DryRun
    } else {
        & "$scriptPath\create-project-issues.ps1" -RepoOwner $RepoOwner -RepoName $RepoName
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Issue creation failed"
    }
} catch {
    Write-Host "❌ Issue creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Complete Project Management Setup Finished!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What was created:" -ForegroundColor Cyan
Write-Host "• GitHub Project Board with phase-based structure" -ForegroundColor White
Write-Host "• Custom fields (Phase, Priority, Completion %)" -ForegroundColor White
Write-Host "• Issues for all major project tasks" -ForegroundColor White
Write-Host "• Proper labeling and categorization" -ForegroundColor White
Write-Host "• Historical work marked as complete" -ForegroundColor White
Write-Host ""
Write-Host "📝 Manual steps needed:" -ForegroundColor Yellow
Write-Host "1. Go to: https://github.com/$RepoOwner/$RepoName/projects" -ForegroundColor White
Write-Host "2. Open your new project board" -ForegroundColor White
Write-Host "3. Add the created issues to the project board" -ForegroundColor White
Write-Host "4. Organize issues into appropriate columns" -ForegroundColor White
Write-Host "5. Set up automation rules (optional)" -ForegroundColor White
Write-Host ""
Write-Host "🎯 Priority Tasks to Focus On:" -ForegroundColor Cyan
Write-Host "• 🔄 2.6 Complete Transfer Market System (80% done)" -ForegroundColor White
Write-Host "• 🎮 7.1 Skill Points & Rating System (Critical)" -ForegroundColor White
Write-Host "• 🎮 7.2 Performance Milestone Bonuses (Critical)" -ForegroundColor White
Write-Host "• 🎮 7.3 Comprehensive Leaderboards (High priority)" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Quick Links:" -ForegroundColor Blue
Write-Host "Repository: https://github.com/$RepoOwner/$RepoName" -ForegroundColor Blue
Write-Host "Projects: https://github.com/$RepoOwner/$RepoName/projects" -ForegroundColor Blue
Write-Host "Issues: https://github.com/$RepoOwner/$RepoName/issues" -ForegroundColor Blue
