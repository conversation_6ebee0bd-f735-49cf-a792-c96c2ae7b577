const PlayerMatchingService = require('../services/playerMatchingService');
const Player = require('../models/Player');

/**
 * Controller for player matching operations
 * Handles API endpoints for robust player name matching
 */
class PlayerMatchingController {
  constructor() {
    this.matchingService = new PlayerMatchingService();
  }

  /**
   * Match a single player name from OCR
   * @route POST /api/player-matching/match-single
   * @access Private (Admin/User)
   */
  async matchSinglePlayer(req, res) {
    try {
      const { ocrName, teamFilter, roleFilter, requireManualVerification } = req.body;

      if (!ocrName || typeof ocrName !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'OCR name is required and must be a string'
        });
      }

      const options = {
        teamFilter,
        roleFilter,
        requireManualVerification: requireManualVerification || false
      };

      const result = await this.matchingService.matchPlayer(ocrName, options);

      return res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Error in matchSinglePlayer:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Match multiple player names from OCR (batch processing)
   * @route POST /api/player-matching/match-batch
   * @access Private (Admin/User)
   */
  async matchBatchPlayers(req, res) {
    try {
      const { ocrNames, teamFilter, roleFilter } = req.body;

      if (!Array.isArray(ocrNames) || ocrNames.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'OCR names must be a non-empty array'
        });
      }

      // Validate that all names are strings
      const invalidNames = ocrNames.filter(name => typeof name !== 'string');
      if (invalidNames.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'All OCR names must be strings'
        });
      }

      const options = {
        teamFilter,
        roleFilter
      };

      const result = await this.matchingService.matchPlayers(ocrNames, options);

      return res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Error in matchBatchPlayers:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Manually confirm a player match
   * @route POST /api/player-matching/confirm-match
   * @access Private (Admin/User)
   */
  async confirmMatch(req, res) {
    try {
      const { ocrName, playerId, createAlias } = req.body;

      if (!ocrName || !playerId) {
        return res.status(400).json({
          success: false,
          message: 'OCR name and player ID are required'
        });
      }

      const result = await this.matchingService.confirmMatch(
        ocrName, 
        playerId, 
        createAlias !== false // Default to true
      );

      if (!result.success) {
        return res.status(400).json(result);
      }

      return res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Error in confirmMatch:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Add a custom alias for a player
   * @route POST /api/player-matching/add-alias
   * @access Private (Admin only)
   */
  async addAlias(req, res) {
    try {
      const { alias, playerId } = req.body;

      if (!alias || !playerId) {
        return res.status(400).json({
          success: false,
          message: 'Alias and player ID are required'
        });
      }

      // Verify player exists
      const player = await Player.findById(playerId);
      if (!player) {
        return res.status(404).json({
          success: false,
          message: 'Player not found'
        });
      }

      // Add the alias
      this.matchingService.addAlias(alias, player.name);

      return res.json({
        success: true,
        message: `Alias "${alias}" added for player "${player.name}"`
      });

    } catch (error) {
      console.error('Error in addAlias:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Get matching statistics
   * @route GET /api/player-matching/stats
   * @access Private (Admin only)
   */
  async getMatchingStats(req, res) {
    try {
      const stats = this.matchingService.getMatchingStats();
      
      return res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error('Error in getMatchingStats:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Update matching thresholds
   * @route PUT /api/player-matching/thresholds
   * @access Private (Admin only)
   */
  async updateThresholds(req, res) {
    try {
      const { matchThreshold, verificationThreshold } = req.body;

      if (typeof matchThreshold !== 'number' || typeof verificationThreshold !== 'number') {
        return res.status(400).json({
          success: false,
          message: 'Both thresholds must be numbers'
        });
      }

      if (matchThreshold < 0 || matchThreshold > 1 || verificationThreshold < 0 || verificationThreshold > 1) {
        return res.status(400).json({
          success: false,
          message: 'Thresholds must be between 0 and 1'
        });
      }

      if (verificationThreshold > matchThreshold) {
        return res.status(400).json({
          success: false,
          message: 'Verification threshold cannot be higher than match threshold'
        });
      }

      this.matchingService.updateThresholds(matchThreshold, verificationThreshold);

      return res.json({
        success: true,
        message: 'Thresholds updated successfully',
        data: {
          matchThreshold,
          verificationThreshold
        }
      });

    } catch (error) {
      console.error('Error in updateThresholds:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Clear the player matching cache
   * @route POST /api/player-matching/clear-cache
   * @access Private (Admin only)
   */
  async clearCache(req, res) {
    try {
      this.matchingService.clearCache();
      
      return res.json({
        success: true,
        message: 'Player matching cache cleared successfully'
      });

    } catch (error) {
      console.error('Error in clearCache:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  /**
   * Search players for manual matching (used in frontend dropdowns)
   * @route GET /api/player-matching/search-players
   * @access Private (Admin/User)
   */
  async searchPlayers(req, res) {
    try {
      const { query, limit = 10, teamFilter, roleFilter } = req.query;

      if (!query || query.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Search query must be at least 2 characters long'
        });
      }

      const searchQuery = {
        name: { $regex: query, $options: 'i' }
      };

      if (teamFilter) {
        searchQuery.team = teamFilter;
      }

      if (roleFilter) {
        searchQuery.type = roleFilter;
      }

      const players = await Player.find(searchQuery)
        .select('name type nationality team ratings')
        .limit(parseInt(limit))
        .sort({ name: 1 })
        .lean();

      return res.json({
        success: true,
        data: players
      });

    } catch (error) {
      console.error('Error in searchPlayers:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

// Create a singleton instance
const playerMatchingController = new PlayerMatchingController();

// Export the methods bound to the instance
module.exports = {
  matchSinglePlayer: playerMatchingController.matchSinglePlayer.bind(playerMatchingController),
  matchBatchPlayers: playerMatchingController.matchBatchPlayers.bind(playerMatchingController),
  confirmMatch: playerMatchingController.confirmMatch.bind(playerMatchingController),
  addAlias: playerMatchingController.addAlias.bind(playerMatchingController),
  getMatchingStats: playerMatchingController.getMatchingStats.bind(playerMatchingController),
  updateThresholds: playerMatchingController.updateThresholds.bind(playerMatchingController),
  clearCache: playerMatchingController.clearCache.bind(playerMatchingController),
  searchPlayers: playerMatchingController.searchPlayers.bind(playerMatchingController)
};