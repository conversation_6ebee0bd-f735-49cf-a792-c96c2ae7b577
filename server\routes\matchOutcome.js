const express = require('express');
const router = express.Router();
const matchOutcomeController = require('../controllers/matchOutcomeController');
const { authenticateToken } = require('../middlewares/auth');
const { requireAdmin } = require('../middlewares/adminAuth');

/**
 * Match Outcome Routes
 * 
 * These routes handle match outcome calculation, tournament standings updates,
 * and related functionality for processing cricket match results.
 */

/**
 * @route POST /api/match-outcome/calculate
 * @desc Calculate match outcome from OCR data
 * @access Private
 * @body {
 *   ocrData: Object,     // OCR extracted match data
 *   tournamentId: String, // Tournament ID (optional)
 *   matchId: String      // Match ID (optional)
 * }
 */
router.post('/calculate', authenticateToken, matchOutcomeController.calculateOutcome);

/**
 * @route POST /api/match-outcome/update-standings
 * @desc Update tournament standings with match outcome
 * @access Private
 * @body {
 *   tournamentId: String, // Tournament ID
 *   matchId: String,      // Match ID
 *   outcome: Object       // Match outcome data
 * }
 */
router.post('/update-standings', authenticateToken, matchOutcomeController.updateStandings);

/**
 * @route POST /api/match-outcome/process-match
 * @desc Process complete match (calculate outcome + update standings)
 * @access Private
 * @body {
 *   ocrData: Object,      // OCR extracted match data
 *   tournamentId: String, // Tournament ID
 *   matchId: String       // Match ID
 * }
 */
router.post('/process-match', authenticateToken, matchOutcomeController.processMatch);

/**
 * @route GET /api/match-outcome/standings/:tournamentId
 * @desc Get tournament standings
 * @access Private
 * @param tournamentId - Tournament ID
 * @query phaseIndex - Phase index (optional, defaults to latest phase)
 */
router.get('/standings/:tournamentId', authenticateToken, matchOutcomeController.getStandings);

/**
 * @route POST /api/match-outcome/validate
 * @desc Validate match outcome data
 * @access Private
 * @body {
 *   outcome: Object // Match outcome data to validate
 * }
 */
router.post('/validate', authenticateToken, matchOutcomeController.validateOutcome);

/**
 * @route GET /api/match-outcome/stats
 * @desc Get match outcome service statistics
 * @access Private
 */
router.get('/stats', authenticateToken, matchOutcomeController.getStats);

/**
 * @route GET /api/match-outcome/admin/matches/:id
 * @desc Get match details for admin
 * @access Private (Admin only)
 * @param id - Match ID
 */
router.get('/admin/matches/:id', authenticateToken, requireAdmin, matchOutcomeController.getAdminMatch);

/**
 * @route GET /api/match-outcome/admin/tournament-matches/:tournamentId
 * @desc Get all matches for a tournament (admin)
 * @access Private (Admin only)
 * @param tournamentId - Tournament ID
 */
router.get('/admin/tournament-matches/:tournamentId', authenticateToken, requireAdmin, matchOutcomeController.getTournamentMatches);

module.exports = router;