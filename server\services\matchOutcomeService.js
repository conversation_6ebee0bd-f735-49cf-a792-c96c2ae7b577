const Tournament = require('../models/Tournament');
const Team = require('../models/Team');
const Player = require('../models/Player');

/**
 * Match Outcome Calculation Service
 * 
 * This service processes OCR-extracted match data and calculates:
 * - Match winner based on scores
 * - Points allocation for teams
 * - Net run rate calculations
 * - Tournament standings updates
 * - Player performance statistics
 */
class MatchOutcomeService {
  constructor() {
    this.initialized = false;
  }

  /**
   * Initialize the service
   */
  async initialize() {
    try {
      console.log('Initializing Match Outcome Service...');
      this.initialized = true;
      console.log('Match Outcome Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Match Outcome Service:', error);
      throw error;
    }
  }

  /**
   * Calculate match outcome from OCR data
   * @param {Object} ocrData - OCR extracted match data
   * @param {string} tournamentId - Tournament ID
   * @param {Object|string} matchIdOrObject - Match ID or match object for testing
   * @returns {Promise<Object>} - Match outcome with winner, points, and statistics
   */
  async calculateMatchOutcome(ocrData, tournamentId, matchIdOrObject = null, team1Id = null, team2Id = null, homeTeamBattedFirst = null, team1IsHomeTeam = null) {
    console.log('DEBUG: calculateMatchOutcome called', { tournamentId, matchIdOrObject });
    console.log('DEBUG: team1Id param', team1Id, 'team2Id param', team2Id);
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      console.log('Calculating match outcome from OCR data...');

      // Extract team scores from OCR data
      const team1Score = this.extractTeamScore(ocrData, 'team1');
      const team2Score = this.extractTeamScore(ocrData, 'team2');

      // Use explicit team1Id/team2Id mapping if provided
      let team1ObjId = team1Id;
      let team2ObjId = team2Id;
      let team1Name = ocrData.team1 || 'Team 1';
      let team2Name = ocrData.team2 || 'Team 2';
      let tournament = null;
      // (moved match block below)
      
      // Attempt to infer batting order from OCR data
      let inferredBattingOrder = null;
      if (ocrData.resultText) {
        const resultText = ocrData.resultText.toLowerCase();
        // Look for phrases that indicate batting order
        if (resultText.includes('batting first') || resultText.includes('batted first')) {
          if (resultText.includes(ocrData.team1?.toLowerCase())) {
            inferredBattingOrder = { team1BattedFirst: true };
            console.log('Inferred from OCR: Team 1 batted first');
          } else if (resultText.includes(ocrData.team2?.toLowerCase())) {
            inferredBattingOrder = { team1BattedFirst: false };
            console.log('Inferred from OCR: Team 2 batted first');
          }
        }
        
        // Look for chasing/defending indicators
        if (resultText.includes('chasing') || resultText.includes('chase')) {
          if (resultText.includes(ocrData.team1?.toLowerCase())) {
            inferredBattingOrder = { team1BattedFirst: false };
            console.log('Inferred from OCR: Team 1 was chasing');
          } else if (resultText.includes(ocrData.team2?.toLowerCase())) {
            inferredBattingOrder = { team1BattedFirst: true };
            console.log('Inferred from OCR: Team 2 was chasing');
          }
        }
      }

      // Retrieve match information if matchId is provided
      let match = null;
      
      // If matchIdOrObject is an object, use it directly (for testing)
      if (matchIdOrObject && typeof matchIdOrObject === 'object') {
        match = matchIdOrObject;
      }
      // Otherwise, treat it as a matchId and look it up
      else if (matchIdOrObject) {
        const matchId = matchIdOrObject;
        tournament = await Tournament.findById(tournamentId).populate('registeredTeams', 'teamName');
        if (tournament) {
          console.log('DEBUG: registeredTeams', tournament.registeredTeams);
          // Look up real team names from registeredTeams
          let team1Obj = null, team2Obj = null;
          if (tournament.registeredTeams) {
            team1Obj = tournament.registeredTeams.find(
              t => t._id.toString() === team1ObjId?.toString()
            );
            team2Obj = tournament.registeredTeams.find(
              t => t._id.toString() === team2ObjId?.toString()
            );
            // Always use the ObjectId from the team object if available
            team1ObjId = team1Obj ? team1Obj._id : team1ObjId;
            team2ObjId = team2Obj ? team2Obj._id : team2ObjId;
            if (team1Obj) team1Name = team1Obj.teamName;
            if (team2Obj) team2Name = team2Obj.teamName;
            console.log('DEBUG: team1ObjId', team1ObjId, 'team2ObjId', team2ObjId);
            console.log('DEBUG: team1Name', team1Name, 'team2Name', team2Name);
          }
          // Log all match IDs in all phases
          tournament.phases.forEach((phase, idx) => {
            console.log('DEBUG: Phase', idx, 'match IDs:', phase.matches.map(m => m._id.toString()));
          });
          console.log('DEBUG: Searching for matchId:', matchId);
          // Find the match in tournament phases
          for (let i = 0; i < tournament.phases.length; i++) {
            const phase = tournament.phases[i];
            for (let j = 0; j < phase.matches.length; j++) {
              if (phase.matches[j]._id.toString() === matchId) {
                match = phase.matches[j];
                break;
              }
            }
            if (match) break;
          }
        }
      }

      // Defensive check for match initialization
      console.log('DEBUG: match after lookup', match);
      if (!match) {
        console.error('FATAL: match is not initialized! tournamentId:', tournamentId, 'matchId:', matchIdOrObject);
        throw new Error('Match not found or not initialized');
      }
      // Now safe to use match
      // If match object exists, try to get names from it
      if (match) {
        // Assign team names based on home/away status
        if (team1IsHomeTeam) {
          match.homeTeamName = team1Name;
          match.awayTeamName = team2Name;
        } else {
          match.homeTeamName = team2Name;
          match.awayTeamName = team1Name;
        }
        // Override with explicit flags if provided
        if (homeTeamBattedFirst !== null) match.homeTeamBattedFirst = homeTeamBattedFirst;
        if (team1IsHomeTeam !== null) match.team1IsHomeTeam = team1IsHomeTeam;
      }
      // Determine match winner
      const matchResult = this.determineWinner(team1Score, team2Score, ocrData.resultText, match, inferredBattingOrder);

      // Calculate net run rate impact
      const netRunRateData = this.calculateNetRunRate(team1Score, team2Score);

      // Extract player performances
      const playerPerformances = this.extractPlayerPerformances(ocrData, match);

      // Find player of the match
      const playerOfMatch = await this.findPlayerOfMatch(ocrData.playerOfMatch);

      // Prepare match outcome object
      const outcome = {
        winner: matchResult.winner,
        isTie: matchResult.isTie,
        isDuckworthLewis: matchResult.isDuckworthLewis,
        team1Score: team1Score,
        team2Score: team2Score,
        netRunRateData: netRunRateData,
        playerPerformances: playerPerformances,
        playerOfMatch: playerOfMatch,
        resultDescription: matchResult.description,
        extractedAt: new Date(),
        confidence: this.calculateConfidence(ocrData),
        team1Id: team1ObjId,
        team2Id: team2ObjId
      };

      console.log('Match outcome calculated successfully');
      console.log('DEBUG: Final outcome object:', JSON.stringify(outcome, null, 2));
      return outcome;

    } catch (error) {
      console.error('Error calculating match outcome:', error);
      throw error;
    }
  }

  /**
   * Extract team score from OCR data
   * @param {Object} ocrData - OCR data
   * @param {string} teamKey - 'team1' or 'team2'
   * @returns {Object} - Team score object
   */
  extractTeamScore(ocrData, teamKey) {
    const scoreKey = `${teamKey}Score`;
    const score = ocrData[scoreKey] || {};

    return {
      runs: parseInt(score.runs) || 0,
      wickets: parseInt(score.wickets) || 0,
      overs: parseFloat(score.overs) || 0,
      extras: parseInt(score.extras) || 0,
      ballsFaced: parseInt(score.ballsFaced) || this.calculateBallsFaced(score.overs)
    };
  }

  /**
   * Calculate balls faced from overs
   * @param {number} overs - Number of overs
   * @returns {number} - Total balls faced
   */
  calculateBallsFaced(overs) {
    if (!overs) return 0;
    const completeOvers = Math.floor(overs);
    const remainingBalls = Math.round((overs - completeOvers) * 10);
    return (completeOvers * 6) + remainingBalls;
  }

  /**
   * Determine match winner based on scores and result text
   * @param {Object} team1Score - Team 1 score
   * @param {Object} team2Score - Team 2 score
   * @param {string} resultText - OCR extracted result text
   * @param {Object} match - Match object containing homeTeam, team1, team2, and homeTeamBattedFirst
   * @param {Object} inferredBattingOrder - Optional batting order inferred from OCR data
   * @returns {Object} - Match result with winner and description
   */
  determineWinner(team1Score, team2Score, resultText = '', match = null, inferredBattingOrder = null) {
    const result = {
      winner: null,
      isTie: false,
      isDuckworthLewis: false,
      description: ''
    };

    // Check for Duckworth-Lewis
    if (resultText && (resultText.toLowerCase().includes('d/l') || resultText.toLowerCase().includes('duckworth'))) {
      result.isDuckworthLewis = true;
    }

    // Check for tie
    if (team1Score.runs === team2Score.runs && team1Score.wickets === team2Score.wickets) {
      result.isTie = true;
      result.description = 'Match tied';
      return result;
    }

    // Robust winner/result logic using explicit mapping
    // Get all needed info from match object, fallback to null-safe values
    let team1Id = null, team2Id = null, team1Name = 'Team 1', team2Name = 'Team 2', homeTeamBattedFirst = null, team1IsHomeTeam = null;
    if (typeof match === 'object' && match !== null) {
      // Assign team IDs and names based on team1IsHomeTeam flag
      if (match.team1IsHomeTeam) {
        team1Id = match.homeTeam;
        team2Id = match.awayTeam;
        team1Name = match.homeTeamName || 'Team 1';
        team2Name = match.awayTeamName || 'Team 2';
      } else {
        team1Id = match.awayTeam;
        team2Id = match.homeTeam;
        team1Name = match.awayTeamName || 'Team 1';
        team2Name = match.homeTeamName || 'Team 2';
      }
      homeTeamBattedFirst = match.homeTeamBattedFirst;
      team1IsHomeTeam = match.team1IsHomeTeam;
    }

    let battedFirstId, battedFirstName, battedFirstScore;
    let chasingId, chasingName, chasingScore;

    console.log('DEBUG: team1Id', team1Id, 'team2Id', team2Id, 'team1Name', team1Name, 'team2Name', team2Name);
    console.log('DEBUG: team1IsHomeTeam', team1IsHomeTeam, 'homeTeamBattedFirst', homeTeamBattedFirst);
    console.log('DEBUG: team1Score', team1Score, 'team2Score', team2Score);

    // Determine which team batted first based on the form logic
    // The frontend sends homeTeamBattedFirst, but we need to determine team1BattedFirst
    let team1BattedFirst;
    if (team1IsHomeTeam) {
      // Team 1 is home team, so team1BattedFirst = homeTeamBattedFirst
      team1BattedFirst = homeTeamBattedFirst;
    } else {
      // Team 1 is away team, so team1BattedFirst = !homeTeamBattedFirst
      team1BattedFirst = !homeTeamBattedFirst;
    }

    console.log('DEBUG: Calculated team1BattedFirst:', team1BattedFirst);

    if (team1BattedFirst) {
      battedFirstId = team1Id;
      battedFirstName = team1Name;
      battedFirstScore = team1Score;
      chasingId = team2Id;
      chasingName = team2Name;
      chasingScore = team2Score;
    } else {
      battedFirstId = team2Id;
      battedFirstName = team2Name;
      battedFirstScore = team2Score;
      chasingId = team1Id;
      chasingName = team1Name;
      chasingScore = team1Score;
    }
    console.log('DEBUG: battedFirstId', battedFirstId, 'battedFirstName', battedFirstName);
    console.log('DEBUG: chasingId', chasingId, 'chasingName', chasingName);

    // Set winner ObjectId based on description (final, robust fix)
    if (battedFirstScore.runs > chasingScore.runs) {
      // Batted first team won by runs
      result.description = `${battedFirstName} won by ${battedFirstScore.runs - chasingScore.runs} runs`;
      result.winner = battedFirstId;
    } else if (chasingScore.runs > battedFirstScore.runs) {
      // Chasing team won by wickets
      const wicketsRemaining = 10 - chasingScore.wickets;
      result.description = `${chasingName} won by ${wicketsRemaining} wickets`;
      result.winner = chasingId;
    } else {
      // Tie
      result.isTie = true;
      result.description = 'Match tied';
      result.winner = null;
    }
    console.log('DEBUG: result.description', result.description);
    console.log('DEBUG: result.winner', result.winner);

    // Additional validation: if we have resultText, try to cross-validate the winner
    if (resultText && resultText.trim() && !result.isTie) {
      const upperResultText = resultText.toUpperCase();

      // Check if the result text contradicts our calculated winner
      if (upperResultText.includes('WON BY')) {
        const team1NameUpper = team1Name.toUpperCase();
        const team2NameUpper = team2Name.toUpperCase();

        console.log('DEBUG: Checking result text for winner validation');
        console.log('DEBUG: team1NameUpper:', team1NameUpper, 'team2NameUpper:', team2NameUpper);
        console.log('DEBUG: result.winner:', result.winner, 'team1Id:', team1Id, 'team2Id:', team2Id);
        console.log('DEBUG: upperResultText includes team1Name:', upperResultText.includes(team1NameUpper));
        console.log('DEBUG: upperResultText includes team2Name:', upperResultText.includes(team2NameUpper));

        if (upperResultText.includes(team1NameUpper) && result.winner === team2Id) {
          console.log('WARNING: Result text suggests team1 won, but we calculated team2 as winner');
          console.log('Correcting winner to team1Id:', team1Id);
          result.winner = team1Id;
        } else if (upperResultText.includes(team2NameUpper) && result.winner === team1Id) {
          console.log('WARNING: Result text suggests team2 won, but we calculated team1 as winner');
          console.log('Correcting winner to team2Id:', team2Id);
          result.winner = team2Id;
        } else {
          console.log('DEBUG: No winner correction needed - calculated winner matches result text');
        }
      }
    }

    if (resultText && resultText.trim()) {
      if (match === null || homeTeamBattedFirst === undefined || match.homeTeamBattedFirst === undefined) {
        result.description = resultText.trim();
        const upperResultText = resultText.toUpperCase();
        if (upperResultText.includes('WON BY')) {
          if (!result.winner) {
            result.description += ' (Winner needs verification)';
          }
        }
      }
    }

    return result;
  }

  /**
   * Calculate net run rate data for both teams
   * @param {Object} team1Score - Team 1 score
   * @param {Object} team2Score - Team 2 score
   * @returns {Object} - Net run rate data
   */
  calculateNetRunRate(team1Score, team2Score) {
    const team1RunRate = team1Score.ballsFaced > 0 ? 
      (team1Score.runs / team1Score.ballsFaced) * 6 : 0;
    const team2RunRate = team2Score.ballsFaced > 0 ? 
      (team2Score.runs / team2Score.ballsFaced) * 6 : 0;

    return {
      team1RunRate: parseFloat(team1RunRate.toFixed(2)),
      team2RunRate: parseFloat(team2RunRate.toFixed(2)),
      team1NetRunRate: parseFloat((team1RunRate - team2RunRate).toFixed(2)),
      team2NetRunRate: parseFloat((team2RunRate - team1RunRate).toFixed(2))
    };
  }

  /**
   * Extract player performances from OCR data
   * @param {Object} ocrData - OCR data
   * @param {Object} match - Match object containing team references
   * @returns {Array} - Array of player performance objects
   */
  extractPlayerPerformances(ocrData, match) {
    const performances = [];

    // Extract batting performances
    ['team1Batsmen', 'team2Batsmen'].forEach((teamKey, teamIndex) => {
      const batsmen = ocrData[teamKey] || [];
      const teamNumber = teamIndex + 1;

      // Get the actual team ObjectId from the match based on team1IsHomeTeam flag
      let teamId;
      if (match.team1IsHomeTeam) {
        // If team1 is home team: team1 -> homeTeam, team2 -> awayTeam
        teamId = teamIndex === 0 ? match.homeTeam : match.awayTeam;
      } else {
        // If team1 is away team: team1 -> awayTeam, team2 -> homeTeam
        teamId = teamIndex === 0 ? match.awayTeam : match.homeTeam;
      }

      batsmen.forEach(batsman => {
        // Only create performance if we have a valid name
        const playerName = batsman.name && batsman.name.trim() ? batsman.name.trim() : null;
        if (playerName) {
          performances.push({
            playerName: playerName,
            team: teamId, // Use the actual team ObjectId
            batting: {
              runs: parseInt(batsman.runs) || 0,
              ballsFaced: parseInt(batsman.balls) || 0,
              fours: parseInt(batsman.fours) || 0,
              sixes: parseInt(batsman.sixes) || 0,
              notOut: batsman.notOut || false,
              strikeRate: this.calculateStrikeRate(batsman.runs, batsman.balls)
            },
            bowling: {
              overs: 0,
              maidens: 0,
              runs: 0,
              wickets: 0,
              economy: 0
            },
            fielding: {
              catches: 0,
              runOuts: 0,
              stumpings: 0
            }
          });
        }
      });
    });

    // Extract bowling performances
    ['team1Bowlers', 'team2Bowlers'].forEach((teamKey, teamIndex) => {
      const bowlers = ocrData[teamKey] || [];
      // Get the actual team ObjectId from the match based on team1IsHomeTeam flag
      let teamId;
      if (match.team1IsHomeTeam) {
        // If team1 is home team: team1 -> homeTeam, team2 -> awayTeam
        teamId = teamIndex === 0 ? match.homeTeam : match.awayTeam;
      } else {
        // If team1 is away team: team1 -> awayTeam, team2 -> homeTeam
        teamId = teamIndex === 0 ? match.awayTeam : match.homeTeam;
      }

      bowlers.forEach(bowler => {
        // Only create performance if we have a valid name
        const playerName = bowler.name && bowler.name.trim() ? bowler.name.trim() : null;
        if (playerName) {
          // Find existing performance or create new one
          let performance = performances.find(p =>
            p.playerName === playerName && String(p.team) === String(teamId)
          );

          if (!performance) {
            performance = {
              playerName: playerName,
              team: teamId,
              batting: {
                runs: 0,
                ballsFaced: 0,
                fours: 0,
                sixes: 0,
                notOut: false,
                strikeRate: 0
              },
              bowling: {
                overs: 0,
                maidens: 0,
                runs: 0,
                wickets: 0,
                economy: 0
              },
              fielding: {
                catches: 0,
                runOuts: 0,
                stumpings: 0
              }
            };
            performances.push(performance);
          }

          // Update bowling stats
          performance.bowling = {
            overs: parseFloat(bowler.overs) || 0,
            maidens: parseInt(bowler.maidens) || 0,
            runs: parseInt(bowler.runs) || 0,
            wickets: parseInt(bowler.wickets) || 0,
            economy: this.calculateEconomy(bowler.runs, bowler.overs)
          };
        }
      });
    });

    return performances;
  }

  /**
   * Calculate strike rate
   * @param {number} runs - Runs scored
   * @param {number} balls - Balls faced
   * @returns {number} - Strike rate
   */
  calculateStrikeRate(runs, balls) {
    if (!balls || balls === 0) return 0;
    return parseFloat(((runs / balls) * 100).toFixed(2));
  }

  /**
   * Calculate bowling economy rate
   * @param {number} runs - Runs conceded
   * @param {number} overs - Overs bowled
   * @returns {number} - Economy rate
   */
  calculateEconomy(runs, overs) {
    if (!overs || overs === 0) return 0;
    return parseFloat((runs / overs).toFixed(2));
  }

  /**
   * Find player of the match in database
   * @param {string} playerName - Player name from OCR
   * @returns {Promise<string|null>} - Player ID or null
   */
  async findPlayerOfMatch(playerName) {
    if (!playerName || !playerName.trim()) {
      return null;
    }

    try {
      // Simple name matching - in practice, you'd use the player matching service
      const player = await Player.findOne({
        name: { $regex: new RegExp(playerName.trim(), 'i') }
      });

      return player ? player._id : null;
    } catch (error) {
      console.error('Error finding player of the match:', error);
      return null;
    }
  }

  /**
   * Calculate confidence score for the match outcome
   * @param {Object} ocrData - OCR data
   * @returns {number} - Confidence score (0-1)
   */
  calculateConfidence(ocrData) {
    let confidence = 0;
    let factors = 0;

    // Check if team names are present
    if (ocrData.team1 && ocrData.team1.trim()) {
      confidence += 0.2;
    }
    if (ocrData.team2 && ocrData.team2.trim()) {
      confidence += 0.2;
    }
    factors += 2;

    // Check if scores are present
    if (ocrData.team1Score && ocrData.team1Score.runs > 0) {
      confidence += 0.2;
    }
    if (ocrData.team2Score && ocrData.team2Score.runs > 0) {
      confidence += 0.2;
    }
    factors += 2;

    // Check if result text is present
    if (ocrData.resultText && ocrData.resultText.trim()) {
      confidence += 0.2;
    }
    factors += 1;

    return factors > 0 ? confidence : 0;
  }

  /**
   * Update tournament standings based on match outcome
   * @param {string} tournamentId - Tournament ID
   * @param {string} matchId - Match ID
   * @param {Object} outcome - Match outcome
   * @returns {Promise<Object>} - Updated standings
   */
  async updateTournamentStandings(tournamentId, matchId, outcome) {
    try {
      const tournament = await Tournament.findById(tournamentId);
      if (!tournament) {
        throw new Error('Tournament not found');
      }

      // Find the match in tournament phases
      let match = null;
      let phaseIndex = -1;
      let matchIndex = -1;

      for (let i = 0; i < tournament.phases.length; i++) {
        const phase = tournament.phases[i];
        for (let j = 0; j < phase.matches.length; j++) {
          if (phase.matches[j]._id.toString() === matchId) {
            match = phase.matches[j];
            phaseIndex = i;
            matchIndex = j;
            break;
          }
        }
        if (match) break;
      }

      if (!match) {
        throw new Error('Match not found in tournament');
      }

      // Update match result
      // Map scores to correct teams based on user mapping
      const homeTeamScore = match.homeTeam.toString() === (outcome.team1Id ? outcome.team1Id.toString() : '') ? outcome.team1Score : outcome.team2Score;
      const awayTeamScore = match.awayTeam.toString() === (outcome.team2Id ? outcome.team2Id.toString() : '') ? outcome.team2Score : outcome.team1Score;

      // Preserve existing scorecardImages and other result data when updating
      const existingResult = tournament.phases[phaseIndex].matches[matchIndex].result || {};

      // Debug logging to track scorecardImages preservation
      console.log('🔍 matchOutcomeService - Before update:', {
        existingScorecardImages: existingResult.scorecardImages?.length || 0,
        existingDispute: existingResult.dispute,
        existingExtractedData: existingResult.extractedData,
        existingResult: existingResult
      });

      // Selectively preserve important fields while ensuring proper structure
      tournament.phases[phaseIndex].matches[matchIndex].result = {
        // Core match result data (updated)
        winner: outcome.winner || null,
        homeTeamScore,
        awayTeamScore,
        manOfTheMatch: outcome.playerOfMatch,
        playerPerformances: outcome.playerPerformances,
        description: outcome.resultDescription,
        isTie: outcome.isTie,
        isDuckworthLewis: outcome.isDuckworthLewis,

        // Preserve important existing fields
        scorecardImages: existingResult.scorecardImages || [],
        verificationStatus: existingResult.verificationStatus || 'pending',
        verifiedBy: existingResult.verifiedBy || null,
        verifiedAt: existingResult.verifiedAt || null,
        highlights: existingResult.highlights || '',
        matchNotes: existingResult.matchNotes || '',
        duckworthLewisTarget: existingResult.duckworthLewisTarget || 0,

        // Ensure complex nested objects have proper structure (convert to plain objects)
        dispute: existingResult.dispute ? {
          isDisputed: existingResult.dispute.isDisputed || false,
          disputedBy: existingResult.dispute.disputedBy || null,
          disputeReason: existingResult.dispute.disputeReason || '',
          disputeStatus: existingResult.dispute.disputeStatus || 'open',
          disputeResolution: existingResult.dispute.disputeResolution || ''
        } : {
          isDisputed: false,
          disputedBy: null,
          disputeReason: '',
          disputeStatus: 'open',
          disputeResolution: ''
        },
        extractedData: existingResult.extractedData ? {
          isExtracted: existingResult.extractedData.isExtracted || false,
          extractionMethod: existingResult.extractedData.extractionMethod || 'none',
          extractedAt: existingResult.extractedData.extractedAt || null,
          rawData: existingResult.extractedData.rawData || ''
        } : {
          isExtracted: false,
          extractionMethod: 'none',
          extractedAt: null,
          rawData: ''
        }
      };

      // Debug logging to verify scorecardImages are preserved
      const finalResult = tournament.phases[phaseIndex].matches[matchIndex].result;
      console.log('🔍 matchOutcomeService - After update:', {
        updatedScorecardImages: finalResult.scorecardImages?.length || 0,
        finalDispute: finalResult.dispute,
        finalExtractedData: finalResult.extractedData,
        disputeType: typeof finalResult.dispute,
        extractedDataType: typeof finalResult.extractedData,
        updatedResult: finalResult
      });

      tournament.phases[phaseIndex].matches[matchIndex].status = 'completed';

      // Update team standings
      await this.updateTeamStandings(tournament, phaseIndex, match, outcome);

      // Save tournament
      await tournament.save();

      console.log('Tournament standings updated successfully');
      return tournament.phases[phaseIndex].standings;

    } catch (error) {
      console.error('Error updating tournament standings:', error);
      throw error;
    }
  }

  /**
   * Update team standings within a phase
   * @param {Object} tournament - Tournament object
   * @param {number} phaseIndex - Phase index
   * @param {Object} match - Match object
   * @param {Object} outcome - Match outcome
   */
  async updateTeamStandings(tournament, phaseIndex, match, outcome) {
    const phase = tournament.phases[phaseIndex];
    
    // Find or create standings for both teams
    let homeTeamStanding = phase.standings.find(s => s.team.toString() === match.homeTeam.toString());
    let awayTeamStanding = phase.standings.find(s => s.team.toString() === match.awayTeam.toString());

    if (!homeTeamStanding) {
      homeTeamStanding = {
        team: match.homeTeam,
        played: 0,
        won: 0,
        lost: 0,
        tied: 0,
        noResult: 0,
        points: 0,
        netRunRate: 0
      };
      phase.standings.push(homeTeamStanding);
    }

    if (!awayTeamStanding) {
      awayTeamStanding = {
        team: match.awayTeam,
        played: 0,
        won: 0,
        lost: 0,
        tied: 0,
        noResult: 0,
        points: 0,
        netRunRate: 0
      };
      phase.standings.push(awayTeamStanding);
    }

    // Update played count
    homeTeamStanding.played += 1;
    awayTeamStanding.played += 1;

    // Update win/loss/tie counts and points
    if (outcome.isTie) {
      homeTeamStanding.tied += 1;
      awayTeamStanding.tied += 1;
      homeTeamStanding.points += tournament.pointsForTie || 1;
      awayTeamStanding.points += tournament.pointsForTie || 1;
    } else if (outcome.winner === 'team1') {
      homeTeamStanding.won += 1;
      awayTeamStanding.lost += 1;
      homeTeamStanding.points += tournament.pointsForWin || 2;
    } else if (outcome.winner === 'team2') {
      awayTeamStanding.won += 1;
      homeTeamStanding.lost += 1;
      awayTeamStanding.points += tournament.pointsForWin || 2;
    } else {
      // No result
      homeTeamStanding.noResult += 1;
      awayTeamStanding.noResult += 1;
      homeTeamStanding.points += tournament.pointsForNoResult || 1;
      awayTeamStanding.points += tournament.pointsForNoResult || 1;
    }

    // Update net run rates (simplified calculation)
    homeTeamStanding.netRunRate = this.updateNetRunRate(
      homeTeamStanding.netRunRate, 
      outcome.netRunRateData.team1NetRunRate,
      homeTeamStanding.played
    );
    
    awayTeamStanding.netRunRate = this.updateNetRunRate(
      awayTeamStanding.netRunRate,
      outcome.netRunRateData.team2NetRunRate,
      awayTeamStanding.played
    );
  }

  /**
   * Update cumulative net run rate
   * @param {number} currentNRR - Current net run rate
   * @param {number} matchNRR - Match net run rate
   * @param {number} matchesPlayed - Total matches played
   * @returns {number} - Updated net run rate
   */
  updateNetRunRate(currentNRR, matchNRR, matchesPlayed) {
    if (matchesPlayed === 1) {
      return parseFloat(matchNRR.toFixed(3));
    }
    
    const totalNRR = (currentNRR * (matchesPlayed - 1)) + matchNRR;
    return parseFloat((totalNRR / matchesPlayed).toFixed(3));
  }

  /**
   * Get match outcome statistics
   * @returns {Object} - Service statistics
   */
  getStats() {
    return {
      initialized: this.initialized,
      version: '1.0.0',
      features: [
        'Match winner determination',
        'Points calculation',
        'Net run rate calculation',
        'Player performance extraction',
        'Tournament standings update',
        'Confidence scoring'
      ]
    };
  }
}

module.exports = MatchOutcomeService;