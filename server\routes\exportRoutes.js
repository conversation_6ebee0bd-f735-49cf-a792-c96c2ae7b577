const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { exportAllPlayerNames } = require('../utils/robustIplScraper');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');

/**
 * @route POST /api/export/ipl-players
 * @desc Export all IPL players to CSV
 * @access Private/Admin
 */
router.post('/ipl-players', authenticateToken, roleAuth('admin'), async (req, res) => {
  try {
    console.log('Starting simple IPL player name export from API...');

    // Run the export
    const outputPath = await exportAllPlayerNames();

    // Check if file exists
    if (!fs.existsSync(outputPath)) {
      return res.status(500).json({
        success: false,
        message: 'Export failed - file not created'
      });
    }

    // Get the filename from the path
    const filename = path.basename(outputPath);

    // Return success with file info
    return res.json({
      success: true,
      message: 'IPL players exported successfully',
      file: {
        path: outputPath,
        name: filename,
        url: `/exports/${filename}`
      }
    });
  } catch (error) {
    console.error('Error exporting IPL players:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while exporting IPL players'
    });
  }
});

/**
 * @route GET /api/export/downloads/:filename
 * @desc Download an exported file
 * @access Private/Admin
 */
router.get('/downloads/:filename', authenticateToken, roleAuth('admin'), (req, res) => {
  try {
    console.log('Download request received for file:', req.params.filename);
    console.log('User:', req.user);

    const { filename } = req.params;

    // Sanitize filename to prevent directory traversal
    const sanitizedFilename = path.basename(filename);
    const filePath = path.join(__dirname, '../exports', sanitizedFilename);

    console.log('Looking for file at path:', filePath);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found:', filePath);
      return res.status(404).json({
        success: false,
        message: 'File not found'
      });
    }

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${sanitizedFilename}"`);
    res.setHeader('Content-Type', 'text/csv');

    // Create read stream and pipe to response
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Handle errors
    fileStream.on('error', (err) => {
      console.error('Error streaming file:', err);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Error streaming file'
        });
      }
    });

    console.log('File download initiated');
  } catch (error) {
    console.error('Error downloading file:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while downloading file'
    });
  }
});

module.exports = router;
