<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cricket Card Background Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .card-background {
            width: 300px;
            height: 485px;
            background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .card-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M50 0 L100 50 L50 100 L0 50 Z" fill="rgba(255,255,255,0.05)"/></svg>');
            background-size: 30px 30px;
            opacity: 0.5;
        }
        
        .card-background::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 50% 50%, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
        }
        
        .card-border {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            pointer-events: none;
        }
        
        .card-shine {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.2) 50%,
                rgba(255,255,255,0) 100%
            );
            transform: skewX(-20deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .instructions {
            margin-top: 20px;
            max-width: 500px;
            text-align: center;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .instructions h2 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions p {
            line-height: 1.5;
            color: #555;
        }
        
        .instructions code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        button {
            margin-top: 15px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card-background" id="cardBackground">
            <div class="card-border"></div>
            <div class="card-shine"></div>
        </div>
        
        <div class="instructions">
            <h2>Cricket Card Background Generator</h2>
            <p>This is a simple generator for a cricket player card background. Take a screenshot of the card above and save it as <code>/public/card-backgrounds/cricket-card-bg.png</code> in your project.</p>
            <p>You can right-click on the card and select "Save image as..." to download it.</p>
            <button id="downloadBtn">Download Background</button>
        </div>
    </div>
    
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const cardElement = document.getElementById('cardBackground');
            
            // Create a canvas element
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas dimensions to match the card
            canvas.width = cardElement.offsetWidth;
            canvas.height = cardElement.offsetHeight;
            
            // Draw the card to the canvas
            const cardStyles = window.getComputedStyle(cardElement);
            ctx.fillStyle = cardStyles.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Convert canvas to data URL and create download link
            const dataURL = canvas.toDataURL('image/png');
            const downloadLink = document.createElement('a');
            downloadLink.href = dataURL;
            downloadLink.download = 'cricket-card-bg.png';
            downloadLink.click();
        });
    </script>
</body>
</html>
