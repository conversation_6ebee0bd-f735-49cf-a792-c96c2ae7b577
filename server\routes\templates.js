const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middlewares/auth');
const Template = require('../models/Template');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Configure multer for template image uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/templates');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'template-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// @route   GET /api/admin/templates/test
// @desc    Test endpoint to verify route is working
// @access  Private
router.get('/test', authenticateToken, (req, res) => {
  console.log('🚨🚨🚨 TEMPLATE ROUTER TEST ENDPOINT HIT 🚨🚨🚨');
  console.log('Template test endpoint hit by user:', req.user?.id);
  res.json({
    message: 'Template route is working!',
    user: req.user?.id,
    timestamp: new Date().toISOString(),
    source: 'TEMPLATE ROUTER'
  });
});

// @route   DELETE /api/admin/templates/test-delete
// @desc    Test DELETE endpoint to verify delete routing
// @access  Private
router.delete('/test-delete', authenticateToken, (req, res) => {
  console.log('🚨🚨🚨 TEMPLATE ROUTER DELETE TEST HIT 🚨🚨🚨');
  console.log('Template DELETE test endpoint hit by user:', req.user?.id);
  res.json({
    message: 'Template DELETE route is working!',
    user: req.user?.id,
    timestamp: new Date().toISOString(),
    source: 'TEMPLATE ROUTER DELETE TEST'
  });
});

// @route   GET /api/admin/templates/debug-simple
// @desc    Simple debug endpoint without auth
// @access  Public (for debugging)
router.get('/debug-simple', async (req, res) => {
  try {
    console.log('Simple debug endpoint hit');

    // Get all templates including inactive ones
    const allTemplates = await Template.find({});
    const activeTemplates = await Template.find({ isActive: true });

    console.log('Simple Debug - Total templates in DB:', allTemplates.length);
    console.log('Simple Debug - Active templates in DB:', activeTemplates.length);

    res.json({
      message: 'Simple template debug info',
      totalTemplates: allTemplates.length,
      activeTemplates: activeTemplates.length,
      templates: allTemplates.map(t => ({
        id: t._id,
        name: t.name,
        isActive: t.isActive,
        regionsCount: t.regions?.length || 0,
        hasImage: !!t.originalImageData,
        createdAt: t.createdAt
      }))
    });
  } catch (error) {
    console.error('Simple debug endpoint error:', error);
    res.status(500).json({
      message: 'Simple debug endpoint error',
      error: error.message
    });
  }
});

// @route   GET /api/admin/templates/debug
// @desc    Debug endpoint to check database contents
// @access  Private
router.get('/debug', authenticateToken, async (req, res) => {
  try {
    console.log('Template debug endpoint hit by user:', req.user?.id);

    // Get all templates including inactive ones
    const allTemplates = await Template.find({})
      .select('name description regions isActive originalImageData createdAt')
      .sort({ createdAt: -1 });

    const activeTemplates = await Template.find({ isActive: true })
      .select('name description regions isActive originalImageData createdAt')
      .sort({ createdAt: -1 });

    console.log('Debug - Total templates in DB:', allTemplates.length);
    console.log('Debug - Active templates in DB:', activeTemplates.length);

    res.json({
      message: 'Template debug info',
      totalTemplates: allTemplates.length,
      activeTemplates: activeTemplates.length,
      allTemplates: allTemplates.map(t => ({
        id: t._id,
        name: t.name,
        isActive: t.isActive,
        regionsCount: t.regions.length,
        hasImage: !!t.originalImageData,
        createdAt: t.createdAt
      })),
      activeTemplatesOnly: activeTemplates.map(t => ({
        id: t._id,
        name: t.name,
        isActive: t.isActive,
        regionsCount: t.regions.length,
        hasImage: !!t.originalImageData,
        createdAt: t.createdAt
      }))
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    res.status(500).json({
      message: 'Debug endpoint error',
      error: error.message
    });
  }
});

// @route   POST /api/admin/templates
// @desc    Create a new template
// @access  Private (Admin only)
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('🚀 TEMPLATE SAVE: POST endpoint hit by user:', req.user?.id);
    console.log('🚀 TEMPLATE SAVE: Timestamp:', new Date().toISOString());

    const { name, description, regions, originalImageData, originalImageName } = req.body;

    // Log request details WITHOUT the image data to avoid memory issues
    console.log('📋 TEMPLATE SAVE: Template name:', name);
    console.log('📋 TEMPLATE SAVE: Regions count:', regions?.length || 0);
    console.log('📋 TEMPLATE SAVE: Has image data:', !!originalImageData);
    console.log('📋 TEMPLATE SAVE: Image name:', originalImageName);
    if (originalImageData) {
      console.log('📋 TEMPLATE SAVE: Image size:', `${Math.round(originalImageData.length / 1024)}KB`);
    }

    // Validate required fields
    if (!name || !regions || regions.length === 0) {
      console.log('❌ TEMPLATE SAVE: Validation failed - Missing required fields');
      console.log('❌ TEMPLATE SAVE: Name provided:', !!name);
      console.log('❌ TEMPLATE SAVE: Regions count:', regions?.length || 0);
      return res.status(400).json({
        message: 'Template name and at least one region are required'
      });
    }

    // Check image data size and warn if too large
    if (originalImageData) {
      const imageSizeKB = Math.round(originalImageData.length / 1024);
      const imageSizeMB = Math.round(imageSizeKB / 1024);

      console.log(`📊 TEMPLATE SAVE: Image size check - ${imageSizeKB}KB (${imageSizeMB}MB)`);

      if (imageSizeMB > 15) {
        console.log('❌ TEMPLATE SAVE: Image too large for MongoDB (>15MB)');
        console.log('❌ TEMPLATE SAVE: MongoDB has 16MB document limit');
        return res.status(413).json({
          message: 'Image data is too large. MongoDB documents have a 16MB limit. Please use a smaller image or compress it.',
          imageSize: `${imageSizeMB}MB`,
          maxSize: '15MB'
        });
      }

      if (imageSizeMB > 10) {
        console.log('⚠️ TEMPLATE SAVE: Large image detected, may cause performance issues');
      }
    }

    // Check if template name already exists (only among active templates)
    const existingTemplate = await Template.findOne({ name, isActive: true });
    if (existingTemplate) {
      return res.status(400).json({
        message: 'Template with this name already exists'
      });
    }

    // Validate regions
    const validatedRegions = regions.map((region, index) => {
      if (!region.fieldType || typeof region.x !== 'number' || typeof region.y !== 'number' ||
          typeof region.width !== 'number' || typeof region.height !== 'number') {
        throw new Error(`Invalid region data at index ${index}`);
      }

      return {
        fieldType: region.fieldType,
        x: Math.round(region.x),
        y: Math.round(region.y),
        width: Math.round(region.width),
        height: Math.round(region.height),
        validation: getValidationRules(region.fieldType)
      };
    });

    console.log('💾 TEMPLATE SAVE: Creating template document...');
    console.log('💾 TEMPLATE SAVE: Regions to save:', validatedRegions.length);

    const template = new Template({
      name,
      description: description || '',
      regions: validatedRegions,
      originalImageData: originalImageData || null,
      originalImageName: originalImageName || null,
      createdBy: req.user.id,
      isActive: true
    });

    console.log('💾 TEMPLATE SAVE: Starting database save operation...');

    const startTime = Date.now();
    await template.save();
    const saveTime = Date.now() - startTime;

    console.log(`✅ TEMPLATE SAVE: Successfully saved in ${saveTime}ms`);
    console.log(`✅ TEMPLATE SAVE: Template ID: ${template._id}`);
    console.log(`✅ TEMPLATE SAVE: Template name: ${template.name}`);

    res.status(201).json({
      message: 'Template created successfully',
      template: {
        id: template._id,
        name: template.name,
        description: template.description,
        regionsCount: template.regions.length,
        createdAt: template.createdAt
      }
    });

  } catch (error) {
    console.error('❌ TEMPLATE SAVE: Error occurred!');
    console.error('❌ TEMPLATE SAVE: Error message:', error.message);
    console.error('❌ TEMPLATE SAVE: Error name:', error.name);
    console.error('❌ TEMPLATE SAVE: Error code:', error.code);
    console.error('❌ TEMPLATE SAVE: Template name:', req.body?.name);
    console.error('❌ TEMPLATE SAVE: User ID:', req.user?.id);

    // Log stack trace for debugging
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ TEMPLATE SAVE: Error stack:', error.stack);
    }

    // Check for specific MongoDB errors
    if (error.name === 'ValidationError') {
      console.error('❌ MongoDB validation error:', error.errors);
      return res.status(400).json({
        message: 'Template validation failed',
        error: error.message,
        details: error.errors
      });
    }

    if (error.code === 11000) {
      console.error('❌ Duplicate key error');
      return res.status(400).json({
        message: 'Template with this name already exists',
        error: 'Duplicate template name'
      });
    }

    // Check for MongoDB document size errors
    if (error.message && error.message.includes('BSONObj size')) {
      console.error('❌ MongoDB document size error - image too large');
      return res.status(413).json({
        message: 'Template data is too large for MongoDB. Please use a smaller image.',
        error: 'Document size exceeds MongoDB limit',
        suggestion: 'Try compressing the image or saving without image data'
      });
    }

    // Check for network/timeout errors
    if (error.name === 'MongoNetworkError' || error.name === 'MongoTimeoutError') {
      console.error('❌ MongoDB network/timeout error');
      return res.status(503).json({
        message: 'Database connection issue. Please try again.',
        error: 'Database temporarily unavailable'
      });
    }

    // Generic server error
    console.error('❌ Generic server error during template creation');
    res.status(500).json({
      message: 'Server error while creating template',
      error: error.message,
      type: error.name,
      suggestion: 'Try saving without image data if the image is very large'
    });
  }
});

// @route   GET /api/admin/templates
// @desc    Get all templates
// @access  Private (Admin only)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const templates = await Template.find({ isActive: true })
      .select('name description regions originalImageData createdAt updatedAt')
      .sort({ createdAt: -1 });

    const templatesWithStats = templates.map(template => ({
      id: template._id,
      name: template.name,
      description: template.description,
      regionsCount: template.regions.length,
      fieldTypes: [...new Set(template.regions.map(r => r.fieldType))],
      hasImage: !!template.originalImageData,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    }));

    res.json({
      templates: templatesWithStats,
      total: templatesWithStats.length
    });

  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({
      message: 'Server error while fetching templates'
    });
  }
});

// @route   GET /api/admin/templates/:id
// @desc    Get a specific template
// @access  Private (Admin only)
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);

    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    res.json(template);

  } catch (error) {
    console.error('Error fetching template:', error);
    res.status(500).json({
      message: 'Server error while fetching template'
    });
  }
});

// @route   PUT /api/admin/templates/:id
// @desc    Update a template
// @access  Private (Admin only)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { name, description, regions } = req.body;

    const template = await Template.findById(req.params.id);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Validate regions if provided
    if (regions) {
      const validatedRegions = regions.map((region, index) => {
        if (!region.fieldType || typeof region.x !== 'number' || typeof region.y !== 'number' ||
            typeof region.width !== 'number' || typeof region.height !== 'number') {
          throw new Error(`Invalid region data at index ${index}`);
        }

        return {
          fieldType: region.fieldType,
          x: Math.round(region.x),
          y: Math.round(region.y),
          width: Math.round(region.width),
          height: Math.round(region.height),
          validation: getValidationRules(region.fieldType)
        };
      });
      template.regions = validatedRegions;
    }

    if (name) template.name = name;
    if (description !== undefined) template.description = description;
    template.updatedAt = new Date();

    await template.save();

    res.json({
      message: 'Template updated successfully',
      template: {
        id: template._id,
        name: template.name,
        description: template.description,
        regionsCount: template.regions.length,
        updatedAt: template.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating template:', error);
    res.status(500).json({
      message: 'Server error while updating template',
      error: error.message
    });
  }
});

// @route   DELETE /api/admin/templates/:id
// @desc    Delete a template (permanent delete to avoid duplicate name issues)
// @access  Private (Admin only)
router.delete('/:id', authenticateToken, async (req, res) => {
  // FORCE IMMEDIATE LOGGING TO BOTH CONSOLE AND FILE
  const fs = require('fs');
  const path = require('path');
  const logMessage = `🚨🚨🚨 DELETE ROUTE HIT: ${new Date().toISOString()} - ID: ${req.params.id}`;

  console.log(logMessage);
  console.error(logMessage);
  process.stdout.write(logMessage + '\n');
  process.stderr.write(logMessage + '\n');

  // Force write to log file
  try {
    const logFile = path.join(__dirname, '../logs', `delete-debug-${new Date().toISOString().split('T')[0]}.log`);
    fs.appendFileSync(logFile, logMessage + '\n');
  } catch (err) {
    console.error('Failed to write delete log:', err);
  }
  try {
    console.log('🚨🚨🚨 TEMPLATE DELETE ENDPOINT HIT 🚨🚨🚨');
    console.log('🔍 Template ID:', req.params.id);
    console.log('🔍 User ID:', req.user?.id);
    console.log('🔍 Request headers:', JSON.stringify(req.headers, null, 2));

    const template = await Template.findById(req.params.id);
    if (!template) {
      console.log('❌ Template not found for ID:', req.params.id);
      return res.status(404).json({ message: 'Template not found' });
    }

    console.log('🗑️ Permanently deleting template:', template.name);
    console.log('🗑️ Template details:', {
      id: template._id,
      name: template.name,
      isActive: template.isActive,
      createdAt: template.createdAt
    });

    // Permanent deletion to avoid duplicate name issues
    const deleteResult = await Template.findByIdAndDelete(req.params.id);
    console.log('🗑️ Delete result:', deleteResult ? 'SUCCESS' : 'FAILED');

    console.log('✅ Template permanently deleted successfully');
    res.json({ message: 'Template permanently deleted successfully' });

  } catch (error) {
    console.error('❌ Error deleting template:', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({
      message: 'Server error while deleting template',
      error: error.message
    });
  }
});

// @route   DELETE /api/admin/templates/:id/permanent
// @desc    Permanently delete a template (hard delete)
// @access  Private (Admin only)
router.delete('/:id/permanent', authenticateToken, async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    await Template.findByIdAndDelete(req.params.id);

    res.json({ message: 'Template permanently deleted successfully' });

  } catch (error) {
    console.error('Error permanently deleting template:', error);
    res.status(500).json({
      message: 'Server error while permanently deleting template'
    });
  }
});

// @route   POST /api/admin/templates/:id/test
// @desc    Test a template with a scorecard image
// @access  Private (Admin only)
router.post('/:id/test', authenticateToken, upload.single('scorecard'), async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    if (!req.file) {
      return res.status(400).json({ message: 'Scorecard image is required' });
    }

    // Here we'll implement template-based extraction
    const extractionResult = await testTemplateExtraction(template, req.file.path);

    res.json({
      message: 'Template test completed',
      extractionResult,
      template: {
        id: template._id,
        name: template.name,
        regionsCount: template.regions.length
      }
    });

  } catch (error) {
    console.error('Error testing template:', error);
    res.status(500).json({
      message: 'Server error while testing template',
      error: error.message
    });
  }
});

// Helper function to get validation rules for field types
function getValidationRules(fieldType) {
  const validationRules = {
    // Match Information
    match_venue: { pattern: '^[A-Z0-9\\s,.-]+$', maxLength: 100, required: true },
    match_time: { pattern: '^[A-Z0-9\\s:.-]+$', maxLength: 50, required: false },
    player_of_match: { pattern: '^[A-Z\\s]+$', maxLength: 50, required: false },

    // Team Names
    team1_name: { pattern: '^[A-Z0-9\\s]+$', maxLength: 30, required: true },
    team2_name: { pattern: '^[A-Z0-9\\s]+$', maxLength: 30, required: true },

    // Team Scores
    team1_runs_scored: { pattern: '^\\d{1,3}$', min: 0, max: 500, required: true },
    team2_runs_scored: { pattern: '^\\d{1,3}$', min: 0, max: 500, required: true },

    // Team Overs
    team1_overs_played: { pattern: '^\\d{1,2}(\\.\\d{1})?$', min: 0, max: 50, required: true },
    team2_overs_played: { pattern: '^\\d{1,2}(\\.\\d{1})?$', min: 0, max: 50, required: true },

    // Team Wickets
    team1_wickets_lost: { pattern: '^\\d{1,2}$', min: 0, max: 10, required: true },
    team2_wickets_lost: { pattern: '^\\d{1,2}$', min: 0, max: 10, required: true },

    // Team 1 Batsmen
    team1_batsman_name: { pattern: '^[A-Z\\s]+$', maxLength: 50, required: true },
    team1_batsman_runs_scored: { pattern: '^\\d{1,3}\\*?$', min: 0, max: 300, required: true },
    team1_batsman_balls_faced: { pattern: '^\\(\\d{1,3}\\)$', min: 0, max: 300, required: false },

    // Team 1 Bowlers
    team1_bowler_name: { pattern: '^[A-Z\\s]+$', maxLength: 50, required: true },
    team1_bowler_figure: { pattern: '^\\d{1,2}-\\d{1,3}$', maxLength: 10, required: false },
    team1_bowler_wickets_taken: { pattern: '^\\d{1,2}$', min: 0, max: 10, required: true },
    team1_bowler_runs_conceded: { pattern: '^\\d{1,3}$', min: 0, max: 200, required: true },

    // Team 2 Batsmen
    team2_batsman_name: { pattern: '^[A-Z\\s]+$', maxLength: 50, required: true },
    team2_batsman_runs_scored: { pattern: '^\\d{1,3}\\*?$', min: 0, max: 300, required: true },
    team2_batsman_balls_faced: { pattern: '^\\(\\d{1,3}\\)$', min: 0, max: 300, required: false },

    // Team 2 Bowlers
    team2_bowler_name: { pattern: '^[A-Z\\s]+$', maxLength: 50, required: true },
    team2_bowler_figure: { pattern: '^\\d{1,2}-\\d{1,3}$', maxLength: 10, required: false },
    team2_bowler_wickets_taken: { pattern: '^\\d{1,2}$', min: 0, max: 10, required: true },
    team2_bowler_runs_conceded: { pattern: '^\\d{1,3}$', min: 0, max: 200, required: true }
  };

  return validationRules[fieldType] || { required: false };
}

// Template-based extraction function
async function testTemplateExtraction(template, imagePath) {
  const { spawn } = require('child_process');
  const path = require('path');

  return new Promise((resolve, reject) => {
    console.log(`Testing template "${template.name}" with ${template.regions.length} regions`);

    // Prepare template data for Python script
    const templateData = {
      name: template.name,
      regions: template.regions.map(region => ({
        fieldType: region.fieldType,
        x: region.x,
        y: region.y,
        width: region.width,
        height: region.height
      }))
    };

    // Use the working simple OCR script instead of the problematic template parser
    const scriptPath = path.join(__dirname, '../scripts/simple_ocr.py');
    const pythonPath = process.env.PYTHON_PATH || 'python';

    console.log(`Running simple OCR extraction: ${pythonPath} ${scriptPath}`);

    const pythonProcess = spawn(pythonPath, [
      scriptPath,
      '--image', imagePath,
      '--format', 'json',
      '--template', JSON.stringify(templateData)
    ]);

    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    pythonProcess.on('close', (code) => {
      console.log(`Template extraction completed with code: ${code}`);

      if (code === 0) {
        try {
          // Parse the JSON output
          const jsonStartMarker = '===TEMPLATE_EXTRACTION_START===';
          const jsonEndMarker = '===TEMPLATE_EXTRACTION_END===';

          if (stdout.includes(jsonStartMarker) && stdout.includes(jsonEndMarker)) {
            const startIndex = stdout.indexOf(jsonStartMarker) + jsonStartMarker.length;
            const endIndex = stdout.indexOf(jsonEndMarker);
            const jsonString = stdout.substring(startIndex, endIndex).trim();

            const extractionResult = JSON.parse(jsonString);

            resolve({
              success: true,
              message: `Template extraction completed successfully`,
              template: {
                name: template.name,
                regionsCount: template.regions.length
              },
              extractedData: extractionResult,
              processingTime: extractionResult.processingTime || 'Unknown'
            });
          } else {
            // Fallback: try to parse entire stdout as JSON
            const result = JSON.parse(stdout);
            resolve({
              success: true,
              message: 'Template extraction completed',
              template: {
                name: template.name,
                regionsCount: template.regions.length
              },
              extractedData: result
            });
          }
        } catch (parseError) {
          console.error('❌ Failed to parse template extraction result:', parseError);
          resolve({
            success: false,
            message: 'Template extraction completed but failed to parse results',
            error: parseError.message,
            rawOutput: stdout,
            stderr: stderr
          });
        }
      } else {
        console.error('Template extraction failed with exit code:', code);
        console.error('STDERR:', stderr);
        console.error('STDOUT:', stdout);
        resolve({
          success: false,
          message: 'Template extraction failed',
          error: stderr || stdout || `Process exited with code ${code}`,
          exitCode: code,
          stdout: stdout,
          stderr: stderr
        });
      }
    });

    pythonProcess.on('error', (error) => {
      console.error('Failed to start template extraction process:', error);
      reject({
        success: false,
        message: 'Failed to start template extraction process',
        error: error.message
      });
    });

    // Set timeout
    setTimeout(() => {
      pythonProcess.kill();
      reject({
        success: false,
        message: 'Template extraction timeout',
        error: 'Process timed out after 60 seconds'
      });
    }, 60000); // 60 second timeout
  });
}

module.exports = router;
