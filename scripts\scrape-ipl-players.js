const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');
const https = require('https');

// Create directories if they don't exist
const imagesDir = path.join(__dirname, '../client/public/uploads/players/ipl');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to download image
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        const file = fs.createWriteStream(filepath);
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`Downloaded: ${filepath}`);
          resolve(filepath);
        });
      } else {
        reject(`Failed to download ${url}: ${response.statusCode}`);
      }
    }).on('error', (err) => {
      reject(`Error downloading ${url}: ${err.message}`);
    });
  });
};

// Function to sanitize filename
const sanitizeFilename = (name) => {
  return name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
};

// Main scraping function
async function scrapeIPLPlayers(teamUrl) {
  try {
    console.log(`Scraping players from: ${teamUrl}`);
    
    // Get the HTML content
    const response = await axios.get(teamUrl);
    const $ = cheerio.load(response.data);
    
    const players = [];
    
    // Find all player cards
    $('.ih-td-tab').each((index, element) => {
      const playerCard = $(element);
      
      // Extract player name
      const playerName = playerCard.find('.ih-pt-ic').text().trim();
      
      // Extract player role
      const playerRole = playerCard.find('.ih-pt-role').text().trim();
      
      // Extract player image URL
      const playerImageUrl = playerCard.find('.ih-pt-image img').attr('src');
      
      if (playerName && playerImageUrl) {
        players.push({
          name: playerName,
          type: playerRole,
          imageUrl: playerImageUrl
        });
      }
    });
    
    // Download images and create player data
    const playerData = [];
    
    for (const player of players) {
      const filename = `${sanitizeFilename(player.name)}.jpg`;
      const filepath = path.join(imagesDir, filename);
      
      try {
        await downloadImage(player.imageUrl, filepath);
        
        playerData.push({
          name: player.name,
          type: player.type,
          image: `/uploads/players/ipl/${filename}`,
          nationality: 'India', // Default, can be updated later
          battingHand: 'Right Handed', // Default, can be updated later
          bowlingHand: 'Right Arm Medium', // Default, can be updated later
          ratings: {
            overall: Math.floor(Math.random() * 20) + 70 // Random rating between 70-90
          },
          stats: {
            battingAverage: (Math.random() * 50 + 10).toFixed(2),
            strikeRate: (Math.random() * 150 + 100).toFixed(2),
            wickets: Math.floor(Math.random() * 100),
            economy: (Math.random() * 8 + 4).toFixed(2),
            highScore: Math.floor(Math.random() * 150)
          }
        });
      } catch (error) {
        console.error(`Error processing ${player.name}: ${error}`);
      }
    }
    
    // Save player data to JSON file
    const dataFilePath = path.join(__dirname, '../client/public/data/ipl-players.json');
    
    // Create directory if it doesn't exist
    const dataDir = path.dirname(dataFilePath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(dataFilePath, JSON.stringify(playerData, null, 2));
    console.log(`Saved player data to: ${dataFilePath}`);
    console.log(`Total players scraped: ${playerData.length}`);
    
    return playerData;
  } catch (error) {
    console.error(`Scraping error: ${error}`);
    return [];
  }
}

// URL of the team to scrape
const teamUrl = 'https://www.iplt20.com/teams/delhi-capitals/squad/2025';

// Run the scraper
scrapeIPLPlayers(teamUrl)
  .then(players => {
    console.log('Scraping completed successfully!');
  })
  .catch(error => {
    console.error('Scraping failed:', error);
  });
