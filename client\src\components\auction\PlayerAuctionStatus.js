import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Divider,
  IconButton,
  TextField,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Tooltip
} from '@mui/material';
import {
  Gavel as GavelIcon,
  AccessTime as AccessTimeIcon,
  ArrowUpward as ArrowUpwardIcon,
  Person as PersonIcon,
  EmojiEvents as TrophyIcon,
  MonetizationOn as MoneyIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { format } from 'date-fns';

// Bid button with animation
const AnimatedBidButton = ({ onClick, disabled, amount, isHighestBidder, auction }) => {
  return (
    <motion.div
      whileHover={{ scale: disabled ? 1 : 1.03 }}
      whileTap={{ scale: disabled ? 1 : 0.97 }}
    >
      <Button
        variant="contained"
        color="primary"
        size="large"
        startIcon={<GavelIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem', md: '1.5rem' } }} />}
        onClick={onClick}
        disabled={disabled}
        fullWidth
        sx={{
          py: { xs: 1, sm: 1.25, md: 1.5 },
          fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {isHighestBidder && auction?.currentBid
          ? 'You are the highest bidder'
          : auction?.currentBid
          ? `Bid ${amount.toLocaleString()} Credits`
          : 'No Bids Yet'}

        {!disabled && !isHighestBidder && (
          <motion.div
            initial={{ opacity: 0, x: -100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, repeat: Infinity, repeatType: 'reverse' }}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%)',
              pointerEvents: 'none'
            }}
          />
        )}
      </Button>
    </motion.div>
  );
};

// Custom bid input
const CustomBidInput = ({ value, onChange, minimumBid, availableBudget, onSubmit }) => {
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const newValue = parseInt(e.target.value) || 0;
    onChange(newValue);

    if (newValue < minimumBid) {
      setError(`Minimum bid is ${minimumBid.toLocaleString()} Credits`);
    } else if (newValue > availableBudget) {
      setError(`Exceeds your available budget of ${availableBudget.toLocaleString()} Credits`);
    } else {
      setError('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !error) {
      onSubmit();
    }
  };

  return (
    <Box sx={{ mt: { xs: 1, sm: 1.5, md: 2 } }}>
      <TextField
        fullWidth
        label="Custom Bid Amount"
        type="number"
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        error={!!error}
        helperText={error || `Minimum: ${minimumBid.toLocaleString()} Credits`}
        size="small"
        sx={{
          '& .MuiInputLabel-root': {
            fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' }
          },
          '& .MuiInputBase-input': {
            fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
            py: { xs: 0.75, sm: 1 }
          },
          '& .MuiFormHelperText-root': {
            fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' },
            mt: 0.5
          }
        }}
        InputProps={{
          startAdornment: <InputAdornment position="start">$</InputAdornment>,
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                color="primary"
                onClick={onSubmit}
                disabled={!!error}
                size="small"
                sx={{ p: { xs: 0.5, sm: 0.75 } }}
              >
                <GavelIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};

// Timer component with animation
const AuctionTimer = ({ timeLeft, endTime }) => {
  const theme = useTheme();
  const isUnder30Seconds = timeLeft && timeLeft.includes(':') &&
    parseInt(timeLeft.split(':')[0]) === 0 &&
    parseInt(timeLeft.split(':')[1]) === 0 &&
    parseInt(timeLeft.split(':')[2]) < 30;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 1, md: 1.5 } }}>
      <AccessTimeIcon sx={{
        mr: 1,
        fontSize: { xs: '1.2rem', sm: '1.3rem', md: '1.4rem' },
        color: isUnder30Seconds ? 'error.main' : 'primary.main',
        animation: isUnder30Seconds ? 'pulse 1s infinite' : 'none',
        '@keyframes pulse': {
          '0%': { opacity: 1 },
          '50%': { opacity: 0.5 },
          '100%': { opacity: 1 },
        }
      }} />

      <Box>
        <Typography
          variant="h6"
          color={isUnder30Seconds ? 'error.main' : 'primary.main'}
          sx={{
            fontFamily: 'monospace',
            fontWeight: 'bold',
            fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' },
            animation: isUnder30Seconds ? 'pulse 1s infinite' : 'none',
          }}
        >
          {timeLeft || 'Calculating...'}
        </Typography>

        <Typography variant="caption" color="text.secondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' } }}>
          Ends at {format(new Date(endTime), 'MMM dd, yyyy HH:mm')}
        </Typography>
      </Box>
    </Box>
  );
};

// Main player auction status component
const PlayerAuctionStatus = ({
  auction,
  timeLeft,
  onBid,
  currentUser,
  availableBudget
}) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const isHighestBidder = auction?.currentBidder?._id === currentUser?.id;
  const [customBidAmount, setCustomBidAmount] = useState(
    auction ? auction.currentBid + auction.minimumBidIncrement : 0
  );
  const [showCustomBid, setShowCustomBid] = useState(false);

  // Update custom bid amount when auction changes
  useEffect(() => {
    if (auction) {
      setCustomBidAmount(auction.currentBid + auction.minimumBidIncrement);
    }
  }, [auction]);

  if (!auction) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6">No active auction selected</Typography>
      </Paper>
    );
  }

  const minimumBid = auction.currentBid + auction.minimumBidIncrement;

  // Quick bid options
  const quickBidOptions = [
    minimumBid,
    minimumBid + auction.minimumBidIncrement,
    minimumBid + (auction.minimumBidIncrement * 2)
  ];

  return (
    <Paper
      elevation={3}
      sx={{
        p: { xs: 1, sm: 1.5, md: 2 },
        mb: { xs: 1, sm: 1.5, md: 2 },
        borderLeft: isHighestBidder ? `4px solid ${theme.palette.success.main}` : `4px solid ${theme.palette.primary.main}`,
        position: 'relative',
        overflow: 'hidden',
        maxHeight: { xs: 'calc(100vh - 200px)', sm: 'none' },
        overflowY: { xs: 'auto', sm: 'visible' }
      }}
    >
      {isHighestBidder && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            bgcolor: 'success.main',
            color: 'white',
            py: { xs: 0.25, sm: 0.5 },
            px: { xs: 1.5, sm: 2 },
            transform: 'rotate(45deg) translate(20%, -50%)',
            transformOrigin: 'top right',
            boxShadow: 1,
            zIndex: 1
          }}
        >
          <Typography variant="caption" component="div" fontWeight="bold" sx={{ fontSize: { xs: '0.65rem', sm: '0.75rem' } }}>
            WINNING
          </Typography>
        </Box>
      )}

      <Grid container spacing={{ xs: 1, sm: 1.5, md: 2 }}>
        {/* Player image for mobile - shown at top on small screens */}
        <Grid item xs={12} display={{ xs: 'block', md: 'none' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              component="img"
              src={auction?.player?.image || '/uploads/players/default.png'}
              alt={auction?.player?.name}
              sx={{
                width: 60,
                height: 60,
                borderRadius: 1.5,
                objectFit: 'cover',
                mr: 1.5,
                boxShadow: 1
              }}
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = '/uploads/players/default.png';
              }}
            />
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 0.25, fontSize: { xs: '0.9rem', sm: '1rem' } }}>
                {auction?.player?.name}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                {auction?.player?.type} • {auction?.player?.nationality}
              </Typography>
            </Box>
          </Box>
          <Divider sx={{ my: 1 }} />
        </Grid>

        {/* Main content */}
        <Grid item xs={12} md={8}>
          <AuctionTimer timeLeft={timeLeft} endTime={auction.endTime} />

          {/* Player name - hidden on mobile as it's shown above */}
          <Box display={{ xs: 'none', md: 'block' }}>
            <Typography variant="h5" gutterBottom>
              {auction?.player?.name}
            </Typography>

            <Typography variant="subtitle1" color="text.secondary" gutterBottom>
              {auction?.player?.type} • {auction?.player?.nationality}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: { xs: 0, md: 1 } }}>
            <GavelIcon sx={{ mr: 0.75, fontSize: { xs: '1rem', sm: '1.25rem', md: '1.5rem' } }} />
            <Typography variant={{ xs: 'subtitle1', sm: 'h6' }} sx={{ fontSize: { xs: '0.9rem', sm: '1.1rem', md: '1.25rem' } }}>
              Current Bid: {auction?.currentBid?.toLocaleString()} $
            </Typography>
          </Box>

          {auction?.currentBidder && (
            <Box sx={{ mt: 0.5, fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }, display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
              <Typography variant="body2" component="div" sx={{ mr: 0.5, fontSize: 'inherit' }}>
                Highest Bidder:
              </Typography>
              {isHighestBidder ? (
                <Chip
                  icon={<PersonIcon sx={{ fontSize: { xs: '0.8rem', sm: '1rem' } }} />}
                  label="You"
                  color="success"
                  variant="outlined"
                  size="small"
                  sx={{
                    height: { xs: 20, sm: 24 },
                    '& .MuiChip-label': { fontSize: { xs: '0.7rem', sm: '0.8rem' } }
                  }}
                />
              ) : (
                <Chip
                  avatar={<Avatar src={auction.currentBidder.profilePicture || '/default-avatar.png'} sx={{ width: { xs: 16, sm: 24 }, height: { xs: 16, sm: 24 } }} />}
                  label={auction.currentBidder.username}
                  variant="outlined"
                  size="small"
                  sx={{
                    height: { xs: 20, sm: 24 },
                    '& .MuiChip-label': { fontSize: { xs: '0.7rem', sm: '0.8rem' } }
                  }}
                />
              )}
            </Box>
          )}

          <Divider sx={{ my: { xs: 1, sm: 1.5, md: 2 } }} />

          {!isHighestBidder && (
            <>
              <Typography variant="subtitle2" gutterBottom sx={{ fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' } }}>
                Quick Bid Options:
              </Typography>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: { xs: 0.5, sm: 0.75, md: 1 }, mb: { xs: 1, sm: 1.5, md: 2 } }}>
                {quickBidOptions.map((amount, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    color="primary"
                    size="small"
                    onClick={() => onBid(auction, amount)}
                    disabled={amount > availableBudget}
                    startIcon={<MoneyIcon sx={{ fontSize: { xs: '0.8rem', sm: '1rem' } }} />}
                    sx={{
                      py: { xs: 0.25, sm: 0.5 },
                      px: { xs: 0.5, sm: 0.75 },
                      fontSize: { xs: '0.7rem', sm: '0.8rem' }
                    }}
                  >
                    {amount.toLocaleString()}
                  </Button>
                ))}

                <Button
                  variant="outlined"
                  color="secondary"
                  size="small"
                  onClick={() => setShowCustomBid(!showCustomBid)}
                  startIcon={showCustomBid ? <GavelIcon sx={{ fontSize: { xs: '0.8rem', sm: '1rem' } }} /> : <AddIcon sx={{ fontSize: { xs: '0.8rem', sm: '1rem' } }} />}
                  sx={{
                    py: { xs: 0.25, sm: 0.5 },
                    px: { xs: 0.5, sm: 0.75 },
                    fontSize: { xs: '0.7rem', sm: '0.8rem' }
                  }}
                >
                  {showCustomBid ? 'Hide' : 'Custom'}
                </Button>
              </Box>

              {showCustomBid && (
                <CustomBidInput
                  value={customBidAmount}
                  onChange={setCustomBidAmount}
                  minimumBid={minimumBid}
                  availableBudget={availableBudget}
                  onSubmit={() => onBid(auction, customBidAmount)}
                />
              )}
            </>
          )}

          <Box sx={{ mt: 2 }}>
            <AnimatedBidButton
              onClick={() => onBid(auction, minimumBid)}
              disabled={isHighestBidder || minimumBid > availableBudget}
              amount={minimumBid}
              isHighestBidder={isHighestBidder}
              auction={auction}
            />
          </Box>
        </Grid>

        {/* Player image and stats - hidden on mobile */}
        <Grid item md={4} display={{ xs: 'none', md: 'block' }}>
          <Box
            component="img"
            src={auction?.player?.image || '/uploads/players/default.png'}
            alt={auction?.player?.name}
            sx={{
              width: '100%',
              height: 'auto',
              maxHeight: { md: 160, lg: 180 },
              objectFit: 'contain',
              borderRadius: 2,
              boxShadow: 2
            }}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = '/uploads/players/default.png';
            }}
          />

          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Player Stats
            </Typography>

            <Grid container spacing={1}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Overall Rating
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {auction?.player?.ratings?.overall || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  Position
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {auction?.player?.type || 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PlayerAuctionStatus;
