import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alogActions,
  <PERSON>ton,
  Box,
  Typography,
  Stepper,
  Step,
  Step<PERSON>abel,
  IconButton,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  CloudUpload as UploadIcon,
  Edit as EditIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import ScorecardUploadStep from './ScorecardUploadStep';
import MatchFormStep from './MatchFormStep';

/**
 * Match Result Wizard - Fresh Implementation
 * 
 * This component handles the complete flow:
 * 1. Upload scorecard image
 * 2. Process with OCR
 * 3. Display extracted data in JSON format
 * 4. Auto-populate match form with validation highlighting
 * 5. Allow manual corrections
 * 6. Submit match result
 */
const MatchResultWizard = ({ open, onClose, tournament, onMatchAdded }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [ocrData, setOcrData] = useState(null);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [error, setError] = useState(null);

  const steps = ['Upload Scorecard', 'Review & Edit Match Details'];

  const handleReset = () => {
    setActiveStep(0);
    setOcrData(null);
    setUploadedImage(null);
    setError(null);
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const handleUploadSuccess = (result) => {
    console.log('Upload successful, OCR result:', result);
    
    if (result.ocrData) {
      // Always proceed to the form step, even if OCR failed
      setOcrData(result.ocrData);
      setUploadedImage(result.scorecard?.url);
      setActiveStep(1); // Move to form step
      
      // If OCR failed, keep the error message but still allow manual entry
      if (!result.ocrData.success) {
        // Don't clear the error, as we want to show the OCR failure message
        // but we still proceed to the form for manual entry
        console.log('OCR failed but proceeding to form for manual entry');
      } else {
        // OCR was successful, clear any errors
        setError(null);
      }
    } else {
      setError('OCR processing failed. Please try again or enter details manually.');
    }
  };

  const handleMatchSubmit = (matchData) => {
    console.log('Match submitted:', matchData);
    if (onMatchAdded) {
      onMatchAdded(matchData);
    }
    handleClose();
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <ScorecardUploadStep
            tournament={tournament}
            onUploadSuccess={handleUploadSuccess}
            onError={setError}
          />
        );
      case 1:
        return (
          <MatchFormStep
            tournament={tournament}
            ocrData={ocrData}
            uploadedImage={uploadedImage}
            onSubmit={handleMatchSubmit}
            onBack={() => setActiveStep(0)}
            onError={setError}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Add Match Result</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Stepper */}
        <Box sx={{ mb: 4 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label}>
                <StepLabel
                  icon={
                    index === 0 ? <UploadIcon /> : 
                    index === 1 ? <EditIcon /> : 
                    <SaveIcon />
                  }
                >
                  {label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Step Content */}
        <Box sx={{ minHeight: '400px' }}>
          {renderStepContent()}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} color="inherit">
          Cancel
        </Button>
        {activeStep > 0 && (
          <Button onClick={() => setActiveStep(activeStep - 1)} color="primary">
            Back
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default MatchResultWizard;
