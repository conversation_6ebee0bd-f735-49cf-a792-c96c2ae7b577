const mongoose = require('mongoose');
require('dotenv').config();

async function checkMatch() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    // Find the specific match
    const matchId = '68787b242a02cd1cf7ccc915';
    let match = null;
    
    for (const phase of tournament.phases) {
      const foundMatch = phase.matches.find(m => m._id.toString() === matchId);
      if (foundMatch) {
        match = foundMatch;
        break;
      }
    }
    
    if (!match) {
      console.log('Match not found');
      return;
    }
    
    console.log('🔍 Match found:');
    console.log('Match ID:', match._id);
    console.log('Status:', match.status);
    console.log('Home Team:', match.homeTeam);
    console.log('Away Team:', match.awayTeam);
    
    console.log('\n📊 Match Result:');
    console.log('Winner:', match.result?.winner);
    console.log('Home Team Score:', match.result?.homeTeamScore);
    console.log('Away Team Score:', match.result?.awayTeamScore);
    
    console.log('\n📸 Scorecard Images:');
    console.log('scorecardImages length:', match.result?.scorecardImages?.length || 0);
    console.log('scorecardImages:', JSON.stringify(match.result?.scorecardImages, null, 2));
    
    console.log('\n🔍 Other image fields:');
    console.log('match.scorecardImage:', match.scorecardImage ? 'Present' : 'Not present');
    console.log('match.ocrData:', match.ocrData ? 'Present' : 'Not present');
    
    if (match.result?.scorecardImages?.length > 0) {
      console.log('\n✅ SUCCESS: scorecardImages are preserved!');
    } else {
      console.log('\n❌ ISSUE: scorecardImages are missing!');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkMatch();
