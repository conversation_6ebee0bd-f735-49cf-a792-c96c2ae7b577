/**
 * Player Matching Test Script
 * 
 * Tests the Robust Player Name Matching feature independently
 * 
 * Usage: node test-player-matching.js [test-type]
 * Examples:
 *   node test-player-matching.js single    # Test single player matching
 *   node test-player-matching.js batch     # Test batch player matching
 *   node test-player-matching.js fuzzy     # Test fuzzy matching scenarios
 */

const PlayerMatchingService = require('./services/playerMatchingService');
const Player = require('./models/Player');
const connectDB = require('./config/db');
require('dotenv').config();

class PlayerMatchingTest {
  constructor() {
    this.matchingService = new PlayerMatchingService();
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  /**
   * Test single player matching with various scenarios
   */
  async testSinglePlayerMatching() {
    console.log('🔍 SINGLE PLAYER MATCHING TEST');
    console.log('==============================\n');

    // Test cases with different matching scenarios using actual database players
    const testCases = [
      {
        name: 'Exact Match Test',
        ocrName: 'CAMERON GREEN',
        expectedType: 'automatic'
      },
      {
        name: 'Partial Match Test',
        ocrName: 'C GREEN',
        expectedType: 'automatic'
      },
      {
        name: 'OCR Error Test',
        ocrName: 'CAM3RON GR33N',  // OCR misread 'E' as '3'
        expectedType: 'manual_verification'
      },
      {
        name: 'Case Insensitive Test',
        ocrName: 'tim david',
        expectedType: 'automatic'
      },
      {
        name: 'Initials Test',
        ocrName: 'S CURRAN',
        expectedType: 'automatic'
      },
      {
        name: 'No Match Test',
        ocrName: 'Unknown Player XYZ',
        expectedType: 'no_match'
      }
    ];

    for (const testCase of testCases) {
      await this.runSingleTest(testCase);
    }

    this.displayResults();
  }

  /**
   * Test batch player matching
   */
  async testBatchPlayerMatching() {
    console.log('📦 BATCH PLAYER MATCHING TEST');
    console.log('=============================\n');

    const ocrNames = [
      'CAMERON GREEN',
      'TIM DAV1D',      // OCR error
      'S CURRAN',
      'SURYA KUMAR YADAV',
      'Unknown Player',
      'ISHAN KISHAN',
      'MATHEESHA PATHIRANA'
    ];

    console.log(`Testing batch matching for ${ocrNames.length} players...\n`);

    try {
      const results = await this.matchingService.matchPlayers(ocrNames);
      
      console.log('📊 BATCH MATCHING RESULTS:');
      console.log(`Total Players: ${results.totalPlayers}`);
      console.log(`Automatic Matches: ${results.automaticMatches.length}`);
      console.log(`Manual Verification Required: ${results.manualVerificationRequired.length}`);
      console.log(`No Matches: ${results.noMatches.length}`);
      console.log(`Errors: ${results.errors.length}\n`);

      // Display automatic matches
      if (results.automaticMatches.length > 0) {
        console.log('✅ AUTOMATIC MATCHES:');
        results.automaticMatches.forEach((match, i) => {
          console.log(`  ${i + 1}. "${match.ocrName}" → ${match.player.name} (${match.similarity.toFixed(2)})`);
        });
        console.log('');
      }

      // Display manual verification cases
      if (results.manualVerificationRequired.length > 0) {
        console.log('⚠️  MANUAL VERIFICATION REQUIRED:');
        results.manualVerificationRequired.forEach((match, i) => {
          console.log(`  ${i + 1}. "${match.ocrName}" → Suggestions:`);
          match.suggestions.slice(0, 3).forEach((suggestion, j) => {
            console.log(`     ${j + 1}. ${suggestion.name} (${suggestion.similarity.toFixed(2)})`);
          });
        });
        console.log('');
      }

      // Display no matches
      if (results.noMatches.length > 0) {
        console.log('❌ NO MATCHES FOUND:');
        results.noMatches.forEach((match, i) => {
          console.log(`  ${i + 1}. "${match.ocrName}"`);
        });
        console.log('');
      }

      this.testResults.passed++;
      console.log('✅ Batch matching test completed successfully\n');

    } catch (error) {
      console.log('❌ Batch matching test failed:', error.message);
      this.testResults.failed++;
    }

    this.testResults.total++;
  }

  /**
   * Test fuzzy matching scenarios
   */
  async testFuzzyMatching() {
    console.log('🔀 FUZZY MATCHING TEST');
    console.log('======================\n');

    const fuzzyTestCases = [
      {
        name: 'OCR Character Confusion',
        tests: [
          { ocrName: 'CAM3RON GR33N', expected: 'CAMERON GREEN' },
          { ocrName: 'TIM DAV1D', expected: 'TIM DAVID' },
          { ocrName: 'SURYA KUMAR YAOAV', expected: 'SURYA KUMAR YADAV' }
        ]
      },
      {
        name: 'Name Order Variations',
        tests: [
          { ocrName: 'GREEN CAMERON', expected: 'CAMERON GREEN' },
          { ocrName: 'DAVID TIM', expected: 'TIM DAVID' }
        ]
      },
      {
        name: 'Partial Names',
        tests: [
          { ocrName: 'CAMERON', expected: 'CAMERON GREEN' },
          { ocrName: 'CURRAN', expected: 'SAM CURRAN' },
          { ocrName: 'PATHIRANA', expected: 'MATHEESHA PATHIRANA' }
        ]
      },
      {
        name: 'Extra Spaces/Characters',
        tests: [
          { ocrName: 'CAMERON  GREEN', expected: 'CAMERON GREEN' },
          { ocrName: 'TIM-DAVID', expected: 'TIM DAVID' },
          { ocrName: 'ISHAN.KISHAN', expected: 'ISHAN KISHAN' }
        ]
      }
    ];

    for (const category of fuzzyTestCases) {
      console.log(`📋 ${category.name}:`);
      
      for (const test of category.tests) {
        try {
          const result = await this.matchingService.matchPlayer(test.ocrName);
          
          if (result.matchType === 'automatic' && 
              result.player.name.toLowerCase().includes(test.expected.toLowerCase().split(' ')[0])) {
            console.log(`  ✅ "${test.ocrName}" → ${result.player.name} (${result.similarity.toFixed(2)})`);
          } else if (result.matchType === 'manual_verification') {
            const topSuggestion = result.suggestions[0];
            if (topSuggestion && topSuggestion.name.toLowerCase().includes(test.expected.toLowerCase().split(' ')[0])) {
              console.log(`  ⚠️  "${test.ocrName}" → ${topSuggestion.name} (${topSuggestion.similarity.toFixed(2)}) [Manual]`);
            } else {
              console.log(`  ❌ "${test.ocrName}" → No good match found`);
            }
          } else {
            console.log(`  ❌ "${test.ocrName}" → No match found`);
          }
          
        } catch (error) {
          console.log(`  ❌ "${test.ocrName}" → Error: ${error.message}`);
        }
      }
      console.log('');
    }
  }

  /**
   * Run a single test case
   */
  async runSingleTest(testCase) {
    console.log(`📋 ${testCase.name}:`);
    console.log(`   Testing: "${testCase.ocrName}"`);
    
    try {
      const result = await this.matchingService.matchPlayer(testCase.ocrName);
      
      console.log(`   Result: ${result.matchType}`);
      
      if (result.matchType === 'automatic') {
        console.log(`   Matched: ${result.player.name} (confidence: ${result.confidence.toFixed(2)})`);
      } else if (result.matchType === 'manual_verification') {
        console.log(`   Top suggestion: ${result.candidates[0]?.player?.name} (${result.candidates[0]?.score.toFixed(2)})`);
      }
      
      // Check if result matches expectation
      if (result.matchType === testCase.expectedType) {
        console.log('   ✅ Test PASSED\n');
        this.testResults.passed++;
      } else {
        console.log(`   ❌ Test FAILED (expected: ${testCase.expectedType})\n`);
        this.testResults.failed++;
      }
      
    } catch (error) {
      console.log(`   ❌ Test ERROR: ${error.message}\n`);
      this.testResults.failed++;
    }
    
    this.testResults.total++;
  }

  /**
   * Display test results summary
   */
  displayResults() {
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    console.log(`Tests Passed: ${this.testResults.passed}`);
    console.log(`Tests Failed: ${this.testResults.failed}`);
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%\n`);
    
    if (this.testResults.passed === this.testResults.total) {
      console.log('🎉 ALL TESTS PASSED! Player matching is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Review the matching logic or test expectations.');
    }
  }

  /**
   * Test player matching performance
   */
  async testPerformance() {
    console.log('⚡ PERFORMANCE TEST');
    console.log('==================\n');

    const testNames = [
      'CAMERON GREEN', 'TIM DAVID', 'SURYA KUMAR YADAV', 'ISHAN KISHAN', 'JASON BEHRENDORFF',
      'CHRIS JORDAN', 'DEVON CONWAY', 'DEEPAK HOODA', 'RAHUL TRIPATHI', 'RUTURAJ GAIKWAD'
    ];

    console.log(`Testing performance with ${testNames.length} players...`);
    
    const startTime = Date.now();
    
    try {
      const results = await this.matchingService.matchPlayers(testNames);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`\n⏱️  Performance Results:`);
      console.log(`   Total time: ${duration}ms`);
      console.log(`   Average per player: ${(duration / testNames.length).toFixed(1)}ms`);
      console.log(`   Successful matches: ${results.automaticMatches.length}/${testNames.length}`);
      
      if (duration < 5000) { // Less than 5 seconds
        console.log('   ✅ Performance: GOOD');
      } else {
        console.log('   ⚠️  Performance: SLOW (consider optimization)');
      }
      
    } catch (error) {
      console.log(`   ❌ Performance test failed: ${error.message}`);
    }
  }
}

// Main execution
async function main() {
  // Connect to database
  try {
    await connectDB();
    console.log('✅ Database connected successfully\n');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }

  const tester = new PlayerMatchingTest();
  const testType = process.argv[2] || 'single';
  
  console.log('🚀 PLAYER MATCHING TEST SUITE');
  console.log('==============================\n');
  
  switch (testType.toLowerCase()) {
    case 'single':
      await tester.testSinglePlayerMatching();
      break;
      
    case 'batch':
      await tester.testBatchPlayerMatching();
      break;
      
    case 'fuzzy':
      await tester.testFuzzyMatching();
      break;
      
    case 'performance':
      await tester.testPerformance();
      break;
      
    case 'all':
      await tester.testSinglePlayerMatching();
      await tester.testBatchPlayerMatching();
      await tester.testFuzzyMatching();
      await tester.testPerformance();
      break;
      
    default:
      console.log('❌ Invalid test type. Available options:');
      console.log('   single      - Test single player matching');
      console.log('   batch       - Test batch player matching');
      console.log('   fuzzy       - Test fuzzy matching scenarios');
      console.log('   performance - Test matching performance');
      console.log('   all         - Run all tests');
      break;
  }
}

// Handle errors and run
main().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});