/**
 * Simple Test Runner with File Output
 * 
 * Runs tests and saves results to files for easy viewing
 * 
 * Usage: node test-runner.js [test-type]
 */

const fs = require('fs');
const path = require('path');
const OCRService = require('./services/ocrService');
const PlayerMatchingService = require('./services/playerMatchingService');
const MatchOutcomeService = require('./services/matchOutcomeService');

class TestRunner {
  constructor() {
    this.ocrService = new OCRService();
    this.playerMatchingService = new PlayerMatchingService();
    this.matchOutcomeService = new MatchOutcomeService();
    this.outputFile = path.join(__dirname, 'test-results.txt');
    this.results = [];
  }

  log(message) {
    console.log(message);
    this.results.push(message);
  }

  async saveResults() {
    const timestamp = new Date().toISOString();
    const content = `Test Results - ${timestamp}\n${'='.repeat(50)}\n\n${this.results.join('\n')}\n`;
    
    try {
      fs.writeFileSync(this.outputFile, content);
      console.log(`\n📄 Results saved to: ${this.outputFile}`);
    } catch (error) {
      console.error('Failed to save results:', error.message);
    }
  }

  async testOCRBasic() {
    this.log('🧪 BASIC OCR TEST');
    this.log('=================');
    
    try {
      const imagePath = path.join(__dirname, 'uploads', 'scorecards', 'scorecard1.jpg');
      this.log(`Testing: ${path.basename(imagePath)}`);
      
      const result = await this.ocrService.processImageWithOCRSpace(imagePath);
      
      if (result.success) {
        this.log('✅ OCR extraction successful');
        this.log(`Team 1: "${result.team1}"`);
        this.log(`Team 2: "${result.team2}"`);
        this.log(`Team 1 Score: ${result.team1Score.runs}-${result.team1Score.wickets}`);
        this.log(`Team 2 Score: ${result.team2Score.runs}-${result.team2Score.wickets}`);
        this.log(`Batsmen found: ${result.team1Batsmen?.length || 0} + ${result.team2Batsmen?.length || 0}`);
        return result;
      } else {
        this.log('❌ OCR extraction failed: ' + result.error);
        return null;
      }
    } catch (error) {
      this.log('❌ OCR test error: ' + error.message);
      return null;
    }
  }

  async testPlayerMatching() {
    this.log('\n🔍 PLAYER MATCHING TEST');
    this.log('=======================');
    
    const testPlayers = [
      'Virat Kohli',
      'MS Dhoni',
      'V Kohl1',  // OCR error
      'Unknown Player'
    ];
    
    try {
      for (const playerName of testPlayers) {
        this.log(`\nTesting: "${playerName}"`);
        
        try {
          const result = await this.playerMatchingService.matchPlayer(playerName);
          
          switch (result.matchType) {
            case 'automatic':
              this.log(`✅ Automatic match: ${result.player.name} (${result.similarity.toFixed(2)})`);
              break;
            case 'manual_verification':
              this.log(`⚠️  Manual verification: ${result.suggestions[0]?.name} (${result.suggestions[0]?.similarity.toFixed(2)})`);
              break;
            case 'no_match':
              this.log(`❌ No match found`);
              break;
          }
        } catch (error) {
          this.log(`❌ Error: ${error.message}`);
        }
      }
      
      this.log('\n✅ Player matching test completed');
      return true;
    } catch (error) {
      this.log('❌ Player matching test failed: ' + error.message);
      return false;
    }
  }

  async testMatchOutcome(ocrData) {
    this.log('\n🏆 MATCH OUTCOME TEST');
    this.log('=====================');
    
    if (!ocrData) {
      // Use mock data if no OCR data available
      ocrData = {
        success: true,
        team1: 'Team A',
        team2: 'Team B',
        team1Score: { runs: 180, wickets: 6, overs: 20.0 },
        team2Score: { runs: 165, wickets: 10, overs: 19.2 },
        team1Batsmen: [
          { name: 'Player A1', runs: 45, balls: 32 },
          { name: 'Player A2', runs: 38, balls: 28 }
        ],
        team2Batsmen: [
          { name: 'Player B1', runs: 42, balls: 35 },
          { name: 'Player B2', runs: 35, balls: 28 }
        ]
      };
      this.log('Using mock data for testing');
    }
    
    try {
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(ocrData, 'test-tournament');
      
      this.log(`Winner: ${outcome.winner}`);
      this.log(`Margin: ${outcome.margin}`);
      this.log(`Team 1 Points: ${outcome.team1Points}`);
      this.log(`Team 2 Points: ${outcome.team2Points}`);
      
      if (outcome.team1Stats) {
        this.log(`Team 1 NRR: ${outcome.team1Stats.netRunRate?.toFixed(3) || 'N/A'}`);
      }
      if (outcome.team2Stats) {
        this.log(`Team 2 NRR: ${outcome.team2Stats.netRunRate?.toFixed(3) || 'N/A'}`);
      }
      
      this.log('\n✅ Match outcome test completed');
      return true;
    } catch (error) {
      this.log('❌ Match outcome test failed: ' + error.message);
      return false;
    }
  }

  async runAllTests() {
    this.log('🚀 COMPREHENSIVE FEATURE TESTING');
    this.log('=================================');
    
    let passedTests = 0;
    let totalTests = 3;
    
    // Test 1: OCR
    const ocrData = await this.testOCRBasic();
    if (ocrData) passedTests++;
    
    // Test 2: Player Matching
    const playerMatchingResult = await this.testPlayerMatching();
    if (playerMatchingResult) passedTests++;
    
    // Test 3: Match Outcome
    const matchOutcomeResult = await this.testMatchOutcome(ocrData);
    if (matchOutcomeResult) passedTests++;
    
    // Summary
    this.log('\n📊 FINAL SUMMARY');
    this.log('================');
    this.log(`Tests Passed: ${passedTests}/${totalTests}`);
    this.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
      this.log('\n🎉 ALL TESTS PASSED! Both features are working correctly.');
    } else {
      this.log('\n⚠️  Some tests failed. Check the details above.');
    }
    
    await this.saveResults();
  }

  async testQuick() {
    this.log('⚡ QUICK FEATURE TEST');
    this.log('====================');
    
    // Quick OCR test
    this.log('\n1. Testing OCR...');
    try {
      const imagePath = path.join(__dirname, 'uploads', 'scorecards', 'scorecard1.jpg');
      const result = await this.ocrService.processImageWithOCRSpace(imagePath);
      this.log(result.success ? '✅ OCR working' : '❌ OCR failed');
    } catch (error) {
      this.log('❌ OCR error: ' + error.message);
    }
    
    // Quick player matching test
    this.log('\n2. Testing Player Matching...');
    try {
      const result = await this.playerMatchingService.matchPlayer('Virat Kohli');
      this.log(result.matchType === 'automatic' ? '✅ Player matching working' : '⚠️  Player matching needs attention');
    } catch (error) {
      this.log('❌ Player matching error: ' + error.message);
    }
    
    // Quick match outcome test
    this.log('\n3. Testing Match Outcome...');
    try {
      const mockData = {
        team1: 'A', team2: 'B',
        team1Score: { runs: 180, wickets: 6, overs: 20 },
        team2Score: { runs: 165, wickets: 10, overs: 19.2 }
      };
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(mockData, 'test');
      this.log(outcome.winner ? '✅ Match outcome working' : '❌ Match outcome failed');
    } catch (error) {
      this.log('❌ Match outcome error: ' + error.message);
    }
    
    this.log('\n✅ Quick test completed');
    await this.saveResults();
  }
}

// Main execution
async function main() {
  const runner = new TestRunner();
  const testType = process.argv[2] || 'quick';
  
  switch (testType.toLowerCase()) {
    case 'all':
      await runner.runAllTests();
      break;
    case 'quick':
      await runner.testQuick();
      break;
    case 'ocr':
      await runner.testOCRBasic();
      await runner.saveResults();
      break;
    case 'player':
      await runner.testPlayerMatching();
      await runner.saveResults();
      break;
    case 'outcome':
      await runner.testMatchOutcome();
      await runner.saveResults();
      break;
    default:
      console.log('Available test types: all, quick, ocr, player, outcome');
      break;
  }
}

main().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});