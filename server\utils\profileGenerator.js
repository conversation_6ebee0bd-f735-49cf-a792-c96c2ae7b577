const https = require('https');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// IPL player ID mappings - Auto-generated 2025-05-05T15:18:17.158Z
// Format: 'player name': 'id' // team
const IPL_PLAYER_IDS = {
    'virat kohli': '164', // RCB
    'rohit sharma': '107', // MI
    'ms dhoni': '1', // CSK
    'jasprit bumrah': '1124', // MI
    'rishabh pant': '2972', // DC
    'kl rahul': '1125', // LSG
    'hardik pandya': '2740', // MI
    'ravindra jadeja': '9', // CSK
    'suryakumar yadav': '108', // MI
    'shikhar dhawan': '41', // PBKS
    'david warner': '170', // DC
    'jos buttler': '509', // RR
    'faf du plessis': '24', // RCB
    'kane williamson': '440', // GT
    'rashid khan': '2885', // GT
    'andre russell': '177', // KKR
    'nicholas pooran': '1703', // LSG
    'quinton de kock': '834', // LSG
    'mitchell starc': '964', // KKR
    'pat cummins': '488', // SRH
    'trent boult': '969', // RR
    'kagiso rabada': '1664', // PBKS
    'yuzvendra chahal': '111', // RR
    'kuldeep yadav': '261', // DC
    'ravichandran ashwin': '8', // RR
    'mohammed shami': '94', // GT
    'mohammed siraj': '3840', // RCB
    'arshdeep singh': '4698', // PBKS
    'avesh khan': '1561', // LSG
    'umran malik': '15154', // SRH
    'sanju samson': '258', // RR
    'ishan kishan': '2975', // MI
    'shreyas iyer': '1563', // KKR
    'shubman gill': '3761', // GT
    'ruturaj gaikwad': '5443', // CSK
    'yashasvi jaiswal': '13538', // RR
    'rinku singh': '3830', // KKR
    'tilak varma': '15154', // MI
    'abhishek sharma': '3760', // SRH
    'rahul tripathi': '3838', // SRH
    'prithvi shaw': '3764', // DC
    'devdutt padikkal': '5430', // RR
    'venkatesh iyer': '8540', // KKR
    'deepak chahar': '140', // CSK
    'shardul thakur': '1745', // DC
    'axar patel': '1113', // DC
    'washington sundar': '2973', // SRH
    'krunal pandya': '3183', // LSG
    'rahul tewatia': '1749', // GT
    'riyan parag': '8280', // RR
};

async function searchPlayerImage(playerName) {
    try {
        console.log('Searching for player image:', playerName);
        const normalizedName = playerName.toLowerCase();

        // If we have the player's ID, try that first
        if (IPL_PLAYER_IDS[normalizedName]) {
            const imageUrl = `https://documents.iplt20.com/ipl/IPLHeadshot2025/${IPL_PLAYER_IDS[normalizedName]}.png`;
            try {
                const response = await axios.head(imageUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.iplt20.com/',
                        'Origin': 'https://www.iplt20.com'
                    }
                });

                if (response.status === 200) {
                    console.log('Found player image via ID:', imageUrl);
                    return imageUrl;
                }
            } catch (error) {
                console.log('Image not found with known ID, trying fallback...');
            }
        }

        // Try sequential IDs as fallback (within a reasonable range)
        for (let id = 1; id <= 1000; id++) {
            const imageUrl = `https://documents.iplt20.com/ipl/IPLHeadshot2025/${id}.png`;
            try {
                const response = await axios.head(imageUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.iplt20.com/',
                        'Origin': 'https://www.iplt20.com'
                    }
                });

                // If we found an image, save the ID for future use
                if (response.status === 200) {
                    IPL_PLAYER_IDS[normalizedName] = id.toString();
                    console.log(`Found player image at ID ${id}, saving for future use`);
                    return imageUrl;
                }
            } catch (error) {
                continue; // Try next ID
            }
        }

        throw new Error(`No image found for ${playerName}`);
    } catch (error) {
        console.error('Error searching for player image:', error.message);
        throw error;
    }
}

async function downloadImage(url, filepath) {
    try {
        const response = await axios({
            url,
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://www.iplt20.com/',
                'Origin': 'https://www.iplt20.com',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            }
        });
        await fs.writeFile(filepath, response.data);
        return filepath;
    } catch (error) {
        console.error('Error downloading image:', error);
        throw error;
    }
}

async function generatePlayerProfile(playerName) {
    if (!playerName) {
        console.error('No player name provided');
        return '/uploads/players/default.png';
    }

    try {
        // Create unique filename
        const filename = `player-${uuidv4()}.png`;
        const tempPath = path.join(__dirname, '..', 'uploads', 'temp', filename);

        // Ensure temp directory exists
        await fs.mkdir(path.join(__dirname, '..', 'uploads', 'temp'), { recursive: true });

        let imageUrl;
        try {
            // Try to get player image
            imageUrl = await searchPlayerImage(playerName);
            console.log('Found player image URL:', imageUrl);

            // Download the image
            await downloadImage(imageUrl, tempPath);

            // Move to players directory
            const finalPath = path.join(__dirname, '..', 'uploads', 'players', filename);
            await fs.rename(tempPath, finalPath);

            // Clean up temp file if it still exists
            try {
                await fs.unlink(tempPath);
            } catch (error) {
                // Ignore if file doesn't exist
            }

            return `/uploads/players/${filename}`;
        } catch (searchError) {
            console.error('Failed to find player image:', searchError.message);
            return '/uploads/players/default.png';
        }
    } catch (error) {
        console.error('Error generating player profile:', error.message);
        return '/uploads/players/default.png';
    }
}

module.exports = {
    generatePlayerProfile,
    searchPlayerImage
};