import React from 'react';
import { Box, Typography, IconButton, Card, CardMedia, CardContent, CardActions, Chip, Tooltip } from '@mui/material';
import RemoveIcon from '@mui/icons-material/Remove';
import InfoIcon from '@mui/icons-material/Info';
import { styled } from '@mui/material/styles';

// Styled components for FIFA-inspired card
const PlayerCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s, box-shadow 0.2s',
  position: 'relative',
  overflow: 'hidden',
  borderRadius: '12px',
  background: 'linear-gradient(135deg, #e6c656 0%, #a17c32 100%)',
  boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 28px rgba(0, 0, 0, 0.4)',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-50%',
    width: '200%',
    height: '100%',
    background: 'linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0) 100%)',
    transform: 'rotate(30deg)',
    zIndex: 1,
    pointerEvents: 'none',
  }
}));

const PlayerImage = styled(CardMedia)(({ theme }) => ({
  height: 200,
  objectFit: 'contain',
  backgroundColor: 'rgba(0, 0, 0, 0.05)',
  position: 'relative',
  zIndex: 2
}));

const PlayerInfo = styled(CardContent)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(2),
  color: '#fff',
  textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)',
  position: 'relative',
  zIndex: 2
}));

const PlayerName = styled(Typography)(({ theme }) => ({
  fontWeight: 'bold',
  marginBottom: theme.spacing(1),
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis'
}));

const PlayerRating = styled(Box)(({ theme, rating }) => {
  // Get color based on rating
  const getRatingColor = (rating) => {
    if (rating >= 90) return '#00c853'; // Green for 90+
    if (rating >= 80) return '#ffab00'; // Amber for 80-89
    if (rating >= 70) return '#2196f3'; // Blue for 70-79
    if (rating >= 60) return '#e040fb'; // Purple for 60-69
    return '#ffffff'; // White for below 60
  };

  return {
    position: 'absolute',
    top: 10,
    left: 10,
    width: 40,
    height: 40,
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 'bold',
    fontSize: '1.2rem',
    color: getRatingColor(rating),
    border: '2px solid rgba(255, 255, 255, 0.2)',
    zIndex: 3
  };
});

const PlayerType = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 10,
  right: 10,
  padding: '4px 8px',
  borderRadius: '4px',
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  color: '#fff',
  fontWeight: 'bold',
  fontSize: '0.8rem',
  zIndex: 3
}));

const PlayerActions = styled(CardActions)(({ theme }) => ({
  justifyContent: 'space-between',
  padding: theme.spacing(1, 2),
  backgroundColor: 'rgba(0, 0, 0, 0.1)',
  position: 'relative',
  zIndex: 2
}));

const TeamRosterPlayerCard = ({ player, onRemove }) => {
  // Get player type abbreviation
  const getPlayerTypeAbbr = (type) => {
    switch (type?.toLowerCase()) {
      case 'batsman':
        return 'BAT';
      case 'bowler':
        return 'BWL';
      case 'batting allrounder':
      case 'bowling allrounder':
        return 'BAR';
      case 'all-rounder':
      case 'allrounder':
        return 'ARD';
      case 'wicket keeper':
        return 'WKT';
      default:
        return type || 'BAT';
    }
  };

  // Check if player was won in an auction
  const wasWonInAuction = player.auctionWin && player.auctionWin.auctionId;

  return (
    <PlayerCard>
      <PlayerRating rating={player.ratings?.overall || 70}>
        {player.ratings?.overall || 70}
      </PlayerRating>

      <PlayerType>
        {getPlayerTypeAbbr(player.type)}
      </PlayerType>

      {/* Won in Auction Tag */}
      {wasWonInAuction && (
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 10,
          }}
        >
          <Chip
            label="Won"
            color="success"
            size="small"
            sx={{
              fontWeight: 'bold',
              backgroundColor: '#4caf50',
              color: 'white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            }}
          />
        </Box>
      )}

      <PlayerImage
        component="img"
        image={player.image || '/uploads/players/default.png'}
        alt={player.name}
        onError={(e) => {
          e.target.onerror = null;
          e.target.src = '/uploads/players/default.png';
        }}
      />

      <PlayerInfo>
        <PlayerName variant="h6" gutterBottom>
          {player.name}
        </PlayerName>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
          <Chip
            label={player.nationality || 'Unknown'}
            size="small"
            sx={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              color: '#fff',
              fontWeight: 'bold'
            }}
          />

          {/* Winning Bid Amount */}
          {wasWonInAuction && (
            <Chip
              label={`${player.auctionWin.amount.toLocaleString()} $`}
              size="small"
              sx={{
                backgroundColor: 'rgba(76, 175, 80, 0.8)',
                color: '#fff',
                fontWeight: 'bold'
              }}
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
          <Box>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>Batting</Typography>
            <Typography variant="body2" fontWeight="bold">
              {player.battingHand || 'Right'}
            </Typography>
          </Box>

          <Box>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>Bowling</Typography>
            <Typography variant="body2" fontWeight="bold">
              {player.bowlingHand !== 'None' ? player.bowlingHand : 'N/A'}
            </Typography>
          </Box>
        </Box>
      </PlayerInfo>

      <PlayerActions>
        <Tooltip title="View Player Details">
          <IconButton
            size="small"
            sx={{
              color: '#fff',
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { backgroundColor: 'rgba(33, 150, 243, 0.8)' }
            }}
          >
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Remove from Team">
          <IconButton
            size="small"
            onClick={() => onRemove(player)}
            sx={{
              color: '#fff',
              backgroundColor: 'rgba(0, 0, 0, 0.3)',
              '&:hover': { backgroundColor: 'rgba(244, 67, 54, 0.8)' }
            }}
          >
            <RemoveIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </PlayerActions>
    </PlayerCard>
  );
};

export default TeamRosterPlayerCard;
