import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './AdminMatchManagement.css';

const AdminMatchManagement = () => {
  const [tournaments, setTournaments] = useState([]);
  const [selectedTournament, setSelectedTournament] = useState('');
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingMatch, setEditingMatch] = useState(null);
  const [teams, setTeams] = useState([]);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');

  useEffect(() => {
    fetchTournaments();
    fetchTeams();
  }, []);

  const fetchTournaments = async () => {
    try {
      const response = await axios.get('/api/tournaments');
      setTournaments(response.data.data || []);
    } catch (error) {
      console.error('Error fetching tournaments:', error);
      showMessage('Error fetching tournaments', 'error');
    }
  };

  const fetchTeams = async () => {
    try {
      const response = await axios.get('/api/teams');
      setTeams(response.data.data || []);
    } catch (error) {
      console.error('Error fetching teams:', error);
    }
  };

  const fetchMatches = async (tournamentId) => {
    if (!tournamentId) return;
    
    setLoading(true);
    try {
      const response = await axios.get(`/api/match-outcome/admin/tournament-matches/${tournamentId}`);
      setMatches(response.data.data.matches || []);
    } catch (error) {
      console.error('Error fetching matches:', error);
      showMessage('Error fetching matches', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleTournamentChange = (tournamentId) => {
    setSelectedTournament(tournamentId);
    setMatches([]);
    setEditingMatch(null);
    if (tournamentId) {
      fetchMatches(tournamentId);
    }
  };

  const handleDeleteMatch = async (match) => {
    if (!window.confirm(`Are you sure you want to delete the match between ${getTeamName(match.homeTeam)} vs ${getTeamName(match.awayTeam)}?`)) {
      return;
    }

    try {
      await axios.delete(`/api/match-outcome/admin/match/${selectedTournament}/${match.phaseIndex}/${match._id}`);
      showMessage('Match deleted successfully', 'success');
      fetchMatches(selectedTournament);
    } catch (error) {
      console.error('Error deleting match:', error);
      showMessage('Error deleting match', 'error');
    }
  };

  const handleEditMatch = (match) => {
    setEditingMatch({
      ...match,
      homeTeam: typeof match.homeTeam === 'object' ? match.homeTeam._id : match.homeTeam,
      awayTeam: typeof match.awayTeam === 'object' ? match.awayTeam._id : match.awayTeam,
      date: match.date ? new Date(match.date).toISOString().split('T')[0] : '',
      homeTeamScore: match.result?.homeTeamScore || { runs: '', wickets: '', overs: '' },
      awayTeamScore: match.result?.awayTeamScore || { runs: '', wickets: '', overs: '' },
      winner: match.result?.winner || ''
    });
  };

  const handleSaveMatch = async () => {
    try {
      const updateData = {
        homeTeam: editingMatch.homeTeam,
        awayTeam: editingMatch.awayTeam,
        date: editingMatch.date,
        venue: editingMatch.venue,
        format: editingMatch.format
      };

      // Include result if scores are provided
      if (editingMatch.homeTeamScore.runs || editingMatch.awayTeamScore.runs) {
        updateData.result = {
          homeTeamScore: {
            runs: parseInt(editingMatch.homeTeamScore.runs) || 0,
            wickets: parseInt(editingMatch.homeTeamScore.wickets) || 0,
            overs: parseFloat(editingMatch.homeTeamScore.overs) || 0
          },
          awayTeamScore: {
            runs: parseInt(editingMatch.awayTeamScore.runs) || 0,
            wickets: parseInt(editingMatch.awayTeamScore.wickets) || 0,
            overs: parseFloat(editingMatch.awayTeamScore.overs) || 0
          },
          winner: editingMatch.winner || null
        };
      }

      await axios.put(`/api/match-outcome/admin/match/${selectedTournament}/${editingMatch.phaseIndex}/${editingMatch._id}`, updateData);
      showMessage('Match updated successfully', 'success');
      setEditingMatch(null);
      fetchMatches(selectedTournament);
    } catch (error) {
      console.error('Error updating match:', error);
      showMessage('Error updating match', 'error');
    }
  };

  const getTeamName = (teamId) => {
    if (typeof teamId === 'object' && teamId.teamName) {
      return teamId.teamName;
    }
    const team = teams.find(t => t._id === teamId);
    return team ? team.teamName : 'Unknown Team';
  };

  const showMessage = (text, type) => {
    setMessage(text);
    setMessageType(type);
    setTimeout(() => {
      setMessage('');
      setMessageType('');
    }, 5000);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status) => {
    const statusClass = status === 'completed' ? 'status-completed' : 
                       status === 'ongoing' ? 'status-ongoing' : 'status-scheduled';
    return <span className={`status-badge ${statusClass}`}>{status}</span>;
  };

  return (
    <div className="admin-match-management">
      <div className="header">
        <h2>Admin Match Management</h2>
        <p>Manage tournament matches - edit details, update results, or delete matches</p>
      </div>

      {message && (
        <div className={`message ${messageType}`}>
          {message}
        </div>
      )}

      <div className="tournament-selector">
        <label htmlFor="tournament-select">Select Tournament:</label>
        <select 
          id="tournament-select"
          value={selectedTournament} 
          onChange={(e) => handleTournamentChange(e.target.value)}
        >
          <option value="">Choose a tournament...</option>
          {tournaments.map(tournament => (
            <option key={tournament._id} value={tournament._id}>
              {tournament.name}
            </option>
          ))}
        </select>
      </div>

      {loading && <div className="loading">Loading matches...</div>}

      {matches.length > 0 && (
        <div className="matches-container">
          <h3>Matches ({matches.length})</h3>
          <div className="matches-grid">
            {matches.map(match => (
              <div key={match._id} className="match-card">
                <div className="match-header">
                  <div className="teams">
                    <span className="team">{getTeamName(match.homeTeam)}</span>
                    <span className="vs">vs</span>
                    <span className="team">{getTeamName(match.awayTeam)}</span>
                  </div>
                  {getStatusBadge(match.status)}
                </div>
                
                <div className="match-details">
                  <div className="detail">
                    <strong>Phase:</strong> {match.phaseName}
                  </div>
                  <div className="detail">
                    <strong>Date:</strong> {formatDate(match.date)}
                  </div>
                  <div className="detail">
                    <strong>Venue:</strong> {match.venue || 'Not set'}
                  </div>
                  <div className="detail">
                    <strong>Format:</strong> {match.format || 'Not set'}
                  </div>
                </div>

                {match.result && (
                  <div className="match-result">
                    <div className="scores">
                      <div className="score">
                        <strong>{getTeamName(match.homeTeam)}:</strong> 
                        {match.result.homeTeamScore?.runs || 0}/{match.result.homeTeamScore?.wickets || 0} 
                        ({match.result.homeTeamScore?.overs || 0} overs)
                      </div>
                      <div className="score">
                        <strong>{getTeamName(match.awayTeam)}:</strong> 
                        {match.result.awayTeamScore?.runs || 0}/{match.result.awayTeamScore?.wickets || 0} 
                        ({match.result.awayTeamScore?.overs || 0} overs)
                      </div>
                    </div>
                    {match.result.winner && (
                      <div className="winner">
                        <strong>Winner:</strong> {getTeamName(match.result.winner)}
                      </div>
                    )}
                  </div>
                )}

                <div className="match-actions">
                  <button 
                    className="btn btn-edit" 
                    onClick={() => handleEditMatch(match)}
                  >
                    Edit
                  </button>
                  <button 
                    className="btn btn-delete" 
                    onClick={() => handleDeleteMatch(match)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedTournament && matches.length === 0 && !loading && (
        <div className="no-matches">
          <p>No matches found for this tournament.</p>
        </div>
      )}

      {editingMatch && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>Edit Match</h3>
              <button className="close-btn" onClick={() => setEditingMatch(null)}>×</button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>Home Team:</label>
                <select 
                  value={editingMatch.homeTeam} 
                  onChange={(e) => setEditingMatch({...editingMatch, homeTeam: e.target.value})}
                >
                  {teams.map(team => (
                    <option key={team._id} value={team._id}>{team.teamName}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Away Team:</label>
                <select 
                  value={editingMatch.awayTeam} 
                  onChange={(e) => setEditingMatch({...editingMatch, awayTeam: e.target.value})}
                >
                  {teams.map(team => (
                    <option key={team._id} value={team._id}>{team.teamName}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label>Date:</label>
                <input 
                  type="date" 
                  value={editingMatch.date} 
                  onChange={(e) => setEditingMatch({...editingMatch, date: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label>Venue:</label>
                <input 
                  type="text" 
                  value={editingMatch.venue || ''} 
                  onChange={(e) => setEditingMatch({...editingMatch, venue: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label>Format:</label>
                <select 
                  value={editingMatch.format || ''} 
                  onChange={(e) => setEditingMatch({...editingMatch, format: e.target.value})}
                >
                  <option value="">Select format</option>
                  <option value="T20">T20</option>
                  <option value="ODI">ODI</option>
                  <option value="Test">Test</option>
                </select>
              </div>

              <div className="scores-section">
                <h4>Match Result (Optional)</h4>
                
                <div className="team-score">
                  <h5>Home Team Score</h5>
                  <div className="score-inputs">
                    <input 
                      type="number" 
                      placeholder="Runs" 
                      value={editingMatch.homeTeamScore.runs} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        homeTeamScore: {...editingMatch.homeTeamScore, runs: e.target.value}
                      })}
                    />
                    <input 
                      type="number" 
                      placeholder="Wickets" 
                      value={editingMatch.homeTeamScore.wickets} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        homeTeamScore: {...editingMatch.homeTeamScore, wickets: e.target.value}
                      })}
                    />
                    <input 
                      type="number" 
                      step="0.1" 
                      placeholder="Overs" 
                      value={editingMatch.homeTeamScore.overs} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        homeTeamScore: {...editingMatch.homeTeamScore, overs: e.target.value}
                      })}
                    />
                  </div>
                </div>

                <div className="team-score">
                  <h5>Away Team Score</h5>
                  <div className="score-inputs">
                    <input 
                      type="number" 
                      placeholder="Runs" 
                      value={editingMatch.awayTeamScore.runs} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        awayTeamScore: {...editingMatch.awayTeamScore, runs: e.target.value}
                      })}
                    />
                    <input 
                      type="number" 
                      placeholder="Wickets" 
                      value={editingMatch.awayTeamScore.wickets} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        awayTeamScore: {...editingMatch.awayTeamScore, wickets: e.target.value}
                      })}
                    />
                    <input 
                      type="number" 
                      step="0.1" 
                      placeholder="Overs" 
                      value={editingMatch.awayTeamScore.overs} 
                      onChange={(e) => setEditingMatch({
                        ...editingMatch, 
                        awayTeamScore: {...editingMatch.awayTeamScore, overs: e.target.value}
                      })}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label>Winner:</label>
                  <select 
                    value={editingMatch.winner} 
                    onChange={(e) => setEditingMatch({...editingMatch, winner: e.target.value})}
                  >
                    <option value="">No winner / Tie</option>
                    <option value={editingMatch.homeTeam}>{getTeamName(editingMatch.homeTeam)}</option>
                    <option value={editingMatch.awayTeam}>{getTeamName(editingMatch.awayTeam)}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="btn btn-cancel" onClick={() => setEditingMatch(null)}>Cancel</button>
              <button className="btn btn-save" onClick={handleSaveMatch}>Save Changes</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminMatchManagement;