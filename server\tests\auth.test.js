const axios = require('axios');

const API_URL = 'http://localhost:5000';
let token = null;

// Test configuration - Replace these values with your credentials
const config = {
  email: process.env.TEST_EMAIL || '<EMAIL>',
  password: process.env.TEST_PASSWORD || 'Summer@00'
};

const testAuth = async () => {
  console.log('\n🔒 Testing Authentication & Protected Routes\n');
  console.log('Using email:', config.email);

  try {
    // Test 1: Try accessing protected route without token
    console.log('Test 1: Accessing protected route without token');
    try {
      await axios.get(`${API_URL}/api/auth/me`);
      console.log('❌ Failed: Should not allow access without token');
    } catch (err) {
      if (err.response?.status === 401) {
        console.log('✅ Success: Unauthorized access properly rejected');
      }
    }

    // Test 2: Login and get token
    console.log('\nTest 2: Login and JWT token generation');
    const loginResponse = await axios.post(`${API_URL}/api/auth/login`, {
      email: config.email,
      password: config.password
    });

    if (loginResponse.data.token) {
      token = loginResponse.data.token;
      console.log('✅ Success: Received JWT token');
      console.log('Token starts with:', token.substring(0, 20) + '...');
    } else {
      console.log('❌ Failed: No token received');
    }

    // Test 3: Access protected route with token
    console.log('\nTest 3: Accessing protected route with token');
    const meResponse = await axios.get(`${API_URL}/api/auth/me`, {
      headers: { 'x-auth-token': token }
    });

    if (meResponse.data) {
      console.log('✅ Success: Protected route accessed successfully');
      console.log('User data:', {
        username: meResponse.data.username,
        email: meResponse.data.email,
        role: meResponse.data.role
      });
    }

    // Test 4: Test invalid token
    console.log('\nTest 4: Testing invalid token');
    try {
      await axios.get(`${API_URL}/api/auth/me`, {
        headers: { 'x-auth-token': 'invalid_token' }
      });
      console.log('❌ Failed: Should not accept invalid token');
    } catch (err) {
      if (err.response?.status === 401) {
        console.log('✅ Success: Invalid token properly rejected');
      }
    }

  } catch (err) {
    console.error('\n❌ Test failed:', err.response?.data?.msg || err.message);
  }
};

if (!config.email || !config.password) {
  console.log('\n⚠️ Please set your test credentials first!');
  console.log('You can either:');
  console.log('1. Edit the config object in this file');
  console.log('2. Set TEST_EMAIL and TEST_PASSWORD environment variables\n');
} else {
  // Run the tests
  testAuth();
}