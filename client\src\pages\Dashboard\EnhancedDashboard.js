import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  useTheme,
  useMediaQuery,
  alpha,
  Button
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Person as PlayerIcon,
  AttachMoney as MoneyIcon,
  InsertChart as StatsIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import ResponsiveDashboardCard from '../../components/dashboard/ResponsiveDashboardCard';

const EnhancedDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Welcome Section */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 3, md: 4 },
          mb: 4,
          borderRadius: 3,
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(45deg, ${alpha(theme.palette.primary.dark, 0.8)}, ${alpha(theme.palette.primary.main, 0.6)})`
            : `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.8)}, ${alpha(theme.palette.primary.light, 0.6)})`,
          color: '#fff',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z" fill="%23ffffff" fill-opacity="0.1" fill-rule="evenodd"/%3E%3C/svg%3E")',
            opacity: 0.3,
            zIndex: 0
          }}
        />

        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <Typography
            variant="h4"
            gutterBottom
            sx={{
              fontSize: { xs: '1.75rem', sm: '2.25rem', md: '2.5rem' },
              fontWeight: 'bold',
              mb: 1,
              textShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          >
            Welcome back, {user?.username}!
          </Typography>

          <Typography
            variant="subtitle1"
            sx={{
              fontSize: { xs: '1rem', sm: '1.1rem' },
              mb: 3,
              opacity: 0.9,
              maxWidth: '800px',
              textShadow: '0 1px 2px rgba(0,0,0,0.1)'
            }}
          >
            {user?.role === 'team_owner' && `Team: ${user.teamName}`}
            {user?.role === 'admin' && 'Admin Dashboard'}
            {(!user?.role || user?.role === 'viewer') && 'Cricket Tournament Management System'}
          </Typography>

          {user?.role === 'team_owner' && (
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'flex-start', sm: 'center' },
              gap: 2,
              mt: 2
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                py: 1,
                px: 3,
                bgcolor: alpha('#fff', 0.2),
                borderRadius: 3,
                backdropFilter: 'blur(10px)'
              }}>
                <MoneyIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>
                  {user.virtualCurrency?.toLocaleString() || '0'} Credits
                </Typography>
              </Box>

              <Button
                variant="contained"
                startIcon={<MoneyIcon />}
                onClick={() => navigate('/market')}
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha('#fff', 0.9)
                  },
                  fontWeight: 'medium',
                  py: 1,
                  px: 3,
                  borderRadius: 3
                }}
              >
                Go to Market
              </Button>
            </Box>
          )}
        </Box>
      </Paper>

      {/* Main Dashboard Cards */}
      <Grid container spacing={3} sx={{ justifyContent: 'center' }}>
        <Grid item xs={12} sm={6} md={3}>
          <ResponsiveDashboardCard
            title="My Team"
            subtitle="Manage players and team"
            icon={<PlayerIcon fontSize="large" />}
            iconColor="primary"
            actionText="View Team"
            onClick={() => navigate('/my-team')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <ResponsiveDashboardCard
            title="Tournaments"
            subtitle="Join and view tournaments"
            icon={<TrophyIcon fontSize="large" />}
            iconColor="success"
            actionText="View Tournaments"
            onClick={() => navigate('/tournaments')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <ResponsiveDashboardCard
            title="Transfer Market"
            subtitle="Buy and sell players"
            icon={<MoneyIcon fontSize="large" />}
            iconColor="secondary"
            actionText="Go to Market"
            onClick={() => navigate('/market')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <ResponsiveDashboardCard
            title="Leaderboards"
            subtitle="Stats and rankings"
            icon={<StatsIcon fontSize="large" />}
            iconColor="info"
            actionText="View Stats"
            onClick={() => navigate('/leaderboards')}
          />
        </Grid>
      </Grid>
    </Container>
  );
};

export default EnhancedDashboard;
