import axios from 'axios';
import { API_URL } from '../config';

/**
 * Start the training server
 * 
 * @returns {Promise<Object>} - Promise with server start result
 */
export const startTrainingServer = async () => {
  try {
    const response = await axios.post(`${API_URL}/training/start-server`);
    return response.data;
  } catch (error) {
    console.error('Error starting training server:', error);
    throw error.response?.data || error;
  }
};

/**
 * Stop the training server
 * 
 * @returns {Promise<Object>} - Promise with server stop result
 */
export const stopTrainingServer = async () => {
  try {
    const response = await axios.post(`${API_URL}/training/stop-server`);
    return response.data;
  } catch (error) {
    console.error('Error stopping training server:', error);
    throw error.response?.data || error;
  }
};

/**
 * Check if the training server is running
 * 
 * @returns {Promise<Object>} - Promise with server status
 */
export const getTrainingServerStatus = async () => {
  try {
    const response = await axios.get(`${API_URL}/training/server-status`);
    return response.data;
  } catch (error) {
    console.error('Error checking training server status:', error);
    throw error.response?.data || error;
  }
};

/**
 * Upload a scorecard image for training
 * 
 * @param {File} file - The image file to upload
 * @returns {Promise<Object>} - Promise with upload result
 */
export const uploadScorecardForTraining = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post(`${API_URL}/training/upload-scorecard`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading scorecard for training:', error);
    throw error.response?.data || error;
  }
};

/**
 * Train the ML model
 * 
 * @returns {Promise<Object>} - Promise with training result
 */
export const trainModel = async () => {
  try {
    const response = await axios.post(`${API_URL}/training/train-model`);
    return response.data;
  } catch (error) {
    console.error('Error training model:', error);
    throw error.response?.data || error;
  }
};

/**
 * Get the status of the trained model
 * 
 * @returns {Promise<Object>} - Promise with model status
 */
export const getModelStatus = async () => {
  try {
    const response = await axios.get(`${API_URL}/training/model-status`);
    return response.data;
  } catch (error) {
    console.error('Error getting model status:', error);
    throw error.response?.data || error;
  }
};

export default {
  startTrainingServer,
  stopTrainingServer,
  getTrainingServerStatus,
  uploadScorecardForTraining,
  trainModel,
  getModelStatus
};
