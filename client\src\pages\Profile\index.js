import React, { useState } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Button,
  TextField,
  Grid,
  Paper,
  Snackbar,
  Alert,
  IconButton,
  Di<PERSON>r,
  Container,
  Card,
  CardContent,
} from '@mui/material';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { useAuth } from '../../hooks/useAuth';
import { uploadProfileImage } from '../../services/uploadService';
import { useNavigate } from 'react-router-dom';

const Profile = () => {
  const { user, updateProfile, error, logout } = useAuth();
  const navigate = useNavigate();
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [formError, setFormError] = useState('');
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    teamName: user?.teamName || '',
    password: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Handle form field changes
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  // Handle save profile
  const handleSave = async (e) => {
    e.preventDefault();
    setFormError('');
    setSuccess(false);
    setSuccessMessage('');

    // Validate new password if provided
    if (formData.newPassword) {
      if (formData.newPassword !== formData.confirmPassword) {
        return setFormError('New passwords do not match');
      }

      if (formData.newPassword.length < 6) {
        return setFormError('Password must be at least 6 characters');
      }

      if (!formData.password) {
        return setFormError('Current password is required to set a new password');
      }
    }

    try {
      setLoading(true);
      const response = await updateProfile({
        username: formData.username,
        email: formData.email,
        teamName: formData.teamName !== user.teamName ? formData.teamName : undefined,
        password: formData.password || undefined,
        newPassword: formData.newPassword || undefined
      });

      setSuccessMessage(response.msg);
      setSuccess(true);
      setEditing(false);

      // Reset password fields
      setFormData(prev => ({
        ...prev,
        password: '',
        newPassword: '',
        confirmPassword: ''
      }));

      // If password was changed, logout after 2 seconds
      if (response.passwordChanged) {
        setTimeout(() => {
          logout();
          navigate('/login');
        }, 2000);
      }
    } catch (err) {
      console.error('Profile update error:', err);
      setFormError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  // Handle profile photo upload
  const handleProfilePhotoUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const fileType = file.type;
    if (!fileType.match(/^image\/(jpeg|jpg|png|gif)$/)) {
      return setFormError('Please upload an image file (jpeg, jpg, png, gif)');
    }

    try {
      setLoading(true);

      // Create a FormData object and append the file with the correct field name
      const formData = new FormData();
      formData.append('profileImage', file); // Must match the field name expected by the server

      console.log('Uploading profile image with FormData');
      const response = await uploadProfileImage(formData);
      console.log('Upload response:', response);

      if (!response || !response.filePath) {
        throw new Error('Invalid response from server');
      }

      // Update user profile with new image path
      await updateProfile({ profilePicture: response.filePath });
      setSuccessMessage('Profile photo updated successfully');
      setSuccess(true);
    } catch (err) {
      console.error('Profile photo upload error:', err);
      setFormError(typeof err === 'string' ? err : 'Failed to upload profile photo');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Profile
        </Typography>
        <Divider />
      </Box>

      <Grid container spacing={4}>
        {/* Profile Photo Section */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Avatar
                src={user?.profilePicture || '/static/images/avatar/default.jpg'}
                alt={user?.username}
                sx={{ width: 150, height: 150, mb: 2 }}
              />
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="profile-photo-upload"
                type="file"
                onChange={handleProfilePhotoUpload}
              />
              <label htmlFor="profile-photo-upload">
                <Button
                  variant="contained"
                  component="span"
                  startIcon={<PhotoCameraIcon />}
                  fullWidth
                  disabled={loading}
                >
                  Change Photo
                </Button>
              </label>
            </CardContent>
          </Card>
        </Grid>

        {/* Profile Info Section */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h5" component="h2">
                {editing ? 'Edit Profile' : 'Profile Information'}
              </Typography>
              {!editing ? (
                <Button
                  startIcon={<EditIcon />}
                  onClick={() => setEditing(true)}
                >
                  Edit
                </Button>
              ) : (
                <Box>
                  <IconButton color="primary" onClick={handleSave} disabled={loading}>
                    <SaveIcon />
                  </IconButton>
                  <IconButton color="error" onClick={() => setEditing(false)} disabled={loading}>
                    <CancelIcon />
                  </IconButton>
                </Box>
              )}
            </Box>

            <form onSubmit={handleSave}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={!editing || loading}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={!editing || loading}
                  />
                </Grid>
                {user?.role === 'team_owner' && (
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Team Name"
                      name="teamName"
                      value={formData.teamName}
                      onChange={handleChange}
                      disabled={!editing || loading}
                    />
                  </Grid>
                )}
                {editing && (
                  <>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="subtitle1" gutterBottom>
                        Change Password (optional)
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Current Password"
                        name="password"
                        type="password"
                        value={formData.password}
                        onChange={handleChange}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="New Password"
                        name="newPassword"
                        type="password"
                        value={formData.newPassword}
                        onChange={handleChange}
                        disabled={loading}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Confirm New Password"
                        name="confirmPassword"
                        type="password"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        disabled={loading}
                      />
                    </Grid>
                  </>
                )}
              </Grid>

              {/* Only show submit button when in mobile view since the save icon might be missed */}
              {editing && (
                <Box sx={{ mt: 3, display: { md: 'none' } }}>
                  <Button
                    type="submit"
                    variant="contained"
                    fullWidth
                    disabled={loading}
                  >
                    Save Changes
                  </Button>
                </Box>
              )}
            </form>

            {/* Account Details Section */}
            <Box sx={{ mt: 4 }}>
              <Typography variant="subtitle1" gutterBottom>
                Account Details
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Role
                  </Typography>
                  <Typography variant="body1">
                    {user?.role === 'team_owner' ? 'Team Owner' : user?.role === 'admin' ? 'Administrator' : 'Viewer'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Joined
                  </Typography>
                  <Typography variant="body1">
                    {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
                  </Typography>
                </Grid>
                {user?.role === 'team_owner' && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Virtual Currency
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {user?.virtualCurrency?.toLocaleString() || '0'} Credits
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Success/Error messages */}
      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSuccess(false)} severity="success" sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!formError || !!error}
        autoHideDuration={6000}
        onClose={() => setFormError('')}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setFormError('')} severity="error" sx={{ width: '100%' }}>
          {formError || error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Profile;