const { uploadToCloud } = require('../utils/fileUpload');
const path = require('path');
const fs = require('fs');
const { removeBackground } = require('../utils/imageProcessor');

/**
 * Upload profile image
 * @route POST /api/upload/profile
 * @access Private
 */
exports.uploadProfileImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ msg: 'No image uploaded' });
    }
    
    console.log('Profile image upload attempt:', {
      filename: req.file.originalname,
      fieldname: req.file.fieldname,
      mimetype: req.file.mimetype,
      destination: req.file.destination,
      path: req.file.path
    });
    
    const filePath = await uploadToCloud(req.file);
    
    res.json({
      success: true,
      filePath
    });
  } catch (err) {
    console.error('Error uploading profile image:', err);
    res.status(500).json({ 
      error: 'Server error during profile image upload', 
      details: err.message 
    });
  }
};

/**
 * Upload player image
 * @route POST /api/upload/player
 * @access Private (Admin only)
 */
exports.uploadPlayerImage = async (req, res) => {
  try {
    console.log('Player image upload request received:', {
      body: req.body,
      file: req.file ? 'File present' : 'No file in request',
      headers: req.headers['content-type']
    });
    
    if (!req.file) {
      return res.status(400).json({ msg: 'No image uploaded' });
    }
    
    console.log('Player image details:', {
      filename: req.file.originalname,
      fieldname: req.file.fieldname,
      mimetype: req.file.mimetype,
      destination: req.file.destination,
      path: req.file.path
    });
    
    // Process the image to remove background
    const processedImagePath = await removeBackground(req.file.path);
    
    // Use the processed image path for cloud upload
    const filePath = await uploadToCloud({
      ...req.file,
      path: processedImagePath
    });
    
    // Clean up the processed image if it's different from original
    if (processedImagePath !== req.file.path && fs.existsSync(processedImagePath)) {
      fs.unlinkSync(processedImagePath);
    }
    
    console.log('Upload to cloud successful, returning path:', filePath);
    
    res.json({
      success: true,
      filePath
    });
  } catch (err) {
    console.error('Error uploading player image:', err);
    res.status(500).json({ 
      error: 'Server error during player image upload', 
      details: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

/**
 * Upload scorecard image
 * @route POST /api/upload/scorecard
 * @access Private
 */
exports.uploadScorecardImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ msg: 'No image uploaded' });
    }
    
    const filePath = await uploadToCloud(req.file);
    
    res.json({
      success: true,
      filePath
    });
  } catch (err) {
    console.error('Error uploading scorecard image:', err);
    res.status(500).send('Server error');
  }
};

/**
 * Upload multiple player images
 * @route POST /api/upload/players-bulk
 * @access Private (Admin only)
 */
exports.uploadBulkPlayerImages = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ msg: 'No images uploaded' });
    }
    
    const uploadPromises = req.files.map(async (file) => {
      // Process each image to remove background
      const processedImagePath = await removeBackground(file.path);
      
      // Upload the processed image
      const filePath = await uploadToCloud({
        ...file,
        path: processedImagePath
      });
      
      // Clean up the processed image
      if (processedImagePath !== file.path && fs.existsSync(processedImagePath)) {
        fs.unlinkSync(processedImagePath);
      }
      
      return filePath;
    });
    
    const filePaths = await Promise.all(uploadPromises);
    
    res.json({
      success: true,
      filePaths
    });
  } catch (err) {
    console.error('Error uploading bulk player images:', err);
    res.status(500).json({ 
      error: 'Server error during bulk upload',
      details: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};