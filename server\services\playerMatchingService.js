const Player = require('../models/Player');
const { calculateSimilarity, normalizePlayerName } = require('../utils/playerPhotoMatcher');

/**
 * Service for robust player name matching between OCR-extracted names and database players
 * Implements Priority 1 from the roadmap: Robust Player Name Matching
 */
class PlayerMatchingService {
  constructor() {
    this.matchThreshold = 0.7; // Minimum similarity score for automatic matching
    this.verificationThreshold = 0.5; // Minimum score to suggest for manual verification
    this.playerCache = new Map(); // Cache for frequently accessed players
    this.aliasMap = new Map(); // Map for known player aliases
    
    // Initialize common cricket name aliases
    this.initializeAliases();
  }

  /**
   * Initialize the service
   * @returns {Promise<void>}
   */
  async initialize() {
    // Pre-load some players into cache for faster matching
    try {
      const popularPlayers = await Player.find()
        .sort({ popularity: -1 })
        .limit(100);
      
      popularPlayers.forEach(player => {
        this.playerCache.set(player._id.toString(), player);
      });
      
      console.log(`Pre-loaded ${popularPlayers.length} popular players into matching cache`);
    } catch (error) {
      console.error('Error pre-loading players into cache:', error);
    }
  }

  /**
   * Initialize common cricket player name aliases and variations
   */
  initializeAliases() {
    // Common name variations in cricket scorecards
    const commonAliases = {
      // Format: 'alias': 'full_name'
      'MS Dhoni': 'Mahendra Singh Dhoni',
      'MSD': 'Mahendra Singh Dhoni',
      'Virat': 'Virat Kohli',
      'VK': 'Virat Kohli',
      'Rohit': 'Rohit Sharma',
      'Hitman': 'Rohit Sharma',
      'Bumrah': 'Jasprit Bumrah',
      'Jadeja': 'Ravindra Jadeja',
      'Sir Jadeja': 'Ravindra Jadeja',
      'ABD': 'AB de Villiers',
      'AB': 'AB de Villiers',
      'Faf': 'Faf du Plessis',
      'Warner': 'David Warner',
      'Finch': 'Aaron Finch',
      'Maxwell': 'Glenn Maxwell',
      'Maxi': 'Glenn Maxwell',
      'Stoinis': 'Marcus Stoinis',
      'Russell': 'Andre Russell',
      'Dre Russ': 'Andre Russell',
      'Pollard': 'Kieron Pollard',
      'Malinga': 'Lasith Malinga',
      'Rashid': 'Rashid Khan',
      'Narine': 'Sunil Narine',
      'Gayle': 'Chris Gayle',
      'Universe Boss': 'Chris Gayle'
    };

    for (const [alias, fullName] of Object.entries(commonAliases)) {
      this.aliasMap.set(normalizePlayerName(alias), normalizePlayerName(fullName));
    }
  }

  /**
   * Add a custom alias for a player
   * @param {string} alias - The alias name
   * @param {string} fullName - The full player name
   */
  addAlias(alias, fullName) {
    this.aliasMap.set(normalizePlayerName(alias), normalizePlayerName(fullName));
  }

  /**
   * Match a single OCR-extracted player name against the database
   * @param {string} ocrName - The name extracted from OCR
   * @param {Object} options - Matching options
   * @returns {Promise<Object>} - Matching result
   */
  async matchPlayer(ocrName, options = {}) {
    try {
      const {
        teamFilter = null, // Filter by team if provided
        roleFilter = null, // Filter by role if provided
        requireManualVerification = false
      } = options;

      console.log(`🔍 Matching player: "${ocrName}"`);

      // Normalize the OCR name
      const normalizedOcrName = normalizePlayerName(ocrName);
      
      // Check if this is a known alias
      const aliasMatch = this.aliasMap.get(normalizedOcrName);
      if (aliasMatch) {
        console.log(`📝 Found alias mapping: "${ocrName}" -> "${aliasMatch}"`);
        return await this.matchPlayer(aliasMatch, { ...options, requireManualVerification: false });
      }

      // Get all players from database (with optional filters)
      const players = await this.getPlayersForMatching(teamFilter, roleFilter);
      
      if (players.length === 0) {
        return {
          success: false,
          ocrName,
          matchType: 'no_players_available',
          message: 'No players available for matching'
        };
      }

      // Find the best matches
      const matches = this.findMatches(normalizedOcrName, players);
      
      // Determine the result based on match quality
      return this.determineMatchResult(ocrName, normalizedOcrName, matches, requireManualVerification);

    } catch (error) {
      console.error('Error matching player:', error);
      return {
        success: false,
        ocrName,
        matchType: 'error',
        error: error.message
      };
    }
  }

  /**
   * Match multiple OCR-extracted player names
   * @param {Array<string>} ocrNames - Array of names extracted from OCR
   * @param {Object} options - Matching options
   * @returns {Promise<Object>} - Batch matching result
   */
  async matchPlayers(ocrNames, options = {}) {
    try {
      console.log(`🔍 Batch matching ${ocrNames.length} players`);
      
      const results = {
        totalPlayers: ocrNames.length,
        automaticMatches: [],
        manualVerificationRequired: [],
        noMatches: [],
        errors: []
      };

      // Process each player name
      for (const ocrName of ocrNames) {
        const matchResult = await this.matchPlayer(ocrName, options);
        
        switch (matchResult.matchType) {
          case 'automatic':
            results.automaticMatches.push(matchResult);
            break;
          case 'manual_verification':
            results.manualVerificationRequired.push(matchResult);
            break;
          case 'no_match':
          case 'no_players_available':
            results.noMatches.push(matchResult);
            break;
          case 'error':
            results.errors.push(matchResult);
            break;
        }
      }

      // Calculate success metrics
      results.automaticMatchRate = (results.automaticMatches.length / results.totalPlayers) * 100;
      results.totalMatchRate = ((results.automaticMatches.length + results.manualVerificationRequired.length) / results.totalPlayers) * 100;

      console.log(`✅ Batch matching complete:`);
      console.log(`   - Automatic matches: ${results.automaticMatches.length}/${results.totalPlayers} (${results.automaticMatchRate.toFixed(1)}%)`);
      console.log(`   - Manual verification: ${results.manualVerificationRequired.length}`);
      console.log(`   - No matches: ${results.noMatches.length}`);
      console.log(`   - Errors: ${results.errors.length}`);

      return results;

    } catch (error) {
      console.error('Error in batch matching:', error);
      throw error;
    }
  }

  /**
   * Get players from database for matching
   * @param {string} teamFilter - Optional team filter
   * @param {string} roleFilter - Optional role filter
   * @returns {Promise<Array>} - Array of players
   */
  async getPlayersForMatching(teamFilter = null, roleFilter = null) {
    try {
      const query = {};
      
      if (teamFilter) {
        query.team = teamFilter;
      }
      
      if (roleFilter) {
        query.type = roleFilter;
      }

      // Get players with essential fields only for performance
      const players = await Player.find(query)
        .select('name type nationality team owner ratings')
        .lean();

      return players;
    } catch (error) {
      console.error('Error fetching players for matching:', error);
      return [];
    }
  }

  /**
   * Find matches for a normalized OCR name
   * @param {string} normalizedOcrName - Normalized OCR name
   * @param {Array} players - Array of players to match against
   * @returns {Array} - Array of matches with scores
   */
  findMatches(normalizedOcrName, players) {
    const matches = [];

    for (const player of players) {
      const normalizedPlayerName = normalizePlayerName(player.name);
      
      // Calculate similarity score
      const score = calculateSimilarity(normalizedOcrName, normalizedPlayerName);
      
      if (score >= this.verificationThreshold) {
        matches.push({
          player,
          score,
          normalizedName: normalizedPlayerName
        });
      }
    }

    // Sort by score (highest first)
    matches.sort((a, b) => b.score - a.score);
    
    return matches;
  }

  /**
   * Determine the final match result based on scores and thresholds
   * @param {string} ocrName - Original OCR name
   * @param {string} normalizedOcrName - Normalized OCR name
   * @param {Array} matches - Array of potential matches
   * @param {boolean} requireManualVerification - Force manual verification
   * @returns {Object} - Match result
   */
  determineMatchResult(ocrName, normalizedOcrName, matches, requireManualVerification) {
    if (matches.length === 0) {
      return {
        success: false,
        ocrName,
        normalizedOcrName,
        matchType: 'no_match',
        message: 'No similar players found in database'
      };
    }

    const bestMatch = matches[0];
    const secondBestScore = matches.length > 1 ? matches[1].score : 0;
    const scoreDifference = bestMatch.score - secondBestScore;

    // Automatic match criteria:
    // 1. Score above threshold
    // 2. Clear winner (significant score difference or only one good match)
    // 3. Not forced to manual verification
    if (!requireManualVerification && 
        bestMatch.score >= this.matchThreshold && 
        (scoreDifference >= 0.2 || matches.length === 1)) {
      
      return {
        success: true,
        ocrName,
        normalizedOcrName,
        matchType: 'automatic',
        player: bestMatch.player,
        confidence: bestMatch.score,
        message: `Automatically matched with ${(bestMatch.score * 100).toFixed(1)}% confidence`
      };
    }

    // Manual verification required
    return {
      success: false,
      ocrName,
      normalizedOcrName,
      matchType: 'manual_verification',
      candidates: matches.slice(0, 5), // Top 5 candidates for manual review
      bestCandidate: bestMatch,
      message: `Manual verification required. Best match: ${bestMatch.player.name} (${(bestMatch.score * 100).toFixed(1)}% confidence)`
    };
  }

  /**
   * Manually confirm a player match
   * @param {string} ocrName - Original OCR name
   * @param {string} playerId - Confirmed player ID
   * @param {boolean} createAlias - Whether to create an alias for future matches
   * @returns {Promise<Object>} - Confirmation result
   */
  async confirmMatch(ocrName, playerId, createAlias = true) {
    try {
      const player = await Player.findById(playerId);
      
      if (!player) {
        return {
          success: false,
          message: 'Player not found'
        };
      }

      // Create alias if requested
      if (createAlias) {
        this.addAlias(ocrName, player.name);
        console.log(`📝 Created alias: "${ocrName}" -> "${player.name}"`);
      }

      return {
        success: true,
        ocrName,
        player,
        message: `Manually confirmed match: "${ocrName}" -> "${player.name}"`
      };

    } catch (error) {
      console.error('Error confirming match:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get matching statistics for monitoring and improvement
   * @returns {Object} - Statistics object
   */
  getMatchingStats() {
    return {
      aliasCount: this.aliasMap.size,
      cacheSize: this.playerCache.size,
      matchThreshold: this.matchThreshold,
      verificationThreshold: this.verificationThreshold
    };
  }

  /**
   * Update matching thresholds
   * @param {number} matchThreshold - New automatic match threshold
   * @param {number} verificationThreshold - New verification threshold
   */
  updateThresholds(matchThreshold, verificationThreshold) {
    this.matchThreshold = matchThreshold;
    this.verificationThreshold = verificationThreshold;
    console.log(`Updated thresholds: match=${matchThreshold}, verification=${verificationThreshold}`);
  }

  /**
   * Clear the player cache (useful after database updates)
   */
  clearCache() {
    this.playerCache.clear();
    console.log('Player matching cache cleared');
  }
}

module.exports = PlayerMatchingService;