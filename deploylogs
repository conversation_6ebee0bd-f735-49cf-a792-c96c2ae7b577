Initializing deployment
Clonning Repo github.com/rhingonekar/rplwebapp.git to /etc/dokploy/applications/rpl-rplapp-kwaaag/code: ✅
Cloning into '/etc/dokploy/applications/rpl-rplapp-kwaaag/code'...
remote: Enumerating objects: 1380, done.
Cloned github.com/rhingonekar/rplwebapp.git: ✅
Build dockerfile: ✅
Source Type: github: ✅
#0 building with "default" instance using docker driver
#1 [internal] load build definition from Dockerfile
#1 transferring dockerfile: 1.63kB done
#1 DONE 0.0s
#2 [internal] load metadata for docker.io/library/node:18-alpine
#2 DONE 0.4s
#3 [internal] load .dockerignore
#3 transferring context: 985B done
#3 DONE 0.0s
#4 [client-build 1/6] FROM docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e
#4 resolve docker.io/library/node:18-alpine@sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e done
#4 sha256:8d6421d663b4c28fd3ebc498332f249011d118945588d0a35cb9bc4b8ca09d9e 7.67kB / 7.67kB done
#4 sha256:929b04d7c782f04f615cf785488fed452b6569f87c73ff666ad553a7554f0006 1.72kB / 1.72kB done
#4 sha256:ee77c6cd7c1886ecc802ad6cedef3a8ec1ea27d1fb96162bf03dd3710839b8da 6.18kB / 6.18kB done
#4 DONE 0.1s
#5 [internal] load build context
#5 ...
#6 [server 2/9] WORKDIR /app
#6 DONE 0.2s
#5 [internal] load build context
#5 ...
#7 [client-build 2/6] WORKDIR /app/client
#7 DONE 0.2s
#5 [internal] load build context
#5 transferring context: 45.41MB 0.8s done
#5 DONE 0.8s
#8 [client-build 3/6] COPY client/package*.json ./
#8 DONE 0.2s
#9 [server 3/9] RUN apk add --no-cache     python3     py3-pip     tesseract-ocr     tesseract-ocr-data-eng     imagemagick     && rm -rf /var/cache/apk/*
#9 0.258 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/x86_64/APKINDEX.tar.gz
#9 0.352 fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/x86_64/APKINDEX.tar.gz
#9 0.839 (1/72) Installing libxau (1.0.11-r4)
#9 0.842 (2/72) Installing libmd (1.1.0-r0)
#9 0.846 (3/72) Installing libbsd (0.12.2-r0)
#9 0.850 (4/72) Installing libxdmcp (1.1.5-r1)
#9 0.852 (5/72) Installing libxcb (1.16.1-r0)
#9 0.865 (6/72) Installing libx11 (1.8.10-r0)
#9 0.903 (7/72) Installing libxext (1.3.6-r2)
#9 0.906 (8/72) Installing libbz2 (1.0.8-r6)
#9 0.909 (9/72) Installing fftw-double-libs (3.3.10-r6)
#9 0.932 (10/72) Installing libexpat (2.7.0-r0)
#9 0.936 (11/72) Installing brotli-libs (1.1.0-r2)
#9 0.948 (12/72) Installing libpng (1.6.47-r0)
#9 0.953 (13/72) Installing freetype (2.13.3-r0)
#9 0.963 (14/72) Installing fontconfig (2.15.0-r1)
#9 0.976 (15/72) Installing libgomp (14.2.0-r4)
#9 0.983 (16/72) Installing lcms2 (2.16-r0)
#9 0.989 (17/72) Installing libltdl (2.4.7-r3)
#9 0.992 (18/72) Installing xz-libs (5.6.3-r1)
#9 0.998 (19/72) Installing libxml2 (2.13.4-r6)
#9 1.012 (20/72) Installing imagemagick-libs (********-r0)
#9 1.051 (21/72) Installing imagemagick (********-r0)
#9 1.102 (22/72) Installing libffi (3.4.7-r0)
#9 1.105 (23/72) Installing gdbm (1.24-r0)
#9 1.109 (24/72) Installing mpdecimal (4.0.0-r0)
#9 1.114 (25/72) Installing ncurses-terminfo-base (6.5_p20241006-r3)
#9 1.120 (26/72) Installing libncursesw (6.5_p20241006-r3)
#9 1.127 (27/72) Installing libpanelw (6.5_p20241006-r3)
#9 1.129 (28/72) Installing readline (8.2.13-r0)
#9 1.135 (29/72) Installing sqlite-libs (3.48.0-r2)
#9 1.157 (30/72) Installing python3 (3.12.11-r0)
#9 1.429 (31/72) Installing python3-pycache-pyc0 (3.12.11-r0)
#9 1.583 (32/72) Installing pyc (3.12.11-r0)
#9 1.583 (33/72) Installing py3-setuptools-pyc (70.3.0-r0)
#9 1.638 (34/72) Installing py3-pip-pyc (24.3.1-r0)
#9 1.721 (35/72) Installing py3-parsing (3.1.4-r0)
#9 1.728 (36/72) Installing py3-parsing-pyc (3.1.4-r0)
#9 1.737 (37/72) Installing py3-packaging-pyc (24.2-r0)
#9 1.743 (38/72) Installing python3-pyc (3.12.11-r0)
#9 1.743 (39/72) Installing py3-packaging (24.2-r0)
#9 1.749 (40/72) Installing py3-setuptools (70.3.0-r0)
#9 1.793 (41/72) Installing py3-pip (24.3.1-r0)
#9 1.868 (42/72) Installing libxrender (0.9.11-r5)
#9 1.870 (43/72) Installing pixman (0.43.4-r1)
#9 1.880 (44/72) Installing cairo (1.18.4-r0)
#9 1.895 (45/72) Installing libintl (0.22.5-r0)
#9 1.899 (46/72) Installing libeconf (0.6.3-r0)
#9 1.902 (47/72) Installing libblkid (2.40.4-r1)
#9 1.907 (48/72) Installing libmount (2.40.4-r1)
#9 1.912 (49/72) Installing pcre2 (10.43-r0)
#9 1.923 (50/72) Installing glib (2.82.5-r0)
#9 1.974 (51/72) Installing graphite2 (1.3.14-r6)
#9 1.979 (52/72) Installing harfbuzz (9.0.0-r1)
#9 1.994 (53/72) Installing icu-data-en (74.2-r1)
#9 2.019 Executing icu-data-en-74.2-r1.post-install
#9 2.021 *
#9 2.021 * If you need ICU with non-English locales and legacy charset support, install
#9 2.021 * package icu-data-full.
#9 2.021 *
#9 2.022 (54/72) Installing icu-libs (74.2-r1)
#9 2.066 (55/72) Installing giflib (5.2.2-r1)
#9 2.069 (56/72) Installing libjpeg-turbo (3.0.4-r0)
#9 2.078 (57/72) Installing imagemagick-jpeg (********-r0)
#9 2.081 (58/72) Installing libsharpyuv (1.4.0-r0)
#9 2.084 (59/72) Installing libwebp (1.4.0-r0)
#9 2.094 (60/72) Installing libwebpdemux (1.4.0-r0)
#9 2.096 (61/72) Installing libwebpmux (1.4.0-r0)
#9 2.099 (62/72) Installing imagemagick-webp (********-r0)
#9 2.102 (63/72) Installing zstd-libs (1.5.6-r2)
#9 2.112 (64/72) Installing tiff (4.7.0-r0)
#9 2.120 (65/72) Installing imagemagick-tiff (********-r0)
#9 2.124 (66/72) Installing leptonica (1.84.1-r0)
#9 2.152 (67/72) Installing libxft (2.3.8-r3)
#9 2.156 (68/72) Installing fribidi (1.0.16-r0)
#9 2.159 (69/72) Installing pango (1.54.0-r1)
#9 2.170 (70/72) Installing imagemagick-pango (********-r0)
#9 2.173 (71/72) Installing tesseract-ocr (5.5.0-r0)
#9 2.224 (72/72) Installing tesseract-ocr-data-eng (5.5.0-r0)
#9 2.436 Executing busybox-1.37.0-r12.trigger
#9 2.441 Executing glib-2.82.5-r0.trigger
#9 2.445 OK: 129 MiB in 89 packages
#9 DONE 3.1s
#10 [client-build 4/6] RUN npm ci
#10 2.210 npm warn EBADENGINE Unsupported engine {
#10 2.210 npm warn EBADENGINE   package: 'react-router@7.7.0',
#10 2.210 npm warn EBADENGINE   required: { node: '>=20.0.0' },
#10 2.210 npm warn EBADENGINE   current: { node: 'v18.20.8', npm: '10.8.2' }
#10 2.210 npm warn EBADENGINE }
#10 2.212 npm warn EBADENGINE Unsupported engine {
#10 2.212 npm warn EBADENGINE   package: 'react-router-dom@7.7.0',
#10 2.212 npm warn EBADENGINE   required: { node: '>=20.0.0' },
#10 2.212 npm warn EBADENGINE   current: { node: 'v18.20.8', npm: '10.8.2' }
#10 2.212 npm warn EBADENGINE }
#10 ...
#11 [server 4/9] COPY server/package*.json ./
#11 DONE 0.1s
#12 [server 5/9] RUN npm install --platform=linux --arch=x64 --libc=musl sharp
#12 ...
#10 [client-build 4/6] RUN npm ci
#10 7.014 npm warn deprecated w3c-hr-time@1.0.2: Use your platform's native performance.now() and performance.timeOrigin.
#10 7.592 npm warn deprecated stable@0.1.8: Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility
#10 7.593 npm warn deprecated sourcemap-codec@1.4.8: Please use @jridgewell/sourcemap-codec instead
#10 7.956 npm warn deprecated rollup-plugin-terser@7.0.2: This package has been deprecated and is no longer maintained. Please use @rollup/plugin-terser
#10 8.011 npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
#10 8.042 npm warn deprecated workbox-google-analytics@6.6.0: It is not compatible with newer versions of GA starting with v4, as long as you are using GAv3 it should be ok, but the package is not longer being maintained
#10 8.352 npm warn deprecated q@1.5.1: You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.
#10 8.352 npm warn deprecated
#10 8.352 npm warn deprecated (For a CapTP with native promises, see @endo/eventual-send and @endo/captp)
#10 8.837 npm warn deprecated workbox-cacheable-response@6.6.0: workbox-background-sync@6.6.0
#10 10.18 npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
#10 10.45 npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
#10 11.40 npm warn deprecated domexception@2.0.1: Use your platform's native DOMException instead
#10 12.88 npm warn deprecated abab@2.0.6: Use your platform's native atob() and btoa() methods instead
#10 13.59 npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
#10 13.62 npm warn deprecated svgo@1.3.2: This SVGO version is no longer supported. Upgrade to v2.x.x.
#10 13.63 npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
#10 14.55 npm warn deprecated @babel/plugin-proposal-private-methods@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
#10 14.55 npm warn deprecated @babel/plugin-proposal-optional-chaining@7.21.0: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-chaining instead.
#10 14.55 npm warn deprecated @babel/plugin-proposal-numeric-separator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-numeric-separator instead.
#10 14.55 npm warn deprecated @babel/plugin-proposal-nullish-coalescing-operator@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-nullish-coalescing-operator instead.
#10 14.62 npm warn deprecated @babel/plugin-proposal-class-properties@7.18.6: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
#10 16.83 npm warn deprecated @babel/plugin-proposal-private-property-in-object@7.21.11: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
#10 ...
#12 [server 5/9] RUN npm install --platform=linux --arch=x64 --libc=musl sharp
#12 5.592 npm warn deprecated @types/long@5.0.0: This is a stub types definition. long provides its own type definitions, so you do not need this installed.
#12 5.655 npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
#12 ...
#10 [client-build 4/6] RUN npm ci
#10 21.29 npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
#10 ...
#12 [server 5/9] RUN npm install --platform=linux --arch=x64 --libc=musl sharp
#12 20.71
#12 20.71 added 438 packages, and audited 439 packages in 20s
#12 20.71
#12 20.71 77 packages are looking for funding
#12 20.71   run `npm fund` for details
#12 20.73
#12 20.73 6 vulnerabilities (4 low, 2 high)
#12 20.73
#12 20.73 To address issues that do not require attention, run:
#12 20.73   npm audit fix
#12 20.73
#12 20.73 To address all issues possible (including breaking changes), run:
#12 20.73   npm audit fix --force
#12 20.73
#12 20.73 Some issues need review, and may require choosing
#12 20.73 a different dependency.
#12 20.73
#12 20.73 Run `npm audit` for details.
#12 20.73 npm notice
#12 20.73 npm notice New major version of npm available! 10.8.2 -> 11.4.2
#12 20.73 npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
#12 20.73 npm notice To update run: npm install -g npm@11.4.2
#12 20.73 npm notice
#12 DONE 21.4s
#13 [server 6/9] RUN npm ci --only=production
#13 0.435 npm warn config only Use `--omit=dev` to omit dev dependencies from the install.
#13 3.505 npm warn deprecated @types/long@5.0.0: This is a stub types definition. long provides its own type definitions, so you do not need this installed.
#13 4.164 npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
#13 6.616
#13 6.616 added 418 packages, and audited 419 packages in 6s
#13 6.616
#13 6.616 75 packages are looking for funding
#13 6.616   run `npm fund` for details
#13 6.624
#13 6.624 6 vulnerabilities (4 low, 2 high)
#13 6.624
#13 6.624 To address issues that do not require attention, run:
#13 6.624   npm audit fix
#13 6.624
#13 6.624 To address all issues possible (including breaking changes), run:
#13 6.624   npm audit fix --force
#13 6.624
#13 6.624 Some issues need review, and may require choosing
#13 6.624 a different dependency.
#13 6.624
#13 6.624 Run `npm audit` for details.
#13 DONE 7.6s
#10 [client-build 4/6] RUN npm ci
#10 ...
#14 [server 7/9] COPY server/ ./
#14 DONE 0.7s
#10 [client-build 4/6] RUN npm ci
#10 34.98
#10 34.98 added 1449 packages, and audited 1450 packages in 35s
#10 34.98
#10 34.98 295 packages are looking for funding
#10 34.98   run `npm fund` for details
#10 34.99
#10 34.99 12 vulnerabilities (3 low, 3 moderate, 6 high)
#10 34.99
#10 34.99 To address all issues (including breaking changes), run:
#10 34.99   npm audit fix --force
#10 34.99
#10 34.99 Run `npm audit` for details.
#10 35.00 npm notice
#10 35.00 npm notice New major version of npm available! 10.8.2 -> 11.4.2
#10 35.00 npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.4.2
#10 35.00 npm notice To update run: npm install -g npm@11.4.2
#10 35.00 npm notice
#10 DONE 35.6s
#15 [client-build 5/6] COPY client/ ./
#15 DONE 0.1s
#16 [client-build 6/6] RUN npm run build
#16 0.356
#16 0.356 > client@0.1.0 build
#16 0.356 > cross-env CI=false GENERATE_SOURCEMAP=false react-scripts build
#16 0.356
#16 2.125 Creating an optimized production build...
#16 83.86 Compiled with warnings.
#16 83.86
#16 83.86 [eslint]
#16 83.86 src/App.js
#16 83.86   Line 28:8:  'SimpleCameraTest' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/AdminMatchManagement.js
#16 83.86   Line 18:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/components/CricketPlayerCard.js
#16 83.86   Line 2:15:  'Typography' is defined but never used                   no-unused-vars
#16 83.86   Line 48:9:  'getNationalityCode' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/ExportIplPlayers.js
#16 83.86   Line 10:3:  'Link' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/ImportIplPlayers.js
#16 83.86   Line 9:3:   'Card' is defined but never used         no-unused-vars
#16 83.86   Line 10:3:  'CardContent' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/Tournaments/EnhancedMatchForm.js
#16 83.86   Line 151:6:    React Hook useEffect has a missing dependency: 'mapExtractedDataToForm'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86   Line 437:9:    'prepareMatchDataForApi' is assigned a value but never used                                                                no-unused-vars
#16 83.86   Line 734:9:    'markFieldValidated' is assigned a value but never used                                                                    no-unused-vars
#16 83.86   Line 781:11:   'team1Wickets' is assigned a value but never used                                                                          no-unused-vars
#16 83.86   Line 1011:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
#16 83.86   Line 1011:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
#16 83.86   Line 1016:30:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
#16 83.86   Line 1016:90:  Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations                               no-mixed-operators
#16 83.86
#16 83.86 src/components/Tournaments/MatchDetail.js
#16 83.86   Line 10:3:   'Grid' is defined but never used              no-unused-vars
#16 83.86   Line 11:3:   'Divider' is defined but never used           no-unused-vars
#16 83.86   Line 18:3:   'Tooltip' is defined but never used           no-unused-vars
#16 83.86   Line 26:3:   'Accordion' is defined but never used         no-unused-vars
#16 83.86   Line 27:3:   'AccordionSummary' is defined but never used  no-unused-vars
#16 83.86   Line 28:3:   'AccordionDetails' is defined but never used  no-unused-vars
#16 83.86   Line 40:17:  'ExpandMoreIcon' is defined but never used    no-unused-vars
#16 83.86
#16 83.86 src/components/Tournaments/MatchFormStep.js
#16 83.86   Line 110:11:  'isOcrFailed' is assigned a value but never used   no-unused-vars
#16 83.86   Line 389:13:  'playerIssues' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/Tournaments/OCRDataViewer.js
#16 83.86   Line 18:3:  'TextField' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/Tournaments/PlayerMatchingViewer.js
#16 83.86   Line 165:57:  'candidates' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/Tournaments/ScorecardUploadStep.js
#16 83.86   Line 23:12:  'ImageIcon' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/Training/ImageSelectionModal.js
#16 83.86   Line 13:3:   'FormControlLabel' is defined but never used  no-unused-vars
#16 83.86   Line 26:12:  'ImageIcon' is defined but never used         no-unused-vars
#16 83.86
#16 83.86 src/components/Training/ScorecardLabeler.js
#16 83.86   Line 2:10:    'API_URL' is defined but never used                    no-unused-vars
#16 83.86   Line 1041:9:  'getCategoryColor' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/admin/TemplateBuilder.js
#16 83.86   Line 6:9:    'imageRef' is assigned a value but never used                                                                    no-unused-vars
#16 83.86   Line 16:10:  'isResizing' is assigned a value but never used                                                                  no-unused-vars
#16 83.86   Line 16:22:  'setIsResizing' is assigned a value but never used                                                               no-unused-vars
#16 83.86   Line 872:7:  Expected a default case                                                                                          default-case
#16 83.86   Line 960:6:  React Hook useEffect has a missing dependency: 'redrawCanvas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/components/auction/AuctionPlayerCard.js
#16 83.86   Line 3:3:  'Box' is defined but never used         no-unused-vars
#16 83.86   Line 6:3:  'Typography' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/auction/BidActivityFeed.js
#16 83.86   Line 13:3:   'IconButton' is defined but never used         no-unused-vars
#16 83.86   Line 20:18:  'ArrowUpwardIcon' is defined but never used    no-unused-vars
#16 83.86   Line 21:20:  'ArrowDownwardIcon' is defined but never used  no-unused-vars
#16 83.86   Line 22:17:  'AccessTimeIcon' is defined but never used     no-unused-vars
#16 83.86   Line 24:18:  'TrophyIcon' is defined but never used         no-unused-vars
#16 83.86
#16 83.86 src/components/auction/PlayerAuctionStatus.js
#16 83.86   Line 1:38:   'useRef' is defined but never used                  no-unused-vars
#16 83.86   Line 10:3:   'LinearProgress' is defined but never used          no-unused-vars
#16 83.86   Line 17:3:   'Tooltip' is defined but never used                 no-unused-vars
#16 83.86   Line 22:18:  'ArrowUpwardIcon' is defined but never used         no-unused-vars
#16 83.86   Line 24:18:  'TrophyIcon' is defined but never used              no-unused-vars
#16 83.86   Line 151:9:  'theme' is assigned a value but never used          no-unused-vars
#16 83.86   Line 202:9:  'isSmallScreen' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/dashboard/ResponsiveDashboardCard.js
#16 83.86   Line 3:3:   'Box' is defined but never used                no-unused-vars
#16 83.86   Line 47:9:  'isTablet' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/layout/NewLayout.js
#16 83.86   Line 41:8:   'Brightness4Icon' is defined but never used           no-unused-vars
#16 83.86   Line 42:8:   'Brightness7Icon' is defined but never used           no-unused-vars
#16 83.86   Line 45:8:   'AccountBalanceWalletIcon' is defined but never used  no-unused-vars
#16 83.86   Line 171:9:  'isDark' is assigned a value but never used           no-unused-vars
#16 83.86
#16 83.86 src/components/players/CardGallery.js
#16 83.86   Line 5:3:  'Typography' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/players/ImportPlayersDialog.js
#16 83.86   Line 21:3:  'Stack' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/components/ui/ResponsiveCard.js
#16 83.86   Line 9:3:   'Box' is defined but never used                no-unused-vars
#16 83.86   Line 40:9:  'isTablet' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/Admin/OcrSettings.js
#16 83.86   Line 8:3:   'Card' is defined but never used            no-unused-vars
#16 83.86   Line 9:3:   'CardHeader' is defined but never used      no-unused-vars
#16 83.86   Line 10:3:  'CardContent' is defined but never used     no-unused-vars
#16 83.86   Line 11:3:  'TextField' is defined but never used       no-unused-vars
#16 83.86   Line 13:3:  'Divider' is defined but never used         no-unused-vars
#16 83.86   Line 48:9:  'theme' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/Admin/PlayerManagement.js
#16 83.86   Line 8:3:    'Card' is defined but never used                      no-unused-vars
#16 83.86   Line 9:3:    'CardContent' is defined but never used               no-unused-vars
#16 83.86   Line 21:3:   'IconButton' is defined but never used                no-unused-vars
#16 83.86   Line 30:11:  'EditIcon' is defined but never used                  no-unused-vars
#16 83.86   Line 35:15:  'CheckBoxIcon' is defined but never used              no-unused-vars
#16 83.86   Line 36:27:  'CheckBoxOutlineBlankIcon' is defined but never used  no-unused-vars
#16 83.86   Line 53:10:  'filteredPlayers' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/Auction/AuctionListings.js
#16 83.86   Line 32:8:   'MoneyIcon' is defined but never used                                                                                      no-unused-vars
#16 83.86   Line 42:3:   'deleteAuction' is defined but never used                                                                                  no-unused-vars
#16 83.86   Line 49:7:   'AuctionCard' is assigned a value but never used                                                                           no-unused-vars
#16 83.86   Line 443:6:  React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Auction/LiveAuctionDashboard.js
#16 83.86   Line 15:3:    'Button' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
#16 83.86   Line 21:3:    'useMediaQuery' is defined but never used                                                                                                                                                                                                                          no-unused-vars
#16 83.86   Line 28:12:   'GavelIcon' is defined but never used                                                                                                                                                                                                                              no-unused-vars
#16 83.86   Line 29:17:   'AccessTimeIcon' is defined but never used                                                                                                                                                                                                                         no-unused-vars
#16 83.86   Line 36:10:   'format' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
#16 83.86   Line 38:10:   'motion' is defined but never used                                                                                                                                                                                                                                 no-unused-vars
#16 83.86   Line 65:10:   'bidLoading' is assigned a value but never used                                                                                                                                                                                                                    no-unused-vars
#16 83.86   Line 149:32:  The ref value 'socketRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'socketRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
#16 83.86   Line 157:6:   React Hook useEffect has a missing dependency: 'selectedAuction'. Either include it or remove the dependency array                                                                                                                                                 react-hooks/exhaustive-deps
#16 83.86   Line 217:9:   'handleNewBid' is assigned a value but never used                                                                                                                                                                                                                  no-unused-vars
#16 83.86   Line 423:6:   React Hook useEffect has a missing dependency: 'refreshActiveBidsTotal'. Either include it or remove the dependency array                                                                                                                                          react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Dashboard/EnhancedDashboard.js
#16 83.86   Line 27:9:  'isMobile' is assigned a value but never used  no-unused-vars
#16 83.86   Line 28:9:  'isTablet' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/Players/MyTeam.js
#16 83.86   Line 6:3:   'Box' is defined but never used                                                                                  no-unused-vars
#16 83.86   Line 28:6:  React Hook useEffect has a missing dependency: 'fetchPlayers'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/TeamManagement/TeamDashboard.js
#16 83.86   Line 16:3:  'CardActions' is defined but never used     no-unused-vars
#16 83.86   Line 20:3:  'ListItemAvatar' is defined but never used  no-unused-vars
#16 83.86   Line 21:3:  'Avatar' is defined but never used          no-unused-vars
#16 83.86
#16 83.86 src/pages/TeamManagement/TeamRoster.js
#16 83.86   Line 17:3:  'IconButton' is defined but never used         no-unused-vars
#16 83.86   Line 28:3:  'Tooltip' is defined but never used            no-unused-vars
#16 83.86   Line 33:8:  'RemoveIcon' is defined but never used         no-unused-vars
#16 83.86   Line 35:8:  'InfoIcon' is defined but never used           no-unused-vars
#16 83.86   Line 45:9:  'isMobile' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/TeamManagement/TeamSettings.js
#16 83.86   Line 16:3:  'CardContent' is defined but never used  no-unused-vars
#16 83.86   Line 24:3:  'Chip' is defined but never used         no-unused-vars
#16 83.86   Line 30:8:  'EditIcon' is defined but never used     no-unused-vars
#16 83.86
#16 83.86 src/pages/Tournaments/SimpleCameraTest.js
#16 83.86   Line 21:10:  'cameraPermission' is assigned a value but never used                                                          no-unused-vars
#16 83.86   Line 125:6:  React Hook useEffect has a missing dependency: 'stopCamera'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Tournaments/SimplifiedGuidedCapture.js
#16 83.86   Line 611:6:   React Hook useCallback has a missing dependency: 'debugMode'. Either include it or remove the dependency array                                                                                                                                                                                 react-hooks/exhaustive-deps
#16 83.86   Line 756:26:  Assignments to the 'tournamentId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect  react-hooks/exhaustive-deps
#16 83.86   Line 757:21:  Assignments to the 'matchId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect       react-hooks/exhaustive-deps
#16 83.86   Line 778:6:   React Hook useEffect has a missing dependency: 'initCamera'. Either include it or remove the dependency array                                                                                                                                                                                  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Tournaments/TestGuidedCapture.js
#16 83.86   Line 22:10:  'Link' is defined but never used                        no-unused-vars
#16 83.86   Line 56:9:   'handleUploadError' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/pages/Tournaments/TournamentDetail.js
#16 83.86   Line 31:3:   'Tooltip' is defined but never used                                                                                 no-unused-vars
#16 83.86   Line 32:3:   'IconButton' is defined but never used                                                                              no-unused-vars
#16 83.86   Line 46:13:  'PersonIcon' is defined but never used                                                                              no-unused-vars
#16 83.86   Line 74:6:   React Hook useEffect has a missing dependency: 'fetchTournament'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Tournaments/TournamentForm.js
#16 83.86   Line 18:3:  'IconButton' is defined but never used                                                                                                 no-unused-vars
#16 83.86   Line 73:6:  React Hook useEffect has missing dependencies: 'fetchTournament' and 'isEditMode'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Tournaments/TournamentList.js
#16 83.86   Line 21:3:  'IconButton' is defined but never used                                                                               no-unused-vars
#16 83.86   Line 22:3:  'Tooltip' is defined but never used                                                                                  no-unused-vars
#16 83.86   Line 54:6:  React Hook useEffect has a missing dependency: 'fetchTournaments'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/Training/ScorecardTrainingPage.js
#16 83.86   Line 46:6:  React Hook useEffect has missing dependencies: 'fetchModelStatus' and 'fetchServerStatus'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/pages/TransferMarket/TransferMarket.js
#16 83.86   Line 10:3:  'CircularProgress' is defined but never used   no-unused-vars
#16 83.86   Line 13:3:  'CardContent' is defined but never used        no-unused-vars
#16 83.86   Line 14:3:  'CardMedia' is defined but never used          no-unused-vars
#16 83.86   Line 15:3:  'CardActions' is defined but never used        no-unused-vars
#16 83.86   Line 16:3:  'Chip' is defined but never used               no-unused-vars
#16 83.86   Line 22:3:  'Tooltip' is defined but never used            no-unused-vars
#16 83.86   Line 36:8:  'InfoIcon' is defined but never used           no-unused-vars
#16 83.86   Line 44:9:  'isMobile' is assigned a value but never used  no-unused-vars
#16 83.86
#16 83.86 src/services/exportService.js
#16 83.86   Line 46:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/mlTrainingService.js
#16 83.86   Line 103:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/ocrService.js
#16 83.86   Line 106:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/playerMatchingService.js
#16 83.86   Line 271:11:  'noMatch' is assigned a value but never used                    no-unused-vars
#16 83.86   Line 335:1:   Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/playerPhotoService.js
#16 83.86   Line 42:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/playerService.js
#16 83.86   Line 194:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/scorecardService.js
#16 83.86   Line 60:7:   Expected an error object to be thrown                           no-throw-literal
#16 83.86   Line 65:7:   Expected an error object to be thrown                           no-throw-literal
#16 83.86   Line 67:7:   Expected an error object to be thrown                           no-throw-literal
#16 83.86   Line 69:7:   Expected an error object to be thrown                           no-throw-literal
#16 83.86   Line 71:7:   Expected an error object to be thrown                           no-throw-literal
#16 83.86   Line 112:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/teamService.js
#16 83.86   Line 189:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/tournamentService.js
#16 83.86   Line 147:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/services/trainingService.js
#16 83.86   Line 528:5:    Unreachable code                      no-unreachable
#16 83.86   Line 1248:22:  'box' was used before it was defined  no-use-before-define
#16 83.86
#16 83.86 src/services/uploadService.js
#16 83.86   Line 153:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 src/theme/EnhancedThemeProvider.js
#16 83.86   Line 49:6:  React Hook useMemo has a missing dependency: 'themeOptions'. Either include it or remove the dependency array                                        react-hooks/exhaustive-deps
#16 83.86   Line 65:7:  React Hook useMemo has missing dependencies: 'handleSetTheme', 'themeNames', and 'themeOptions'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
#16 83.86
#16 83.86 src/theme/ThemeOptions.js
#16 83.86   Line 1:10:  'alpha' is defined but never used  no-unused-vars
#16 83.86
#16 83.86 src/utils/auth.js
#16 83.86   Line 60:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export
#16 83.86
#16 83.86 Search for the keywords to learn more about each warning.
#16 83.86 To ignore, add // eslint-disable-next-line to the line before.
#16 83.86
#16 83.86 File sizes after gzip:
#16 83.86
#16 83.91   456.99 kB  build/static/js/main.27ec6104.js
#16 83.91   7.04 kB    build/static/css/main.1ff9a38f.css
#16 83.91   1.72 kB    build/static/js/206.1430d834.chunk.js
#16 83.91
#16 83.91 The project was built assuming it is hosted at /.
#16 83.91 You can control this with the homepage field in your package.json.
#16 83.91
#16 83.91 The build folder is ready to be deployed.
#16 83.91 You may serve it with a static server:
#16 83.91
#16 83.91   npm install -g serve
#16 83.91   serve -s build
#16 83.91
#16 83.91 Find out more about deployment here:
#16 83.91
#16 83.91   https://cra.link/deployment
#16 83.91
#16 DONE 84.4s
#17 [server 8/9] COPY --from=client-build /app/client/build ./public
#17 DONE 0.1s
#18 [server 9/9] RUN mkdir -p uploads/scorecards
#18 DONE 0.2s
#19 exporting to image
#19 exporting layers
#19 exporting layers 7.1s done
#19 writing image sha256:c226ae9b33add66390df786084832c6d2a327376e4cc496089498ccc98b5f3a3 done
#19 naming to docker.io/library/rpl-rplapp-kwaaag done
#19 DONE 7.1s
Docker Deployed: ✅