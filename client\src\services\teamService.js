import axios from 'axios';
import { API_URL } from '../config';

// Base API configuration for team endpoints
const TeamAPI = axios.create({
  baseURL: `${API_URL}/teams`
});

// Add auth token to requests if available
TeamAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');

  if (token) {
    config.headers['x-auth-token'] = token;
  }

  return config;
});

/**
 * Get team settings for the current user
 * @returns {Promise} Team settings data
 */
export const getTeamSettings = async () => {
  try {
    const response = await TeamAPI.get('/settings');
    return response.data;
  } catch (error) {
    console.error('Error fetching team settings:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Update team settings
 * @param {Object} teamData - Team settings data
 * @returns {Promise} Updated team data
 */
export const updateTeamSettings = async (teamData) => {
  try {
    const response = await TeamAPI.put('/settings', teamData);
    return response.data;
  } catch (error) {
    console.error('Error updating team settings:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Upload team logo
 * @param {FormData} formData - Form data containing the logo file
 * @returns {Promise} Upload result with file path
 */
export const uploadTeamLogo = async (formData) => {
  try {
    const response = await TeamAPI.post('/logo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading team logo:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get team roster (players in the team)
 * @param {Object} params - Query parameters for pagination, sorting, etc.
 * @returns {Promise} Team roster data
 */
export const getTeamRoster = async (params) => {
  try {
    const response = await TeamAPI.get('/roster', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching team roster:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get team statistics
 * @returns {Promise} Team statistics data
 */
export const getTeamStatistics = async () => {
  try {
    const response = await TeamAPI.get('/statistics');
    return response.data;
  } catch (error) {
    console.error('Error fetching team statistics:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Add player to team
 * @param {string} playerId - ID of the player to add
 * @returns {Promise} Result of the operation
 */
export const addPlayerToTeam = async (playerId) => {
  try {
    const response = await TeamAPI.post('/roster/add', { playerId });
    return response.data;
  } catch (error) {
    console.error('Error adding player to team:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Remove player from team
 * @param {string} playerId - ID of the player to remove
 * @returns {Promise} Result of the operation
 */
export const removePlayerFromTeam = async (playerId) => {
  try {
    const response = await TeamAPI.post('/roster/remove', { playerId });
    return response.data;
  } catch (error) {
    console.error('Error removing player from team:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get team budget information
 * @returns {Promise} Team budget data
 */
export const getTeamBudget = async () => {
  try {
    const response = await TeamAPI.get('/budget');
    return response.data;
  } catch (error) {
    console.error('Error fetching team budget:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Update team budget allocation
 * @param {Object} budgetData - Budget allocation data
 * @returns {Promise} Updated budget data
 */
export const updateTeamBudget = async (budgetData) => {
  try {
    const response = await TeamAPI.put('/budget', budgetData);
    return response.data;
  } catch (error) {
    console.error('Error updating team budget:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Create a team for the user
 * @param {Object} teamData - Optional team data (name, etc.)
 * @returns {Promise} Created team data
 */
export const createTeam = async (teamData = {}) => {
  try {
    // Get user info from localStorage if available
    let userData = {};
    try {
      const userJson = localStorage.getItem('user');
      if (userJson) {
        userData = JSON.parse(userJson);
      }
    } catch (e) {
      console.error('Error parsing user data from localStorage:', e);
    }

    // Use team name from user data if not provided
    const payload = {
      ...teamData,
      teamName: teamData.teamName || userData.teamName || 'My Team'
    };

    console.log('Creating team with data:', payload);
    const response = await TeamAPI.post('/create', payload);
    return response.data;
  } catch (error) {
    console.error('Error creating team:', error);
    throw error.response?.data || error.message;
  }
};

export default {
  getTeamSettings,
  updateTeamSettings,
  uploadTeamLogo,
  getTeamRoster,
  getTeamStatistics,
  addPlayerToTeam,
  removePlayerFromTeam,
  getTeamBudget,
  updateTeamBudget,
  createTeam
};
