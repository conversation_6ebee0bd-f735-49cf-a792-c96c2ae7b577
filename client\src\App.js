import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './context/AuthContext';
import { useAuth } from './hooks/useAuth';
import NewLayout from './components/layout/NewLayout';
import EnhancedThemeProvider from './theme/EnhancedThemeProvider';
import { LayoutProvider } from './context/LayoutContext';
import './App.css';
import './styles/adminDashboard.css';
import './styles/mobileDashboard.css';
import initPlayerFormFix from './utils/playerFormFix';

// Import actual page components
import HomePage from './pages/Home';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import Profile from './pages/Profile';
import Dashboard from './pages/Dashboard';
import EnhancedDashboard from './pages/Dashboard/EnhancedDashboard';
import AdminDashboard from './pages/Admin';
import PlayerManagement from './pages/Admin/PlayerManagement';
import TemplateBuilder from './components/admin/TemplateBuilder';
import MyTeam from './pages/Players/MyTeam'; // Import MyTeam component
import TestPlayerCreation from './pages/Admin/TestPlayerCreation'; // Import test component
import TestIplImport from './components/TestIplImport'; // Import IPL import test component
import TestGuidedCapture from './pages/Tournaments/TestGuidedCapture'; // Import guided capture test page
import SimpleCameraTest from './pages/Tournaments/SimpleCameraTest'; // Import simple camera test
import SimplifiedGuidedCapture from './pages/Tournaments/SimplifiedGuidedCapture'; // Import simplified guided capture
import OcrComparison from './pages/OcrComparison'; // Import OCR comparison page
import OcrSettings from './pages/Admin/OcrSettings'; // Import OCR settings page

// Training
import ScorecardTrainingPage from './pages/Training/ScorecardTrainingPage'; // Import scorecard training page

// Team Management Pages
import TeamDashboard from './pages/TeamManagement/TeamDashboard';
import TeamSettings from './pages/TeamManagement/TeamSettings';
import TeamRoster from './pages/TeamManagement/TeamRoster';
// We'll uncomment this as we implement it
// import TeamBudget from './pages/TeamManagement/TeamBudget';

// Transfer Market
import TransferMarket from './pages/TransferMarket';

// Auction
import AuctionListings from './pages/Auction';
import AuctionManagement from './pages/Admin/AuctionManagement';
import LiveAuctionDashboard from './pages/Auction/LiveAuctionDashboard';
import TestAuctionPage from './components/auction/TestAuctionPage';

// Tournament
import TournamentList from './pages/Tournaments/TournamentList';
import TournamentDetail from './pages/Tournaments/TournamentDetail';
import TournamentForm from './pages/Tournaments/TournamentForm';

// Create React Query client
const queryClient = new QueryClient();

// Protected route component
const ProtectedRoute = ({ children, roles }) => {
  const { user, loading } = useAuth();

  if (loading) return <div>Loading...</div>;

  if (!user) return <Navigate to="/login" replace />;

  if (roles && !roles.includes(user.role)) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};

function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <Profile />
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <EnhancedDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard-old"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin"
        element={
          <ProtectedRoute roles={['admin']}>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/templates"
        element={
          <ProtectedRoute roles={['admin']}>
            <TemplateBuilder />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/players"
        element={
          <ProtectedRoute roles={['admin']}>
            <PlayerManagement />
          </ProtectedRoute>
        }
      />
      <Route
        path="/my-team"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <MyTeam />
          </ProtectedRoute>
        }
      />

      {/* Team Management Routes */}
      <Route
        path="/team"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <TeamDashboard />
          </ProtectedRoute>
        }
      />
      {/* Team Settings route is now active */}
      <Route
        path="/team/settings"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <TeamSettings />
          </ProtectedRoute>
        }
      />
      {/* Team Roster route is now active */}
      <Route
        path="/team/roster"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <TeamRoster />
          </ProtectedRoute>
        }
      />

      {/* Transfer Market route */}
      <Route
        path="/market"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <TransferMarket />
          </ProtectedRoute>
        }
      />

      {/* Auction routes */}
      <Route
        path="/auctions"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <AuctionListings />
          </ProtectedRoute>
        }
      />
      <Route
        path="/auctions/live"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <LiveAuctionDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/auctions"
        element={
          <ProtectedRoute roles={['admin']}>
            <AuctionManagement />
          </ProtectedRoute>
        }
      />
      <Route
        path="/test/auction"
        element={
          <ProtectedRoute>
            <TestAuctionPage />
          </ProtectedRoute>
        }
      />
      {/* We'll uncomment this route as we implement it
      <Route
        path="/team/budget"
        element={
          <ProtectedRoute roles={['team_owner']}>
            <TeamBudget />
          </ProtectedRoute>
        }
      />
      */}

      <Route
        path="/admin/test-player-creation"
        element={
          <ProtectedRoute roles={['admin']}>
            <TestPlayerCreation />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/test-ipl-import"
        element={
          <ProtectedRoute roles={['admin']}>
            <TestIplImport />
          </ProtectedRoute>
        }
      />

      {/* Tournament routes */}
      <Route
        path="/tournaments"
        element={<TournamentList />}
      />
      <Route
        path="/tournaments/:id"
        element={<TournamentDetail />}
      />
      <Route
        path="/tournaments/create"
        element={
          <ProtectedRoute roles={['admin']}>
            <TournamentForm />
          </ProtectedRoute>
        }
      />
      <Route
        path="/tournaments/:id/edit"
        element={
          <ProtectedRoute roles={['admin']}>
            <TournamentForm />
          </ProtectedRoute>
        }
      />

      {/* Scorecard Capture routes */}
      <Route
        path="/test/guided-capture"
        element={
          <ProtectedRoute>
            <TestGuidedCapture />
          </ProtectedRoute>
        }
      />
      <Route
        path="/scorecard-capture"
        element={
          <ProtectedRoute>
            <SimplifiedGuidedCapture />
          </ProtectedRoute>
        }
      />
      <Route
        path="/test/simplified-guided"
        element={
          <ProtectedRoute>
            <SimplifiedGuidedCapture />
          </ProtectedRoute>
        }
      />

      {/* Training routes */}
      <Route
        path="/admin/training/scorecard"
        element={
          <ProtectedRoute roles={['admin']}>
            <ScorecardTrainingPage />
          </ProtectedRoute>
        }
      />

      {/* OCR Comparison route */}
      <Route
        path="/admin/ocr-comparison"
        element={
          <ProtectedRoute roles={['admin']}>
            <OcrComparison />
          </ProtectedRoute>
        }
      />

      {/* OCR Settings route */}
      <Route
        path="/admin/ocr-settings"
        element={
          <ProtectedRoute roles={['admin']}>
            <OcrSettings />
          </ProtectedRoute>
        }
      />
    </Routes>
  );
}

function App() {
  // Initialize the player form fix when the app loads
  useEffect(() => {
    // Apply the player form fix
    const cleanup = initPlayerFormFix();

    // Clean up when the component unmounts
    return () => {
      if (typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <EnhancedThemeProvider>
        <AuthProvider>
          <LayoutProvider>
            <Router>
              <NewLayout>
                <AppRoutes />
              </NewLayout>
            </Router>
          </LayoutProvider>
        </AuthProvider>
      </EnhancedThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
