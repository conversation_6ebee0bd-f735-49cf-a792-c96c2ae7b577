/**
 * <PERSON><PERSON>t to fix team owners who don't have teams
 * 
 * This script:
 * 1. Finds all users with role 'team_owner' who don't have a team reference
 * 2. Creates a team for each of these users
 * 3. Updates the user with the team reference
 * 
 * Run with: node scripts/fixTeams.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Team = require('../models/Team');

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/cricket24';
    console.log('Attempting to connect to MongoDB at:', mongoURI);
    
    await mongoose.connect(mongoURI);
    console.log('MongoDB Connected:', mongoose.connection.host);
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  }
};

// Fix teams for team owners
const fixTeams = async () => {
  try {
    // Find all team owners
    const teamOwners = await User.find({ role: 'team_owner' });
    console.log(`Found ${teamOwners.length} team owners`);
    
    let created = 0;
    let alreadyHasTeam = 0;
    let errors = 0;
    
    // Process each team owner
    for (const user of teamOwners) {
      try {
        // Check if user already has a team reference
        if (user.team) {
          // Verify the team exists
          const team = await Team.findById(user.team);
          if (team) {
            console.log(`User ${user.username} already has a valid team reference: ${team._id}`);
            alreadyHasTeam++;
            continue;
          }
        }
        
        // Check if a team exists for this user by owner field
        const existingTeam = await Team.findOne({ owner: user._id });
        if (existingTeam) {
          console.log(`User ${user.username} has a team by owner field: ${existingTeam._id}`);
          
          // Update user with team reference
          await User.findByIdAndUpdate(user._id, { team: existingTeam._id });
          console.log(`Updated user ${user.username} with team reference`);
          alreadyHasTeam++;
          continue;
        }
        
        // Create a new team for this user
        console.log(`Creating team for user ${user.username}`);
        const team = new Team({
          owner: user._id,
          teamName: user.teamName || `${user.username}'s Team`,
          description: '',
          logo: '/uploads/teams/default-logo.png',
          primaryColor: '#1e88e5',
          secondaryColor: '#bbdefb',
          colorScheme: 'Blue',
          homeGround: '',
          foundedYear: new Date().getFullYear(),
          slogan: '',
          budget: {
            totalBudget: 10000, // Default budget
            allocations: {
              playerAcquisition: { percentage: 60, amount: 6000 },
              playerDevelopment: { percentage: 20, amount: 2000 },
              teamOperations: { percentage: 10, amount: 1000 },
              marketing: { percentage: 5, amount: 500 },
              reserve: { percentage: 5, amount: 500 }
            },
            transactions: []
          }
        });
        
        await team.save();
        console.log(`Team created with ID: ${team._id}`);
        
        // Update user with team reference
        await User.findByIdAndUpdate(user._id, { team: team._id });
        console.log(`Updated user ${user.username} with team reference`);
        
        created++;
      } catch (err) {
        console.error(`Error processing user ${user.username}:`, err);
        errors++;
      }
    }
    
    console.log('\nSummary:');
    console.log(`Total team owners: ${teamOwners.length}`);
    console.log(`Already had teams: ${alreadyHasTeam}`);
    console.log(`Teams created: ${created}`);
    console.log(`Errors: ${errors}`);
    
  } catch (err) {
    console.error('Error fixing teams:', err);
  } finally {
    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log('MongoDB disconnected');
  }
};

// Run the script
connectDB()
  .then(() => fixTeams())
  .catch(err => {
    console.error('Script error:', err);
    process.exit(1);
  });
