#!/usr/bin/env python3
"""Simple OCR Script for Cricket Scorecards

A clean, minimal OCR implementation using PaddleOCR
with structured cricket scorecard data extraction
"""

import sys
import json
import os
import re
from typing import List, Dict, Any
from PIL import Image
import cv2
import numpy as np

def extract_text_from_region(ocr, image, region):
    """Extract text from a specific region of the image"""
    try:
        # Get region coordinates
        x, y, width, height = region['x'], region['y'], region['width'], region['height']

        # Load image if it's a path, otherwise assume it's already loaded
        if isinstance(image, str):
            img = cv2.imread(image)
        else:
            img = image

        if img is None:
            return []

        # Extract the region
        region_img = img[y:y+height, x:x+width]

        if region_img.size == 0:
            return []

        # Run OCR on the region
        result = ocr.predict(region_img)

        # Extract text from results
        texts = []
        if result and len(result) > 0:
            # Handle new PaddleOCR API format
            if isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
                # New API format
                for page_result in result:
                    if 'rec_texts' in page_result:
                        rec_texts = page_result['rec_texts']
                        rec_scores = page_result.get('rec_scores', [])

                        for i, text in enumerate(rec_texts):
                            confidence = rec_scores[i] if i < len(rec_scores) else 1.0
                            if confidence > 0.5 and text.strip():
                                # Clean text to handle Unicode issues
                                clean_text = text.strip().encode('ascii', 'ignore').decode('ascii')
                                if clean_text:
                                    texts.append({
                                        'text': clean_text,
                                        'confidence': confidence
                                    })
            else:
                # Old API format
                for line in result[0] if result[0] else []:
                    try:
                        if isinstance(line, (list, tuple)) and len(line) >= 2:
                            text_info = line[1]
                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 1:
                                text = text_info[0]
                                confidence = text_info[1] if len(text_info) > 1 else 1.0
                            else:
                                text = str(text_info)
                                confidence = 1.0

                            if confidence > 0.5 and text.strip():
                                texts.append({
                                    'text': text.strip(),
                                    'confidence': confidence
                                })
                    except Exception as e:
                        print(f"Error parsing OCR line: {e}", file=sys.stderr)
                        continue

        return texts

    except Exception as e:
        print(f"Error extracting text from region: {e}", file=sys.stderr)
        return []

def extract_text_with_coordinates(ocr, image_path):
    """Extract text with coordinates using PaddleOCR"""
    try:
        # Run OCR on the image
        result = ocr.predict(image_path)
        
        # Debug: Print result structure
        print(f"Result type: {type(result)}", file=sys.stderr)
        print(f"Result length: {len(result)}", file=sys.stderr)
        if result and len(result) > 0:
            print(f"First element type: {type(result[0])}", file=sys.stderr)
            if isinstance(result[0], dict):
                print(f"Dict keys: {result[0].keys()}", file=sys.stderr)
            elif isinstance(result[0], list) and len(result[0]) > 0:
                print(f"First inner element type: {type(result[0][0])}", file=sys.stderr)
                if len(result[0][0]) > 0:
                    print(f"First inner element length: {len(result[0][0])}", file=sys.stderr)
        
        # Extract text elements with coordinates
        text_elements = []
        
        if result and len(result) > 0:
            # Handle different result formats
            if isinstance(result[0], list):
                # Old API format
                print(f"Processing old API format with {len(result[0])} elements", file=sys.stderr)
                for line in result[0]:
                    try:
                        if isinstance(line, (list, tuple)) and len(line) >= 2:
                            bbox = line[0]  # Bounding box coordinates
                            text_info = line[1]
                            
                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text = text_info[0].strip()
                                confidence = float(text_info[1])
                                
                                if text and confidence > 0.5:
                                    # Convert bbox to x, y, width, height format
                                    if isinstance(bbox, (list, tuple)) and len(bbox) > 0:
                                        x_coords = [float(point[0]) for point in bbox if isinstance(point, (list, tuple)) and len(point) >= 2]
                                        y_coords = [float(point[1]) for point in bbox if isinstance(point, (list, tuple)) and len(point) >= 2]
                                        
                                        if x_coords and y_coords:  # Make sure we have valid coordinates
                                            left = int(min(x_coords))
                                            top = int(min(y_coords))
                                            width = int(max(x_coords) - min(x_coords))
                                            height = int(max(y_coords) - min(y_coords))
                                            
                                            text_elements.append({
                                                'text': text,
                                                'x': left,
                                                'y': top,
                                                'width': width,
                                                'height': height,
                                                'confidence': confidence
                                            })
                    except Exception as e:
                        print(f"Error processing old API format line: {e}", file=sys.stderr)
                        continue
            elif isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
                # New API format
                print(f"Processing new API format", file=sys.stderr)
                for page_result in result:
                    print(f"Page result keys: {page_result.keys()}", file=sys.stderr)
                    
                    # Check if we have the expected fields
                    has_texts = 'rec_texts' in page_result
                    has_polys = 'rec_polys' in page_result
                    has_boxes = 'boxes' in page_result
                    
                    print(f"Has texts: {has_texts}, Has polys: {has_polys}, Has boxes: {has_boxes}", file=sys.stderr)
                    
                    if has_texts:
                        rec_texts = page_result['rec_texts']
                        rec_scores = page_result.get('rec_scores', [])
                        
                        # Try to get coordinates from different possible fields
                        coordinates = None
                        if has_polys:
                            coordinates = page_result['rec_polys']
                            print(f"Using rec_polys with {len(coordinates)} elements", file=sys.stderr)
                            if len(coordinates) > 0:
                                print(f"First coordinate: {coordinates[0]}", file=sys.stderr)
                        elif has_boxes:
                            coordinates = page_result['boxes']
                            print(f"Using boxes with {len(coordinates)} elements", file=sys.stderr)
                        
                        for i, text in enumerate(rec_texts):
                            if i >= len(rec_texts):
                                continue
                                
                            confidence = float(rec_scores[i]) if i < len(rec_scores) else 1.0
                            
                            if confidence > 0.5 and text and text.strip():
                                # Get coordinates if available
                                if coordinates and i < len(coordinates):
                                    try:
                                        coord = coordinates[i]
                                        print(f"Processing coordinate: {coord} for text: {text}", file=sys.stderr)
                                        
                                        # Handle numpy arrays or lists
                                        if hasattr(coord, 'tolist'):
                                            coord = coord.tolist()
                                        
                                        if coord and len(coord) > 0:
                                            # Handle different coordinate formats
                                            if isinstance(coord[0], (list, tuple)) or (hasattr(coord[0], '__len__') and not isinstance(coord[0], str)):
                                                # Polygon format: [[x1,y1], [x2,y2], ...]
                                                x_coords = []
                                                y_coords = []
                                                for point in coord:
                                                    if len(point) >= 2:
                                                        x_coords.append(float(point[0]))
                                                        y_coords.append(float(point[1]))
                                            else:
                                                # Box format: [x1,y1,x2,y2,...]
                                                x_coords = [float(coord[j]) for j in range(0, len(coord), 2) if j < len(coord)]
                                                y_coords = [float(coord[j+1]) for j in range(0, len(coord), 2) if j+1 < len(coord)]
                                            
                                            if x_coords and y_coords:  # Make sure we have valid coordinates
                                                left = int(min(x_coords))
                                                top = int(min(y_coords))
                                                width = int(max(x_coords) - min(x_coords))
                                                height = int(max(y_coords) - min(y_coords))
                                                
                                                text_elements.append({
                                                    'text': text.strip(),
                                                    'x': left,
                                                    'y': top,
                                                    'width': width,
                                                    'height': height,
                                                    'confidence': confidence
                                                })
                                    except Exception as e:
                                        print(f"Error processing coordinate: {e}", file=sys.stderr)
                                        continue
        
        print(f"Extracted {len(text_elements)} text elements with coordinates", file=sys.stderr)
        return text_elements
    
    except Exception as e:
        print(f"Error extracting text with coordinates: {e}", file=sys.stderr)
        return []

def parse_cricket_scorecard(texts, text_elements=None):
    """
    Parse cricket scorecard data from extracted text
    
    Args:
        texts: List of extracted text strings from the scorecard
        text_elements: Optional list of text elements with coordinates
    
    Returns:
        Dictionary containing structured cricket scorecard data
    """
    # Clean up the texts first to handle encoding issues
    cleaned_texts = []
    for text in texts:
        # Remove extra spaces between characters
        cleaned_text = re.sub(r'\s+', ' ', text).strip()
        if cleaned_text:
            cleaned_texts.append(cleaned_text)
    
    # Join all texts into a single string for easier pattern matching
    full_text = ' '.join(cleaned_texts)
    
    # Initialize result structure
    scorecard = {
        'match_info': {},
        'teams': [],
        'result': {},
        'player_of_match': None
    }
    
    # Extract match info - more flexible pattern
    match_pattern = r"([\w\s]+)\s+VS\s+([\w\s]+)\s+([\w\d]+)\s+AT\s+([\w\s]+)"
    match = re.search(match_pattern, full_text, re.IGNORECASE)
    if match:
        team1, team2, match_type, venue = match.groups()
        # Fix venue extraction by removing any team names that might have been included
        venue_clean = venue.strip()
        for team_name in ["PHOENIX", "INVINCIBLES"]:
            venue_clean = re.sub(f"\s*{team_name}\s*\d+.*$", "", venue_clean)
            
        scorecard['match_info'] = {
            'team1': team1.strip(),
            'team2': team2.strip(),
            'match_type': match_type.strip(),
            'venue': venue_clean.strip()
        }
    
    # Extract team scores - improved pattern
    team_score_pattern = r"(PHOENIX|INVINCIBLES)\s+(\d+)(?:-(\d+))?\s+OVERS:\s+(\d+(?:\.\d+)?)"
    
    for team_match in re.finditer(team_score_pattern, full_text):
        team_name, score, wickets, overs = team_match.groups()
        team_data = {
            'name': team_name.strip(),
            'score': int(score),
            'wickets': int(wickets) if wickets else 0,
            'overs': float(overs),
            'batting': [],
            'bowling': []
        }
        scorecard['teams'].append(team_data)
    
    # If we have two teams, determine which is which
    if len(scorecard['teams']) == 2:
        # Sort teams by their position in the text
        team1_pos = full_text.find(scorecard['teams'][0]['name'])
        team2_pos = full_text.find(scorecard['teams'][1]['name'], team1_pos + 1)  # Find second occurrence
        
        if team1_pos > team2_pos:
            # Swap teams to ensure team1 appears first in the text
            scorecard['teams'][0], scorecard['teams'][1] = scorecard['teams'][1], scorecard['teams'][0]
    
    # Extract batting performances - improved pattern
    batting_pattern = r"([A-Z][A-Z\s\-]+)\s+(\d+)\*?\s+\((\d+|[A-Z])\)"
    
    # Create a list of all batting performances
    all_batting = []
    
    print(f"Looking for batting performances with pattern: {batting_pattern}", file=sys.stderr)
    
    # Use text elements with coordinates if available
    if text_elements:
        print(f"Using text elements with coordinates for batting extraction", file=sys.stderr)
        # Sort elements by y-coordinate to process them in order
        sorted_elements = sorted(text_elements, key=lambda x: x['y'])
        
        # Look for batting performances by combining adjacent elements
        for i, element in enumerate(sorted_elements):
            text = element['text'].strip()
            
            # Check if this looks like a player name (all caps, multiple words or single name)
            if re.match(r'^[A-Z][A-Z\s\-]+$', text) and len(text.split()) >= 1:
                player_name = text
                runs = None
                balls = None
                
                print(f"Checking potential player: {player_name}", file=sys.stderr)
                
                # Look for runs in the next few elements
                for j in range(i + 1, min(i + 4, len(sorted_elements))):
                    next_element = sorted_elements[j]
                    next_text = next_element['text'].strip()
                    
                    print(f"  Next element: '{next_text}'", file=sys.stderr)
                    
                    # Check if this is a runs score (number, possibly with *)
                    if re.match(r'^\d+\*?$', next_text) and runs is None:
                        runs = next_text
                        print(f"  Found runs: {runs}", file=sys.stderr)
                        
                        # Look for balls in the next element
                        for k in range(j + 1, min(j + 3, len(sorted_elements))):
                            balls_element = sorted_elements[k]
                            balls_text = balls_element['text'].strip()
                            
                            print(f"    Checking balls element: '{balls_text}'", file=sys.stderr)
                            
                            # Check if this is balls (number in parentheses or letter like L)
                            if re.match(r'^\((\d+|[A-Z])\)$', balls_text):
                                balls = balls_text[1:-1]  # Remove parentheses
                                
                                print(f"Found batting performance: {player_name} {runs} ({balls})", file=sys.stderr)
                                
                                # Handle special case where balls might be a letter (like 'L' for LBW)
                                try:
                                    balls_count = int(balls) if balls.isdigit() else 0
                                except (ValueError, AttributeError):
                                    balls_count = 0
                                    
                                batting_data = {
                                    'name': player_name.strip(),
                                    'runs': int(runs.replace('*', '')),
                                    'balls': balls_count,
                                    'dismissal': None if balls.isdigit() else balls,
                                    'position': element['y']  # Use y-coordinate for vertical position
                                }
                                all_batting.append(batting_data)
                                break
                        break
    else:
        print(f"Using regex on full text for batting extraction", file=sys.stderr)
        # Use regex on full text if no coordinates available
        for batting_match in re.finditer(batting_pattern, full_text):
            player_name, runs, balls = batting_match.groups()
            print(f"Found batting match: {player_name} {runs} ({balls})", file=sys.stderr)
            # Handle special case where balls might be a letter (like 'L' for LBW)
            try:
                balls_count = int(balls) if balls.isdigit() else 0
            except (ValueError, AttributeError):
                balls_count = 0
                
            batting_data = {
                'name': player_name.strip(),
                'runs': int(runs),
                'balls': balls_count,
                'dismissal': None if balls.isdigit() else balls,
                'position': full_text.find(player_name)  # Store position for sorting
            }
            all_batting.append(batting_data)
    
    print(f"Found {len(all_batting)} batting performances", file=sys.stderr)
    
    # Sort batting performances by their position in the text
    all_batting.sort(key=lambda x: x['position'])
    
    # Find the dividing point between teams
    if len(scorecard['teams']) == 2:
        # Find the second occurrence of the second team name
        team1_name = scorecard['teams'][0]['name']
        team2_name = scorecard['teams'][1]['name']
        
        print(f"Team 1: {team1_name}, Team 2: {team2_name}", file=sys.stderr)
        
        # Find first occurrence of team2_name
        team_divider = full_text.find(team2_name)
        # Find second occurrence (after the first team's batting)
        second_occurrence = full_text.find(team2_name, team_divider + len(team2_name))
        
        if second_occurrence > 0:
            team_divider = second_occurrence
        
        print(f"Team divider position: {team_divider}", file=sys.stderr)
        
        # If using text elements with coordinates, find the y-coordinate divider
        if text_elements:
            # Find the text element containing the second team name
            team2_elements = [elem for elem in text_elements if team2_name in elem['text']]
            if len(team2_elements) >= 2:  # We need at least two occurrences
                # Use the y-coordinate of the second occurrence as the divider
                team_divider = team2_elements[1]['y']
                print(f"Using coordinate-based team divider: {team_divider}", file=sys.stderr)
        
        # Assign batting performances to teams
        for batting in all_batting:
            # Remove the position field used for sorting
            position = batting.pop('position')
            
            print(f"Assigning {batting['name']} (position {position}) to team", file=sys.stderr)
            
            if position < team_divider:
                scorecard['teams'][0]['batting'].append(batting)
                print(f"Assigned {batting['name']} to {team1_name}", file=sys.stderr)
            else:
                scorecard['teams'][1]['batting'].append(batting)
                print(f"Assigned {batting['name']} to {team2_name}", file=sys.stderr)
    elif len(scorecard['teams']) == 1:
        # If only one team, assign all batting to that team
        for batting in all_batting:
            batting.pop('position')  # Remove position field
            scorecard['teams'][0]['batting'].append(batting)
    
    # Extract bowling performances - improved pattern
    bowling_pattern = r"([A-Z][A-Z\s\-]+)\s+(\d+)-(\d+)"
    
    # Create a list of all bowling performances
    all_bowling = []
    
    # Use text elements with coordinates if available
    if text_elements:
        # Find bowling performances in text elements
        for element in text_elements:
            text = element['text']
            match = re.search(bowling_pattern, text)
            if match:
                player_name, wickets, runs = match.groups()
                bowling_data = {
                    'name': player_name.strip(),
                    'wickets': int(wickets),
                    'runs': int(runs),
                    'position': element['y']  # Use y-coordinate for vertical position
                }
                all_bowling.append(bowling_data)
    else:
        # Use regex on full text if no coordinates available
        for bowling_match in re.finditer(bowling_pattern, full_text):
            player_name, wickets, runs = bowling_match.groups()
            bowling_data = {
                'name': player_name.strip(),
                'wickets': int(wickets),
                'runs': int(runs),
                'position': full_text.find(player_name)  # Store position for sorting
            }
            all_bowling.append(bowling_data)
    
    # Sort bowling performances by their position in the text
    all_bowling.sort(key=lambda x: x['position'])
    
    # Assign bowling performances to teams (bowlers from team 2 bowl to team 1 batters and vice versa)
    if len(scorecard['teams']) == 2:
        # Find the second occurrence of the second team name
        team1_name = scorecard['teams'][0]['name']
        team2_name = scorecard['teams'][1]['name']
        
        # Find first occurrence of team2_name
        team_divider = full_text.find(team2_name)
        # Find second occurrence (after the first team's batting)
        second_occurrence = full_text.find(team2_name, team_divider + len(team2_name))
        
        if second_occurrence > 0:
            team_divider = second_occurrence
        
        # If using text elements with coordinates, find the y-coordinate divider
        if text_elements:
            # Find the text element containing the second team name
            team2_elements = [elem for elem in text_elements if team2_name in elem['text']]
            if len(team2_elements) >= 2:  # We need at least two occurrences
                # Use the y-coordinate of the second occurrence as the divider
                team_divider = team2_elements[1]['y']
        
        # Assign bowlers to opposite team of their batting
        for bowling in all_bowling:
            position = bowling.pop('position')  # Remove position field
            player_name = bowling['name']
            
            # Check if this player is already in a batting lineup
            is_in_team1_batting = any(b['name'] == player_name for b in scorecard['teams'][0]['batting'])
            is_in_team2_batting = any(b['name'] == player_name for b in scorecard['teams'][1]['batting'])
            
            # Assign bowlers to opposite team of their batting
            if is_in_team1_batting:
                scorecard['teams'][1]['bowling'].append(bowling)  # Team 1 batter bowls for team 2
            elif is_in_team2_batting:
                scorecard['teams'][0]['bowling'].append(bowling)  # Team 2 batter bowls for team 1
            else:
                # If not found in batting, use position
                if position < team_divider:
                    scorecard['teams'][1]['bowling'].append(bowling)
                else:
                    scorecard['teams'][0]['bowling'].append(bowling)
    elif len(scorecard['teams']) == 1:
        # If only one team, assign all bowling to that team
        for bowling in all_bowling:
            bowling.pop('position')  # Remove position field
            scorecard['teams'][0]['bowling'].append(bowling)
    
    # Extract match result - improved pattern
    result_pattern = r"(\w+)\s+WON\s+BY\s+(\d+)\s+(\w+)"
    result_match = re.search(result_pattern, full_text)
    if result_match:
        winner, margin, margin_type = result_match.groups()
        scorecard['result'] = {
            'winner': winner.strip(),
            'margin': int(margin),
            'margin_type': margin_type.strip()
        }
    
    # Extract player of the match - improved pattern
    pom_text = None
    for text in cleaned_texts:
        if "PLAYER OF THE MATCH" in text.upper() or "PLAYER OF MATCH" in text.upper():
            pom_text = text
            break
    
    if pom_text:
        # Extract name after the colon
        pom_parts = pom_text.split(':', 1)
        if len(pom_parts) > 1:
            scorecard['player_of_match'] = pom_parts[1].strip()
        else:
            # Try another approach - take everything after "Player of the Match"
            match = re.search(r"Player\s+of\s+the\s+Match:?\s+([\w\s]+)", pom_text, re.IGNORECASE)
            if match:
                scorecard['player_of_match'] = match.group(1).strip()
    
    # Clean up any encoding issues in the data
    scorecard = clean_scorecard_data(scorecard)
    
    # Filter out any incorrect entries (like venue being parsed as a player)
    if len(scorecard['teams']) >= 1:
        # Filter out bowling entries with unusually high wicket counts (likely parsing errors)
        for team in scorecard['teams']:
            team['bowling'] = [b for b in team['bowling'] if b['wickets'] <= 10]
    
    return scorecard

def clean_scorecard_data(data):
    """Clean up any encoding issues in the scorecard data"""
    if isinstance(data, dict):
        return {k: clean_scorecard_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [clean_scorecard_data(item) for item in data]
    elif isinstance(data, str):
        # Remove extra spaces between characters
        return re.sub(r'\s+', ' ', data).strip()
    else:
        return data

# Global flag to prevent duplicate execution
_script_executed = False

def main():
    """Main function"""
    global _script_executed
    if _script_executed:
        print("Script already executed, skipping duplicate call", file=sys.stderr)
        return
    _script_executed = True
    
    # Use hardcoded image path
    image_path = "C:\\Users\\<USER>\\OneDrive\\Documents\\rpl-new\\server\\uploads\\scorecards\\scorecard1.jpg"
    language = 'en'
    output_format = 'json'
    template = None
    output_file = "C:\\Users\\<USER>\\OneDrive\\Documents\\rpl-new\\ocr_output_fixed.json"
    
    try:
        # Import PaddleOCR
        try:
            from paddleocr import PaddleOCR
        except ImportError as e:
            print(json.dumps({
                'success': False,
                'error': 'PaddleOCR not installed. Please run: pip install paddleocr',
                'texts': [],
                'fullText': ''
            }))
            return

        # Check if image exists
        if not os.path.exists(image_path):
            print(json.dumps({
                'success': False,
                'error': f'Image file not found: {image_path}',
                'texts': [],
                'fullText': ''
            }))
            return

        # Initialize OCR with optimized settings for better text detection
        print(f"Initializing PaddleOCR for language: {language}", file=sys.stderr)
        ocr = PaddleOCR(
            lang=language,
            text_det_thresh=0.3,  # Lower threshold for better text detection
            text_det_box_thresh=0.5,
            text_det_unclip_ratio=1.8  # Higher ratio for better text box detection
        )

        # Check if template is provided
        template_data = None
        if template:
            try:
                template_data = json.loads(template)
                print(f"Using template: {template_data.get('name', 'Unknown')} with {len(template_data.get('regions', []))} regions", file=sys.stderr)
            except json.JSONDecodeError as e:
                print(f"Error parsing template JSON: {e}", file=sys.stderr)
                template_data = None

        # Process image
        print(f"Processing image: {image_path}", file=sys.stderr)

        if template_data and template_data.get('regions'):
            # Template-based extraction
            print("Using template-based extraction", file=sys.stderr)

            # Load image once for all regions
            img = cv2.imread(image_path)
            if img is None:
                raise Exception(f"Could not load image: {image_path}")

            # Extract text from each region
            region_results = {}
            all_texts = []

            for region in template_data['regions']:
                field_type = region.get('fieldType', 'unknown')
                print(f"Extracting from region: {field_type} at ({region['x']}, {region['y']}, {region['width']}, {region['height']})", file=sys.stderr)

                region_texts = extract_text_from_region(ocr, img, region)

                if region_texts:
                    # Store by field type
                    if field_type not in region_results:
                        region_results[field_type] = []

                    region_results[field_type].extend(region_texts)

                    # Add to all texts for compatibility
                    for text_info in region_texts:
                        all_texts.append(text_info['text'])

                    print(f"Found {len(region_texts)} text(s) in {field_type} region", file=sys.stderr)
                else:
                    print(f"No text found in {field_type} region", file=sys.stderr)

            texts = all_texts
            text_elements = None  # No coordinates in template mode

        else:
            # Full image OCR (original behavior)
            print("Using full image OCR", file=sys.stderr)
            
            # Extract text with coordinates for better team assignment
            text_elements = extract_text_with_coordinates(ocr, image_path)
            print(f"Extracted {len(text_elements)} text elements with coordinates", file=sys.stderr)
            
            # Extract text from results for backward compatibility
            texts = []
            result = ocr.predict(image_path)

            if result and len(result) > 0:
                # Handle new PaddleOCR API format
                if isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
                    # New API format - result is a list of dictionaries
                    for page_result in result:
                        if 'rec_texts' in page_result:
                            # Extract texts from rec_texts field
                            rec_texts = page_result['rec_texts']
                            rec_scores = page_result.get('rec_scores', [])

                            for i, text in enumerate(rec_texts):
                                confidence = rec_scores[i] if i < len(rec_scores) else 1.0
                                # Only include text with reasonable confidence
                                if confidence > 0.5 and text.strip():
                                    # Clean text to handle Unicode issues
                                    clean_text = text.strip().encode('ascii', 'ignore').decode('ascii')
                                    if clean_text:
                                        texts.append(clean_text)
                else:
                    # Old API format - fallback
                    for line in result[0] if result[0] else []:
                        try:
                            # Handle different result formats
                            if isinstance(line, (list, tuple)) and len(line) >= 2:
                                text_info = line[1]
                                if isinstance(text_info, (list, tuple)) and len(text_info) >= 1:
                                    text = text_info[0]
                                    confidence = text_info[1] if len(text_info) > 1 else 1.0
                                else:
                                    text = str(text_info)
                                    confidence = 1.0

                                # Only include text with reasonable confidence
                                if confidence > 0.5 and text.strip():
                                    # Clean text to handle Unicode issues
                                    clean_text = text.strip().encode('ascii', 'ignore').decode('ascii')
                                    if clean_text:
                                        texts.append(clean_text)

                        except Exception as e:
                            print(f"Error parsing OCR line: {e}", file=sys.stderr)
                            continue

        # Create result
        full_text = ' '.join(texts)
        
        # Parse cricket scorecard data with text elements for better team assignment
        print("Parsing cricket scorecard data...", file=sys.stderr)
        scorecard_data = parse_cricket_scorecard(texts, text_elements)

        result_data = {
            'success': True,
            'texts': texts,
            'fullText': full_text,
            'textCount': len(texts),
            'language': language,
            'scorecard': scorecard_data
        }

        # Add template-specific data if template was used
        if template_data and template_data.get('regions'):
            result_data.update({
                'template_name': template_data.get('name', 'Unknown'),
                'regions_processed': len(template_data['regions']),
                'extraction_method': 'template-based',
                'region_results': region_results
            })
        else:
            result_data['extraction_method'] = 'full-image'

        print(f"OCR completed. Found {len(texts)} text regions.", file=sys.stderr)

        # Output result
        if output_format == 'json':
            # Use json.dumps with ensure_ascii=True to handle encoding issues
            # and indent=2 for better readability
            output_json = json.dumps(result_data, ensure_ascii=True, indent=2)
            
            # Write to file instead of printing to console
            with open(output_file, 'w') as f:
                f.write(output_json)
            print(f"JSON output written to {output_file}", file=sys.stderr)
            
            # Print a simplified version to console
            print(json.dumps({
                'success': True,
                'textCount': len(texts),
                'outputFile': output_file
            }))
        else:
            for text in texts:
                print(text)

    except Exception as e:
        # Return error in JSON format
        error_result = {
            'success': False,
            'error': str(e),
            'texts': [],
            'fullText': ''
        }
        print(json.dumps(error_result, ensure_ascii=True))
        return

if __name__ == '__main__':
    # Check if this script is being run directly
    import traceback
    print(f"Script execution path: {__file__}", file=sys.stderr)
    print(f"Caller information:", file=sys.stderr)
    for line in traceback.format_stack()[:-1]:
        print(line.strip(), file=sys.stderr)
    
    # Run the main function only once
    try:
        # Use a more direct approach to prevent duplicate execution
        main()
        # Exit immediately after running to prevent any potential duplicate execution
        sys.exit(0)
    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)
