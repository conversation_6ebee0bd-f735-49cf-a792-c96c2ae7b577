/* Admin Dashboard Styles */

/* Stats Cards */
.stats-card {
  border-radius: 0;
  padding: 16px 8px;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  margin: 0;
  width: 100%;
  box-shadow: none;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.stats-card .stats-icon {
  font-size: 1.5rem;
  margin-bottom: 4px;
  opacity: 0.9;
}

.stats-card .stats-value {
  font-size: 2rem;
  font-weight: 700;
  margin: 4px 0;
  line-height: 1;
}

.stats-card .stats-label {
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 2px;
  text-align: center;
}

.stats-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Admin Tools */
.admin-tools-container {
  padding: 0;
  margin: 0;
  width: 100%;
}

.admin-tool-card {
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
  align-items: center;
  text-align: center;
  justify-content: flex-start;
}

/* Hover effects are now handled by inline styles */

.admin-tool-card .tool-icon {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: #1976d2;
  transition: all 0.3s ease;
}

.admin-tool-card:hover .tool-icon {
  transform: scale(1.05);
}

.admin-tool-card .tool-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.2;
}

.admin-tool-card .tool-description {
  font-size: 0.85rem;
  margin-bottom: 12px;
  line-height: 1.4;
  color: var(--text-secondary-color, rgba(255, 255, 255, 0.7));
  max-width: 90%;
}

.admin-tool-card .tool-action {
  width: 80%;
  margin: 0 auto;
}

/* Dark mode specific styles */
.dark-mode .stats-card {
  background-color: #2d2d2d;
  color: #ffffff;
}

.dark-mode .admin-tool-card {
  color: #ffffff;
}

/* Dark mode hover effects are now handled by inline styles */

.dark-mode .stats-card::after {
  background: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(255,255,255,0.2) 50%, rgba(0,0,0,0) 100%);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .stats-container {
    padding: 0;
  }

  .admin-dashboard-container {
    padding: 0;
  }
}

@media (max-width: 960px) {
  .stats-card .stats-value {
    font-size: 1.7rem;
  }

  .stats-card .stats-icon {
    font-size: 1.5rem;
  }

  .stats-card {
    padding: 12px 4px;
  }

  .admin-tool-card {
    padding: 12px 8px;
  }
}

@media (max-width: 600px) {
  .stats-container {
    margin-bottom: 8px;
  }

  .stats-card {
    padding: 10px 4px;
  }

  .stats-card .stats-value {
    font-size: 1.5rem;
    margin: 2px 0;
  }

  .stats-card .stats-label {
    font-size: 0.7rem;
    margin-top: 2px;
  }

  .admin-tool-card {
    padding: 12px 8px;
  }

  .admin-tool-card .tool-icon {
    font-size: 1.5rem;
    margin-bottom: 6px;
  }

  .admin-tool-card .tool-title {
    font-size: 1rem;
    margin-bottom: 6px;
  }

  .admin-tool-card .tool-description {
    font-size: 0.8rem;
    margin-bottom: 10px;
    max-width: 95%;
  }

  .admin-dashboard-container {
    padding: 0;
  }

  .admin-section-title {
    font-size: 1.3rem;
    margin-bottom: 8px;
  }
}

/* Tab styles */
.admin-tabs {
  margin-bottom: 16px;
}

.admin-tabs .MuiTab-root {
  text-transform: none;
  font-weight: 500;
  min-width: 100px;
  transition: all 0.3s ease;
  padding: 12px 16px;
}

.admin-tabs .MuiTab-root.Mui-selected {
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.03);
}

.dark-mode .admin-tabs .MuiTab-root.Mui-selected {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Dark mode toggle */
.dark-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
  transform: scale(1.1);
}

/* Animation for page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}
