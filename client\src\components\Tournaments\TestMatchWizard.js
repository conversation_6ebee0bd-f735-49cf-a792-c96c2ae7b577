import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Container,
  Paper
} from '@mui/material';
import MatchResultWizard from './MatchResultWizard';

/**
 * Test Component for Match Result Wizard
 * 
 * This is a simple test component to verify the new implementation works
 */
const TestMatchWizard = () => {
  const [open, setOpen] = useState(false);

  // Mock tournament data for testing
  const mockTournament = {
    _id: '681e581dfe3bc0a041dd937a',
    name: 'Test Tournament',
    registeredTeams: [
      { _id: 'team1', teamName: 'TBA' },
      { _id: 'team2', teamName: 'ABRAR XI' },
      { _id: 'team3', teamName: 'Mumbai Indians' },
      { _id: 'team4', teamName: 'Chennai Super Kings' }
    ]
  };

  const handleMatchAdded = (result) => {
    console.log('Match added successfully:', result);
    alert('Match added successfully! Check console for details.');
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          🏏 New Match Result Wizard Test
        </Typography>
        
        <Typography variant="body1" paragraph>
          This is the new, fresh implementation of the Match Result system.
          Click the button below to test the complete flow:
        </Typography>

        <Box sx={{ my: 3 }}>
          <Typography variant="h6" color="primary" gutterBottom>
            ✨ Features:
          </Typography>
          <Typography variant="body2" component="div" sx={{ textAlign: 'left', maxWidth: 600, mx: 'auto' }}>
            • 📤 <strong>Upload scorecard image</strong> with drag & drop support<br/>
            • 🔍 <strong>OCR processing</strong> with real-time feedback<br/>
            • 📊 <strong>JSON data viewer</strong> showing extracted information<br/>
            • 📝 <strong>Auto-populated form</strong> with validation highlighting<br/>
            • ⚠️ <strong>Error detection</strong> for incorrect OCR values<br/>
            • ✏️ <strong>Manual correction</strong> capabilities<br/>
            • 💾 <strong>Match submission</strong> with complete data
          </Typography>
        </Box>

        <Button
          variant="contained"
          size="large"
          onClick={() => setOpen(true)}
          sx={{ mt: 2 }}
        >
          🚀 Test New Match Result Wizard
        </Button>

        <MatchResultWizard
          open={open}
          onClose={() => setOpen(false)}
          tournament={mockTournament}
          onMatchAdded={handleMatchAdded}
        />
      </Paper>
    </Container>
  );
};

export default TestMatchWizard;
