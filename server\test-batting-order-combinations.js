const MatchOutcomeService = require('./services/matchOutcomeService');

/**
 * Test all combinations of homeTeamBattedFirst and team1IsHomeTeam flags
 * to ensure the determineWinner function produces the correct result descriptions
 */
async function testBattingOrderCombinations() {
  console.log('🏏 TESTING BATTING ORDER FLAG COMBINATIONS');
  console.log('=======================================\n');

  // Initialize the match outcome service
  const matchService = new MatchOutcomeService();
  await matchService.initialize();

  // Create test cases for all combinations
  const testCases = [
    {
      name: 'Team 1 is home team and home team batted first',
      match: { team1IsHomeTeam: true, homeTeamBattedFirst: true },
      team1Score: { runs: 164, wickets: 2, overs: 18.2 },
      team2Score: { runs: 162, wickets: 5, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 2 runs',
      expectedTeam1BattedFirst: true
    },
    {
      name: 'Team 1 is home team and home team batted second',
      match: { team1IsHomeTeam: true, homeTeamBattedFirst: false },
      team1Score: { runs: 164, wickets: 2, overs: 18.2 },
      team2Score: { runs: 162, wickets: 5, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 8 wickets',
      expectedTeam1BattedFirst: false
    },
    {
      name: 'Team 2 is home team and home team batted first',
      match: { team1IsHomeTeam: false, homeTeamBattedFirst: true },
      team1Score: { runs: 164, wickets: 2, overs: 18.2 },
      team2Score: { runs: 162, wickets: 5, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 8 wickets',
      expectedTeam1BattedFirst: false
    },
    {
      name: 'Team 2 is home team and home team batted second',
      match: { team1IsHomeTeam: false, homeTeamBattedFirst: false },
      team1Score: { runs: 164, wickets: 2, overs: 18.2 },
      team2Score: { runs: 162, wickets: 5, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 2 runs',
      expectedTeam1BattedFirst: true
    },
    {
      name: 'Team 1 wins batting second (chasing)',
      match: { team1IsHomeTeam: true, homeTeamBattedFirst: false },
      team1Score: { runs: 165, wickets: 3, overs: 19.2 },
      team2Score: { runs: 164, wickets: 8, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 7 wickets',
      expectedTeam1BattedFirst: false
    },
    {
      name: 'Team 2 wins batting second (chasing)',
      match: { team1IsHomeTeam: true, homeTeamBattedFirst: true },
      team1Score: { runs: 164, wickets: 8, overs: 20.0 },
      team2Score: { runs: 165, wickets: 3, overs: 19.2 },
      expectedWinner: 'team2',
      expectedDescription: 'Team 2 won by 7 wickets',
      expectedTeam1BattedFirst: true
    },
    {
      name: 'Using inferred batting order from OCR (overrides match settings)',
      match: { team1IsHomeTeam: true, homeTeamBattedFirst: true },
      inferredBattingOrder: { team1BattedFirst: false },
      team1Score: { runs: 164, wickets: 2, overs: 18.2 },
      team2Score: { runs: 162, wickets: 5, overs: 20.0 },
      expectedWinner: 'team1',
      expectedDescription: 'Team 1 won by 8 wickets',
      expectedTeam1BattedFirst: false
    }
  ];

  // Run each test case
  let passedTests = 0;
  let failedTests = 0;

  for (const testCase of testCases) {
    console.log(`📋 TEST CASE: ${testCase.name}`);
    console.log(`   Match configuration: ${JSON.stringify(testCase.match)}`);
    if (testCase.inferredBattingOrder) {
      console.log(`   Inferred batting order: ${JSON.stringify(testCase.inferredBattingOrder)}`);
    }
    console.log(`   Team 1 Score: ${testCase.team1Score.runs}-${testCase.team1Score.wickets}`);
    console.log(`   Team 2 Score: ${testCase.team2Score.runs}-${testCase.team2Score.wickets}`);

    // Call determineWinner with the test case data
    const result = matchService.determineWinner(
      testCase.team1Score,
      testCase.team2Score,
      '',
      testCase.match,
      testCase.inferredBattingOrder
    );

    console.log('\n   📊 RESULT:');
    console.log(`   Winner: ${result.winner}`);
    console.log(`   Description: ${result.description}`);
    console.log(`   Expected Description: ${testCase.expectedDescription}`);

    // Validate the result
    const descriptionMatches = result.description.includes(testCase.expectedDescription);
    const winnerMatches = result.winner === testCase.expectedWinner;

    if (descriptionMatches && winnerMatches) {
      console.log(`   ✅ PASSED: Result matches expected outcome`);
      passedTests++;
    } else {
      console.log(`   ❌ FAILED: Result does not match expected outcome`);
      if (!winnerMatches) {
        console.log(`     - Expected winner: ${testCase.expectedWinner}, got: ${result.winner}`);
      }
      if (!descriptionMatches) {
        console.log(`     - Expected description to include: "${testCase.expectedDescription}", got: "${result.description}"`);
      }
      failedTests++;
    }
    console.log('\n' + '-'.repeat(50));
  }

  // Print summary
  console.log(`\n📊 TEST SUMMARY: ${passedTests} passed, ${failedTests} failed`);
  if (failedTests === 0) {
    console.log('✅ All tests passed!');
  } else {
    console.log('❌ Some tests failed!');
  }
}

// Run the tests
testBattingOrderCombinations().catch(err => {
  console.error('Error running tests:', err);
});