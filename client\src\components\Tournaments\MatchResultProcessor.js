import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Card,
  CardMedia,
  CardContent,
  IconButton
} from '@mui/material';
import {
  CameraAlt as CameraIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  ArrowBack as BackIcon,
  ArrowForward as NextIcon,
  Check as ConfirmIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { uploadScorecard } from '../../services/scorecardService';
import { addMatch } from '../../services/tournamentService';

/**
 * Match Result Processor Component
 * 
 * This component handles the complete flow of processing a match result:
 * 1. Capture the scorecard using the guided capture system
 * 2. Extract and verify match data
 * 3. Submit the match result
 * 
 * @param {Object} props Component props
 * @param {Object} props.tournament Tournament object
 * @param {Object} props.match Match object (if editing an existing match)
 * @param {Function} props.onComplete Callback when process is complete
 * @param {Function} props.onCancel Callback when process is cancelled
 */
const MatchResultProcessor = ({ tournament, match, onComplete, onCancel }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Captured data
  const [capturedImage, setCapturedImage] = useState(null);
  const [extractedData, setExtractedData] = useState(null);
  
  // Match data
  const [matchData, setMatchData] = useState({
    opponentTeamId: match?.homeTeam?._id === user?.team?._id ? match?.awayTeam?._id : match?.homeTeam?._id,
    isHomeMatch: match?.homeTeam?._id === user?.team?._id,
    date: match?.date || new Date().toISOString().split('T')[0],
    venue: match?.venue || '',
    result: {
      homeTeamScore: {
        runs: match?.result?.homeTeamScore?.runs || 0,
        wickets: match?.result?.homeTeamScore?.wickets || 0,
        overs: match?.result?.homeTeamScore?.overs || 0
      },
      awayTeamScore: {
        runs: match?.result?.awayTeamScore?.runs || 0,
        wickets: match?.result?.awayTeamScore?.wickets || 0,
        overs: match?.result?.awayTeamScore?.overs || 0
      },
      winnerId: match?.result?.winner || '',
      isTie: match?.result?.isTie || false,
      manOfTheMatch: match?.result?.manOfTheMatch || ''
    }
  });
  
  // Steps
  const steps = ['Capture Scorecard', 'Verify Data', 'Submit Result'];
  
  // Handle capture complete
  const handleCaptureComplete = (result) => {
    setCapturedImage(result.scorecard.url);
    
    // In a real implementation, this would come from OCR
    // For now, we'll use mock data or data from the captured image
    setExtractedData(result.scorecard.data);
    
    // Move to next step
    setActiveStep(1);
  };
  
  // Handle data verification
  const handleDataVerification = () => {
    // Update match data with extracted data
    setMatchData(prevData => ({
      ...prevData,
      result: {
        ...prevData.result,
        homeTeamScore: {
          runs: extractedData.homeTeam.score.runs,
          wickets: extractedData.homeTeam.score.wickets,
          overs: extractedData.homeTeam.score.overs
        },
        awayTeamScore: {
          runs: extractedData.awayTeam.score.runs,
          wickets: extractedData.awayTeam.score.wickets,
          overs: extractedData.awayTeam.score.overs
        },
        // Determine winner based on scores
        winnerId: extractedData.homeTeam.score.runs > extractedData.awayTeam.score.runs
          ? matchData.isHomeMatch ? user.team : matchData.opponentTeamId
          : extractedData.homeTeam.score.runs < extractedData.awayTeam.score.runs
            ? matchData.isHomeMatch ? matchData.opponentTeamId : user.team
            : '',
        isTie: extractedData.homeTeam.score.runs === extractedData.awayTeam.score.runs,
        manOfTheMatch: extractedData.playerOfMatch || ''
      }
    }));
    
    // Move to next step
    setActiveStep(2);
  };
  
  // Handle match data change
  const handleMatchDataChange = (e) => {
    const { name, value } = e.target;
    
    if (name.startsWith('result.')) {
      const resultField = name.split('.')[1];
      setMatchData(prevData => ({
        ...prevData,
        result: {
          ...prevData.result,
          [resultField]: value
        }
      }));
    } else if (name.startsWith('result.homeTeamScore.') || name.startsWith('result.awayTeamScore.')) {
      const [, team, field] = name.split('.');
      setMatchData(prevData => ({
        ...prevData,
        result: {
          ...prevData.result,
          [team]: {
            ...prevData.result[team],
            [field]: value
          }
        }
      }));
    } else {
      setMatchData(prevData => ({
        ...prevData,
        [name]: value
      }));
    }
  };
  
  // Handle submit
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Upload scorecard if we have one
      if (capturedImage) {
        const formData = new FormData();
        
        // Convert data URL to blob if it's a data URL
        if (capturedImage.startsWith('data:')) {
          const blob = await fetch(capturedImage).then(r => r.blob());
          formData.append('scorecard', blob, 'scorecard.jpg');
        }
        
        // Add match data
        formData.append('matchData', JSON.stringify(extractedData));
        
        // Upload to server
        await uploadScorecard(formData, tournament._id, match?._id);
      }
      
      // Add or update match
      const result = await addMatch(tournament._id, matchData);
      
      setSuccess('Match result submitted successfully!');
      
      // Notify parent component
      if (onComplete) {
        onComplete(result);
      }
    } catch (err) {
      console.error('Error submitting match result:', err);
      setError(err.message || 'Failed to submit match result');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle next step
  const handleNext = () => {
    if (activeStep === 1) {
      handleDataVerification();
    } else if (activeStep === 2) {
      handleSubmit();
    } else {
      setActiveStep(prevStep => prevStep + 1);
    }
  };
  
  // Handle back step
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };
  
  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="body1" paragraph>
              Capture the scorecard from your TV/monitor using your device camera.
            </Typography>
            
            <Button
              variant="contained"
              color="primary"
              startIcon={<CameraIcon />}
              onClick={() => navigate('/scorecard-capture')}
              fullWidth
              size="large"
              sx={{ py: 2 }}
            >
              Open Camera Capture
            </Button>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="body1" paragraph>
              Verify the extracted data from the scorecard.
            </Typography>
            
            {capturedImage && (
              <Card sx={{ mb: 3 }}>
                <CardMedia
                  component="img"
                  image={capturedImage}
                  alt="Captured scorecard"
                  sx={{ maxHeight: 300, objectFit: 'contain' }}
                />
              </Card>
            )}
            
            {extractedData && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Home Team
                    </Typography>
                    <Typography variant="h6">
                      {extractedData.homeTeam.name}
                    </Typography>
                    <Typography variant="h4">
                      {extractedData.homeTeam.score.runs}/{extractedData.homeTeam.score.wickets}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Overs: {extractedData.homeTeam.score.overs}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Away Team
                    </Typography>
                    <Typography variant="h6">
                      {extractedData.awayTeam.name}
                    </Typography>
                    <Typography variant="h4">
                      {extractedData.awayTeam.score.runs}/{extractedData.awayTeam.score.wickets}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Overs: {extractedData.awayTeam.score.overs}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Match Result
                    </Typography>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {extractedData.result}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Player of the Match: {extractedData.playerOfMatch}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            )}
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="body1" paragraph>
              Review and submit the match result.
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Match Date"
                  type="date"
                  name="date"
                  value={matchData.date}
                  onChange={handleMatchDataChange}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Venue"
                  name="venue"
                  value={matchData.venue}
                  onChange={handleMatchDataChange}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Score Details
                  </Typography>
                </Divider>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Home Team Score
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Runs"
                      type="number"
                      name="result.homeTeamScore.runs"
                      value={matchData.result.homeTeamScore.runs}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0 }}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Wickets"
                      type="number"
                      name="result.homeTeamScore.wickets"
                      value={matchData.result.homeTeamScore.wickets}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0, max: 10 }}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Overs"
                      type="number"
                      name="result.homeTeamScore.overs"
                      value={matchData.result.homeTeamScore.overs}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0, step: 0.1 }}
                    />
                  </Grid>
                </Grid>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" gutterBottom>
                  Away Team Score
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Runs"
                      type="number"
                      name="result.awayTeamScore.runs"
                      value={matchData.result.awayTeamScore.runs}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0 }}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Wickets"
                      type="number"
                      name="result.awayTeamScore.wickets"
                      value={matchData.result.awayTeamScore.wickets}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0, max: 10 }}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Overs"
                      type="number"
                      name="result.awayTeamScore.overs"
                      value={matchData.result.awayTeamScore.overs}
                      onChange={handleMatchDataChange}
                      inputProps={{ min: 0, step: 0.1 }}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        {getStepContent(activeStep)}
      </Paper>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          onClick={activeStep === 0 ? onCancel : handleBack}
          startIcon={<BackIcon />}
          disabled={loading}
        >
          {activeStep === 0 ? 'Cancel' : 'Back'}
        </Button>
        
        <Button
          variant="contained"
          onClick={handleNext}
          endIcon={activeStep === steps.length - 1 ? <ConfirmIcon /> : <NextIcon />}
          disabled={loading || (activeStep === 0 && !capturedImage)}
          color={activeStep === steps.length - 1 ? 'success' : 'primary'}
        >
          {loading ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              Processing...
            </>
          ) : activeStep === steps.length - 1 ? (
            'Submit'
          ) : (
            'Next'
          )}
        </Button>
      </Box>
    </Box>
  );
};

export default MatchResultProcessor;
