import React, { useState, useCallback } from 'react';
import {
  <PERSON>ton,
  <PERSON>,
  Typography,
  Alert,
  LinearProgress,
  Paper,
  Divider,
  Tabs,
  Tab,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  useTheme,
  useMediaQuery,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material';
import { Upload as UploadIcon, Close as CloseIcon, SportsCricket as CricketIcon } from '@mui/icons-material';
import { importPlayers } from '../../services/playerService';
import ImportIplPlayers from '../ImportIplPlayers';

// No longer using ResponsiveModal - now using Dialog directly to ensure our styles take precedence
const ImportPlayersDialog = ({ open, onClose, onSuccess }) => {
  const [tabValue, setTabValue] = useState(0);
  const [iplPlayers, setIplPlayers] = useState([]);
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [errorDetails, setErrorDetails] = useState([]);
  const [skippedDuplicates, setSkippedDuplicates] = useState([]);
  const [isDragging, setIsDragging] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

  const [playerData, setPlayerData] = useState({
    name: '',
    nationality: '',
    height: '',
    type: '',
    battingHand: '',
    bowlingHand: 'None',
    ratings: {
      overall: 70
    },
    image: '/uploads/players/default.png' // Default image path
    // Age field removed as it's no longer required
  });

  // State for image upload in Create Single Player tab
  const [playerImageFile, setPlayerImageFile] = useState(null);
  const [playerImagePreview, setPlayerImagePreview] = useState(null);

  const playerTypes = ['Batsman', 'Bowler', 'Batting Allrounder', 'Bowling Allrounder', 'Allrounder', 'Wicket Keeper'];
  const battingHands = [
    { value: 'RHB', label: 'Right Hand Bat' },
    { value: 'LHB', label: 'Left Hand Bat' }
  ];
  const bowlingHands = [
    { value: 'None', label: 'Does Not Bowl' },
    { value: 'RF', label: 'Right Fast' },
    { value: 'LF', label: 'Left Fast' },
    { value: 'RFM', label: 'Right Fast Medium' },
    { value: 'LFM', label: 'Left Fast Medium' },
    { value: 'RM', label: 'Right Medium' },
    { value: 'LM', label: 'Left Medium' },
    { value: 'RHWS', label: 'Right Wrist Spin' },
    { value: 'LHWS', label: 'Left Wrist Spin' },
    { value: 'RHFS', label: 'Right Finger Spin' },
    { value: 'LHFS', label: 'Left Finger Spin' }
  ];

  const handleDragOver = useCallback((event) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((event) => {
    event.preventDefault();
    setIsDragging(false);
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      setFile(droppedFile);
      setError(null);
    }
  }, []);

  const handleFileChange = (event) => {
    setFile(event.target.files[0]);
    setError(null);
  };

  const handleSubmit = async () => {
    if (tabValue === 0 && !file) {
      setError('Please select a file first');
      return;
    }

    if (tabValue === 1) {
      // Log the current player data for debugging
      console.log('Player data before validation:', playerData);

      // Check if any required fields are empty or undefined
      const missingFields = [];
      if (!playerData.name) missingFields.push('name');
      if (!playerData.nationality) missingFields.push('nationality');
      if (!playerData.height) missingFields.push('height');
      if (!playerData.type) missingFields.push('type');
      if (!playerData.battingHand) missingFields.push('battingHand');

      if (missingFields.length > 0) {
        console.log('Missing fields:', missingFields);
        setError(`Missing required fields: ${missingFields.join(', ')}`);
        return;
      }
    }

    if (tabValue === 2 && (!iplPlayers || iplPlayers.length === 0)) {
      setError('Please fetch and select IPL players first');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setErrorDetails([]);
      setSkippedDuplicates([]);

      if (tabValue === 0) {
        // File import
        const formData = new FormData();
        formData.append('playersFile', file);
        console.log('Sending import request with file:', file.name);
        const importResult = await importPlayers(formData);

        // Check if there are errors or skipped duplicates
        const hasErrors = importResult.errors && importResult.errors.length > 0;
        const hasSkippedDuplicates = importResult.skippedDuplicates && importResult.skippedDuplicates.length > 0;

        if (hasErrors || hasSkippedDuplicates) {
          let errorMessage = '';

          if (hasErrors) {
            console.log('Import completed with errors:', importResult.errors);
            errorMessage += `Import completed with ${importResult.errorCount || importResult.errors.length} errors.`;
            setErrorDetails(importResult.errors);
          }

          if (hasSkippedDuplicates) {
            console.log('Import skipped duplicates:', importResult.skippedDuplicates);
            if (errorMessage) errorMessage += ' ';
            errorMessage += `${importResult.skippedCount || importResult.skippedDuplicates.length} duplicate players were skipped.`;
            setSkippedDuplicates(importResult.skippedDuplicates);
          }

          // Set the main error message
          setError(errorMessage + ' Please check the details below.');

          // Still call onSuccess to update the UI with any successfully imported players
          onSuccess(importResult);
        } else {
          // No errors or skipped duplicates, close the dialog
          onSuccess(importResult);
          onClose();
        }
      } else if (tabValue === 1) {
        // Single player create - make sure we're sending data with the correct format
        const formData = new FormData();
        formData.append('singlePlayer', 'true');

        // Log the player data before submission for debugging
        console.log('Submitting player data:', playerData);

        // If player image was uploaded, add it to the form data
        if (playerImageFile) {
          formData.append('playerImage', playerImageFile);
          console.log('Including player image in submission');
        }

        // Ensure all required fields are present in a clean object
        const cleanPlayerData = {
          name: playerData.name,
          nationality: playerData.nationality,
          height: playerData.height,
          type: playerData.type,
          battingHand: playerData.battingHand,
          bowlingHand: playerData.bowlingHand || 'None',
          ratings: playerData.ratings || { overall: 70 },
          image: playerData.image || '/uploads/players/default.png'
        };

        formData.append('playerData', JSON.stringify(cleanPlayerData));

        try {
          const importResult = await importPlayers(formData);
          console.log('Player created successfully:', importResult);
          onSuccess({ playersImported: 1, errors: [] });
          onClose();
        } catch (error) {
          console.error('Error creating player:', error);
          setError(error.message || 'Failed to create player');
        }
      } else if (tabValue === 2) {
        // IPL players import
        if (iplPlayers && iplPlayers.length > 0) {
          // Create a batch of players to import
          const formData = new FormData();
          formData.append('singlePlayer', 'false');
          formData.append('batchImport', 'true');
          formData.append('playerData', JSON.stringify(iplPlayers));

          const importResult = await importPlayers(formData);
          onSuccess({
            playersImported: iplPlayers.length,
            errors: importResult.errors || []
          });
          onClose();
        } else {
          setError('No IPL players selected for import');
        }
      }
    } catch (err) {
      console.error('Import error:', err);

      // Handle different error formats
      if (err.errors && Array.isArray(err.errors)) {
        setError('Failed to import players. Please check the details below.');
        setErrorDetails(err.errors);
      } else if (typeof err === 'object' && err.msg) {
        setError(err.msg || 'Failed to import players');
        if (err.details) {
          setErrorDetails(Array.isArray(err.details) ? err.details : [err.details]);
        }
      } else {
        setError(err.message || 'Failed to import players');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
    setError(null);
    setErrorDetails([]);
    setSkippedDuplicates([]);
  };

  // We're now using direct state updates in each field's onChange handler

  // Handle player image upload in Create Single Player tab
  const handlePlayerImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPlayerImageFile(file);
      setPlayerImagePreview(URL.createObjectURL(file));
    }
  };

  // Content for each tab
  const renderTabContent = () => {
    if (tabValue === 0) {
      // Bulk Import Tab
      return (
        <Paper
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          sx={{
            p: { xs: 3, sm: 4 },
            border: '2px dashed',
            borderColor: isDragging ? 'primary.main' : 'grey.300',
            borderRadius: 2,
            backgroundColor: isDragging ? 'action.hover' : 'background.paper',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            '&:hover': {
              borderColor: 'primary.main',
              backgroundColor: 'action.hover',
            },
          }}
          component="label"
        >
          <input
            type="file"
            accept=".csv,.xlsx,.xls"
            hidden
            onChange={handleFileChange}
          />
          <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', mb: 2 }}>
            <UploadIcon sx={{ fontSize: { xs: 40, sm: 48 }, color: 'primary.main' }} />
          </Box>
          <Typography variant="h6" gutterBottom>
            Drag & Drop your file here
          </Typography>
          <Typography variant="body2" color="text.secondary">
            or click to select a file
          </Typography>
          {file && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="primary">
                Selected: {file.name}
              </Typography>
            </Box>
          )}
        </Paper>
      );
    } else if (tabValue === 1) {
      // Create Single Player Tab - Simplified layout to avoid dropdown issues
      return (
        <Box
          sx={{
            backgroundColor: theme => theme.palette.mode === 'light' ? '#f8f9fa' : 'rgba(255,255,255,0.03)',
            borderRadius: 2,
            p: { xs: 2, sm: 3 },
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 3 }}>
            Enter Player Details
          </Typography>

          {/* Basic Information */}
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Basic Information
          </Typography>
          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={playerData.name}
                onChange={(e) => {
                  // Direct update to ensure the value is set correctly
                  setPlayerData(prev => ({
                    ...prev,
                    name: e.target.value
                  }));
                  console.log('Name entered:', e.target.value);
                }}
                required
                size={isMobile ? "small" : "medium"}
                sx={{ bgcolor: 'background.paper' }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nationality"
                name="nationality"
                value={playerData.nationality}
                onChange={(e) => {
                  // Direct update to ensure the value is set correctly
                  setPlayerData(prev => ({
                    ...prev,
                    nationality: e.target.value
                  }));
                  console.log('Nationality entered:', e.target.value);
                }}
                required
                size={isMobile ? "small" : "medium"}
                sx={{ bgcolor: 'background.paper' }}
              />
            </Grid>
          </Grid>

          {/* Physical Attributes */}
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Physical Attributes
          </Typography>
          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Height (cm)"
                name="height"
                value={playerData.height}
                onChange={(e) => {
                  // Format height to ensure it includes "cm"
                  let heightValue = e.target.value;
                  if (heightValue && !heightValue.toLowerCase().includes('cm')) {
                    heightValue = `${heightValue} cm`;
                  }

                  // Direct update to ensure the value is set correctly
                  setPlayerData(prev => ({
                    ...prev,
                    height: heightValue
                  }));
                  console.log('Height entered (formatted):', heightValue);
                }}
                required
                placeholder="175 cm"
                size={isMobile ? "small" : "medium"}
                sx={{ bgcolor: 'background.paper' }}
                helperText="Height in cm (will be formatted automatically)"
              />
            </Grid>
          </Grid>

          {/* Use separate section for dropdown fields to avoid overflow issues */}
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Player Role
          </Typography>
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                required
                size={isMobile ? "small" : "medium"}
                sx={{
                  bgcolor: 'background.paper',
                  mb: 2,
                  '& .MuiOutlinedInput-root': {
                    zIndex: 0 // Default z-index for the select box itself
                  }
                }}
              >
                <InputLabel>Player Type</InputLabel>
                <Select
                  name="type"
                  value={playerData.type}
                  onChange={(e) => {
                    // Direct update to ensure the value is set correctly
                    setPlayerData(prev => ({
                      ...prev,
                      type: e.target.value
                    }));
                    console.log('Player type selected:', e.target.value);
                  }}
                  label="Player Type"
                  MenuProps={{
                    PaperProps: {
                      style: { maxHeight: 250 } // Limit dropdown height
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                    sx: {
                      '& .MuiPaper-root': {
                        zIndex: theme.zIndex.modal + 1,
                      }
                    }
                  }}
                >
                  {playerTypes.map((type) => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
                <FormHelperText>Defines player role</FormHelperText>
              </FormControl>
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6}>
              <FormControl
                fullWidth
                required
                size={isMobile ? "small" : "medium"}
                sx={{
                  bgcolor: 'background.paper',
                  mb: 1,
                  '& .MuiOutlinedInput-root': {
                    zIndex: 0 // Default z-index for the select box itself
                  }
                }}
              >
                <InputLabel>Batting Hand</InputLabel>
                <Select
                  name="battingHand"
                  value={playerData.battingHand}
                  onChange={(e) => {
                    // Direct update to ensure the value is set correctly
                    setPlayerData(prev => ({
                      ...prev,
                      battingHand: e.target.value
                    }));
                    console.log('Batting hand selected:', e.target.value);
                  }}
                  label="Batting Hand"
                  MenuProps={{
                    PaperProps: {
                      style: { maxHeight: 250 } // Limit dropdown height
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                    sx: {
                      '& .MuiPaper-root': {
                        zIndex: theme.zIndex.modal + 1,
                      }
                    }
                  }}
                >
                  {battingHands.map((style) => (
                    <MenuItem key={style.value} value={style.value}>{style.label}</MenuItem>
                  ))}
                </Select>
                <FormHelperText>Left or right handed</FormHelperText>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl
                fullWidth
                required
                size={isMobile ? "small" : "medium"}
                sx={{
                  bgcolor: 'background.paper',
                  mb: 1,
                  '& .MuiOutlinedInput-root': {
                    zIndex: 0 // Default z-index for the select box itself
                  }
                }}
              >
                <InputLabel>Bowling Hand</InputLabel>
                <Select
                  name="bowlingHand"
                  value={playerData.bowlingHand}
                  onChange={(e) => {
                    // Direct update to ensure the value is set correctly
                    setPlayerData(prev => ({
                      ...prev,
                      bowlingHand: e.target.value
                    }));
                    console.log('Bowling hand selected:', e.target.value);
                  }}
                  label="Bowling Hand"
                  MenuProps={{
                    PaperProps: {
                      style: { maxHeight: 250 } // Limit dropdown height
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                    sx: {
                      '& .MuiPaper-root': {
                        zIndex: theme.zIndex.modal + 1,
                      }
                    }
                  }}
                >
                  {bowlingHands.map((style) => (
                    <MenuItem key={style.value} value={style.value}>{style.label}</MenuItem>
                  ))}
                </Select>
                <FormHelperText>Bowling style or none</FormHelperText>
              </FormControl>
            </Grid>
          </Grid>

          {/* Rating */}
          <Divider sx={{ mb: 3 }} />
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Player Rating
          </Typography>
          <TextField
            label="Overall Rating"
            name="ratings.overall"
            type="number"
            value={playerData.ratings.overall}
            onChange={(e) => {
              // Direct update to ensure the value is set correctly
              setPlayerData(prev => ({
                ...prev,
                ratings: {
                  ...prev.ratings,
                  overall: Number(e.target.value)
                }
              }));
              console.log('Rating entered:', e.target.value);
            }}
            required
            InputProps={{
              inputProps: { min: 40, max: 99 }
            }}
            size={isMobile ? "small" : "medium"}
            sx={{
              bgcolor: 'background.paper',
              width: { xs: '100%', sm: 200 }
            }}
            helperText="Player's overall skill rating (40-99)"
          />

          {/* Player Image Upload */}
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Player Image
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
                size={isMobile ? "small" : "medium"}
              >
                Upload Image
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handlePlayerImageChange}
                />
              </Button>

              {playerImagePreview && (
                <Box sx={{ position: 'relative' }}>
                  <img
                    src={playerImagePreview}
                    alt="Player preview"
                    style={{
                      width: 100,
                      height: 100,
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                  />
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: -10,
                      bgcolor: 'background.paper',
                      '&:hover': { bgcolor: 'background.default' }
                    }}
                    onClick={() => {
                      setPlayerImageFile(null);
                      setPlayerImagePreview(null);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
              )}
            </Box>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              If no image is uploaded, the default player image will be used.
            </Typography>
          </Box>
        </Box>
      );
    } else if (tabValue === 2) {
      // IPL Players Import Tab
      return (
        <Box>
          <ImportIplPlayers
            onImportPlayers={(players) => {
              setIplPlayers(players);
              console.log('IPL players selected for import:', players);
            }}
          />
        </Box>
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={fullScreen}
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : 2,
          overflow: 'hidden', // Changed to hidden for the container
          height: 'auto',     // Let content determine height
          maxHeight: { xs: '95vh', sm: '80vh' }, // Reduced max height
          margin: { xs: 1, sm: 2 },
          // Remove the top position adjustment
        }
      }}
      sx={{
        '& .MuiDialog-container': {
          alignItems: 'center', // Center dialog vertically
        }
      }}
    >
      {/* Dialog Title with Tabs */}
      <DialogTitle sx={{ p: 0 }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: { xs: 2, sm: 3 },
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}>
          <Typography variant="h6">
            Import Players
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant={isMobile ? "fullWidth" : "standard"}
          sx={{
            px: { xs: 2, sm: 3 },
            '& .MuiTab-root': {
              minHeight: isMobile ? 44 : 48,
              py: 1
            }
          }}
        >
          <Tab label="Bulk Import" />
          <Tab label="Create Single Player" />
          <Tab label="Import IPL Players" icon={<CricketIcon />} iconPosition="start" />
        </Tabs>
      </DialogTitle>

      {/* Dialog Content with Tabs Content */}
      <DialogContent
        sx={{
          p: { xs: 2, sm: 3 },
          maxHeight: { xs: 'calc(95vh - 170px)', sm: 'calc(80vh - 170px)' }, // Set max height
          overflowY: 'auto',    // Allow vertical scrolling
          overflowX: 'hidden',  // Prevent horizontal scrolling
        }}
      >
        {error && (
          <Box sx={{ mb: 3 }}>
            <Alert
              severity="error"
              sx={{ mb: errorDetails.length > 0 ? 2 : 0, borderRadius: 1 }}
            >
              {error}
            </Alert>

            {errorDetails.length > 0 && (
              <Paper
                sx={{
                  mt: 1,
                  p: 2,
                  maxHeight: '200px',
                  overflow: 'auto',
                  backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255,0,0,0.05)' : 'rgba(255,0,0,0.02)',
                  border: '1px solid',
                  borderColor: 'error.light',
                  borderRadius: 1
                }}
              >
                <Typography variant="subtitle2" color="error" gutterBottom>
                  Error Details:
                </Typography>
                <Box component="ul" sx={{ pl: 2, m: 0 }}>
                  {errorDetails.map((detail, index) => (
                    <Typography
                      component="li"
                      variant="body2"
                      key={index}
                      sx={{
                        mb: 0.5,
                        color: 'text.secondary',
                        fontSize: '0.85rem'
                      }}
                    >
                      {detail}
                    </Typography>
                  ))}
                </Box>
              </Paper>
            )}

            {skippedDuplicates.length > 0 && (
              <Paper
                sx={{
                  mt: 2,
                  p: 2,
                  maxHeight: '200px',
                  overflow: 'auto',
                  backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255,165,0,0.05)' : 'rgba(255,165,0,0.02)',
                  border: '1px solid',
                  borderColor: 'warning.light',
                  borderRadius: 1
                }}
              >
                <Typography variant="subtitle2" color="warning.main" gutterBottom>
                  Skipped Duplicates:
                </Typography>
                <Box component="ul" sx={{ pl: 2, m: 0 }}>
                  {skippedDuplicates.map((detail, index) => (
                    <Typography
                      component="li"
                      variant="body2"
                      key={index}
                      sx={{
                        mb: 0.5,
                        color: 'text.secondary',
                        fontSize: '0.85rem'
                      }}
                    >
                      {detail}
                    </Typography>
                  ))}
                </Box>
              </Paper>
            )}
          </Box>
        )}

        {renderTabContent()}

        {loading && <LinearProgress sx={{ mt: 3 }} />}
      </DialogContent>

      {/* Dialog Actions */}
      <DialogActions
        sx={{
          p: { xs: 2, sm: 3 },
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: theme => theme.palette.mode === 'light' ? 'grey.50' : 'grey.900',
        }}
      >
        <Button
          onClick={onClose}
          size={isMobile ? "medium" : "large"}
          sx={{
            minWidth: { xs: 80, sm: 100 },
            fontWeight: 'medium',
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={tabValue === 0 ? <UploadIcon sx={{ fontSize: 20 }} /> : null}
          size={isMobile ? "medium" : "large"}
          sx={{
            minWidth: { xs: 120, sm: 140 },
            fontWeight: 'medium',
          }}
        >
          {tabValue === 0 ? 'Import' : tabValue === 1 ? 'Create Player' : 'Import IPL Players'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ImportPlayersDialog;