const path = require('path');
const OCRService = require('./services/ocrService');

/**
 * Clean test to show actual OCR extraction without hardcoded team names
 */
async function testCleanExtraction(scorecardName = 'scorecard1.jpg') {
  try {
    console.log('🧪 CLEAN OCR EXTRACTION TEST');
    console.log('============================');
    console.log(`Testing actual extraction output without hardcoded team names`);
    console.log(`Scorecard: ${scorecardName}\n`);

    const ocrService = new OCRService();
    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);

    console.log(`📸 Processing: ${path.basename(imagePath)}`);
    console.log('⏳ Extracting data...\n');

    // Get the raw extraction result using OCR.Space
    const result = await ocrService.processImageWithOCRSpace(imagePath);

    if (!result.success) {
      console.log('❌ Extraction failed:', result.error);
      return;
    }

    // Display EXACTLY what the OCR extracts (no hardcoding)
    console.log('📊 RAW EXTRACTION RESULTS:');
    console.log('==========================');
    console.log(`Team 1: "${result.team1}"`);
    console.log(`Team 2: "${result.team2}"`);
    console.log(`Venue: ${result.venue}`);
    console.log(`Team 1 Score: ${result.team1Score.runs}-${result.team1Score.wickets} (${result.team1Score.overs} overs)`);
    console.log(`Team 2 Score: ${result.team2Score.runs}-${result.team2Score.wickets} (${result.team2Score.overs} overs)`);
    console.log(`Player of Match: ${result.playerOfMatch}`);
    console.log(`Extraction Method: ${result.extractionMethod}`);
    console.log(`Confidence: ${result.confidence}\n`);

    // Display batting sections using ONLY the extracted team names
    console.log(`🏏 ${result.team1.toUpperCase()} BATTING:`);
    if (result.team1Batsmen && result.team1Batsmen.length > 0) {
      result.team1Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${player.strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }

    console.log(`\n🏏 ${result.team2.toUpperCase()} BATTING:`);
    if (result.team2Batsmen && result.team2Batsmen.length > 0) {
      result.team2Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${player.strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }

    // Display bowling sections using ONLY the extracted team names
    console.log(`\n🎳 ${result.team1.toUpperCase()} BOWLING:`);
    if (result.team1Bowlers && result.team1Bowlers.length > 0) {
      result.team1Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }

    console.log(`\n🎳 ${result.team2.toUpperCase()} BOWLING:`);
    if (result.team2Bowlers && result.team2Bowlers.length > 0) {
      result.team2Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📋 EXTRACTION SUMMARY:');
    console.log(`✅ Team Names: "${result.team1}" vs "${result.team2}"`);
    console.log(`✅ Total Batsmen: ${(result.team1Batsmen?.length || 0) + (result.team2Batsmen?.length || 0)}`);
    console.log(`✅ Total Bowlers: ${(result.team1Bowlers?.length || 0) + (result.team2Bowlers?.length || 0)}`);
    console.log(`✅ Venue: ${result.venue}`);
    console.log(`✅ Player of Match: ${result.playerOfMatch}`);

    console.log('\n🎯 CONCLUSION:');
    console.log('This shows the ACTUAL extraction output without any hardcoded team names.');
    console.log('The team names you see above are exactly what the OCR extracted from the image.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the clean test with optional scorecard parameter
const scorecardName = process.argv[2] || 'scorecard1.jpg';
testCleanExtraction(scorecardName);
