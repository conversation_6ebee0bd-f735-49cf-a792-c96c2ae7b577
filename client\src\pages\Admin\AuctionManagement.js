import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  Snackbar,
  Tooltip,
  Switch,
  FormControlLabel,
  Pagination
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import GavelIcon from '@mui/icons-material/Gavel';
import { useAuth } from '../../hooks/useAuth';
import { format } from 'date-fns';
import {
  getAuctions,
  createAuction,
  updateAuction,
  deleteAuction
} from '../../services/auctionService';
import { getPlayers } from '../../services/playerService';

const AuctionManagement = () => {
  // eslint-disable-next-line no-unused-vars
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [auctions, setAuctions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingAuction, setEditingAuction] = useState(null);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [auctionToDelete, setAuctionToDelete] = useState(null);

  const [availablePlayers, setAvailablePlayers] = useState([]);
  const [selectedPlayer, setSelectedPlayer] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [filteredPlayers, setFilteredPlayers] = useState([]);
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [bulkSelectionEnabled, setBulkSelectionEnabled] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [auctionForm, setAuctionForm] = useState({
    playerId: '',
    startingPrice: 1000,
    minimumBidIncrement: 100,
    startTime: new Date(Date.now() + 3600000).toISOString().slice(0, 16), // 1 hour from now
    endTime: new Date(Date.now() + 86400000).toISOString().slice(0, 16),  // 24 hours from now
    isActive: true,
    currentBid: 0,
    currentBidderId: '',
    reservePrice: 0,
    description: ''
  });

  // Load auctions and available players
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load auctions from API
        const auctionsData = await getAuctions({
          page: currentPage,
          limit: 10,
          search: searchTerm
        });

        setAuctions(auctionsData.auctions);
        setTotalPages(auctionsData.pagination.pages);

        // Load players from API
        const playersData = await getPlayers();
        setAvailablePlayers(playersData.players || []);
      } catch (err) {
        console.error('Error loading auction data:', err);
        setError('Failed to load auction data: ' + (err.msg || err.message));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [currentPage, searchTerm]);

  // Handle page change
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };



  // Handle form input changes
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setAuctionForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle category selection
  const handleCategorySelect = (e) => {
    const category = e.target.value;
    setSelectedCategory(category);
    
    if (category) {
      let filtered;
      switch(category) {
        case 'Batsmen':
          filtered = availablePlayers.filter(p => p.type === 'Batsman');
          break;
        case 'Allrounders':
          filtered = availablePlayers.filter(p => 
            ['Allrounder', 'Batting Allrounder', 'Bowling Allrounder'].includes(p.type)
          );
          break;
        case 'Bowlers':
          filtered = availablePlayers.filter(p => p.type === 'Bowler');
          break;
        case 'WicketKeepers':
          filtered = availablePlayers.filter(p => p.type === 'Wicket Keeper');
          break;
        default:
          filtered = availablePlayers;
      }
      setFilteredPlayers(filtered);
    } else {
      setFilteredPlayers(availablePlayers);
    }
    
    // Reset selections when category changes
    if (bulkSelectionEnabled) {
      setSelectedPlayers([]);
      setAuctionForm(prev => ({ ...prev, playerIds: [] }));
    } else {
      setSelectedPlayer('');
      setAuctionForm(prev => ({ ...prev, playerId: '' }));
    }
  };

  // Handle player selection
  const handlePlayerSelect = (e) => {
    if (bulkSelectionEnabled) {
      const playerIds = e.target.value;
      setSelectedPlayers(playerIds);
      setAuctionForm(prev => ({
        ...prev,
        playerIds
      }));
    } else {
      const playerId = e.target.value;
      setSelectedPlayer(playerId);
      setAuctionForm(prev => ({
        ...prev,
        playerId
      }));
    }
  };

  // Handle bulk selection toggle
  const handleBulkSelectionToggle = (e) => {
    const enabled = e.target.checked;
    setBulkSelectionEnabled(enabled);
    if (enabled) {
      setSelectedPlayer('');
      setSelectedPlayers([]);
      setAuctionForm(prev => ({ ...prev, playerId: '', playerIds: [] }));
    } else {
      setSelectedPlayers([]);
      setSelectedPlayer('');
      setAuctionForm(prev => ({ ...prev, playerIds: [], playerId: '' }));
    }
  };

  // Handle select all players in category
  const handleSelectAllPlayers = () => {
    const playerIds = filteredPlayers.map(player => player._id);
    setSelectedPlayers(playerIds);
    setAuctionForm(prev => ({
      ...prev,
      playerIds
    }));
  };

  // Open dialog to create a new auction
  const handleCreateAuction = () => {
    setEditingAuction(null);
    setSelectedPlayer('');
    setSelectedCategory('');
    setFilteredPlayers(availablePlayers);
    setAuctionForm({
      playerId: '',
      startingPrice: 1000,
      minimumBidIncrement: 100,
      startTime: new Date(Date.now() + 3600000).toISOString().slice(0, 16),
      endTime: new Date(Date.now() + 86400000).toISOString().slice(0, 16),
      isActive: true,
      reservePrice: 0,
      description: ''
    });
    setDialogOpen(true);
  };

  // Open dialog to edit an existing auction
  const handleEditAuction = (auction) => {
    setEditingAuction(auction);
    setSelectedPlayer(auction.player._id);
    setAuctionForm({
      playerId: auction.player._id,
      startingPrice: auction.startingPrice,
      minimumBidIncrement: auction.minimumBidIncrement,
      startTime: new Date(auction.startTime).toISOString().slice(0, 16),
      endTime: new Date(auction.endTime).toISOString().slice(0, 16),
      isActive: auction.isActive,
      reservePrice: auction.reservePrice || 0,
      description: auction.description || '',
      currentBid: auction.currentBid || 0,
      currentBidderId: auction.currentBidder ? auction.currentBidder._id : ''
    });
    setDialogOpen(true);
  };

  // Open confirm dialog to delete an auction
  const openDeleteConfirm = (auction) => {
    setAuctionToDelete(auction);
    setConfirmDeleteOpen(true);
  };

  // Delete an auction
  const handleDeleteAuction = async () => {
    if (!auctionToDelete) return;

    try {
      await deleteAuction(auctionToDelete._id);

      // Refresh auctions list
      const auctionsData = await getAuctions({
        page: currentPage,
        limit: 10,
        search: searchTerm
      });

      setAuctions(auctionsData.auctions);
      setTotalPages(auctionsData.pagination.pages);

      setSuccess('Auction deleted successfully');
      setConfirmDeleteOpen(false);
    } catch (err) {
      console.error('Error deleting auction:', err);
      setError('Failed to delete auction: ' + (err.msg || err.message));
    }
  };

  // Save auction (create or update)
  const handleSaveAuction = async () => {
    try {
      // Validate form
      if (bulkSelectionEnabled) {
        if (!selectedPlayers.length) {
          setError('Please select at least one player');
          return;
        }
      } else {
        if (!auctionForm.playerId) {
          setError('Please select a player');
          return;
        }
      }

      if (auctionForm.startingPrice <= 0) {
        setError('Starting price must be greater than 0');
        return;
      }

      if (new Date(auctionForm.startTime) >= new Date(auctionForm.endTime)) {
        setError('End time must be after start time');
        return;
      }

      if (editingAuction) {
        // Update existing auction
        await updateAuction(editingAuction._id, auctionForm);
        setSuccess('Auction updated successfully');
      } else {
        if (bulkSelectionEnabled) {
          // Create multiple auctions
          const createPromises = selectedPlayers.map(playerId =>
            createAuction({
              ...auctionForm,
              playerId
            })
          );
          await Promise.all(createPromises);
          setSuccess(`Successfully created ${selectedPlayers.length} auctions`);
        } else {
          // Create single auction
          await createAuction(auctionForm);
          setSuccess('Auction created successfully');
        }
      }

      // Refresh auctions list
      const auctionsData = await getAuctions({
        page: currentPage,
        limit: 10,
        search: searchTerm
      });

      setAuctions(auctionsData.auctions);
      setTotalPages(auctionsData.pagination.pages);

      // Close dialog
      setDialogOpen(false);
      // Reset bulk selection state
      setBulkSelectionEnabled(false);
      setSelectedPlayers([]);
    } catch (err) {
      console.error('Error saving auction:', err);
      setError('Failed to save auction: ' + (err.msg || err.message));
    }
  };



  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4">
            Auction Management
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateAuction}
          >
            Create Auction
          </Button>
        </Box>
        <Typography variant="subtitle1" color="text.secondary">
          Create and manage player auctions
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      <Paper sx={{ p: 3, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search auctions by player name, type, or description"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 3 }}
        />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : auctions.length === 0 ? (
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <Typography variant="body1" color="text.secondary">
              No auctions found. Create your first auction by clicking the "Create Auction" button.
            </Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Player</TableCell>
                    <TableCell>Starting Price</TableCell>
                    <TableCell>Current Bid</TableCell>
                    <TableCell>Start Time</TableCell>
                    <TableCell>End Time</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {auctions.map((auction) => (
                    <TableRow key={auction._id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box sx={{ ml: 1 }}>
                            <Typography variant="subtitle2">
                              {auction.player?.name || 'Unknown Player'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {auction.player?.type || 'N/A'} • OVR {auction.player?.ratings?.overall || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>{auction.startingPrice.toLocaleString()} Credits</TableCell>
                      <TableCell>
                        {auction.currentBid.toLocaleString()} Credits
                        {auction.currentBidder && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            by {auction.currentBidder.username || 'Unknown'}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>{format(new Date(auction.startTime), 'MMM dd, yyyy HH:mm')}</TableCell>
                      <TableCell>{format(new Date(auction.endTime), 'MMM dd, yyyy HH:mm')}</TableCell>
                      <TableCell>
                        <Chip
                          label={auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
                          color={
                            auction.status === 'live' ? 'success' :
                            auction.status === 'scheduled' ? 'info' :
                            auction.status === 'completed' ? 'primary' :
                            auction.status === 'cancelled' ? 'error' : 'default'
                          }
                          size="small"
                          variant={auction.status === 'live' ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Edit Auction">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleEditAuction(auction)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Auction">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => openDeleteConfirm(auction)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {totalPages > 1 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Paper>

      {/* Create/Edit Auction Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingAuction ? 'Edit Auction' : 'Create New Auction'}
        </DialogTitle>
        <DialogContent dividers>
          {editingAuction && editingAuction.status === 'live' && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              Warning: This auction is live. Editing it may affect current bidders.
              {editingAuction.currentBid > 0 && ` Current highest bid: ${editingAuction.currentBid.toLocaleString()} Credits.`}
            </Alert>
          )}
          
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Filter by Category</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={handleCategorySelect}
                  label="Filter by Category"
                >
                  <MenuItem value="">
                    <em>All Players</em>
                  </MenuItem>
                  <MenuItem value="Batsmen">Batsmen</MenuItem>
                  <MenuItem value="Allrounders">Allrounders</MenuItem>
                  <MenuItem value="Bowlers">Bowlers</MenuItem>
                  <MenuItem value="WicketKeepers">Wicket Keepers</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={bulkSelectionEnabled}
                      onChange={handleBulkSelectionToggle}
                      color="primary"
                    />
                  }
                  label="Enable Bulk Selection"
                />
                {bulkSelectionEnabled && selectedCategory && (
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleSelectAllPlayers}
                    sx={{ ml: 2 }}
                  >
                    Select All {selectedCategory}
                  </Button>
                )}
              </Box>
              <FormControl fullWidth required>
                <InputLabel>{bulkSelectionEnabled ? 'Select Players' : 'Select Player'}</InputLabel>
                <Select
                  multiple={bulkSelectionEnabled}
                  value={bulkSelectionEnabled ? selectedPlayers : selectedPlayer}
                  onChange={handlePlayerSelect}
                  label={bulkSelectionEnabled ? 'Select Players' : 'Select Player'}
                >
                  {!bulkSelectionEnabled && (
                    <MenuItem value="">
                      <em>Select a player</em>
                    </MenuItem>
                  )}
                  {(selectedCategory ? filteredPlayers : availablePlayers).map((player) => (
                    <MenuItem key={player._id} value={player._id}>
                      {player.name} ({player.type}) - OVR {player.ratings.overall}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Starting Price (Credits)"
                name="startingPrice"
                type="number"
                value={auctionForm.startingPrice}
                onChange={handleFormChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Minimum Bid Increment"
                name="minimumBidIncrement"
                type="number"
                value={auctionForm.minimumBidIncrement}
                onChange={handleFormChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Reserve Price (Optional)"
                name="reservePrice"
                type="number"
                value={auctionForm.reservePrice}
                onChange={handleFormChange}
                helperText="Minimum price for the auction to be successful"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={auctionForm.isActive}
                    onChange={handleFormChange}
                    name="isActive"
                    color="primary"
                  />
                }
                label="Active"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Start Time"
                name="startTime"
                type="datetime-local"
                value={auctionForm.startTime}
                onChange={handleFormChange}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Time"
                name="endTime"
                type="datetime-local"
                value={auctionForm.endTime}
                onChange={handleFormChange}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                multiline
                rows={3}
                value={auctionForm.description}
                onChange={handleFormChange}
                placeholder="Add details about this auction"
              />
            </Grid>

            {/* Current Bid and Bidder Fields - Only shown when editing an auction with bids */}
            {editingAuction && editingAuction.currentBid > 0 && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
                    Current Bid Information
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    As an admin, you can modify the current bid amount and bidder if needed.
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Current Bid Amount"
                    name="currentBid"
                    type="number"
                    value={auctionForm.currentBid}
                    onChange={handleFormChange}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">$</InputAdornment>,
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Current Bidder ID"
                    name="currentBidderId"
                    value={auctionForm.currentBidderId}
                    onChange={handleFormChange}
                    helperText={editingAuction.currentBidder ? `Current bidder: ${editingAuction.currentBidder.username || 'Unknown'}` : 'No current bidder'}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSaveAuction}
            variant="contained"
            color="primary"
            startIcon={<GavelIcon />}
          >
            {editingAuction ? 'Update Auction' : 'Create Auction'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog
        open={confirmDeleteOpen}
        onClose={() => setConfirmDeleteOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            Are you sure you want to delete this auction for {auctionToDelete?.player?.name}?
          </Typography>
          
          {auctionToDelete?.status === 'live' && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Warning: This auction is live. Deleting it will cancel all active bids and refund any bidders.
            </Alert>
          )}
          
          {auctionToDelete?.currentBid > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Current bid: {auctionToDelete?.currentBid.toLocaleString()} Credits
                {auctionToDelete?.currentBidder && ` by ${auctionToDelete.currentBidder.username || 'Unknown'}`}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteAuction} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AuctionManagement;
