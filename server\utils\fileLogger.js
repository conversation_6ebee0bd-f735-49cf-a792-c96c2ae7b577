const fs = require('fs');
const path = require('path');

class FileLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '../logs');
    this.ensureLogsDirectory();
  }

  ensureLogsDirectory() {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  getLogFileName(type = 'general') {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logsDir, `${type}-${date}.log`);
  }

  writeLog(type, level, message, data = null) {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        type,
        message,
        data: data ? (typeof data === 'object' ? JSON.stringify(data, null, 2) : data) : null
      };

      const logLine = `[${timestamp}] [${level}] [${type}] ${message}\n`;
      const dataLine = data ? `DATA: ${JSON.stringify(data, null, 2)}\n` : '';
      const separator = '---\n';

      const fullLog = logLine + dataLine + separator;

      // Write to specific log file
      const logFile = this.getLogFileName(type);
      fs.appendFileSync(logFile, fullLog);

      // Also write to general log
      if (type !== 'general') {
        const generalLogFile = this.getLogFileName('general');
        fs.appendFileSync(generalLogFile, fullLog);
      }

      // Force console output as well
      console.log(`📝 LOGGED TO FILE: ${type} - ${message}`);
      
    } catch (error) {
      // Fallback to console if file logging fails
      console.error('FILE LOGGER ERROR:', error.message);
      console.log(`FALLBACK LOG: [${type}] ${message}`, data);
    }
  }

  // Convenience methods
  info(type, message, data) {
    this.writeLog(type, 'INFO', message, data);
  }

  error(type, message, data) {
    this.writeLog(type, 'ERROR', message, data);
  }

  warn(type, message, data) {
    this.writeLog(type, 'WARN', message, data);
  }

  debug(type, message, data) {
    this.writeLog(type, 'DEBUG', message, data);
  }

  // Template-specific logging
  templateSave(message, data) {
    this.info('template-save', message, data);
  }

  templateError(message, data) {
    this.error('template-save', message, data);
  }

  // Request logging
  request(method, url, data) {
    this.info('requests', `${method} ${url}`, data);
  }

  // Get recent logs for debugging
  getRecentLogs(type = 'general', lines = 50) {
    try {
      const logFile = this.getLogFileName(type);
      if (fs.existsSync(logFile)) {
        const content = fs.readFileSync(logFile, 'utf8');
        const allLines = content.split('\n');
        return allLines.slice(-lines).join('\n');
      }
      return 'No logs found';
    } catch (error) {
      return `Error reading logs: ${error.message}`;
    }
  }

  // Clear old logs
  clearOldLogs(daysOld = 7) {
    try {
      const files = fs.readdirSync(this.logsDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      files.forEach(file => {
        const filePath = path.join(this.logsDir, file);
        const stats = fs.statSync(filePath);
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          console.log(`Deleted old log file: ${file}`);
        }
      });
    } catch (error) {
      console.error('Error clearing old logs:', error.message);
    }
  }
}

// Create singleton instance
const logger = new FileLogger();

module.exports = logger;
