import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';

const TestAuctionPage = () => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [auctions, setAuctions] = useState([]);
  const [bidAmount, setBidAmount] = useState('');
  const [selectedAuction, setSelectedAuction] = useState('');
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io('http://localhost:5000', {
      auth: {
        token: localStorage.getItem('token') // Assuming JWT token is stored
      }
    });

    newSocket.on('connect', () => {
      setConnected(true);
      addMessage('Connected to server', 'success');
    });

    newSocket.on('disconnect', () => {
      setConnected(false);
      addMessage('Disconnected from server', 'error');
    });

    newSocket.on('auction_update', (data) => {
      addMessage(`Auction Update: ${data.type} for auction ${data.auctionId}`, 'info');
      console.log('Auction update received:', data);
    });

    newSocket.on('global_auction_update', (data) => {
      addMessage(`Global Auction Update: ${data.type}`, 'info');
      console.log('Global auction update received:', data);
    });

    setSocket(newSocket);

    // Fetch initial auctions
    fetchAuctions();

    return () => {
      newSocket.close();
    };
  }, []);

  const addMessage = (message, type) => {
    const timestamp = new Date().toLocaleTimeString();
    setMessages(prev => [...prev, { message, type, timestamp }]);
  };

  const fetchAuctions = async () => {
    try {
      const response = await fetch('/api/auctions');
      const data = await response.json();
      if (data.success) {
        setAuctions(data.auctions);
        addMessage(`Loaded ${data.auctions.length} auctions`, 'success');
      }
    } catch (error) {
      addMessage('Error fetching auctions: ' + error.message, 'error');
    }
  };

  const joinAuctionRoom = (auctionId) => {
    if (socket && connected) {
      socket.emit('join_auction', { auctionId });
      addMessage(`Joined auction room: ${auctionId}`, 'success');
    }
  };

  const placeBid = async () => {
    if (!selectedAuction || !bidAmount) {
      addMessage('Please select an auction and enter bid amount', 'error');
      return;
    }

    try {
      const response = await fetch(`/api/auctions/${selectedAuction}/bid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ amount: parseFloat(bidAmount) })
      });

      const data = await response.json();
      if (data.success) {
        addMessage(`Bid placed successfully: $${bidAmount}`, 'success');
        setBidAmount('');
      } else {
        addMessage('Error placing bid: ' + data.message, 'error');
      }
    } catch (error) {
      addMessage('Error placing bid: ' + error.message, 'error');
    }
  };

  const subscribeToUpdates = () => {
    if (socket && connected) {
      socket.emit('subscribe_auction_updates');
      addMessage('Subscribed to auction updates', 'success');
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Real-Time Auction Test Page</h1>
      
      {/* Connection Status */}
      <div style={{ 
        padding: '10px', 
        marginBottom: '20px', 
        backgroundColor: connected ? '#d4edda' : '#f8d7da',
        border: `1px solid ${connected ? '#c3e6cb' : '#f5c6cb'}`,
        borderRadius: '4px'
      }}>
        Status: {connected ? '🟢 Connected' : '🔴 Disconnected'}
      </div>

      {/* Controls */}
      <div style={{ marginBottom: '20px' }}>
        <button onClick={fetchAuctions} style={{ marginRight: '10px' }}>
          Refresh Auctions
        </button>
        <button onClick={subscribeToUpdates} style={{ marginRight: '10px' }}>
          Subscribe to Updates
        </button>
      </div>

      {/* Auctions List */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Available Auctions ({auctions.length})</h3>
        <select 
          value={selectedAuction} 
          onChange={(e) => setSelectedAuction(e.target.value)}
          style={{ width: '300px', padding: '5px' }}
        >
          <option value="">Select an auction...</option>
          {auctions.map(auction => (
            <option key={auction._id} value={auction._id}>
              {auction.player?.name || 'Unknown Player'} - Current: ${auction.currentBid}
            </option>
          ))}
        </select>
        {selectedAuction && (
          <button 
            onClick={() => joinAuctionRoom(selectedAuction)}
            style={{ marginLeft: '10px' }}
          >
            Join Room
          </button>
        )}
      </div>

      {/* Bid Placement */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Place Bid</h3>
        <input
          type="number"
          value={bidAmount}
          onChange={(e) => setBidAmount(e.target.value)}
          placeholder="Enter bid amount"
          style={{ padding: '5px', marginRight: '10px' }}
        />
        <button onClick={placeBid}>Place Bid</button>
      </div>

      {/* Messages Log */}
      <div>
        <h3>Activity Log</h3>
        <div style={{ 
          height: '300px', 
          overflowY: 'scroll', 
          border: '1px solid #ccc', 
          padding: '10px',
          backgroundColor: '#f8f9fa'
        }}>
          {messages.map((msg, index) => (
            <div key={index} style={{ 
              marginBottom: '5px',
              color: msg.type === 'error' ? 'red' : msg.type === 'success' ? 'green' : 'blue'
            }}>
              <span style={{ color: '#666' }}>[{msg.timestamp}]</span> {msg.message}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestAuctionPage;