🔌 Connecting to database...
Attempting to connect to MongoDB at: undefined
⚠️  Database connection failed, continuing with limited functionality...
🏏 TESTING BATTING ORDER FIX
===========================

Initializing Match Outcome Service...
Match Outcome Service initialized successfully

📋 TEST CASE 1: Team 1 is home team and home team batted first
   Match configuration: {"team1IsHomeTeam":true,"homeTeamBattedFirst":true}
   Team 1 (Chennai Super Kings): 164-2
   Team 2 (Mumbai Indians): 162-5
Calculating match outcome from OCR data...
DEBUG - Match object: {"team1IsHomeTeam":true,"homeTeamBattedFirst":true}
DEBUG - homeTeamBattedFirst: true
DEBUG - team1IsHomeTeam: true, calculated team1BattedFirst: true
Match outcome calculated successfully

   📊 RESULT:
   Winner: team1
   Description: Team 1 won by 2 runs
   Expected team1BattedFirst: true
   ✅ PASSED: Description correctly includes 'runs'

📋 TEST CASE 2: Team 1 is home team and home team batted second
   Match configuration: {"team1IsHomeTeam":true,"homeTeamBattedFirst":false}
   Team 1 (Chennai Super Kings): 164-2
   Team 2 (Mumbai Indians): 162-5
Calculating match outcome from OCR data...
DEBUG - Match object: {"team1IsHomeTeam":true,"homeTeamBattedFirst":false}
DEBUG - homeTeamBattedFirst: false
DEBUG - team1IsHomeTeam: true, calculated team1BattedFirst: false
Match outcome calculated successfully

   📊 RESULT:
   Winner: team1
   Description: Team 1 won by 8 wickets
   Expected team1BattedFirst: false
   ✅ PASSED: Description correctly includes 'wickets'

📋 TEST CASE 3: Team 2 is home team and home team batted first
   Match configuration: {"team1IsHomeTeam":false,"homeTeamBattedFirst":true}
   Team 1 (Chennai Super Kings): 164-2
   Team 2 (Mumbai Indians): 162-5
Calculating match outcome from OCR data...
DEBUG - Match object: {"team1IsHomeTeam":false,"homeTeamBattedFirst":true}
DEBUG - homeTeamBattedFirst: true
DEBUG - team1IsHomeTeam: false, calculated team1BattedFirst: false
Match outcome calculated successfully

   📊 RESULT:
   Winner: team1
   Description: Team 1 won by 8 wickets
   Expected team1BattedFirst: false
   ✅ PASSED: Description correctly includes 'wickets'

📋 TEST CASE 4: Team 2 is home team and home team batted second
   Match configuration: {"team1IsHomeTeam":false,"homeTeamBattedFirst":false}
   Team 1 (Chennai Super Kings): 164-2
   Team 2 (Mumbai Indians): 162-5
Calculating match outcome from OCR data...
DEBUG - Match object: {"team1IsHomeTeam":false,"homeTeamBattedFirst":false}
DEBUG - homeTeamBattedFirst: false
DEBUG - team1IsHomeTeam: false, calculated team1BattedFirst: true
Match outcome calculated successfully

   📊 RESULT:
   Winner: team1
   Description: Team 1 won by 2 runs
   Expected team1BattedFirst: true
   ✅ PASSED: Description correctly includes 'runs'

✅ All test cases completed!
