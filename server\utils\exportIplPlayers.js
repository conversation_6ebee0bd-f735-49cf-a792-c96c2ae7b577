/**
 * Utility to export all IPL player names to a CSV file
 * This helps with matching player names in the XLSX import
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// List of IPL team URLs to scrape
const IPL_TEAM_URLS = [
  'https://www.iplt20.com/teams/chennai-super-kings/squad/2025',
  'https://www.iplt20.com/teams/delhi-capitals/squad/2025',
  'https://www.iplt20.com/teams/gujarat-titans/squad/2025',
  'https://www.iplt20.com/teams/kolkata-knight-riders/squad/2025',
  'https://www.iplt20.com/teams/lucknow-super-giants/squad/2025',
  'https://www.iplt20.com/teams/mumbai-indians/squad/2025',
  'https://www.iplt20.com/teams/punjab-kings/squad/2025',
  'https://www.iplt20.com/teams/rajasthan-royals/squad/2025',
  'https://www.iplt20.com/teams/royal-challengers-bengaluru/squad/2025',
  'https://www.iplt20.com/teams/sunrisers-hyderabad/squad/2025'
];

/**
 * Fetch player data from an IPL team URL
 * @param {string} url - IPL team URL
 * @param {string} teamName - Team name
 * @returns {Promise<Array>} - Array of player data
 */
async function fetchTeamPlayers(url, teamName) {
  console.log(`Fetching player data from ${url}`);
  let browser = null;

  try {
    console.log('Launching puppeteer browser...');
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
      timeout: 60000
    });

    const page = await browser.newPage();
    page.setDefaultNavigationTimeout(60000);

    // Enable more detailed logging
    page.on('console', msg => console.log('Browser console:', msg.text()));

    console.log('Navigating to URL...');
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });

    // Take a screenshot for debugging
    await page.screenshot({ path: `${teamName}-debug.png` });
    console.log(`Saved screenshot to ${teamName}-debug.png`);

    console.log('Waiting for player cards to load...');

    let players = [];

    try {
      // Try different selectors to find player cards
      const selectors = [
        '.ih-pcard1',
        '.player-card',
        '[class*="player"]',
        '.squad-player'
      ];

      let playerElements = null;
      let usedSelector = '';

      for (const selector of selectors) {
        console.log(`Trying selector: ${selector}`);
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          const count = await page.evaluate((sel) => document.querySelectorAll(sel).length, selector);
          console.log(`Found ${count} elements with selector ${selector}`);

          if (count > 0) {
            playerElements = await page.evaluate((sel) => {
              return Array.from(document.querySelectorAll(sel)).map(el => {
                // Get the HTML for debugging
                const html = el.outerHTML;
                return { element: el, html };
              });
            }, selector);

            usedSelector = selector;
            console.log(`Using selector ${selector}, found ${playerElements.length} elements`);
            break;
          }
        } catch (e) {
          console.log(`Selector ${selector} failed:`, e.message);
        }
      }

      if (!playerElements || playerElements.length === 0) {
        console.log('No player elements found with any selector, trying to get all page content');

        // Get all page content for debugging
        const pageContent = await page.content();
        console.log('Page content length:', pageContent.length);
        fs.writeFileSync(`${teamName}-page-content.html`, pageContent);
        console.log(`Saved page content to ${teamName}-page-content.html`);

        // Try a more generic approach - look for any elements with player names
        players = await page.evaluate(() => {
          // Look for any elements that might contain player names
          const allElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div');
          const possiblePlayers = [];

          for (const el of allElements) {
            const text = el.textContent.trim();
            // Look for elements that might be player names (at least 2 words, 5-30 chars)
            if (text && text.includes(' ') && text.length > 5 && text.length < 30) {
              possiblePlayers.push({ name: text });
            }
          }

          return possiblePlayers;
        });

        console.log(`Found ${players.length} possible player names using generic approach`);
      } else {
        // Extract player data using the found selector
        players = await page.evaluate((selector) => {
          const playerCards = document.querySelectorAll(selector);
          console.log(`Processing ${playerCards.length} player cards`);

          return Array.from(playerCards).map(card => {
            // Try different selectors for player name
            let name = '';
            const nameSelectors = [
              '.ih-p-name h2',
              '.player-name',
              'h2',
              'h3',
              '[class*="name"]',
              'strong'
            ];

            for (const sel of nameSelectors) {
              const el = card.querySelector(sel);
              if (el && el.textContent.trim()) {
                name = el.textContent.trim();
                break;
              }
            }

            return { name };
          }).filter(player => player.name);
        }, usedSelector);

        console.log(`Extracted ${players.length} player names`);
      }

      // Add team name to each player
      players = players.map(player => ({
        ...player,
        team: teamName
      }));

      // Log the first few players for debugging
      console.log('Sample players:');
      players.slice(0, 5).forEach((p, i) => console.log(`  ${i+1}. ${p.name}`));

    } catch (error) {
      console.error(`Error extracting players from ${teamName}:`, error);
      return [];
    }

    console.log(`Found ${players.length} players for ${teamName}`);
    return players;
  } catch (error) {
    console.error(`Error fetching data for ${teamName}:`, error);
    return [];
  } finally {
    if (browser) {
      console.log('Closing browser...');
      await browser.close();
    }
  }
}

/**
 * Extract team name from URL
 * @param {string} url - IPL team URL
 * @returns {string} - Team name
 */
function getTeamNameFromUrl(url) {
  const matches = url.match(/teams\/([^\/]+)/);
  if (matches && matches[1]) {
    return matches[1]
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  return 'Unknown Team';
}

/**
 * Export players to CSV file
 * @param {Array} players - Array of player data
 * @param {string} outputPath - Path to save CSV file
 */
async function exportToCsv(players, outputPath) {
  // Simplify to only include player names as requested
  const csvWriter = createCsvWriter({
    path: outputPath,
    header: [
      { id: 'name', title: 'Player Name' }
    ]
  });

  // Log the players being exported
  console.log(`Exporting ${players.length} players to CSV`);
  players.forEach((player, index) => {
    if (index < 10) { // Log first 10 for debugging
      console.log(`Player ${index + 1}: ${player.name} (${player.team})`);
    }
  });

  await csvWriter.writeRecords(players);
  console.log(`CSV file saved to ${outputPath}`);
}

/**
 * Main function to scrape all teams and export to CSV
 */
async function exportAllIplPlayers() {
  console.log('Starting IPL player export...');

  let allPlayers = [];

  // Scrape each team
  for (const url of IPL_TEAM_URLS) {
    const teamName = getTeamNameFromUrl(url);
    const players = await fetchTeamPlayers(url, teamName);
    allPlayers = [...allPlayers, ...players];
  }

  console.log(`Total players found: ${allPlayers.length}`);

  // Create output directory if it doesn't exist
  const outputDir = path.join(__dirname, '../exports');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Export to CSV
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputPath = path.join(outputDir, `ipl-players-${timestamp}.csv`);
  await exportToCsv(allPlayers, outputPath);

  console.log('Export completed successfully!');
  return outputPath;
}

// If script is run directly, execute the export
if (require.main === module) {
  exportAllIplPlayers()
    .then(outputPath => {
      console.log(`Export completed. File saved to: ${outputPath}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error during export:', error);
      process.exit(1);
    });
} else {
  // Export for use in other modules
  module.exports = {
    exportAllIplPlayers
  };
}
