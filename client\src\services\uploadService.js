import axios from 'axios';
import { API_URL } from '../config';

const API = axios.create({
  baseURL: `${API_URL}/upload`,
});

// Add auth token to requests if available
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['x-auth-token'] = token;
  }
  return config;
});

// Upload profile image
export const uploadProfileImage = async (formData) => {
  try {
    console.log('uploadProfileImage called with FormData');

    // Check if formData is valid
    if (!(formData instanceof FormData)) {
      console.error('Invalid formData provided to uploadProfileImage');
      throw new Error('Invalid form data');
    }

    // Log the content of the FormData for debugging
    for (let pair of formData.entries()) {
      console.log('FormData contains:', pair[0], pair[1].name);
    }

    // Make sure we're not manually setting Content-Type
    // Let the browser set it with the correct boundary
    const response = await API.post('/profile', formData);

    console.log('Profile image upload response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in uploadProfileImage:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Upload player image
export const uploadPlayerImage = async (formData) => {
  try {
    console.log('uploadPlayerImage called with FormData');

    // Check if formData is valid
    if (!(formData instanceof FormData)) {
      console.error('Invalid formData provided to uploadPlayerImage');
      throw new Error('Invalid form data');
    }

    // Log the content of the FormData for debugging
    for (let pair of formData.entries()) {
      console.log('FormData contains:', pair[0], pair[1].name);
    }

    // Make sure we're not manually setting Content-Type
    // Let the browser set it with the correct boundary
    const response = await API.post('/player', formData);

    console.log('Player image upload response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in uploadPlayerImage:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Upload scorecard image
export const uploadScorecardImage = async (formData) => {
  try {
    console.log('uploadScorecardImage called with FormData');

    // Check if formData is valid
    if (!(formData instanceof FormData)) {
      console.error('Invalid formData provided to uploadScorecardImage');
      throw new Error('Invalid form data');
    }

    // Log the content of the FormData for debugging
    for (let pair of formData.entries()) {
      console.log('FormData contains:', pair[0], pair[1].name);
    }

    // Make sure we're not manually setting Content-Type
    // Let the browser set it with the correct boundary
    const response = await API.post('/scorecard', formData);

    console.log('Scorecard image upload response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in uploadScorecardImage:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

// Upload bulk player images
export const uploadBulkPlayerImages = async (formData) => {
  try {
    console.log('uploadBulkPlayerImages called with FormData');

    // Check if formData is valid
    if (!(formData instanceof FormData)) {
      console.error('Invalid formData provided to uploadBulkPlayerImages');
      throw new Error('Invalid form data');
    }

    // Log the content of the FormData for debugging
    for (let pair of formData.entries()) {
      console.log('FormData contains:', pair[0], pair[1].name);
    }

    // Make sure we're not manually setting Content-Type
    // Let the browser set it with the correct boundary
    const response = await API.post('/players-bulk', formData);

    console.log('Bulk player images upload response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in uploadBulkPlayerImages:', error);
    console.error('Response data:', error.response?.data);
    console.error('Status code:', error.response?.status);

    const data = error.response?.data;
    // Prefer error message fields from server
    const msg = data?.error || data?.msg || error.message;
    throw msg;
  }
};

export default {
  uploadProfileImage,
  uploadPlayerImage,
  uploadScorecardImage,
  uploadBulkPlayerImages
};