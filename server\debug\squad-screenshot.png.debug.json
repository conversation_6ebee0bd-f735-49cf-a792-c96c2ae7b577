{"text_elements": [{"text": "10<", "y": 23.0, "confidence": 0.841120719909668, "box": [1060, 15, 1090, 31]}, {"text": "寮", "y": 438.5, "confidence": 0.8725627660751343, "box": [637, 397, 694, 480]}, {"text": "Sunrisers", "y": 624.5, "confidence": 0.9987195134162903, "box": [329, 602, 521, 647]}, {"text": "<PERSON><PERSON>", "y": 636.0, "confidence": 0.642831027507782, "box": [714, 624, 832, 648]}, {"text": "<PERSON>", "y": 633.5, "confidence": 0.9858919382095337, "box": [863, 620, 1030, 647]}, {"text": "Hyderabad", "y": 679.0, "confidence": 0.9996883869171143, "box": [325, 648, 556, 710]}, {"text": "2016", "y": 762.5, "confidence": 0.9993584156036377, "box": [407, 748, 472, 777]}, {"text": "mlaecnnologesfoteccal", "y": 779.5, "confidence": 0.6116427183151245, "box": [712, 766, 1176, 793]}, {"text": "…n your consent for othe! putposes .i", "y": 818.0, "confidence": 0.8544648885726929, "box": [714, 803, 1162, 833]}, {"text": "secified in the cookie policy", "y": 859.5, "confidence": 0.8705236315727234, "box": [711, 843, 1022, 876]}, {"text": "0ay", "y": 928.5, "confidence": 0.5159068703651428, "box": [1094, 899, 1257, 958]}], "raw_result": "[{'input_path': None, 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[0, ..., 0],\n        ...,\n        [0, ..., 0]],\n\n       ...,\n\n       [[0, ..., 0],\n        ...,\n        [0, ..., 0]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': True, 'use_doc_unwarping': True}, 'angle': 0, 'rot_img': array([[[0, ..., 0],\n        ...,\n        [0, ..., 0]],\n\n       ...,\n\n       [[0, ..., 0],\n        ...,\n        [0, ..., 0]]], dtype=uint8), 'output_img': array([[[255, ..., 255],\n        ...,\n        [  0, ...,   0]],\n\n       ...,\n\n       [[  0, ...,   0],\n        ...,\n        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[1060,   15],\n       ...,\n       [1060,   31]], dtype=int16), array([[871, 385],\n       ...,\n       [879, 517]], dtype=int16), array([[637, 397],\n       ...,\n       [637, 480]], dtype=int16), array([[639, 483],\n       ...,\n       [639, 500]], dtype=int16), array([[329, 602],\n       ...,\n       [329, 647]], dtype=int16), array([[714, 624],\n       ...,\n       [714, 648]], dtype=int16), array([[863, 620],\n       ...,\n       [863, 647]], dtype=int16), array([[325, 654],\n       ...,\n       [326, 710]], dtype=int16), array([[709, 689],\n       ...,\n       [709, 716]], dtype=int16), array([[713, 728],\n       ...,\n       [713, 752]], dtype=int16), array([[1110,  734],\n       ...,\n       [1110,  744]], dtype=int16), array([[407, 748],\n       ...,\n       [407, 777]], dtype=int16), array([[712, 766],\n       ...,\n       [712, 793]], dtype=int16), array([[714, 803],\n       ...,\n       [714, 832]], dtype=int16), array([[602, 841],\n       ...,\n       [602, 876]], dtype=int16), array([[711, 843],\n       ...,\n       [711, 871]], dtype=int16), array([[1094,  899],\n       ...,\n       [1094,  958]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': True}, 'text_det_params': {'limit_side_len': 736, 'limit_type': 'min', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.6, 'unclip_ratio': 1.5}, 'text_type': 'general', 'text_rec_score_thresh': 0.0, 'rec_texts': ['10<', '', '寮', '1', 'Sunrisers', 'Caman', 'Pat Cummins', 'Hyderabad', '∴a:aeoc1e', 'C…', '：', '2016', 'mlaecnnologesfoteccal', '…n your consent for othe! putposes .i', '2', 'secified in the cookie policy', '0ay'], 'rec_scores': [0.841120719909668, 0.0, 0.8725627660751343, 0.2242325097322464, 0.9987195134162903, 0.642831027507782, 0.9858919382095337, 0.9996883869171143, 0.4951745569705963, 0.36361953616142273, 0.48395460844039917, 0.9993584156036377, 0.6116427183151245, 0.8544648885726929, 0.289146363735199, 0.8705236315727234, 0.5159068703651428], 'rec_polys': [array([[1060,   15],\n       ...,\n       [1060,   31]], dtype=int16), array([[871, 385],\n       ...,\n       [879, 517]], dtype=int16), array([[637, 397],\n       ...,\n       [637, 480]], dtype=int16), array([[639, 483],\n       ...,\n       [639, 500]], dtype=int16), array([[329, 602],\n       ...,\n       [329, 647]], dtype=int16), array([[714, 624],\n       ...,\n       [714, 648]], dtype=int16), array([[863, 620],\n       ...,\n       [863, 647]], dtype=int16), array([[325, 654],\n       ...,\n       [326, 710]], dtype=int16), array([[709, 689],\n       ...,\n       [709, 716]], dtype=int16), array([[713, 728],\n       ...,\n       [713, 752]], dtype=int16), array([[1110,  734],\n       ...,\n       [1110,  744]], dtype=int16), array([[407, 748],\n       ...,\n       [407, 777]], dtype=int16), array([[712, 766],\n       ...,\n       [712, 793]], dtype=int16), array([[714, 803],\n       ...,\n       [714, 832]], dtype=int16), array([[602, 841],\n       ...,\n       [602, 876]], dtype=int16), array([[711, 843],\n       ...,\n       [711, 871]], dtype=int16), array([[1094,  899],\n       ...,\n       [1094,  958]], dtype=int16)], 'textline_orientation_angles': [1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1], 'rec_boxes': array([[1060, ...,   31],\n       ...,\n       [1094, ...,  958]], dtype=int16)}]"}