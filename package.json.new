{"name": "big-ant-cricket-24-tournament-management", "version": "1.0.0", "description": "Tournament management system for Big Ant Cricket 24 with player cards, performance tracking, and tournament management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "cd client && npm start", "dev:full": "concurrently \"npm run dev\" \"npm run client\"", "install:paddleocr": "python server/scripts/install_paddle_ocr.py", "test": "jest"}, "dependencies": {"@google-cloud/vision": "^3.1.3", "bcryptjs": "^2.4.3", "concurrently": "^8.0.1", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.1.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22"}, "engines": {"node": ">=14.0.0"}}