/* CSS Variables for theming */
:root {
  --tb-bg-primary: #f8f9fa;
  --tb-bg-secondary: #ffffff;
  --tb-bg-tertiary: #f8f9fa;
  --tb-text-primary: #2c3e50;
  --tb-text-secondary: #6c757d;
  --tb-border-color: #e9ecef;
  --tb-shadow: 0 4px 20px rgba(0,0,0,0.08);
  --tb-shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
}

/* Dark mode variables */
.theme-dark {
  --tb-bg-primary: #111827;
  --tb-bg-secondary: #1f2937;
  --tb-bg-tertiary: #374151;
  --tb-text-primary: #f3f4f6;
  --tb-text-secondary: #d1d5db;
  --tb-border-color: #4b5563;
  --tb-shadow: 0 4px 20px rgba(0,0,0,0.3);
  --tb-shadow-hover: 0 8px 25px rgba(0,0,0,0.4);
}

.template-builder {
  padding: 24px;
  max-width: 1800px;
  margin: 0 auto;
  min-height: 100vh;
  background: var(--tb-bg-primary);
  color: var(--tb-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.template-builder-header {
  text-align: center;
  margin-bottom: 32px;
  background: var(--tb-bg-secondary);
  padding: 32px;
  border-radius: 12px;
  box-shadow: var(--tb-shadow);
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.template-builder-header h2 {
  color: var(--tb-text-primary);
  margin-bottom: 12px;
  font-size: 28px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.template-builder-header p {
  color: var(--tb-text-secondary);
  font-size: 16px;
  margin-bottom: 24px;
  transition: color 0.3s ease;
}

.instructions {
  text-align: left;
  max-width: 800px;
  margin: 0 auto;
  background: var(--tb-bg-tertiary);
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
  transition: background-color 0.3s ease;
}

.instructions h4 {
  color: var(--tb-text-primary);
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.instructions ul {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: var(--tb-text-secondary);
  transition: color 0.3s ease;
}

.template-form {
  background: var(--tb-bg-secondary);
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 32px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  box-shadow: var(--tb-shadow);
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--tb-text-primary);
  transition: color 0.3s ease;
}

.form-group input,
.form-group textarea {
  padding: 12px 16px;
  border: 2px solid var(--tb-border-color);
  border-radius: 8px;
  font-size: 14px;
  background: var(--tb-bg-secondary);
  color: var(--tb-text-primary);
  transition: border-color 0.2s, background-color 0.3s ease, color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Dark mode specific input styling */
.theme-dark .form-group input,
.theme-dark .form-group textarea {
  background: var(--tb-bg-tertiary);
}

.theme-dark .form-group input::placeholder,
.theme-dark .form-group textarea::placeholder {
  color: var(--tb-text-secondary);
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

.template-builder-content {
  display: grid;
  grid-template-columns: 1fr 480px;
  gap: 32px;
  align-items: start;
  min-height: 600px;
}

.canvas-container {
  background: var(--tb-bg-secondary);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--tb-shadow);
  border: 1px solid var(--tb-border-color);
  min-height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.controls-panel {
  background: var(--tb-bg-secondary);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--tb-shadow);
  height: fit-content;
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.field-types h3 {
  margin-bottom: 15px;
  color: var(--tb-text-primary);
  font-size: 16px;
  transition: color 0.3s ease;
}

.field-type-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  margin-bottom: 32px;
}

.field-type-btn {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 2px solid var(--tb-border-color);
  background: var(--tb-bg-secondary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  text-align: left;
  font-weight: 500;
  color: var(--tb-text-primary);
}

.field-type-btn:hover {
  background: var(--tb-bg-tertiary);
  transform: translateY(-1px);
}

.field-type-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
  font-weight: 600;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.regions-list h3 {
  margin-bottom: 15px;
  color: var(--tb-text-primary);
  font-size: 16px;
  transition: color 0.3s ease;
}

.regions-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--tb-border-color);
  border-radius: 4px;
  margin-bottom: 20px;
  background: var(--tb-bg-secondary);
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.region-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-bottom: 1px solid var(--tb-border-color);
  transition: background 0.2s;
}

.region-item:hover {
  background: var(--tb-bg-tertiary);
}

.region-item:last-child {
  border-bottom: none;
}

.region-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.region-info span {
  font-weight: 500;
  margin-right: 8px;
  color: var(--tb-text-primary);
  transition: color 0.3s ease;
}

.region-info small {
  color: var(--tb-text-secondary);
  font-size: 11px;
  transition: color 0.3s ease;
}

.delete-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background 0.2s;
}

.delete-btn:hover {
  background: #ff3742;
}

.template-status {
  margin-bottom: 15px;
  padding: 15px;
  background: var(--tb-bg-tertiary);
  border-radius: 6px;
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.status-indicators {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.status-indicator {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.2s;
}

.status-indicator.valid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-indicator.invalid {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.template-actions {
  display: flex;
  gap: 10px;
}

.save-btn,
.clear-btn,
.manage-btn,
.reset-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #229954;
  transform: translateY(-1px);
}

.save-btn:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  background: #e74c3c;
  color: white;
}

.clear-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.manage-btn {
  background: #3498db;
  color: white;
}

.manage-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.reset-btn {
  background: #f39c12;
  color: white;
}

.reset-btn:hover {
  background: #e67e22;
  transform: translateY(-1px);
}

/* Template List Styles */
.template-list {
  margin-top: 20px;
  padding: 20px;
  background: var(--tb-bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.template-list h3 {
  margin: 0 0 15px 0;
  color: var(--tb-text-primary);
  font-size: 18px;
  transition: color 0.3s ease;
}

.templates-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.template-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: var(--tb-bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--tb-border-color);
  transition: all 0.2s;
}

.template-card:hover {
  border-color: #007bff;
  box-shadow: var(--tb-shadow-hover);
}

.template-info {
  flex: 1;
}

.template-info h4 {
  margin: 0 0 5px 0;
  color: var(--tb-text-primary);
  font-size: 16px;
  transition: color 0.3s ease;
}

.template-info p {
  margin: 0 0 5px 0;
  color: var(--tb-text-secondary);
  font-size: 14px;
  transition: color 0.3s ease;
}

.template-info small {
  color: var(--tb-text-secondary);
  font-size: 12px;
  transition: color 0.3s ease;
}

.template-actions-small {
  display: flex;
  gap: 8px;
}

.load-btn,
.test-btn,
.delete-btn-small {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.load-btn {
  background: #28a745;
  color: white;
}

.load-btn:hover {
  background: #218838;
}

.test-btn {
  background: #17a2b8;
  color: white;
}

.test-btn:hover {
  background: #138496;
}

.delete-btn-small {
  background: #dc3545;
  color: white;
}

.delete-btn-small:hover {
  background: #c82333;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .template-builder-content {
    grid-template-columns: 1fr 420px;
    gap: 24px;
  }
}

@media (max-width: 1200px) {
  .template-builder {
    padding: 20px;
  }

  .template-builder-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .controls-panel {
    order: -1;
    padding: 20px;
  }

  .field-type-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .template-builder {
    padding: 16px;
  }

  .template-form {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .field-type-grid {
    grid-template-columns: 1fr;
  }

  .template-actions {
    flex-direction: column;
    gap: 12px;
  }

  .controls-panel {
    padding: 16px;
  }

  .canvas-container {
    padding: 16px;
  }
}

/* Canvas styling */
canvas {
  max-width: 100%;
  height: auto;
  display: block;
  transition: cursor 0.1s ease;
  border: 2px solid var(--tb-border-color);
  border-radius: 4px;
  background: var(--tb-bg-secondary);
}

canvas:hover {
  border-color: #007bff;
}

/* Scrollbar styling for regions list */
.regions-container::-webkit-scrollbar {
  width: 6px;
}

.regions-container::-webkit-scrollbar-track {
  background: var(--tb-bg-tertiary);
  border-radius: 3px;
}

.regions-container::-webkit-scrollbar-thumb {
  background: var(--tb-border-color);
  border-radius: 3px;
}

.regions-container::-webkit-scrollbar-thumb:hover {
  background: var(--tb-text-secondary);
}

/* Dark mode specific scrollbar styling */
.theme-dark .regions-container::-webkit-scrollbar-track {
  background: var(--tb-bg-tertiary);
}

.theme-dark .regions-container::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.theme-dark .regions-container::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Instructions */
.instructions {
  background: #e8f4fd;
  border: 1px solid #bee5eb;
  border-radius: 6px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.instructions h4 {
  margin: 0 0 10px 0;
  color: #0c5460;
}

.instructions ul {
  margin: 0 0 10px 0;
  padding-left: 20px;
  color: #0c5460;
}

.instructions li {
  margin-bottom: 5px;
}

.instructions p {
  margin: 10px 0 0 0;
  color: #0c5460;
  font-style: italic;
}

/* Canvas Controls */
.canvas-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: var(--tb-bg-tertiary);
  border-radius: 6px;
  margin-bottom: 15px;
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.mapping-mode,
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mapping-mode label,
.zoom-controls label {
  font-weight: 600;
  color: var(--tb-text-primary);
  margin-right: 5px;
  transition: color 0.3s ease;
}

.mode-buttons,
.zoom-buttons {
  display: flex;
  gap: 5px;
}

.mode-btn,
.zoom-buttons button {
  padding: 8px 12px;
  border: 1px solid var(--tb-border-color);
  background: var(--tb-bg-secondary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  color: var(--tb-text-primary);
}

.mode-btn:hover,
.zoom-buttons button:hover {
  background: var(--tb-bg-tertiary);
  border-color: var(--tb-border-color);
}

.mode-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.zoom-buttons span {
  padding: 8px 12px;
  background: var(--tb-bg-tertiary);
  border-radius: 4px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
  color: var(--tb-text-primary);
  border: 1px solid var(--tb-border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.canvas-wrapper {
  overflow: auto;
  max-height: 600px;
  border: 1px solid var(--tb-border-color);
  border-radius: 4px;
  transition: border-color 0.3s ease;
}

.mapping-help {
  margin-top: 10px;
  padding: 10px;
  background: var(--tb-bg-tertiary);
  border: 1px solid var(--tb-border-color);
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.mapping-help p {
  margin: 2px 0;
  color: var(--tb-text-secondary);
  transition: color 0.3s ease;
}

.selected-region-info {
  margin-top: 10px;
  padding: 10px;
  background: var(--tb-bg-tertiary);
  border: 1px solid var(--tb-border-color);
  border-radius: 4px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.selected-region-info h4 {
  margin: 0 0 5px 0;
  color: var(--tb-text-primary);
  font-size: 14px;
  transition: color 0.3s ease;
}

.selected-region-info p {
  margin: 3px 0;
  color: var(--tb-text-secondary);
  font-size: 11px;
  transition: color 0.3s ease;
}
