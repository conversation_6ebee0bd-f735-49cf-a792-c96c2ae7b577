Player matching service initialized successfully
🧪 CLEAN OCR EXTRACTION TEST
============================
Testing actual extraction output without hardcoded team names
Scorecard: scorecard1.jpg

📸 Processing: scorecard1.jpg
⏳ Extracting data...

Processing image with OCR.Space: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
🔧 Enhancing image for better OCR accuracy...
Enhancing image for OCR: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
Enhanced image saved to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1_enhanced.jpg
✅ Using enhanced image: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1_enhanced.jpg
Calling OCR.Space API...
OCR.Space processing failed: Cannot read properties of null (reading 'ocrSpaceTimeout')
Saved OCR.Space error to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\ocr-output\extracted\2025-06-09T15-34-53-876Z_scorecard1_ocrspace_error.json
OCR.Space failed due to error. Attempting fallback to Google Vision API...
[GoogleVision] Processing image: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
Processing image with Google Cloud Vision: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
Google Vision detected 122 text elements
[GoogleVision] Full text annotation: missing, pages: 0
[GoogleVision] Text annotations: missing, elements: 0
[GoogleVision] Text elements: present, count: 122
[GoogleVision] Using provided textElements
Error extracting cricket data: OCRError: Failed to extract text elements
    at GoogleVisionService.extractTextElements (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:652:13)
    at GoogleVisionService.extractCricketData (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:1340:33)
    at GoogleVisionService.processImage (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:478:32)
    at async OCRService.processImageWithOCRSpace (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\ocrService.js:311:23)
    at async testCleanExtraction (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\test-clean-extraction.js:21:20) {
  details: {
    cause: OCRError: No text elements extracted
        at GoogleVisionService.extractTextElements (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:642:15)
        at GoogleVisionService.extractCricketData (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:1340:33)
        at GoogleVisionService.processImage (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\googleVisionService.js:478:32)
        at async OCRService.processImageWithOCRSpace (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\ocrService.js:311:23)
        at async testCleanExtraction (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\test-clean-extraction.js:21:20) {
      details: [Object]
    },
    method: 'textAnnotations'
  }
}
[GoogleVision] Results saved to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\ocr-output\processed\2025-06-09T15-34-54-744Z_scorecard1_google_cricket.json
[GoogleVision] Processing completed in 600ms {
  success: '',
  ocrStatus: 'failed',
  extractedBatsmen: 0,
  processingTime: 600
}
Google Vision fallback also failed: Google Vision processing failed to extract valid cricket data
❌ Test failed: OCR.Space failed: Cannot read properties of null (reading 'ocrSpaceTimeout'). Google Vision fallback failed: Google Vision processing failed to extract valid cricket data
Error stack: Error: OCR.Space failed: Cannot read properties of null (reading 'ocrSpaceTimeout'). Google Vision fallback failed: Google Vision processing failed to extract valid cricket data
    at OCRService.processImageWithOCRSpace (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\services\ocrService.js:327:17)
    at async testCleanExtraction (C:\Users\<USER>\OneDrive\Documents\rpl-new\server\test-clean-extraction.js:21:20)
