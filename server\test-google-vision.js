const GoogleVisionClient = require('./utils/googleVisionClient');
const path = require('path');
const fs = require('fs');

async function testGoogleVision() {
  console.log('🚀 Testing Google Cloud Vision OCR on scorecard9.png...\n');

  try {
    // Test image path
    const imagePath = path.join(__dirname, 'uploads/scorecards/scorecard9.png');
    
    console.log(`📸 Testing with image: ${imagePath}`);

    // Initialize Google Vision client
    const visionClient = new GoogleVisionClient();

    // Perform text detection
    console.log('Detecting text with Google Cloud Vision...');
    const result = await visionClient.detectText(imagePath);
    
    // Save the results to a file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputPath = path.join(__dirname, `ocr-output/extracted/${timestamp}_scorecard9_google_vision.json`);
    fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
    
    console.log(`✅ Google Vision results saved to: ${outputPath}`);
    console.log(`Total text elements detected: ${result.textElements.length}`);
    
    // Look for JOHN MORTIMORE and nearby elements
    console.log('\n👥 Looking for JOHN MORTIMORE:');
    
    // Find JOHN and MORTIMORE elements
    const johnElements = result.textElements.filter(el => 
      el.text.toUpperCase().includes('JOHN') || 
      el.text.toUpperCase().includes('MORTIMORE')
    );
    
    if (johnElements.length > 0) {
      console.log(`Found ${johnElements.length} elements matching JOHN MORTIMORE:`);
      johnElements.forEach(el => {
        console.log(`"${el.text}" at (${el.x}, ${el.y})`);
      });
      
      // Find the JOHN MORTIMORE row specifically
      const johnElement = result.textElements.find(el => el.text.toUpperCase() === 'JOHN');
      const mortiElement = result.textElements.find(el => el.text.toUpperCase() === 'MORTIMORE');
      
      if (johnElement && mortiElement) {
        console.log('\nFound JOHN MORTIMORE specifically:');
        console.log(`"${johnElement.text}" at (${johnElement.x}, ${johnElement.y})`);
        console.log(`"${mortiElement.text}" at (${mortiElement.x}, ${mortiElement.y})`);
        
        // Find elements in the same row as JOHN MORTIMORE
        const johnY = johnElement.y;
        const sameRowElements = result.textElements.filter(el => 
          Math.abs(el.y - johnY) < 20
        );
        
        console.log('\nElements in JOHN MORTIMORE row:');
        sameRowElements.forEach(el => {
          console.log(`"${el.text}" at (${el.x}, ${el.y})`);
        });
        
        // Look for digits that might be runs or balls
        const digitElements = sameRowElements.filter(el => 
          /^\d+$/.test(el.text) || /^\(\d+\)$/.test(el.text)
        );
        
        console.log('\nDigit elements in JOHN MORTIMORE row:');
        digitElements.forEach(el => {
          console.log(`"${el.text}" at (${el.x}, ${el.y})`);
        });
        
        // Try to identify runs and balls
        const runsElement = sameRowElements.find(el => 
          /^\d+$/.test(el.text) && el.x > johnElement.x && el.x < 850
        );
        
        const ballsElement = sameRowElements.find(el => 
          /^\(\d+\)$/.test(el.text) || (el.text === '(' && sameRowElements.some(e => e.text === ')'))
        );
        
        console.log('\nIdentified score elements:');
        if (runsElement) {
          console.log(`Runs: "${runsElement.text}" at (${runsElement.x}, ${runsElement.y})`);
        } else {
          console.log('Runs: Not found');
        }
        
        if (ballsElement) {
          console.log(`Balls: "${ballsElement.text}" at (${ballsElement.x}, ${ballsElement.y})`);
        } else {
          // Try to find separate parentheses
          const openParen = sameRowElements.find(el => el.text === '(');
          const closeParen = sameRowElements.find(el => el.text === ')');
          
          if (openParen && closeParen) {
            // Find digits between parentheses
            const ballsDigit = sameRowElements.find(el => 
              /^\d+$/.test(el.text) && 
              el.x > openParen.x && 
              el.x < closeParen.x
            );
            
            if (ballsDigit) {
              console.log(`Balls: (${ballsDigit.text}) at positions: ( at (${openParen.x}, ${openParen.y}), ${ballsDigit.text} at (${ballsDigit.x}, ${ballsDigit.y}), ) at (${closeParen.x}, ${closeParen.y})`);
            } else {
              console.log('Balls: Parentheses found but no digit between them');
            }
          } else {
            console.log('Balls: Not found');
          }
        }
      }
    } else {
      console.log('❌ JOHN MORTIMORE not found');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testGoogleVision();