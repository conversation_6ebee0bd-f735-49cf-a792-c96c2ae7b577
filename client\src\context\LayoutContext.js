import React, { createContext, useContext, useState } from 'react';

// Create a context for layout-related settings
const LayoutContext = createContext();

/**
 * Provider component for layout settings
 * 
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Child components
 */
export const LayoutProvider = ({ children }) => {
  // State for header visibility
  const [headerVisible, setHeaderVisible] = useState(true);
  
  // Toggle header visibility
  const toggleHeader = (visible) => {
    setHeaderVisible(visible);
  };

  // Context value
  const value = {
    headerVisible,
    toggleHeader
  };

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
};

/**
 * Custom hook to use the layout context
 * 
 * @returns {Object} Layout context value
 */
export const useLayout = () => {
  const context = useContext(LayoutContext);
  
  if (context === undefined) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  
  return context;
};

export default LayoutContext;
