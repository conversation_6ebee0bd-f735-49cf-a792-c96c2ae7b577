const mongoose = require('mongoose');
const redisService = require('./redis');

const connectDB = async () => {
  try {
    // Connect to MongoDB
    console.log('Attempting to connect to MongoDB at:', process.env.MONGODB_URI);
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    
    // Connect to Redis
    console.log('Attempting to connect to Redis...');
    const redisConnected = await redisService.connect();
    
    if (!redisConnected) {
      console.warn('⚠️ Redis connection failed, continuing without caching');
    }
    
    return true;
  } catch (error) {
    console.error(`Database Connection Error: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
};

module.exports = connectDB;