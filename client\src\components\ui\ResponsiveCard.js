import React from 'react';
import PropTypes from 'prop-types';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Box,
  Avatar,
  useMediaQuery,
  useTheme,
  alpha
} from '@mui/material';
import { ArrowForward as ArrowIcon } from '@mui/icons-material';

/**
 * ResponsiveCard - A mobile-responsive card component
 * 
 * This component provides a consistent card layout that adapts to different screen sizes.
 * It maintains consistent height and spacing across all instances.
 */
const ResponsiveCard = ({
  title,
  subtitle,
  icon,
  iconColor = 'primary',
  actionText,
  onClick,
  className,
  sx = {},
  disabled = false,
  variant = 'outlined',
  elevation = 0,
  fullHeight = true,
  minHeight = 220
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  // Determine icon size based on screen size
  const iconSize = isMobile ? 48 : 56;
  
  // Determine padding based on screen size
  const padding = isMobile ? 2 : 3;
  
  // Get color from theme
  const getColor = (colorName) => {
    if (colorName === 'primary') return theme.palette.primary.main;
    if (colorName === 'secondary') return theme.palette.secondary.main;
    if (colorName === 'success') return theme.palette.success.main;
    if (colorName === 'warning') return theme.palette.warning.main;
    if (colorName === 'info') return theme.palette.info.main;
    if (colorName === 'error') return theme.palette.error.main;
    return colorName; // If it's a custom color
  };
  
  const color = getColor(iconColor);
  
  return (
    <Card
      variant={variant}
      elevation={elevation}
      className={className}
      sx={{
        height: fullHeight ? '100%' : 'auto',
        minHeight: minHeight,
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: disabled ? 'none' : 'translateY(-5px)',
          boxShadow: disabled ? 'none' : theme.shadows[3]
        },
        ...sx
      }}
    >
      <CardContent
        sx={{
          p: padding,
          pb: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          flexGrow: 1
        }}
      >
        <Avatar
          sx={{
            bgcolor: alpha(color, 0.1),
            color: color,
            width: iconSize,
            height: iconSize,
            mb: 2
          }}
        >
          {icon}
        </Avatar>
        
        <Typography
          variant={isMobile ? "h6" : "h5"}
          component="h2"
          gutterBottom
          sx={{
            fontWeight: 600,
            fontSize: isMobile ? '1.1rem' : '1.25rem',
            mb: 1
          }}
        >
          {title}
        </Typography>
        
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 2,
            fontSize: isMobile ? '0.85rem' : '0.875rem',
            maxWidth: '90%',
            mx: 'auto'
          }}
        >
          {subtitle}
        </Typography>
      </CardContent>
      
      {actionText && (
        <CardActions
          sx={{
            p: padding,
            pt: 0,
            display: 'flex',
            justifyContent: 'center',
            mt: 'auto'
          }}
        >
          <Button
            variant="contained"
            color={iconColor}
            fullWidth
            onClick={onClick}
            disabled={disabled}
            endIcon={<ArrowIcon />}
            sx={{
              borderRadius: theme.shape.borderRadius,
              textTransform: 'none',
              fontWeight: 600
            }}
          >
            {actionText}
          </Button>
        </CardActions>
      )}
    </Card>
  );
};

ResponsiveCard.propTypes = {
  /**
   * Card title
   */
  title: PropTypes.string.isRequired,
  
  /**
   * Card subtitle or description
   */
  subtitle: PropTypes.string.isRequired,
  
  /**
   * Icon element to display
   */
  icon: PropTypes.node.isRequired,
  
  /**
   * Color for the icon background
   */
  iconColor: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'info', 'error']),
  
  /**
   * Text for the action button
   */
  actionText: PropTypes.string,
  
  /**
   * Function to call when the action button is clicked
   */
  onClick: PropTypes.func,
  
  /**
   * Additional CSS class name
   */
  className: PropTypes.string,
  
  /**
   * Additional styles using MUI's sx prop
   */
  sx: PropTypes.object,
  
  /**
   * Whether the card is disabled
   */
  disabled: PropTypes.bool,
  
  /**
   * Card variant (outlined or elevation)
   */
  variant: PropTypes.oneOf(['outlined', 'elevation']),
  
  /**
   * Card elevation (only used when variant is 'elevation')
   */
  elevation: PropTypes.number,
  
  /**
   * Whether the card should take up the full height of its container
   */
  fullHeight: PropTypes.bool,
  
  /**
   * Minimum height of the card
   */
  minHeight: PropTypes.number
};

export default ResponsiveCard;
