# Simple RPL Cricket Project Setup
# Creates GitHub issues and provides manual project board setup instructions

param(
    [string]$RepoOwner = "rhingonekar",
    [string]$RepoName = "rplwebapp",
    [switch]$DryRun = $false
)

Write-Host "🚀 Simple RPL Cricket Project Setup" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""

# Check GitHub CLI
try {
    gh auth status 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) { throw "Not authenticated" }
    Write-Host "✅ GitHub CLI ready" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub CLI not ready. Please run: gh auth login" -ForegroundColor Red
    exit 1
}

# Define key project issues
$KeyIssues = @(
    @{
        Title = "🔄 2.6 Transfer Market System - Complete Implementation"
        Body = @"
## Status: IN PROGRESS 🔄 (80% Complete)

**Complete the player trading system between teams**

### What's Already Done ✅
- Basic transfer market UI components
- Player listing functionality  
- Market value calculations (partial)
- Transfer market page structure

### What Needs to be Completed ❌
- [ ] **Trading Logic**: Complete the actual player transfer process
- [ ] **Market Value Algorithms**: Implement dynamic pricing based on performance
- [ ] **Transfer History**: Track all player movements between teams
- [ ] **Transaction Validation**: Ensure budget constraints and team limits
- [ ] **Notification System**: Alert users of successful transfers
- [ ] **Transfer Deadlines**: Implement transfer window functionality

### Files to Work On
- ``client/src/pages/TransferMarket/`` (UI components)
- ``server/controllers/transferController.js`` (needs creation/completion)
- ``server/models/Transfer.js`` (transfer history model)

### Priority: 🔴 CRITICAL
This is 80% complete and blocking the player economy system.

### Estimated Time: 1-2 weeks
"@
        Labels = @("🔄 In Progress", "🏆 Trading", "phase-2", "🔴 Critical")
    },
    
    @{
        Title = "🎮 7.1 Skill Points & Rating System - Core Big Ant Cricket 24 Feature"
        Body = @"
## Status: TODO 📋 (0% Complete)

**Implement the core Big Ant Cricket 24 vision: automatic rating increases based on skill points**

### Core Feature Requirements ⭐
- [ ] **1 run = 1 skill point** (base scoring system)
- [ ] **1 wicket = 10 skill points** (bowling rewards)
- [ ] **5000 skill points = +1 rating increase** (progression system)
- [ ] **Admin configurable thresholds** (customizable progression)
- [ ] **Automatic rating updates** after each match
- [ ] **Skill point history tracking** (audit trail)
- [ ] **Rating change notifications** (user feedback)

### Implementation Plan
1. **Database Schema**: Add skill points tracking to Player model
2. **Match Processing**: Update skill points after each match
3. **Rating Calculation**: Implement automatic rating increases
4. **Admin Interface**: Allow threshold configuration
5. **User Interface**: Show skill points and rating progression

### Files to Create/Modify
- ``server/models/Player.js`` (add skillPoints field)
- ``server/services/skillPointsService.js`` (new service)
- ``server/controllers/playerController.js`` (rating updates)
- ``client/src/components/SkillPointsDisplay.js`` (new component)

### Priority: 🔴 CRITICAL
This is THE core feature that makes this a Big Ant Cricket 24 style system.

### Estimated Time: 1 week
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🔴 Critical")
    },
    
    @{
        Title = "🎮 7.2 Performance Milestone Bonuses - Reward Exceptional Performances"
        Body = @"
## Status: TODO 📋 (0% Complete)

**Implement milestone bonus system for exceptional cricket performances**

### Milestone Bonus System ⭐
**Batting Milestones:**
- [ ] **30 runs = +60 bonus points** (good innings)
- [ ] **50 runs = +90 bonus points** (half century)
- [ ] **100 runs = +150 bonus points** (century)

**Bowling Milestones:**
- [ ] **3 wickets = +60 bonus points** (3-wicket haul)
- [ ] **5 wickets = +90 bonus points** (5-wicket haul)

### Technical Requirements
- [ ] **Automatic Detection**: Parse milestones from scorecard OCR
- [ ] **Historical Tracking**: Record all milestone achievements
- [ ] **Milestone Notifications**: Celebrate achievements
- [ ] **Leaderboard Integration**: Track milestone leaders
- [ ] **Admin Configuration**: Customizable bonus amounts

### Implementation Plan
1. **Milestone Detection**: Enhance OCR processing to detect milestones
2. **Bonus Calculation**: Add milestone bonuses to skill points
3. **Achievement System**: Track and display milestone achievements
4. **Notifications**: Celebrate milestone achievements

### Files to Create/Modify
- ``server/services/milestoneService.js`` (new service)
- ``server/controllers/scorecardController.js`` (milestone detection)
- ``client/src/components/MilestoneAchievements.js`` (new component)

### Priority: 🔴 CRITICAL
Essential for the Big Ant Cricket 24 vision - makes exceptional performances meaningful.

### Estimated Time: 1 week
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🔴 Critical")
    },
    
    @{
        Title = "🎮 7.3 Comprehensive Leaderboards - Competitive Gaming Experience"
        Body = @"
## Status: TODO 📋 (0% Complete)

**Create comprehensive leaderboards for competitive cricket gaming**

### Leaderboard Categories 🏆
- [ ] **Most Runs** (overall and format-wise)
- [ ] **Most Wickets** (overall and format-wise)
- [ ] **Most 30s/50s/100s** (batting milestones)
- [ ] **Most 3W/5W Hauls** (bowling milestones)
- [ ] **Man of the Match Awards** (MOM count)
- [ ] **Highest Individual Scores** (best performances)
- [ ] **Best Bowling Figures** (best bowling performances)

### Format-wise Filtering
- [ ] **T10 Format** leaderboards
- [ ] **T20 Format** leaderboards  
- [ ] **ODI Format** leaderboards
- [ ] **Test Format** leaderboards

### Tournament-wise Statistics
- [ ] **Tournament-specific** leaderboards
- [ ] **Overall career** statistics
- [ ] **Season-wise** performance tracking

### Technical Features
- [ ] **Real-time Updates** after each match
- [ ] **Player Ranking System** with points
- [ ] **Historical Performance** tracking
- [ ] **Export Functionality** for statistics

### Implementation Plan
1. **Database Design**: Create leaderboard aggregation tables
2. **Statistics Service**: Calculate and update leaderboards
3. **UI Components**: Create leaderboard display components
4. **Real-time Updates**: Update leaderboards after matches

### Files to Create/Modify
- ``server/models/Leaderboard.js`` (new model)
- ``server/services/leaderboardService.js`` (new service)
- ``client/src/pages/Leaderboards/`` (new page)
- ``client/src/components/LeaderboardTable.js`` (new component)

### Priority: 🟠 HIGH
Critical for competitive gaming experience and player engagement.

### Estimated Time: 2 weeks
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🟠 High")
    }
)

# Function to create issues
function New-KeyIssues {
    param($Issues)
    
    Write-Host "📝 Creating key project issues..." -ForegroundColor Cyan
    $createdIssues = @()
    
    foreach ($issue in $Issues) {
        Write-Host "   Creating: $($issue.Title)" -ForegroundColor White
        
        if ($DryRun) {
            Write-Host "      [DRY RUN] Would create issue" -ForegroundColor Yellow
            continue
        }
        
        try {
            # Create the issue
            $labelsParam = $issue.Labels -join ","
            $issueUrl = gh issue create --title $issue.Title --body $issue.Body --label $labelsParam --repo "$RepoOwner/$RepoName"
            
            Write-Host "      ✅ Created: $issueUrl" -ForegroundColor Green
            $createdIssues += $issueUrl
            
            # Small delay to avoid rate limiting
            Start-Sleep -Milliseconds 500
            
        } catch {
            Write-Host "      ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $createdIssues
}

# Main execution
try {
    Write-Host "Repository: $RepoOwner/$RepoName" -ForegroundColor Cyan
    if ($DryRun) {
        Write-Host "Mode: DRY RUN (no issues will be created)" -ForegroundColor Yellow
    }
    Write-Host ""
    
    # Create key issues
    $createdIssues = New-KeyIssues -Issues $KeyIssues
    
    Write-Host ""
    Write-Host "🎉 Key Issues Created Successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Manual Project Board Setup:" -ForegroundColor Cyan
    Write-Host "1. Go to: https://github.com/$RepoOwner/$RepoName" -ForegroundColor White
    Write-Host "2. Click 'Projects' tab" -ForegroundColor White
    Write-Host "3. Click 'New project'" -ForegroundColor White
    Write-Host "4. Choose 'Board' layout" -ForegroundColor White
    Write-Host "5. Name it: 'RPL Cricket - Big Ant Cricket 24 System'" -ForegroundColor White
    Write-Host "6. Create columns: 📋 Backlog | 🎯 Ready | 🔄 In Progress | 👀 Review | ✅ Complete" -ForegroundColor White
    Write-Host "7. Add the created issues to your board" -ForegroundColor White
    Write-Host ""
    Write-Host "🎯 Immediate Priorities:" -ForegroundColor Yellow
    Write-Host "• 🔄 Complete Transfer Market System (80% done - critical blocker)" -ForegroundColor White
    Write-Host "• 🎮 Implement Skill Points & Rating System (core Big Ant Cricket 24 feature)" -ForegroundColor White
    Write-Host "• 🎮 Add Performance Milestone Bonuses (essential for player progression)" -ForegroundColor White
    Write-Host "• 🎮 Create Comprehensive Leaderboards (competitive gaming experience)" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 Quick Links:" -ForegroundColor Blue
    Write-Host "Repository: https://github.com/$RepoOwner/$RepoName" -ForegroundColor Blue
    Write-Host "Issues: https://github.com/$RepoOwner/$RepoName/issues" -ForegroundColor Blue
    Write-Host "Projects: https://github.com/$RepoOwner/$RepoName/projects" -ForegroundColor Blue
    
} catch {
    Write-Host ""
    Write-Host "❌ Setup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
