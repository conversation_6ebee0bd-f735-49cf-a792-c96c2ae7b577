const redisService = require('../config/redis');

class CacheService {
  constructor() {
    this.client = null;
    this.memoryCache = new Map(); // Fallback in-memory cache
    this.isRedisAvailable = false;
    this.isInitialized = false;
    this.lastRedisCheck = 0;
    this.redisCheckInterval = 30000; // Check Redis status every 30 seconds
  }

  async initialize() {
    if (this.isInitialized) {
      // Only recheck Redis status periodically
      const now = Date.now();
      if (now - this.lastRedisCheck < this.redisCheckInterval) {
        return;
      }
      this.lastRedisCheck = now;
    }

    this.client = redisService.getClient();
    this.isRedisAvailable = redisService.isRedisConnected();
    
    if (!this.isRedisAvailable && !this.isInitialized) {
      console.log('⚠️ Using in-memory cache as Redis fallback');
    }
    this.isInitialized = true;
  }

  async get(key) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        const value = await this.client.get(key);
        return value ? JSON.parse(value) : null;
      } else {
        // Fallback to memory cache
        const cached = this.memoryCache.get(key);
        if (cached && cached.expiry > Date.now()) {
          return cached.value;
        } else if (cached) {
          this.memoryCache.delete(key);
        }
        return null;
      }
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key, value, ttl = 3600) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        const serialized = JSON.stringify(value);
        if (ttl) {
          await this.client.setex(key, ttl, serialized);
        } else {
          await this.client.set(key, serialized);
        }
      } else {
        // Fallback to memory cache
        this.memoryCache.set(key, {
          value,
          expiry: Date.now() + (ttl * 1000)
        });
        
        // Clean up expired entries periodically
        if (this.memoryCache.size > 1000) {
          this.cleanupMemoryCache();
        }
      }
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async del(key) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        await this.client.del(key);
      } else {
        this.memoryCache.delete(key);
      }
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async exists(key) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        const result = await this.client.exists(key);
        return result === 1;
      } else {
        const cached = this.memoryCache.get(key);
        return cached && cached.expiry > Date.now();
      }
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, cached] of this.memoryCache.entries()) {
      if (cached.expiry <= now) {
        this.memoryCache.delete(key);
      }
    }
  }

  // Auction-specific cache methods
  async setAuctionState(auctionId, state, ttl = 3600) {
    return this.set(`auction:${auctionId}`, state, ttl);
  }

  async getAuctionState(auctionId) {
    return this.get(`auction:${auctionId}`);
  }

  async setActiveBids(userId, bids, ttl = 1800) {
    return this.set(`user:${userId}:active_bids`, bids, ttl);
  }

  async getActiveBids(userId) {
    return this.get(`user:${userId}:active_bids`);
  }

  async setLeaderboard(auctionId, leaderboard, ttl = 60) {
    return this.set(`leaderboard:${auctionId}`, leaderboard, ttl);
  }

  async getLeaderboard(auctionId) {
    return this.get(`leaderboard:${auctionId}`);
  }

  // Clear cache patterns
  async clearPattern(pattern) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        const keys = await this.client.keys(pattern);
        if (keys.length > 0) {
          await this.client.del(...keys);
        }
      } else {
        // For memory cache, delete keys matching pattern
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        for (const key of this.memoryCache.keys()) {
          if (regex.test(key)) {
            this.memoryCache.delete(key);
          }
        }
      }
      return true;
    } catch (error) {
      console.error('Cache clear pattern error:', error);
      return false;
    }
  }

  // Bulk operations
  async setBulk(keyValuePairs, ttl = 3600) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable && this.client) {
        const pipeline = this.client.pipeline();
        
        keyValuePairs.forEach(({ key, value }) => {
          pipeline.setex(key, ttl, JSON.stringify(value));
        });
        
        await pipeline.exec();
      } else {
        const expiry = Date.now() + (ttl * 1000);
        keyValuePairs.forEach(({ key, value }) => {
          this.memoryCache.set(key, { value, expiry });
        });
      }
      return true;
    } catch (error) {
      console.error('Bulk cache set error:', error);
      return false;
    }
  }

  isRedisEnabled() {
    return this.isRedisAvailable;
  }
}

module.exports = new CacheService();