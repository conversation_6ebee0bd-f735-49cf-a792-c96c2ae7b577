import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Typography
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { green, red, blue, orange, purple } from '@mui/material/colors';
import axios from 'axios';

// Get the training API URL from localStorage or use default
const getTrainingApiBaseUrl = () => {
  const savedUrl = localStorage.getItem('training_api_url');
  // Try to use the same host as the main app but with a different port
  const currentHost = window.location.hostname;
  return savedUrl || `http://${currentHost}:5001/api`;
};

// Create API instance for the training server
const TRAINING_API = axios.create({
  baseURL: getTrainingApiBaseUrl()
});

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const ImagePreview = styled('img')({
  width: '100%',
  maxHeight: '400px',
  objectFit: 'contain',
  marginTop: '16px',
  marginBottom: '16px',
  border: '1px solid #ddd',
  borderRadius: '4px',
});

const ResultCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  margin: theme.spacing(1, 0),
  backgroundColor: '#f5f5f5',
  borderLeft: `4px solid ${blue[500]}`,
}));

const categoryColors = {
  'team1_name': blue[700],
  'team2_name': red[700],
  'team1_score': blue[500],
  'team2_score': red[500],
  'team1_overs': blue[300],
  'team2_overs': red[300],
  'batsman_name': green[700],
  'batsman_runs': green[500],
  'batsman_balls': green[300],
  'bowler_name': orange[700],
  'bowler_overs': orange[500],
  'bowler_wickets': orange[300],
  'venue': purple[500],
  'match_result': purple[700],
  'player_of_match': purple[300],
};

const ModelTester = () => {
  const [file, setFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState('');

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
      setPreviewUrl(URL.createObjectURL(selectedFile));
      setResults(null);
      setError('');
    }
  };

  const testModel = async () => {
    if (!file) {
      setError('Please select a file first');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await TRAINING_API.post('/predict', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setResults(response.data);
    } catch (err) {
      console.error('Error testing model:', err);
      setError(err.response?.data?.error || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader title="Test Trained Model" />
      <Divider />
      <CardContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="body1" gutterBottom>
              Upload a scorecard image to test if the trained model can correctly identify elements.
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Button
                component="label"
                variant="contained"
                startIcon={<CloudUploadIcon />}
                sx={{ mb: 2 }}
              >
                Upload Scorecard
                <VisuallyHiddenInput type="file" accept="image/*" onChange={handleFileChange} />
              </Button>

              {previewUrl && (
                <ImagePreview src={previewUrl} alt="Scorecard Preview" />
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={testModel}
                disabled={!file || loading}
                sx={{ mb: 2 }}
              >
                {loading ? <CircularProgress size={24} color="inherit" /> : 'Test Model'}
              </Button>

              {error && (
                <Box sx={{ display: 'flex', alignItems: 'center', color: red[500], mb: 2 }}>
                  <ErrorIcon sx={{ mr: 1 }} />
                  <Typography>{error}</Typography>
                </Box>
              )}

              {results && (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', color: green[500], mb: 2 }}>
                    <CheckCircleIcon sx={{ mr: 1 }} />
                    <Typography>Model predictions loaded successfully!</Typography>
                  </Box>

                  <Typography variant="h6" gutterBottom>Results:</Typography>

                  {results.predictions && results.predictions.length > 0 ? (
                    results.predictions.map((pred, index) => (
                      <ResultCard key={index} elevation={1}>
                        <Typography variant="subtitle1" sx={{ color: categoryColors[pred.category] || blue[700], fontWeight: 'bold' }}>
                          {pred.category || 'Unknown'}
                        </Typography>
                        <Typography variant="body1">{pred.text}</Typography>
                        <Typography variant="body2" color="textSecondary">
                          Confidence: {(pred.confidence * 100).toFixed(2)}%
                        </Typography>
                      </ResultCard>
                    ))
                  ) : (
                    <Typography>No elements detected</Typography>
                  )}
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default ModelTester;
