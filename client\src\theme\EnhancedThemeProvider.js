import React, { createContext, useState, useMemo, useContext, useEffect } from 'react';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import { lightTheme, darkTheme, cricketTheme } from './ThemeOptions';

// Create context for theme management
export const EnhancedThemeContext = createContext({
  currentTheme: 'light',
  setTheme: () => {},
  availableThemes: [],
  themeNames: {}
});

// Hook to use the theme context
export const useEnhancedTheme = () => useContext(EnhancedThemeContext);

/**
 * Enhanced Theme Provider Component
 * 
 * Provides theme switching functionality with multiple predefined themes
 * and persists theme selection in localStorage
 */
export const EnhancedThemeProvider = ({ children }) => {
  // Get saved theme from localStorage or use default
  const getSavedTheme = () => {
    const savedTheme = localStorage.getItem('appTheme');
    return savedTheme || 'light';
  };

  // State for current theme
  const [currentTheme, setCurrentTheme] = useState(getSavedTheme);

  // Available themes
  const themeOptions = {
    light: lightTheme,
    dark: darkTheme,
    cricket: cricketTheme
  };

  // Human-readable theme names
  const themeNames = {
    light: 'Light',
    dark: 'Dark',
    cricket: 'Cricket'
  };

  // Create the theme object
  const theme = useMemo(() => {
    return createTheme(themeOptions[currentTheme] || themeOptions.light);
  }, [currentTheme]);

  // Set theme handler
  const handleSetTheme = (themeName) => {
    if (themeOptions[themeName]) {
      setCurrentTheme(themeName);
      localStorage.setItem('appTheme', themeName);
    }
  };

  // Context value
  const contextValue = useMemo(() => ({
    currentTheme,
    setTheme: handleSetTheme,
    availableThemes: Object.keys(themeOptions),
    themeNames
  }), [currentTheme]);

  // Apply theme-specific body classes
  useEffect(() => {
    document.body.classList.remove('theme-light', 'theme-dark', 'theme-cricket');
    document.body.classList.add(`theme-${currentTheme}`);
  }, [currentTheme]);

  return (
    <EnhancedThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </EnhancedThemeContext.Provider>
  );
};

export default EnhancedThemeProvider;
