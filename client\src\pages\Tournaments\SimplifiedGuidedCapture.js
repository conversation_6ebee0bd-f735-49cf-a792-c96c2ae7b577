import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Typography,
  Box,
  Button,
  Paper,
  Alert,
  Grid,
  CircularProgress,
  IconButton,
  Tabs,
  Tab,
  TextField,
  Tooltip
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import FlipCameraAndroidIcon from '@mui/icons-material/FlipCameraAndroid';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import CameraIcon from '@mui/icons-material/Camera';
import CheckIcon from '@mui/icons-material/Check';
import BugReportIcon from '@mui/icons-material/BugReport';
import { uploadScorecard } from '../../services/scorecardService';
import { processScorecardImage } from '../../services/ocrService';
import { useLayout } from '../../context/LayoutContext';
import { useNavigate } from 'react-router-dom';

/**
 * A simplified version of the guided capture component
 * This component focuses solely on getting the camera to work with boundary detection
 *
 * @param {Object} props Component props
 * @param {string} props.tournamentId Tournament ID (optional)
 * @param {string} props.matchId Match ID (optional)
 * @param {Function} props.onUploadSuccess Callback when upload is successful
 * @param {Function} props.onUploadError Callback when upload fails
 * @param {Function} props.onBack Callback to go back to previous page
 */
const SimplifiedGuidedCapture = ({
  tournamentId: propTournamentId,
  matchId: propMatchId,
  onUploadSuccess,
  onUploadError,
  onBack
}) => {
  const navigate = useNavigate();
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const captureCanvasRef = useRef(null);
  const animationFrameId = useRef(null);
  const { toggleHeader } = useLayout();

  // Allow overriding props with session storage values
  let tournamentId = propTournamentId;
  let matchId = propMatchId;

  // Camera states
  const [stream, setStream] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [boundaryDetected, setBoundaryDetected] = useState(false);
  const [availableCameras, setAvailableCameras] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [cameraQuality, setCameraQuality] = useState({ width: 0, height: 0, quality: 'unknown' });

  // Processing states
  const [capturedImage, setCapturedImage] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [processingMessage, setProcessingMessage] = useState('');
  const [uploading, setUploading] = useState(false);
  const [success, setSuccess] = useState(null);

  // Data states
  const [extractedData, setExtractedData] = useState(null);
  const [showDataEditor, setShowDataEditor] = useState(false);
  const [manualEntryMode, setManualEntryMode] = useState(false);
  const [debugMode, setDebugMode] = useState(false);

  // Enumerate available cameras
  const enumerateCameras = async () => {
    try {
      console.log('Enumerating available cameras...');
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');

      console.log('Available video devices:', videoDevices);
      setAvailableCameras(videoDevices);

      // Select the back camera by default if available
      const backCamera = videoDevices.find(device =>
        device.label.toLowerCase().includes('back') ||
        device.label.toLowerCase().includes('rear') ||
        device.label.toLowerCase().includes('environment')
      );

      if (backCamera) {
        console.log('Back camera found:', backCamera.label);
        setSelectedCamera(backCamera.deviceId);
      } else if (videoDevices.length > 0) {
        // Otherwise select the first camera
        console.log('No back camera found, using first camera:', videoDevices[0].label);
        setSelectedCamera(videoDevices[0].deviceId);
      }

      return videoDevices;
    } catch (err) {
      console.error('Error enumerating cameras:', err);
      return [];
    }
  };

  // Initialize camera
  const initCamera = async () => {
    setLoading(true);
    setError(null);

    try {
      // First enumerate cameras if we haven't already
      let cameras = availableCameras;
      if (cameras.length === 0) {
        cameras = await enumerateCameras();
      }

      console.log('Requesting camera access with back camera');

      // Prepare camera constraints with highest possible quality
      const constraints = {
        video: {
          width: { ideal: 3840, min: 1920 }, // Try to get 4K if possible, minimum Full HD
          height: { ideal: 2160, min: 1080 },
          frameRate: { ideal: 30, min: 15 }
        }
      };

      // If we have a selected camera, use it
      if (selectedCamera) {
        constraints.video.deviceId = { exact: selectedCamera };
      } else {
        // Otherwise try to use the back camera
        constraints.video.facingMode = 'environment';
      }

      console.log('Using camera constraints:', constraints);
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      console.log('Camera access granted, stream obtained');

      if (videoRef.current) {
        console.log('Setting video source');
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);

        // Add event listeners with better null checks
        videoRef.current.onloadedmetadata = () => {
          // Make sure videoRef is still valid when this event fires
          if (!videoRef.current) {
            console.warn('Video reference is null in onloadedmetadata event');
            return;
          }

          // Safely access video properties with null checks
          const width = videoRef.current?.videoWidth || 0;
          const height = videoRef.current?.videoHeight || 0;

          // Check if we got valid dimensions
          if (width === 0 || height === 0) {
            console.warn('Invalid video dimensions in onloadedmetadata event:', width, 'x', height);
            // Try again after a short delay
            setTimeout(() => {
              if (videoRef.current) {
                const retryWidth = videoRef.current.videoWidth || 0;
                const retryHeight = videoRef.current.videoHeight || 0;
                if (retryWidth > 0 && retryHeight > 0) {
                  console.log(`Video metadata loaded after retry: ${retryWidth}x${retryHeight}`);

                  // Determine camera quality
                  let quality = 'low';
                  if (retryWidth >= 1920 && retryHeight >= 1080) {
                    quality = 'high';
                  } else if (retryWidth >= 1280 && retryHeight >= 720) {
                    quality = 'medium';
                  }

                  setCameraQuality({ width: retryWidth, height: retryHeight, quality });
                  console.log(`Camera quality: ${quality} (${retryWidth}x${retryHeight})`);

                  startBoundaryDetection();
                } else {
                  console.error('Failed to get valid video dimensions even after retry');
                  setError('Camera initialization failed. Please try again or use the native camera option.');
                }
              }
            }, 500);
            return;
          }

          console.log(`Video metadata loaded: ${width}x${height}`);

          // Determine camera quality
          let quality = 'low';
          if (width >= 1920 && height >= 1080) {
            quality = 'high';
          } else if (width >= 1280 && height >= 720) {
            quality = 'medium';
          }

          setCameraQuality({ width, height, quality });
          console.log(`Camera quality: ${quality} (${width}x${height})`);

          startBoundaryDetection();
        };
      } else {
        console.log('Video ref not available');
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (err) {
      console.error(`Error accessing camera: ${err.message}`);
      setError(`Error accessing camera: ${err.message}`);

      // Try with basic constraints as fallback
      try {
        console.log('Trying with basic video constraints');
        const basicStream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { min: 640, ideal: 1280 },
            height: { min: 480, ideal: 720 }
          }
        });

        if (videoRef.current) {
          console.log('Setting video source with basic constraints');
          videoRef.current.srcObject = basicStream;
          setStream(basicStream);

          // Add event listeners with better null checks (same as above)
          videoRef.current.onloadedmetadata = () => {
            // Make sure videoRef is still valid when this event fires
            if (!videoRef.current) {
              console.warn('Video reference is null in onloadedmetadata event (fallback)');
              return;
            }

            // Safely access video properties with null checks
            const width = videoRef.current?.videoWidth || 0;
            const height = videoRef.current?.videoHeight || 0;

            // Check if we got valid dimensions
            if (width === 0 || height === 0) {
              console.warn('Invalid video dimensions in onloadedmetadata event (fallback):', width, 'x', height);
              // Try again after a short delay
              setTimeout(() => {
                if (videoRef.current) {
                  const retryWidth = videoRef.current.videoWidth || 0;
                  const retryHeight = videoRef.current.videoHeight || 0;
                  if (retryWidth > 0 && retryHeight > 0) {
                    console.log(`Video metadata loaded after retry (fallback): ${retryWidth}x${retryHeight}`);

                    // Determine camera quality
                    let quality = 'low';
                    if (retryWidth >= 1920 && retryHeight >= 1080) {
                      quality = 'high';
                    } else if (retryWidth >= 1280 && retryHeight >= 720) {
                      quality = 'medium';
                    }

                    setCameraQuality({ width: retryWidth, height: retryHeight, quality });
                    console.log(`Camera quality: ${quality} (${retryWidth}x${retryHeight})`);

                    startBoundaryDetection();
                  } else {
                    console.error('Failed to get valid video dimensions even after retry (fallback)');
                    setError('Camera initialization failed. Please try the native camera option instead.');
                  }
                }
              }, 500);
              return;
            }

            console.log(`Video metadata loaded (fallback): ${width}x${height}`);

            // Determine camera quality
            let quality = 'low';
            if (width >= 1920 && height >= 1080) {
              quality = 'high';
            } else if (width >= 1280 && height >= 720) {
              quality = 'medium';
            }

            setCameraQuality({ width, height, quality });
            console.log(`Camera quality: ${quality} (${width}x${height})`);

            startBoundaryDetection();
          };
        } else {
          basicStream.getTracks().forEach(track => track.stop());
        }
      } catch (fallbackErr) {
        console.error(`Fallback also failed: ${fallbackErr.message}`);
        setError(`Camera access failed. Please check your camera permissions and try again.`);
      }
    } finally {
      setLoading(false);
    }
  };

  // Stop camera
  const stopCamera = useCallback(() => {
    if (stream) {
      console.log('Stopping camera stream');
      stream.getTracks().forEach(track => track.stop());
      setStream(null);

      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
        animationFrameId.current = null;
      }
    }
  }, [stream]);

  // Start boundary detection
  const startBoundaryDetection = () => {
    if (!canvasRef.current || !videoRef.current) {
      console.error('Canvas or video ref not available');
      return;
    }

    const detectBoundaries = () => {
      try {
        if (!canvasRef.current || !videoRef.current) return;

        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // Make sure video is playing
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          // Set canvas dimensions to match video
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          // Draw video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Simple boundary detection (just for demonstration)
          const detected = Math.random() > 0.7; // Random detection for demo

          // Draw guide overlay
          drawGuideOverlay(ctx, canvas.width, canvas.height, detected);

          // Update state if boundary detection status changed
          if (detected !== boundaryDetected) {
            setBoundaryDetected(detected);
          }
        }

        // Continue detection loop
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      } catch (err) {
        console.error('Error in boundary detection:', err);
        // Try to continue despite errors
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      }
    };

    // Start detection loop
    detectBoundaries();
  };

  // Draw guide overlay
  const drawGuideOverlay = (ctx, width, height, detected) => {
    // Draw rectangle guide
    const padding = 50;
    const rectWidth = width - (padding * 2);
    const rectHeight = height - (padding * 2);

    // Set style based on detection status
    ctx.strokeStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.lineWidth = 4;

    // Draw rectangle
    ctx.beginPath();
    ctx.rect(padding, padding, rectWidth, rectHeight);
    ctx.stroke();

    // Add corner markers
    const cornerSize = 30;

    // Top-left corner
    ctx.beginPath();
    ctx.moveTo(padding, padding + cornerSize);
    ctx.lineTo(padding, padding);
    ctx.lineTo(padding + cornerSize, padding);
    ctx.stroke();

    // Top-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, padding);
    ctx.lineTo(width - padding, padding);
    ctx.lineTo(width - padding, padding + cornerSize);
    ctx.stroke();

    // Bottom-left corner
    ctx.beginPath();
    ctx.moveTo(padding, height - padding - cornerSize);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(padding + cornerSize, height - padding);
    ctx.stroke();

    // Bottom-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.lineTo(width - padding, height - padding - cornerSize);
    ctx.stroke();

    // Add text guide
    ctx.fillStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';

    if (detected) {
      ctx.fillText('Scorecard Detected!', width / 2, height - 20);
    } else {
      ctx.fillText('Align scorecard within boundaries', width / 2, height - 20);
    }
  };

  // Capture current frame as image
  const captureImage = useCallback(() => {
    if (!videoRef.current || capturedImage || processing) return;

    try {
      setProcessing(true);

      // Create a canvas for the captured image
      const video = videoRef.current;
      const canvas = captureCanvasRef.current;

      if (!canvas) {
        console.error('Capture canvas ref is not available');
        setError('Failed to initialize capture canvas. Please try again.');
        setProcessing(false);
        return;
      }

      // Log video dimensions for debugging
      console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);

      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.error('Video dimensions are invalid:', video.videoWidth, 'x', video.videoHeight);
        setError('Camera stream not properly initialized. Please try again.');
        setProcessing(false);
        return;
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      console.log('Canvas dimensions set to:', canvas.width, 'x', canvas.height);

      // Draw current video frame to canvas
      const ctx = canvas.getContext('2d');

      // Clear the canvas first
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw the video frame
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Check if the canvas has content
      try {
        // Get image data to verify it's not empty/black
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Check if image is mostly black (could indicate capture issue)
        let blackPixels = 0;
        let totalPixels = data.length / 4;

        for (let i = 0; i < data.length; i += 4) {
          // Check if pixel is very dark
          if (data[i] < 20 && data[i + 1] < 20 && data[i + 2] < 20) {
            blackPixels++;
          }
        }

        const blackPercentage = (blackPixels / totalPixels) * 100;
        console.log(`Image is ${blackPercentage.toFixed(2)}% black pixels`);

        if (blackPercentage > 90) {
          console.error('Captured image is mostly black');
          setError('Captured image appears to be black. Please check camera permissions and try again with better lighting.');
          setProcessing(false);
          return;
        }
      } catch (imageDataError) {
        console.error('Error checking image data:', imageDataError);
      }

      // Apply image enhancement for dark background scorecards
      try {
        // Apply contrast enhancement for dark background with white text
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Apply contrast enhancement algorithm specifically for dark backgrounds with white text
        for (let i = 0; i < data.length; i += 4) {
          // Increase contrast for better text recognition
          // This works well for dark backgrounds with white text
          const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;

          // Threshold-based enhancement
          if (avg > 100) { // If pixel is bright (likely text)
            // Make it brighter
            data[i] = Math.min(255, data[i] * 1.5);
            data[i + 1] = Math.min(255, data[i + 1] * 1.5);
            data[i + 2] = Math.min(255, data[i + 2] * 1.5);
          } else { // If pixel is dark (likely background)
            // Make it darker
            data[i] = data[i] * 0.5;
            data[i + 1] = data[i + 1] * 0.5;
            data[i + 2] = data[i + 2] * 0.5;
          }
        }

        // Put the enhanced image data back to canvas
        ctx.putImageData(imageData, 0, 0);
        console.log('Applied image enhancement for dark background scorecard');
      } catch (enhanceError) {
        console.error('Error enhancing image:', enhanceError);
        // Continue with original image if enhancement fails
      }

      // Convert canvas to image data URL with maximum quality
      const imageDataUrl = canvas.toDataURL('image/jpeg', 1.0);
      console.log('Image captured successfully, data URL length:', imageDataUrl.length);
      setCapturedImage(imageDataUrl);

      // Stop camera after capturing
      stopCamera();

      // Show header again for review screen
      toggleHeader(true);

      // Process the image with OCR
      // Use regular promises instead of await since we're in a non-async function
      fetch(imageDataUrl)
        .then(r => r.blob())
        .then(blob => {
          // Show a message about OCR processing
          setProcessingMessage('Enhancing image for better recognition...');
          return processScorecardImage(blob, { debug: debugMode });
        })
        .then(extractedData => {
          // Set the extracted data
          setExtractedData(extractedData);
          setProcessingMessage('Data extraction successful!');

          // Show success message briefly before clearing
          setTimeout(() => {
            setProcessingMessage('');
          }, 1500);

          setError('');
        })
        .catch(ocrError => {
          console.error('Error processing image with OCR:', ocrError);

          // Create a more detailed error message for debug mode
          let errorMessage = ocrError.message || 'Failed to process the image';

          if (debugMode) {
            errorMessage = `OCR processing failed: ${errorMessage}. Check the console for detailed debug information.`;
          } else {
            errorMessage = `Failed to process the image. Please try again with a clearer image or enable debug mode for more details.`;
          }

          // Show error message to user
          setError(errorMessage);

          // Clear any extracted data
          setExtractedData(null);

          // Provide specific guidance based on the error
          if (errorMessage.includes('format') || errorMessage.includes('content')) {
            setProcessingMessage('Try capturing a different view of the scorecard.');
          } else if (errorMessage.includes('large')) {
            setProcessingMessage('Try moving further away from the screen to capture the entire scorecard.');
          } else if (errorMessage.includes('unavailable') || errorMessage.includes('service')) {
            setProcessingMessage('Network issue detected. Try again when you have a better connection.');
          } else {
            setProcessingMessage('Try capturing the image again with better lighting and alignment.');
          }

          // Log additional debugging information
          console.log('Captured image dimensions:', capturedImage ? 'Available' : 'Not available');
          console.log('Network status:', navigator.onLine ? 'Online' : 'Offline');
        })
        .finally(() => {
          setProcessing(false);
        });
    } catch (err) {
      console.error('Error capturing image:', err);
      setError(`Error capturing image: ${err.message}`);
      setProcessing(false);
    }
  }, [capturedImage, processing, stopCamera, toggleHeader]);

  // Reset capture and go back to camera view
  const resetCapture = () => {
    setCapturedImage(null);
    setExtractedData(null);
    setShowDataEditor(false);
    setManualEntryMode(false);
    setError(null);
    setSuccess(null);
    setProcessingMessage('');

    // Hide header again for camera view
    toggleHeader(false);

    // Restart camera
    initCamera();
  };



  // Handle upload
  const handleUpload = () => {
    if (!capturedImage || !extractedData) return;

    setUploading(true);

    // In a real implementation, this would call an API to upload the data
    if (tournamentId && matchId) {
      const formData = new FormData();

      // Convert data URL to blob
      fetch(capturedImage)
        .then(r => r.blob())
        .then(blob => {
          formData.append('scorecard', blob, 'scorecard.jpg');

          // Add match data
          formData.append('matchId', matchId);
          formData.append('tournamentId', tournamentId);
          formData.append('matchData', JSON.stringify(extractedData));

          // Upload to server
          return uploadScorecard(formData);
        })
        .then(result => {
          setSuccess('Scorecard uploaded successfully!');

          // Check if we have a return path in session storage
          const captureContextStr = sessionStorage.getItem('captureContext');
          if (captureContextStr) {
            try {
              const captureContext = JSON.parse(captureContextStr);
              // Clear the capture context
              sessionStorage.removeItem('captureContext');

              // Wait a moment before navigating
              setTimeout(() => {
                // Navigate to the return path
                if (captureContext.returnPath) {
                  navigate(captureContext.returnPath);
                }
              }, 1500);
            } catch (err) {
              console.error('Error parsing capture context:', err);
            }
          }

          if (onUploadSuccess) {
            onUploadSuccess(result);
          }
        })
        .catch(error => {
          console.error('Error uploading scorecard:', error);
          setError(error.message || 'Failed to upload scorecard');
          if (onUploadError) {
            onUploadError(error);
          }
        })
        .finally(() => {
          setUploading(false);
        });
    } else {
      // Demo mode - just simulate success
      setTimeout(() => {
        setSuccess('Scorecard processed successfully! (Demo Mode)');

        if (onUploadSuccess) {
          onUploadSuccess({
            scorecard: {
              url: capturedImage,
              uploadedAt: new Date().toISOString(),
              data: extractedData
            }
          });
        }

        setUploading(false);
      }, 1500);
    }
  };

  // Handle back button
  const handleBack = () => {
    stopCamera();

    // Make sure header is visible when navigating away
    toggleHeader(true);

    // Check if we have a return path in session storage
    const captureContextStr = sessionStorage.getItem('captureContext');
    if (captureContextStr) {
      try {
        const captureContext = JSON.parse(captureContextStr);
        if (captureContext.returnPath) {
          // Clear the capture context
          sessionStorage.removeItem('captureContext');
          // Navigate to the return path
          navigate(captureContext.returnPath);
          return;
        }
      } catch (err) {
        console.error('Error parsing capture context:', err);
      }
    }

    // Fall back to onBack prop if available
    if (onBack) {
      onBack();
    } else {
      // Default fallback - go to tournaments page
      navigate('/tournaments');
    }
  };

  // Check for capture context in session storage
  useEffect(() => {
    const captureContextStr = sessionStorage.getItem('captureContext');
    if (captureContextStr) {
      try {
        const captureContext = JSON.parse(captureContextStr);
        // If we have tournament and match IDs, use them
        if (captureContext.tournamentId && captureContext.matchId) {
          console.log('Found capture context:', captureContext);
          // Set tournament and match IDs from context
          tournamentId = captureContext.tournamentId;
          matchId = captureContext.matchId;
        }
      } catch (err) {
        console.error('Error parsing capture context:', err);
      }
    }
  }, []);

  // Hide header on mount, show on unmount
  useEffect(() => {
    // Hide header when component mounts
    toggleHeader(false);

    // Initialize camera
    initCamera();

    return () => {
      // Stop camera and show header when component unmounts
      stopCamera();
      toggleHeader(true);
    };
  }, [stopCamera, toggleHeader]);

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto', py: 0 }}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 2,
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 1000
      }}>
        <IconButton
          onClick={handleBack}
          sx={{
            mr: 1,
            bgcolor: 'rgba(0,0,0,0.5)',
            color: 'white',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.7)'
            }
          }}
        >
          <ArrowBackIcon />
        </IconButton>
      </Box>

      {/* Camera quality indicator */}
      {!capturedImage && cameraQuality.quality !== 'unknown' && (
        <Box sx={{
          position: 'absolute',
          bottom: 80,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          bgcolor: 'rgba(0,0,0,0.5)',
          color: 'white',
          px: 2,
          py: 0.5,
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <Box sx={{
            width: 10,
            height: 10,
            borderRadius: '50%',
            bgcolor: cameraQuality.quality === 'high' ? 'success.main' :
                    cameraQuality.quality === 'medium' ? 'warning.main' : 'error.main'
          }} />
          <Typography variant="caption">
            {cameraQuality.quality === 'high' ? 'HD Camera' :
             cameraQuality.quality === 'medium' ? 'Medium Quality' : 'Low Quality'}
            ({cameraQuality.width}x{cameraQuality.height})
          </Typography>
        </Box>
      )}

      {/* Camera selection UI */}
      {!capturedImage && (
        <Box sx={{
          position: 'absolute',
          top: 10,
          right: 10,
          zIndex: 1000,
          display: 'flex',
          gap: 1
        }}>
          {availableCameras.length > 1 && (
            <Tooltip title="Switch Camera">
              <IconButton
              onClick={async () => {
                // Stop current camera
                stopCamera();

                // Get current camera index
                const currentIndex = availableCameras.findIndex(camera => camera.deviceId === selectedCamera);

                // Select next camera
                const nextIndex = (currentIndex + 1) % availableCameras.length;
                setSelectedCamera(availableCameras[nextIndex].deviceId);

                // Restart camera with new selection
                await initCamera();
              }}
              sx={{
                bgcolor: 'rgba(0,0,0,0.5)',
                color: 'white',
                '&:hover': {
                  bgcolor: 'rgba(0,0,0,0.7)'
                }
              }}
            >
              <FlipCameraAndroidIcon />
            </IconButton>
            </Tooltip>
          )}

          <Tooltip title="Use Native Camera App (Recommended for Better Quality)">
            <IconButton
              onClick={() => {
                // Show a message explaining the native camera option
                setProcessing(true);
                setProcessingMessage('Opening native camera. This option provides better image quality and stability.');

                // Short delay to show the message before opening the native camera
                setTimeout(() => {
                  // Open native camera app
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.accept = 'image/*';
                  input.capture = 'environment';

                  // Reset processing state if user cancels the camera
                  setTimeout(() => {
                    setProcessing(false);
                    setProcessingMessage('');
                  }, 3000); // Give user 3 seconds to interact with camera

                  input.onchange = (e) => {
                    if (e.target.files && e.target.files[0]) {
                      const file = e.target.files[0];
                      const reader = new FileReader();

                      // Show processing message again
                      setProcessing(true);
                      setProcessingMessage('Processing high-quality image from native camera...');

                      reader.onload = (event) => {
                        // Stop camera
                        stopCamera();

                        // Set captured image
                        setCapturedImage(event.target.result);

                        // Show header
                        toggleHeader(true);

                        // Process the image with detailed extraction
                        // Convert data URL to blob
                        fetch(event.target.result)
                          .then(r => r.blob())
                          .then(blob => {
                            // Force debug mode to true to get detailed player statistics
                            return processScorecardImage(blob, { debug: true });
                          })
                          .then(extractedData => {
                            setExtractedData(extractedData);
                            setProcessingMessage('Data extraction successful!');
                            setTimeout(() => setProcessingMessage(''), 1500);
                            setError('');

                            // Log extraction method for debugging
                            console.log('Extraction method:', extractedData.extractionMethod);

                            // Check if we got player statistics
                            if (extractedData.homeTeam?.batsmen && extractedData.homeTeam.batsmen.length > 0) {
                              console.log('Successfully extracted player statistics!');
                              console.log('Home team batsmen:', extractedData.homeTeam.batsmen);
                              console.log('Away team batsmen:', extractedData.awayTeam?.batsmen);
                              console.log('Home team bowlers:', extractedData.homeTeam?.bowlers);
                              console.log('Away team bowlers:', extractedData.awayTeam?.bowlers);
                            } else {
                              console.log('No player statistics found in extracted data');
                              console.log('Full extracted data:', extractedData);
                            }
                          })
                          .catch(error => {
                            console.error('Error processing image:', error);

                            // Create a more detailed error message for debug mode
                            let errorMessage = error.message || 'Failed to process the image';

                            if (debugMode) {
                              errorMessage = `OCR processing failed: ${errorMessage}. Check the console for detailed debug information.`;
                            } else {
                              errorMessage = `Failed to process the image. Please try again with a clearer image or enable debug mode for more details.`;
                            }

                            setError(errorMessage);
                            setExtractedData(null);
                          })
                          .finally(() => {
                            setProcessing(false);
                          });
                      };

                      reader.readAsDataURL(file);
                    } else {
                      // User canceled the camera
                      setProcessing(false);
                      setProcessingMessage('');
                    }
                  };

                  input.click();
                }, 1000);
              }}
              sx={{
                bgcolor: 'rgba(0,0,0,0.5)',
                color: 'white',
                border: '2px solid #4caf50', // Green border to highlight recommended option
                '&:hover': {
                  bgcolor: 'rgba(0,0,0,0.7)'
                }
              }}
            >
              <PhotoCameraIcon />
              <Box sx={{
                position: 'absolute',
                top: -8,
                right: -8,
                width: 16,
                height: 16,
                borderRadius: '50%',
                bgcolor: 'success.main',
                border: '2px solid white'
              }} />
            </IconButton>
          </Tooltip>

          <Tooltip title={debugMode ? "Debug Mode ON - Click to disable" : "Enable Debug Mode for detailed extraction info"}>
            <IconButton
              onClick={() => setDebugMode(!debugMode)}
              sx={{
                bgcolor: debugMode ? 'rgba(255,152,0,0.6)' : 'rgba(0,0,0,0.5)',
                color: 'white',
                border: debugMode ? '2px solid #ff9800' : 'none',
                '&:hover': {
                  bgcolor: debugMode ? 'rgba(255,152,0,0.8)' : 'rgba(0,0,0,0.7)'
                }
              }}
            >
              <BugReportIcon />
              {debugMode && (
                <Box sx={{
                  position: 'absolute',
                  top: -8,
                  right: -8,
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  bgcolor: 'warning.main',
                  border: '2px solid white'
                }} />
              )}
            </IconButton>
          </Tooltip>
        </Box>
      )}

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            '& .MuiAlert-message': {
              fontSize: '1rem'
            }
          }}
          action={
            <Box>
              {capturedImage && (
                <Button
                  color="inherit"
                  size="small"
                  onClick={resetCapture}
                  sx={{ mr: 1 }}
                >
                  Retake Photo
                </Button>
              )}
              {capturedImage && !manualEntryMode && (
                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={() => {
                    setManualEntryMode(true);
                    setError(null);
                    // Create empty template for manual entry
                    setExtractedData({
                      homeTeam: {
                        name: '',
                        score: {
                          runs: 0,
                          wickets: 0,
                          overs: '0.0'
                        }
                      },
                      awayTeam: {
                        name: '',
                        score: {
                          runs: 0,
                          wickets: 0,
                          overs: '0.0'
                        }
                      },
                      result: '',
                      playerOfMatch: ''
                    });
                    setShowDataEditor(true);
                  }}
                >
                  Enter Manually
                </Button>
              )}
            </Box>
          }
        >
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            Error Processing Image
          </Typography>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {!capturedImage ? (
        // Camera view - full screen
        <Box sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          bgcolor: 'black',
          zIndex: 900
        }}>
          <Box sx={{
            position: 'relative',
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden'
          }}>
            {loading ? (
              <CircularProgress />
            ) : !stream ? (
              <Box sx={{
                textAlign: 'center',
                p: 3,
                width: '100%',
                maxWidth: 400,
                bgcolor: 'rgba(0,0,0,0.7)',
                borderRadius: 2
              }}>
                <Typography variant="h6" color="white" gutterBottom>
                  Camera Access Required
                </Typography>
                <Typography variant="body2" color="white" sx={{ mb: 2, mx: 'auto', maxWidth: '90%' }}>
                  This feature needs access to your camera to capture the scorecard.
                </Typography>
                <Button
                  variant="contained"
                  onClick={initCamera}
                  disabled={loading}
                  size="large"
                  sx={{ width: '80%', mx: 'auto' }}
                >
                  Start Camera
                </Button>
              </Box>
            ) : null}

            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />

            <canvas
              ref={canvasRef}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover'
              }}
            />

            {/* Status indicator */}
            {stream && boundaryDetected && (
              <Box sx={{
                position: 'absolute',
                top: 70,
                right: 16,
                bgcolor: 'success.main',
                color: 'white',
                px: 2,
                py: 1,
                borderRadius: 2,
                fontSize: '0.875rem',
                fontWeight: 'medium'
              }}>
                Scorecard Detected
              </Box>
            )}

            {/* Capture button - positioned at bottom center */}
            {stream && (
              <Box sx={{
                position: 'absolute',
                bottom: 30,
                left: 0,
                right: 0,
                display: 'flex',
                justifyContent: 'center',
                zIndex: 1000
              }}>
                <Button
                  variant="contained"
                  color={boundaryDetected ? "success" : "primary"}
                  onClick={captureImage}
                  startIcon={<CameraIcon />}
                  disabled={processing}
                  size="large"
                  sx={{
                    borderRadius: '30px',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem'
                  }}
                >
                  {boundaryDetected ? "Capture Now" : "Capture"}
                </Button>
              </Box>
            )}
          </Box>
        </Box>

      ) : (
        // Captured image and data view
        <>
          {processing ? (
            // Processing view
            <Paper sx={{ p: 4, textAlign: 'center', mb: 2 }}>
              <CircularProgress size={60} />
              <Typography variant="h6" sx={{ mt: 2 }}>
                {processingMessage || 'Processing Scorecard...'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {processingMessage ? 'This may take a moment' : 'Extracting match data from image'}
              </Typography>
            </Paper>
          ) : (
            <>
              {/* Tabs for switching between image and data */}
              <Paper sx={{ mb: 2 }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs
                    value={showDataEditor ? 1 : 0}
                    onChange={(e, newValue) => setShowDataEditor(newValue === 1)}
                    variant="fullWidth"
                  >
                    <Tab label="Captured Image" />
                    <Tab label="Match Data" disabled={!extractedData} />
                  </Tabs>
                </Box>

                {/* Content based on selected tab */}
                {!showDataEditor ? (
                  // Image view
                  <Box sx={{ p: 2, textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1">
                        Captured Scorecard
                      </Typography>
                      {debugMode && (
                        <Button
                          size="small"
                          variant="contained"
                          color="warning"
                          startIcon={<BugReportIcon />}
                          onClick={() => {
                            // Try processing the image again with debug mode
                            setProcessing(true);
                            setProcessingMessage('Re-processing image in debug mode...');

                            fetch(capturedImage)
                              .then(r => r.blob())
                              .then(blob => {
                                return processScorecardImage(blob, { debug: true });
                              })
                              .then(extractedData => {
                                setExtractedData(extractedData);
                                setProcessingMessage('Debug processing complete!');
                                setTimeout(() => setProcessingMessage(''), 1500);
                                setError('');

                                // Show debug data
                                console.log('Debug data:', extractedData);

                                // Create a more readable debug message
                                const debugMessage = `
Extraction Method: ${extractedData.extractionMethod || 'Unknown'}

Home Team: ${extractedData.homeTeam?.name || 'Unknown'}
Score: ${extractedData.homeTeam?.score?.runs || 0}/${extractedData.homeTeam?.score?.wickets || 0}
Overs: ${extractedData.homeTeam?.score?.overs || '0.0'}

Away Team: ${extractedData.awayTeam?.name || 'Unknown'}
Score: ${extractedData.awayTeam?.score?.runs || 0}/${extractedData.awayTeam?.score?.wickets || 0}
Overs: ${extractedData.awayTeam?.score?.overs || '0.0'}

Result: ${extractedData.result || 'Unknown'}
Player of the Match: ${extractedData.playerOfMatch || 'Unknown'}

Data Source: ${extractedData.extractionMethod?.includes('fallback') || extractedData.extractionMethod?.includes('empty') ? 'EMPTY TEMPLATE (manual entry required)' : 'Extracted from image'}
                                `;

                                alert(debugMessage);
                              })
                              .catch(error => {
                                console.error('Error in debug processing:', error);
                                setError('Debug processing failed: ' + (error.message || 'Unknown error'));
                              })
                              .finally(() => {
                                setProcessing(false);
                              });
                          }}
                        >
                          Debug Process
                        </Button>
                      )}
                    </Box>
                    <Box sx={{ mt: 2 }}>
                      <img
                        src={capturedImage}
                        alt="Captured scorecard"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '60vh',
                          objectFit: 'contain'
                        }}
                      />
                    </Box>
                  </Box>
                ) : extractedData ? (
                  // Data view
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1">
                        {manualEntryMode ? "Enter Match Data" : "Extracted Match Data"}
                      </Typography>
                      <Box>
                        {debugMode && (
                          <Button
                            size="small"
                            variant="contained"
                            color="warning"
                            startIcon={<BugReportIcon />}
                            onClick={() => {
                              console.log('Debug data:', extractedData);

                              // Create a more readable debug message
                              const debugMessage = `
Extraction Method: ${extractedData.extractionMethod || 'Unknown'}

Home Team: ${extractedData.homeTeam?.name || 'Unknown'}
Score: ${extractedData.homeTeam?.score?.runs || 0}/${extractedData.homeTeam?.score?.wickets || 0}
Overs: ${extractedData.homeTeam?.score?.overs || '0.0'}

Away Team: ${extractedData.awayTeam?.name || 'Unknown'}
Score: ${extractedData.awayTeam?.score?.runs || 0}/${extractedData.awayTeam?.score?.wickets || 0}
Overs: ${extractedData.awayTeam?.score?.overs || '0.0'}

Result: ${extractedData.result || 'Unknown'}
Player of the Match: ${extractedData.playerOfMatch || 'Unknown'}

Data Source: ${extractedData.extractionMethod?.includes('fallback') || extractedData.extractionMethod?.includes('empty') ? 'EMPTY TEMPLATE (manual entry required)' : 'Extracted from image'}

Full Debug Data (also in console):
${JSON.stringify(extractedData, null, 2)}
                              `;

                              alert(debugMessage);
                            }}
                            sx={{ mr: 1 }}
                          >
                            View Debug Data
                          </Button>
                        )}
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => setManualEntryMode(!manualEntryMode)}
                        >
                          {manualEntryMode ? "View Data" : "Edit Data"}
                        </Button>
                      </Box>
                    </Box>

                    {manualEntryMode ? (
                      // Manual data entry form
                      <form>
                        <Grid container spacing={3} sx={{ mt: 1 }}>
                          {/* Home team */}
                          <Grid item xs={12} sm={6}>
                            <Paper variant="outlined" sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Home Team
                              </Typography>
                              <TextField
                                fullWidth
                                label="Team Name"
                                variant="outlined"
                                size="small"
                                value={extractedData.homeTeam.name}
                                onChange={(e) => {
                                  setExtractedData({
                                    ...extractedData,
                                    homeTeam: {
                                      ...extractedData.homeTeam,
                                      name: e.target.value
                                    }
                                  });
                                }}
                                sx={{ mb: 2 }}
                                helperText="Enter the exact team name as registered in the tournament"
                              />
                              <Grid container spacing={2}>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Runs"
                                    variant="outlined"
                                    size="small"
                                    type="number"
                                    value={extractedData.homeTeam.score.runs}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        homeTeam: {
                                          ...extractedData.homeTeam,
                                          score: {
                                            ...extractedData.homeTeam.score,
                                            runs: parseInt(e.target.value) || 0
                                          }
                                        }
                                      });
                                    }}
                                  />
                                </Grid>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Wickets"
                                    variant="outlined"
                                    size="small"
                                    type="number"
                                    value={extractedData.homeTeam.score.wickets}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        homeTeam: {
                                          ...extractedData.homeTeam,
                                          score: {
                                            ...extractedData.homeTeam.score,
                                            wickets: parseInt(e.target.value) || 0
                                          }
                                        }
                                      });
                                    }}
                                  />
                                </Grid>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Overs"
                                    variant="outlined"
                                    size="small"
                                    value={extractedData.homeTeam.score.overs}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        homeTeam: {
                                          ...extractedData.homeTeam,
                                          score: {
                                            ...extractedData.homeTeam.score,
                                            overs: e.target.value
                                          }
                                        }
                                      });
                                    }}
                                    helperText="Format: 12.4"
                                  />
                                </Grid>
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Away team */}
                          <Grid item xs={12} sm={6}>
                            <Paper variant="outlined" sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Away Team
                              </Typography>
                              <TextField
                                fullWidth
                                label="Team Name"
                                variant="outlined"
                                size="small"
                                value={extractedData.awayTeam.name}
                                onChange={(e) => {
                                  setExtractedData({
                                    ...extractedData,
                                    awayTeam: {
                                      ...extractedData.awayTeam,
                                      name: e.target.value
                                    }
                                  });
                                }}
                                sx={{ mb: 2 }}
                                helperText="Enter the exact team name as registered in the tournament"
                              />
                              <Grid container spacing={2}>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Runs"
                                    variant="outlined"
                                    size="small"
                                    type="number"
                                    value={extractedData.awayTeam.score.runs}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        awayTeam: {
                                          ...extractedData.awayTeam,
                                          score: {
                                            ...extractedData.awayTeam.score,
                                            runs: parseInt(e.target.value) || 0
                                          }
                                        }
                                      });
                                    }}
                                  />
                                </Grid>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Wickets"
                                    variant="outlined"
                                    size="small"
                                    type="number"
                                    value={extractedData.awayTeam.score.wickets}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        awayTeam: {
                                          ...extractedData.awayTeam,
                                          score: {
                                            ...extractedData.awayTeam.score,
                                            wickets: parseInt(e.target.value) || 0
                                          }
                                        }
                                      });
                                    }}
                                  />
                                </Grid>
                                <Grid item xs={4}>
                                  <TextField
                                    fullWidth
                                    label="Overs"
                                    variant="outlined"
                                    size="small"
                                    value={extractedData.awayTeam.score.overs}
                                    onChange={(e) => {
                                      setExtractedData({
                                        ...extractedData,
                                        awayTeam: {
                                          ...extractedData.awayTeam,
                                          score: {
                                            ...extractedData.awayTeam.score,
                                            overs: e.target.value
                                          }
                                        }
                                      });
                                    }}
                                    helperText="Format: 12.4"
                                  />
                                </Grid>
                              </Grid>
                            </Paper>
                          </Grid>

                          {/* Match result */}
                          <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Match Result
                              </Typography>
                              <TextField
                                fullWidth
                                label="Result"
                                variant="outlined"
                                size="small"
                                value={extractedData.result}
                                onChange={(e) => {
                                  setExtractedData({
                                    ...extractedData,
                                    result: e.target.value
                                  });
                                }}
                                sx={{ mb: 2 }}
                                placeholder="e.g. Team A won by 5 wickets"
                                helperText="Format: '[Team Name] won by [X runs/wickets]'"
                              />
                              <TextField
                                fullWidth
                                label="Player of the Match"
                                variant="outlined"
                                size="small"
                                value={extractedData.playerOfMatch}
                                onChange={(e) => {
                                  setExtractedData({
                                    ...extractedData,
                                    playerOfMatch: e.target.value
                                  });
                                }}
                                helperText="Enter the full name of the player"
                              />

                              <Typography variant="body2" color="text.secondary" sx={{ mt: 3, p: 1, bgcolor: 'background.paper', borderRadius: 1 }}>
                                <strong>Cricket Scorecard Tips:</strong>
                                <ul style={{ paddingLeft: '20px', marginTop: '8px', marginBottom: '8px' }}>
                                  <li>Enter team names exactly as they appear in the tournament</li>
                                  <li>For overs, use format "12.4" (12 complete overs and 4 balls)</li>
                                  <li>Maximum wickets is typically 10 in cricket</li>
                                  <li>Result should clearly indicate the winning team and margin</li>
                                </ul>
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>
                      </form>
                    ) : (
                      // Display extracted data
                      <Grid container spacing={3} sx={{ mt: 1 }}>
                        {/* Data source info */}
                        {extractedData.extractionMethod && (
                          <Grid item xs={12}>
                            <Paper
                              variant="outlined"
                              sx={{
                                p: 1.5,
                                bgcolor: extractedData.extractionMethod.includes('fallback') || extractedData.extractionMethod.includes('empty')
                                  ? 'warning.light'
                                  : 'success.light',
                                borderColor: extractedData.extractionMethod.includes('fallback') || extractedData.extractionMethod.includes('empty')
                                  ? 'warning.main'
                                  : 'success.main'
                              }}
                            >
                              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                {extractedData.extractionMethod.includes('fallback') || extractedData.extractionMethod.includes('empty')
                                  ? '⚠️ Using empty template (please enter match data manually)'
                                  : extractedData.extractionMethod.includes('matched')
                                    ? '✅ Successfully matched scorecard format'
                                    : extractedData.extractionMethod.includes('text-detected')
                                      ? '✓ Detected text in expected regions'
                                      : extractedData.extractionMethod.includes('with-players')
                                        ? '✅ Successfully extracted player statistics'
                                        : `Extraction method: ${extractedData.extractionMethod}`
                                }
                              </Typography>
                            </Paper>
                          </Grid>
                        )}

                        {/* Home team */}
                        <Grid item xs={12} sm={6}>
                          <Paper variant="outlined" sx={{ p: 2 }}>
                            <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }}>
                              Home Team
                            </Typography>
                            <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 1)' }}>
                              {extractedData.homeTeam.name}
                            </Typography>
                            <Typography variant="h4" sx={{ color: 'rgba(255, 255, 255, 1)' }}>
                              {extractedData.homeTeam.score.runs}/{extractedData.homeTeam.score.wickets}
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                              Overs: {extractedData.homeTeam.score.overs}
                            </Typography>
                          </Paper>
                        </Grid>

                        {/* Away team */}
                        <Grid item xs={12} sm={6}>
                          <Paper variant="outlined" sx={{ p: 2 }}>
                            <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }}>
                              Away Team
                            </Typography>
                            <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 1)' }}>
                              {extractedData.awayTeam.name}
                            </Typography>
                            <Typography variant="h4" sx={{ color: 'rgba(255, 255, 255, 1)' }}>
                              {extractedData.awayTeam.score.runs}/{extractedData.awayTeam.score.wickets}
                            </Typography>
                            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                              Overs: {extractedData.awayTeam.score.overs}
                            </Typography>
                          </Paper>
                        </Grid>

                        {/* Match result */}
                        <Grid item xs={12}>
                          <Paper variant="outlined" sx={{ p: 2 }}>
                            <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }} gutterBottom>
                              Match Result
                            </Typography>
                            <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 1)' }}>
                              {extractedData.result}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1, color: 'rgba(255, 255, 255, 0.9)' }}>
                              Player of the Match: {extractedData.playerOfMatch}
                            </Typography>
                            {extractedData.venue && (
                              <Typography variant="body2" sx={{ mt: 0.5, color: 'rgba(255, 255, 255, 0.7)' }}>
                                Venue: {extractedData.venue}
                              </Typography>
                            )}
                          </Paper>
                        </Grid>

                        {/* Player Statistics - only show if available */}
                        {extractedData.homeTeam?.batsmen && extractedData.homeTeam.batsmen.length > 0 && (
                          <>
                            {/* Home Team Batsmen */}
                            <Grid item xs={12} md={6}>
                              <Paper variant="outlined" sx={{ p: 2 }}>
                                <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }} gutterBottom>
                                  {extractedData.homeTeam.name} - Batting
                                </Typography>
                                <Box sx={{ overflowX: 'auto' }}>
                                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                      <tr style={{ borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                                        <th style={{ textAlign: 'left', padding: '8px 16px 8px 0', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Batsman</th>
                                        <th style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Runs</th>
                                        <th style={{ textAlign: 'right', padding: '8px 0 8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Balls</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {extractedData.homeTeam.batsmen.map((batsman, index) => (
                                        <tr key={index} style={{ borderBottom: index < extractedData.homeTeam.batsmen.length - 1 ? '1px solid rgba(224, 224, 224, 0.2)' : 'none' }}>
                                          <td style={{ padding: '8px 16px 8px 0', color: 'rgba(255, 255, 255, 0.9)' }}>{batsman.name}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'bold', color: 'rgba(255, 255, 255, 1)' }}>{batsman.runs}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 0 8px 16px', color: 'rgba(255, 255, 255, 0.7)' }}>({batsman.balls})</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </Box>
                              </Paper>
                            </Grid>

                            {/* Home Team Bowlers */}
                            <Grid item xs={12} md={6}>
                              <Paper variant="outlined" sx={{ p: 2 }}>
                                <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }} gutterBottom>
                                  {extractedData.homeTeam.name} - Bowling
                                </Typography>
                                <Box sx={{ overflowX: 'auto' }}>
                                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                      <tr style={{ borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                                        <th style={{ textAlign: 'left', padding: '8px 16px 8px 0', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Bowler</th>
                                        <th style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>W</th>
                                        <th style={{ textAlign: 'right', padding: '8px 0 8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>R</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {extractedData.homeTeam.bowlers.map((bowler, index) => (
                                        <tr key={index} style={{ borderBottom: index < extractedData.homeTeam.bowlers.length - 1 ? '1px solid rgba(224, 224, 224, 0.2)' : 'none' }}>
                                          <td style={{ padding: '8px 16px 8px 0', color: 'rgba(255, 255, 255, 0.9)' }}>{bowler.name}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'bold', color: 'rgba(255, 255, 255, 1)' }}>{bowler.wickets}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 0 8px 16px', color: 'rgba(255, 255, 255, 0.7)' }}>{bowler.runs}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </Box>
                              </Paper>
                            </Grid>

                            {/* Away Team Batsmen */}
                            <Grid item xs={12} md={6}>
                              <Paper variant="outlined" sx={{ p: 2 }}>
                                <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }} gutterBottom>
                                  {extractedData.awayTeam.name} - Batting
                                </Typography>
                                <Box sx={{ overflowX: 'auto' }}>
                                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                      <tr style={{ borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                                        <th style={{ textAlign: 'left', padding: '8px 16px 8px 0', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Batsman</th>
                                        <th style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Runs</th>
                                        <th style={{ textAlign: 'right', padding: '8px 0 8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Balls</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {extractedData.awayTeam.batsmen.map((batsman, index) => (
                                        <tr key={index} style={{ borderBottom: index < extractedData.awayTeam.batsmen.length - 1 ? '1px solid rgba(224, 224, 224, 0.2)' : 'none' }}>
                                          <td style={{ padding: '8px 16px 8px 0', color: 'rgba(255, 255, 255, 0.9)' }}>{batsman.name}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'bold', color: 'rgba(255, 255, 255, 1)' }}>{batsman.runs}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 0 8px 16px', color: 'rgba(255, 255, 255, 0.7)' }}>({batsman.balls})</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </Box>
                              </Paper>
                            </Grid>

                            {/* Away Team Bowlers */}
                            <Grid item xs={12} md={6}>
                              <Paper variant="outlined" sx={{ p: 2 }}>
                                <Typography variant="subtitle2" sx={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: 'bold' }} gutterBottom>
                                  {extractedData.awayTeam.name} - Bowling
                                </Typography>
                                <Box sx={{ overflowX: 'auto' }}>
                                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                      <tr style={{ borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                                        <th style={{ textAlign: 'left', padding: '8px 16px 8px 0', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>Bowler</th>
                                        <th style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>W</th>
                                        <th style={{ textAlign: 'right', padding: '8px 0 8px 16px', fontWeight: 'normal', color: 'rgba(255, 255, 255, 0.7)' }}>R</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {extractedData.awayTeam.bowlers.map((bowler, index) => (
                                        <tr key={index} style={{ borderBottom: index < extractedData.awayTeam.bowlers.length - 1 ? '1px solid rgba(224, 224, 224, 0.2)' : 'none' }}>
                                          <td style={{ padding: '8px 16px 8px 0', color: 'rgba(255, 255, 255, 0.9)' }}>{bowler.name}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 16px', fontWeight: 'bold', color: 'rgba(255, 255, 255, 1)' }}>{bowler.wickets}</td>
                                          <td style={{ textAlign: 'right', padding: '8px 0 8px 16px', color: 'rgba(255, 255, 255, 0.7)' }}>{bowler.runs}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </Box>
                              </Paper>
                            </Grid>
                          </>
                        )}

                        {/* Debug info */}
                        {debugMode && extractedData.extractionMethod && (
                          <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 2, bgcolor: 'background.paper' }}>
                              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                Debug Information
                              </Typography>
                              <Typography variant="body2" component="div">
                                <strong>Extraction Method:</strong> {extractedData.extractionMethod}
                              </Typography>
                              {extractedData.debug && (
                                <Box sx={{ mt: 1 }}>
                                  <details>
                                    <summary>
                                      <Typography variant="body2" component="span" sx={{ cursor: 'pointer', textDecoration: 'underline' }}>
                                        View Debug Details
                                      </Typography>
                                    </summary>
                                    <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1, overflow: 'auto', maxHeight: '200px' }}>
                                      <pre style={{ margin: 0, fontSize: '12px' }}>
                                        {JSON.stringify(extractedData.debug, null, 2)}
                                      </pre>
                                    </Box>
                                  </details>
                                </Box>
                              )}
                            </Paper>
                          </Grid>
                        )}
                      </Grid>
                    )}
                  </Box>
                ) : null}
              </Paper>

              {/* Action buttons */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Button
                  variant="outlined"
                  onClick={resetCapture}
                  disabled={uploading}
                  startIcon={<ArrowBackIcon />}
                >
                  Retake
                </Button>

                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleUpload}
                  disabled={uploading || !extractedData}
                  startIcon={uploading ? <CircularProgress size={20} /> : <CheckIcon />}
                >
                  {uploading ? 'Uploading...' : 'Confirm & Upload'}
                </Button>
              </Box>
            </>
          )}
        </>
      )}

      {/* Hidden canvas for capturing */}
      <canvas
        ref={captureCanvasRef}
        style={{ display: 'none' }}
      />
    </Box>
  );
};

export default SimplifiedGuidedCapture;
