# Cricket Scorecard ML Training System

This directory contains the machine learning training system for cricket scorecard data extraction. The system allows you to:

1. Upload cricket scorecard images
2. Label text elements with their categories (team names, scores, player names, etc.)
3. Train a machine learning model to automatically classify text elements
4. Use the trained model to extract structured data from new scorecards

## Directory Structure

- `scorecard_trainer.py`: Main script for training the ML model
- `labeling_server.py`: Flask server for the labeling interface
- `data/`: Directory for training data
  - `images/`: Directory for scorecard images
  - `annotations/`: Directory for annotation JSON files
- `models/`: Directory for trained models

## Requirements

- Python 3.7 or higher
- PaddleOCR
- scikit-learn
- Flask
- Node.js (for the web interface)

## Installation

1. Install Python dependencies:

```bash
pip install paddleocr scikit-learn flask flask-cors
```

2. Make sure PaddleOCR is properly installed:

```bash
python -c "from paddleocr import PaddleOCR; print('PaddleOCR installed successfully')"
```

## Usage

### Starting the Labeling Server

The labeling server can be started from the web interface or manually:

```bash
python labeling_server.py --host 0.0.0.0 --port 5000
```

### Training the Model

The model can be trained from the web interface or manually:

```bash
python scorecard_trainer.py --train
```

## API Endpoints

The labeling server provides the following API endpoints:

- `POST /api/upload`: Upload a scorecard image
- `GET /api/images`: Get all uploaded images
- `GET /api/image/<filename>`: Get a specific image
- `GET /api/annotations/<filename>`: Get annotation for a specific image
- `POST /api/save-annotation`: Save annotation for an image
- `GET /api/categories`: Get all available categories
- `POST /api/train`: Train the ML model
- `GET /api/model-status`: Get the status of the trained model

## Integration with the Main Application

The training system is integrated with the main application through the `/api/training` routes in the server. These routes allow you to:

1. Start and stop the training server
2. Upload scorecard images for training
3. Train the model
4. Check the model status

## Workflow

1. Start the training server
2. Upload scorecard images
3. Label the text elements in each image
4. Train the model
5. Use the trained model for automatic data extraction

## Continuous Improvement

The system is designed for continuous improvement:

1. When the model makes mistakes on new scorecards, add them to the training data
2. Label the new scorecards
3. Retrain the model
4. Deploy the improved model

This cycle ensures that the model gets better over time as it sees more diverse scorecards.
