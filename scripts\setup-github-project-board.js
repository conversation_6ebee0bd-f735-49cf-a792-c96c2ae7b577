#!/usr/bin/env node

/**
 * GitHub Project Board Setup Script
 * 
 * This script automatically creates a GitHub Project Board with all the tasks,
 * labels, milestones, and automation rules defined in our project structure.
 * 
 * Prerequisites:
 * 1. Install GitHub CLI: https://cli.github.com/
 * 2. Authenticate: gh auth login
 * 3. Install dependencies: npm install @octokit/rest dotenv
 * 
 * Usage:
 * node scripts/setup-github-project-board.js
 * 
 * Environment Variables:
 * GITHUB_TOKEN - Your GitHub personal access token (optional if using gh CLI)
 * GITHUB_OWNER - Repository owner (e.g., "yourusername")
 * GITHUB_REPO - Repository name (e.g., "rpl")
 */

const { Octokit } = require('@octokit/rest');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

class GitHubProjectBoardSetup {
  constructor() {
    this.owner = process.env.GITHUB_OWNER || this.getGitHubOwner();
    this.repo = process.env.GITHUB_REPO || this.getGitHubRepo();
    this.token = process.env.GITHUB_TOKEN || this.getGitHubToken();
    
    this.octokit = new Octokit({
      auth: this.token,
    });

    console.log(`🚀 Setting up GitHub Project Board for ${this.owner}/${this.repo}`);
  }

  /**
   * Get GitHub owner from git remote
   */
  getGitHubOwner() {
    try {
      const remote = execSync('git remote get-url origin', { encoding: 'utf8' }).trim();
      const match = remote.match(/github\.com[:/]([^/]+)/);
      return match ? match[1] : null;
    } catch (error) {
      console.error('❌ Could not determine GitHub owner from git remote');
      return null;
    }
  }

  /**
   * Get GitHub repo name from git remote
   */
  getGitHubRepo() {
    try {
      const remote = execSync('git remote get-url origin', { encoding: 'utf8' }).trim();
      const match = remote.match(/github\.com[:/][^/]+\/([^/]+?)(?:\.git)?$/);
      return match ? match[1] : null;
    } catch (error) {
      console.error('❌ Could not determine GitHub repo from git remote');
      return null;
    }
  }

  /**
   * Get GitHub token from gh CLI
   */
  getGitHubToken() {
    try {
      return execSync('gh auth token', { encoding: 'utf8' }).trim();
    } catch (error) {
      console.error('❌ Could not get GitHub token. Please run: gh auth login');
      return null;
    }
  }

  /**
   * Create repository labels
   */
  async createLabels() {
    console.log('\n📋 Creating repository labels...');

    const labels = [
      // Priority Labels
      { name: '🔴 Critical', color: 'B60205', description: 'Must be completed immediately' },
      { name: '🟠 High', color: 'D93F0B', description: 'Important for current milestone' },
      { name: '🟡 Medium', color: 'FBCA04', description: 'Should be completed soon' },
      { name: '🟢 Low', color: '0E8A16', description: 'Nice to have, can be deferred' },

      // Type Labels
      { name: '🐛 Bug', color: 'D73A4A', description: 'Issues and fixes' },
      { name: '✨ Feature', color: 'A2EEEF', description: 'New functionality' },
      { name: '🔧 Enhancement', color: '7057FF', description: 'Improvements to existing features' },
      { name: '📚 Documentation', color: '0075CA', description: 'Documentation updates' },
      { name: '🧪 Testing', color: 'F9D0C4', description: 'Testing-related tasks' },
      { name: '🚀 Deployment', color: 'C5DEF5', description: 'Deployment and infrastructure' },

      // Component Labels
      { name: '🎨 Frontend', color: 'E99695', description: 'React/UI related' },
      { name: '⚙️ Backend', color: 'F7E7CE', description: 'Node.js/Express/API related' },
      { name: '🗄️ Database', color: 'FEF2C0', description: 'MongoDB/Data related' },
      { name: '🔍 OCR', color: 'C2E0C6', description: 'OCR processing related' },
      { name: '🏆 Auction', color: 'BFDADC', description: 'Auction system related' },
      { name: '👥 Team', color: 'C5DEF5', description: 'Team management related' },
      { name: '🏟️ Tournament', color: 'BFD4F2', description: 'Tournament related' },
      { name: '📊 Analytics', color: 'D4C5F9', description: 'Analytics and reporting' },
      { name: '🎮 Big Ant Cricket 24', color: 'FFD700', description: 'Big Ant Cricket 24 specific features' },

      // Status Labels
      { name: '🆕 New', color: 'EDEDED', description: 'Newly created task' },
      { name: '🔄 In Development', color: 'FBCA04', description: 'Currently being developed' },
      { name: '⏳ Waiting', color: 'D4C5F9', description: 'Waiting for external input' },
      { name: '🧪 Testing', color: 'F9D0C4', description: 'In testing phase' },
      { name: '📝 Needs Review', color: 'C2E0C6', description: 'Awaiting code review' },
      { name: '✅ Done', color: '0E8A16', description: 'Completed and verified' }
    ];

    for (const label of labels) {
      try {
        await this.octokit.issues.createLabel({
          owner: this.owner,
          repo: this.repo,
          name: label.name,
          color: label.color,
          description: label.description
        });
        console.log(`✅ Created label: ${label.name}`);
      } catch (error) {
        if (error.status === 422) {
          console.log(`⚠️  Label already exists: ${label.name}`);
        } else {
          console.error(`❌ Error creating label ${label.name}:`, error.message);
        }
      }
    }
  }

  /**
   * Create project milestones
   */
  async createMilestones() {
    console.log('\n🎯 Creating project milestones...');

    const milestones = [
      {
        title: 'Core System Completion',
        description: 'Complete Transfer Market System, Match Result Processing, and Post-Auction Processing',
        due_on: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString() // 2 weeks from now
      },
      {
        title: 'Big Ant Cricket 24 Integration',
        description: 'Implement Skill Points & Rating System, Performance Milestone Bonuses, and Comprehensive Leaderboards',
        due_on: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000).toISOString() // 5 weeks from now
      },
      {
        title: 'Advanced Analytics',
        description: 'Strike Rate & Economy Calculations, Fastest Milestones Tracking, and Venue-based Performance Analytics',
        due_on: new Date(Date.now() + 49 * 24 * 60 * 60 * 1000).toISOString() // 7 weeks from now
      },
      {
        title: 'Production Optimization',
        description: 'Complete Testing & Quality Assurance, Database Optimization, and Performance Improvements',
        due_on: new Date(Date.now() + 56 * 24 * 60 * 60 * 1000).toISOString() // 8 weeks from now
      },
      {
        title: 'Enhanced Features',
        description: 'Test Match Support, Enhanced Match Validation, and Advanced Search & Filtering',
        due_on: new Date(Date.now() + 70 * 24 * 60 * 60 * 1000).toISOString() // 10 weeks from now
      }
    ];

    for (const milestone of milestones) {
      try {
        const response = await this.octokit.issues.createMilestone({
          owner: this.owner,
          repo: this.repo,
          title: milestone.title,
          description: milestone.description,
          due_on: milestone.due_on
        });
        console.log(`✅ Created milestone: ${milestone.title}`);
      } catch (error) {
        if (error.status === 422) {
          console.log(`⚠️  Milestone already exists: ${milestone.title}`);
        } else {
          console.error(`❌ Error creating milestone ${milestone.title}:`, error.message);
        }
      }
    }
  }

  /**
   * Create GitHub issues for all tasks
   */
  async createIssues() {
    console.log('\n📝 Creating GitHub issues for all tasks...');

    // Load task data from our detailed breakdown
    const tasks = this.loadTaskData();

    for (const task of tasks) {
      try {
        const issue = await this.octokit.issues.create({
          owner: this.owner,
          repo: this.repo,
          title: task.title,
          body: task.body,
          labels: task.labels,
          milestone: task.milestone
        });
        console.log(`✅ Created issue: ${task.title}`);
      } catch (error) {
        console.error(`❌ Error creating issue ${task.title}:`, error.message);
      }
    }
  }

  /**
   * Load task data from our detailed breakdown files
   */
  loadTaskData() {
    return [
      // CRITICAL PRIORITY TASKS (Complete First)
      {
        title: '🔄 Complete Transfer Market System',
        body: `## Description
Complete the player trading system between teams with market value calculations and transfer history.

## Acceptance Criteria
- [ ] Player trading between teams
- [ ] Market value calculations
- [ ] Transfer history tracking
- [ ] Transaction validation

## Files
- \`client/src/pages/TransferMarket/\`
- \`server/controllers/\` (needs completion)

## Remaining Work
Complete trading logic, market value algorithms, transaction history

## Phase
2.6: Player & Team Management

## Priority
� Critical

## Estimated Effort
1 week`,
        labels: ['🔴 Critical', '✨ Feature', '🏆 Auction', '🎨 Frontend'],
        milestone: 'Core System Completion'
      },
      {
        title: '🎮 Implement Advanced Skill Points & Rating System',
        body: `## Description
Implement automatic rating increases based on skill points (5000 points = +1 rating), configurable thresholds.

## Acceptance Criteria
- [ ] 1 run = 1 skill point
- [ ] 1 wicket = 10 skill points
- [ ] 5000 skill points = +1 rating increase
- [ ] Admin configurable thresholds
- [ ] Automatic rating updates after each match

## Big Ant Cricket 24 Alignment
This is a core feature from the original vision where player ratings increase based on performance.

## Phase
7.1: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical

## Estimated Effort
1 week`,
        labels: ['🔴 Critical', '✨ Feature', '🎮 Big Ant Cricket 24', '⚙️ Backend'],
        milestone: 'Big Ant Cricket 24 Integration'
      },
      {
        title: '🎮 Add Performance Milestone Bonuses',
        body: `## Description
Implement milestone bonus system: 30's (+60 points), 50's (+90 points), 100's (+150 points), 3W hauls (+60 points), 5W hauls (+90 points).

## Acceptance Criteria
- [ ] Batting milestones: 30 (+60), 50 (+90), 100 (+150) bonus points
- [ ] Bowling milestones: 3W (+60), 5W (+90) bonus points
- [ ] Automatic detection from scorecard OCR
- [ ] Historical milestone tracking

## Big Ant Cricket 24 Alignment
Essential for the original vision where milestones provide bonus skill points.

## Phase
7.2: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical

## Estimated Effort
1 week`,
        labels: ['🔴 Critical', '✨ Feature', '🎮 Big Ant Cricket 24', '🔍 OCR'],
        milestone: 'Big Ant Cricket 24 Integration'
      },

      // HIGH PRIORITY TASKS (Complete Next)
      {
        title: '🔄 Refine Match Result Processing',
        body: `## Description
Complete score validation, winner determination, player statistics updates, and match verification.

## Acceptance Criteria
- [ ] Refine score validation logic
- [ ] Complete player statistics updates
- [ ] Improve match verification
- [ ] Winner determination accuracy

## Files
- \`server/controllers/matchOutcomeController.js\`
- \`server/controllers/scorecardController.js\`

## Phase
3.5: Tournament & Match Management

## Priority
🟠 High

## Estimated Effort
1 week`,
        labels: ['🟠 High', '🔧 Enhancement', '🏟️ Tournament', '⚙️ Backend'],
        milestone: 'Core System Completion'
      },
      {
        title: '🔄 Complete Post-Auction Processing',
        body: `## Description
Finalize player assignment to teams, payment processing, and auction result finalization.

## Acceptance Criteria
- [ ] Complete player assignment logic
- [ ] Finalize payment processing
- [ ] Auction result notifications
- [ ] Post-auction cleanup

## Files
- Auction controller (needs completion)

## Phase
4.6: Auction System

## Priority
🟠 High

## Estimated Effort
1 week`,
        labels: ['🟠 High', '🔧 Enhancement', '🏆 Auction', '⚙️ Backend'],
        milestone: 'Core System Completion'
      },
      {
        title: '🎮 Build Comprehensive Leaderboards',
        body: `## Description
Create comprehensive leaderboards for Most Runs, 30's, 50's, 100's, Wickets, 3W/5W Hauls, MOM - format and tournament wise.

## Acceptance Criteria
- [ ] Multiple leaderboard categories
- [ ] Format-wise filtering (T10, T20, ODI, Test)
- [ ] Tournament-wise and overall statistics
- [ ] Real-time updates after each match

## Big Ant Cricket 24 Alignment
Leaderboards are essential for competitive gaming experience.

## Phase
7.3: Big Ant Cricket 24 Integration Features

## Priority
🟠 High

## Estimated Effort
2 weeks`,
        labels: ['🟠 High', '✨ Feature', '🎮 Big Ant Cricket 24', '📊 Analytics'],
        milestone: 'Big Ant Cricket 24 Integration'
      },
      {
        title: '🧪 Complete Testing & Quality Assurance',
        body: `## Description
Add comprehensive test coverage, set up automated testing pipeline.

## Acceptance Criteria
- [ ] Unit tests for all major components
- [ ] Integration tests for API endpoints
- [ ] End-to-end testing for user workflows
- [ ] Automated testing pipeline

## Phase
6.4: Production & Deployment

## Priority
🟠 High

## Estimated Effort
2 weeks`,
        labels: ['� High', '🧪 Testing', '🔧 Enhancement', '🚀 Deployment'],
        milestone: 'Production Optimization'
      },

      // MEDIUM PRIORITY TASKS
      {
        title: '� Add Strike Rate & Economy Calculations',
        body: `## Description
Auto-calculate strike rates (runs/balls) and economy rates (runs/overs) from scorecard OCR.

## Acceptance Criteria
- [ ] Strike rate calculation: (runs/balls) * 100
- [ ] Economy rate calculation: runs conceded/overs bowled
- [ ] Automatic calculation from OCR data
- [ ] Historical tracking and trends

## Phase
7.4: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week`,
        labels: ['🟡 Medium', '✨ Feature', '🎮 Big Ant Cricket 24', '📊 Analytics'],
        milestone: 'Advanced Analytics'
      },
      {
        title: '🎮 Enhanced Match Validation',
        body: `## Description
Auto-detect chase/defend from scorecard, improved team name validation.

## Acceptance Criteria
- [ ] Automatic chase/defend detection
- [ ] Enhanced team name validation
- [ ] Match format auto-detection
- [ ] Improved error handling and user feedback

## Phase
7.8: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week`,
        labels: ['🟡 Medium', '🔧 Enhancement', '🎮 Big Ant Cricket 24', '🔍 OCR'],
        milestone: 'Enhanced Features'
      },
      {
        title: '🗄️ Database Optimization',
        body: `## Description
MongoDB indexing, query optimization, connection pooling, performance tuning.

## Acceptance Criteria
- [ ] Add database indexes for frequently queried fields
- [ ] Optimize slow queries
- [ ] Implement connection pooling
- [ ] Performance monitoring and tuning

## Phase
6.2: Production & Deployment

## Priority
🟡 Medium

## Estimated Effort
1 week`,
        labels: ['🟡 Medium', '🔧 Enhancement', '🗄️ Database', '⚙️ Backend'],
        milestone: 'Production Optimization'
      },

      // ADDITIONAL BIG ANT CRICKET 24 FEATURES
      {
        title: '🎮 Fastest Milestones Tracking',
        body: `## Description
Track fastest 50's, 100's with Top 5 rankings, dynamic updates when records are broken.

## Acceptance Criteria
- [ ] Track fastest 50's and 100's (balls faced)
- [ ] Top 5 rankings for each milestone
- [ ] Dynamic updates when records are broken
- [ ] Personal best tracking

## Phase
7.5: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1.5 weeks`,
        labels: ['🟡 Medium', '✨ Feature', '🎮 Big Ant Cricket 24', '📊 Analytics'],
        milestone: 'Advanced Analytics'
      },
      {
        title: '🎮 Venue-based Performance Analytics',
        body: `## Description
Track performance by venue with tournament filtering capabilities.

## Acceptance Criteria
- [ ] Performance statistics by venue
- [ ] Tournament filtering
- [ ] Venue-specific leaderboards
- [ ] Historical venue performance

## Phase
7.6: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
1 week`,
        labels: ['🟡 Medium', '✨ Feature', '🎮 Big Ant Cricket 24', '📊 Analytics'],
        milestone: 'Advanced Analytics'
      },
      {
        title: '🎮 Test Match Support',
        body: `## Description
4-innings match processing, extended scorecard handling for Test format.

## Acceptance Criteria
- [ ] Support for 4-innings matches
- [ ] Extended scorecard processing
- [ ] Test-specific statistics
- [ ] Multi-day match handling

## Phase
7.7: Big Ant Cricket 24 Integration Features

## Priority
🟡 Medium

## Estimated Effort
2 weeks`,
        labels: ['🟡 Medium', '✨ Feature', '🎮 Big Ant Cricket 24', '🔍 OCR'],
        milestone: 'Enhanced Features'
      }
    ];
  }

  /**
   * Set up GitHub Actions workflow for project board automation
   */
  async setupAutomation() {
    console.log('\n⚙️ Setting up GitHub Actions automation...');

    const workflowContent = `name: Project Board Automation

on:
  issues:
    types: [opened, closed, reopened]
  pull_request:
    types: [opened, closed, merged]
  push:
    branches: [main, master]

jobs:
  update-project-board:
    runs-on: ubuntu-latest
    steps:
      - name: Update Project Board
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: RPL Cricket - Big Ant Cricket 24 Tournament System
          column: In Progress
          repo-token: \${{ secrets.GITHUB_TOKEN }}
`;

    const workflowDir = '.github/workflows';
    const workflowPath = path.join(workflowDir, 'project-board-automation.yml');

    // Create .github/workflows directory if it doesn't exist
    if (!fs.existsSync(workflowDir)) {
      fs.mkdirSync(workflowDir, { recursive: true });
    }

    // Write workflow file
    fs.writeFileSync(workflowPath, workflowContent);
    console.log('✅ Created GitHub Actions workflow for project board automation');
  }

  /**
   * Main setup function
   */
  async setup() {
    try {
      console.log('🚀 Starting GitHub Project Board setup...\n');

      if (!this.owner || !this.repo || !this.token) {
        console.error('❌ Missing required information:');
        console.error(`   Owner: ${this.owner || 'MISSING'}`);
        console.error(`   Repo: ${this.repo || 'MISSING'}`);
        console.error(`   Token: ${this.token ? 'PRESENT' : 'MISSING'}`);
        console.error('\nPlease ensure you have:');
        console.error('1. Authenticated with GitHub CLI: gh auth login');
        console.error('2. Set environment variables or run from git repository');
        return;
      }

      await this.createLabels();
      await this.createMilestones();
      await this.createIssues();
      await this.setupAutomation();

      console.log('\n🎉 GitHub Project Board setup completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('1. Go to your GitHub repository');
      console.log('2. Click on "Projects" tab');
      console.log('3. Create a new project board');
      console.log('4. Add the created issues to appropriate columns');
      console.log('5. Configure automation rules as needed');

    } catch (error) {
      console.error('❌ Setup failed:', error.message);
    }
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  const setup = new GitHubProjectBoardSetup();
  setup.setup();
}

module.exports = GitHubProjectBoardSetup;
