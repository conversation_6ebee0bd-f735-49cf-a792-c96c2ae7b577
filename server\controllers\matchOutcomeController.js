const MatchOutcomeService = require('../services/matchOutcomeService');
const Tournament = require('../models/Tournament');
const Team = require('../models/Team');

/**
 * Match Outcome Controller
 * 
 * Handles HTTP requests for match outcome calculation and tournament standings updates
 */
class MatchOutcomeController {
  constructor() {
    this.matchOutcomeService = new MatchOutcomeService();
    this.initializeService();
  }

  async initializeService() {
    try {
      await this.matchOutcomeService.initialize();
    } catch (error) {
      console.error('Failed to initialize MatchOutcomeService:', error);
    }
  }

  /**
   * Calculate match outcome from OCR data
   * POST /api/match-outcome/calculate
   */
  calculateOutcome = async (req, res) => {
    try {
      const { ocrData, tournamentId, matchId, team1Id, team2Id } = req.body;

      if (!ocrData) {
        return res.status(400).json({
          success: false,
          error: 'OCR data is required'
        });
      }

      console.log('Calculating match outcome for tournament:', tournamentId);

      // Calculate match outcome
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(
        ocrData, 
        tournamentId, 
        matchId,
        team1Id,
        team2Id
      );

      res.json({
        success: true,
        data: {
          outcome,
          message: 'Match outcome calculated successfully'
        }
      });

    } catch (error) {
      console.error('Error calculating match outcome:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to calculate match outcome',
        details: error.message
      });
    }
  };

  /**
   * Update tournament standings with match outcome
   * POST /api/match-outcome/update-standings
   */
  updateStandings = async (req, res) => {
    try {
      const { tournamentId, matchId, outcome } = req.body;

      if (!tournamentId || !matchId || !outcome) {
        return res.status(400).json({
          success: false,
          error: 'Tournament ID, match ID, and outcome are required'
        });
      }

      console.log('Updating tournament standings for match:', matchId);

      // Update tournament standings
      const updatedStandings = await this.matchOutcomeService.updateTournamentStandings(
        tournamentId,
        matchId,
        outcome
      );

      res.json({
        success: true,
        data: {
          standings: updatedStandings,
          message: 'Tournament standings updated successfully'
        }
      });

    } catch (error) {
      console.error('Error updating tournament standings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update tournament standings',
        details: error.message
      });
    }
  };

  /**
   * Process complete match from OCR data (calculate outcome + update standings)
   * POST /api/match-outcome/process-match
   */
  processMatch = async (req, res) => {
    console.log('DEBUG: processMatch endpoint called', req.body);
    console.log('DEBUG: req.body', req.body);
    try {
      const { ocrData, tournamentId, matchId, team1Id, team2Id } = req.body;
      console.log('DEBUG: processMatch called with team1Id:', team1Id, 'team2Id:', team2Id);

      if (!ocrData || !tournamentId || !matchId) {
        return res.status(400).json({
          success: false,
          error: 'OCR data, tournament ID, and match ID are required'
        });
      }

      console.log('Processing complete match outcome for tournament:', tournamentId);

      // Step 1: Calculate match outcome
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(
        ocrData,
        tournamentId,
        matchId,
        team1Id ? team1Id.toString() : null,
        team2Id ? team2Id.toString() : null,
        req.body.homeTeamBattedFirst,
        req.body.team1IsHomeTeam
      );

      // Step 2: Update tournament standings
      const updatedStandings = await this.matchOutcomeService.updateTournamentStandings(
        tournamentId,
        matchId,
        outcome
      );

      res.json({
        success: true,
        data: {
          outcome,
          standings: updatedStandings,
          message: 'Match processed and standings updated successfully'
        }
      });

    } catch (error) {
      console.error('Error processing match:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process match',
        details: error.message
      });
    }
  };

  /**
   * Get tournament standings
   * GET /api/match-outcome/standings/:tournamentId
   */
  getStandings = async (req, res) => {
    try {
      const { tournamentId } = req.params;
      const { phaseIndex } = req.query;

      const tournament = await Tournament.findById(tournamentId)
        .populate('phases.standings.team', 'teamName logo')
        .populate('registeredTeams', 'teamName logo');

      if (!tournament) {
        return res.status(404).json({
          success: false,
          error: 'Tournament not found'
        });
      }

      let standings = [];
      if (phaseIndex !== undefined && tournament.phases[phaseIndex]) {
        standings = tournament.phases[phaseIndex].standings;
      } else if (tournament.phases.length > 0) {
        // Get standings from the latest phase
        standings = tournament.phases[tournament.phases.length - 1].standings;
      }

      // Sort standings by points (descending) and net run rate (descending)
      standings.sort((a, b) => {
        if (b.points !== a.points) {
          return b.points - a.points;
        }
        return b.netRunRate - a.netRunRate;
      });

      res.json({
        success: true,
        data: {
          tournament: {
            id: tournament._id,
            name: tournament.name,
            status: tournament.status
          },
          standings,
          pointsSystem: {
            win: tournament.pointsForWin,
            tie: tournament.pointsForTie,
            noResult: tournament.pointsForNoResult
          }
        }
      });

    } catch (error) {
      console.error('Error getting tournament standings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tournament standings',
        details: error.message
      });
    }
  };

  /**
   * Get match outcome service statistics
   * GET /api/match-outcome/stats
   */
  getStats = async (req, res) => {
    try {
      const stats = this.matchOutcomeService.getStats();
      
      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      console.error('Error getting match outcome stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get service statistics',
        details: error.message
      });
    }
  };

  /**
   * Validate match outcome data
   * POST /api/match-outcome/validate
   */
  validateOutcome = async (req, res) => {
    try {
      const { outcome } = req.body;

      if (!outcome) {
        return res.status(400).json({
          success: false,
          error: 'Outcome data is required'
        });
      }

      const validation = this.validateOutcomeData(outcome);

      res.json({
        success: true,
        data: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings
        }
      });

    } catch (error) {
      console.error('Error validating match outcome:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate match outcome',
        details: error.message
      });
    }
  };

  /**
   * Get match details for admin
   * GET /api/match-outcome/admin/matches/:id
   */
  getAdminMatch = async (req, res) => {
    try {
      const { id } = req.params;

      // Find the match in tournaments
      const tournament = await Tournament.findOne({
        $or: [
          { 'phases.matches._id': id },
          { 'matches._id': id }
        ]
      })
      .populate('phases.matches.homeTeam', 'teamName logo')
      .populate('phases.matches.awayTeam', 'teamName logo')
      .populate('phases.matches.result.winner', 'teamName logo')
      .populate('phases.matches.result.manOfTheMatch', 'name')
      .populate('matches.homeTeam', 'teamName logo')
      .populate('matches.awayTeam', 'teamName logo')
      .populate('matches.result.winner', 'teamName logo')
      .populate('matches.result.manOfTheMatch', 'name');

      if (!tournament) {
        return res.status(404).json({
          success: false,
          error: 'Match not found'
        });
      }

      let match = null;
      let phaseIndex = -1;

      // Search in phases
      for (let i = 0; i < tournament.phases.length; i++) {
        const phase = tournament.phases[i];
        if (phase.matches) {
          const foundMatch = phase.matches.find(m => m._id.toString() === id);
          if (foundMatch) {
            match = foundMatch;
            phaseIndex = i;
            break;
          }
        }
      }

      // Search in main matches array if not found in phases
      if (!match && tournament.matches) {
        match = tournament.matches.find(m => m._id.toString() === id);
      }

      if (!match) {
        return res.status(404).json({
          success: false,
          error: 'Match not found'
        });
      }

      res.json({
        success: true,
        data: {
          match,
          tournament: {
            id: tournament._id,
            name: tournament.name,
            status: tournament.status
          },
          phaseIndex: phaseIndex >= 0 ? phaseIndex : null
        }
      });

    } catch (error) {
      console.error('Error getting admin match details:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get match details',
        details: error.message
      });
    }
  };

  /**
   * Get all matches for a tournament (admin)
   * GET /api/match-outcome/admin/tournament-matches/:tournamentId
   */
  getTournamentMatches = async (req, res) => {
    try {
      const { tournamentId } = req.params;

      // Find the tournament
      const tournament = await Tournament.findById(tournamentId)
        .populate('phases.matches.homeTeam', 'teamName logo')
        .populate('phases.matches.awayTeam', 'teamName logo')
        .populate('phases.matches.result.winner', 'teamName logo')
        .populate('phases.matches.result.manOfTheMatch', 'name');

      if (!tournament) {
        return res.status(404).json({
          success: false,
          error: 'Tournament not found'
        });
      }

      // Collect all matches from all phases
      const matches = [];
      
      // Add phase information to each match
      if (tournament.phases && tournament.phases.length > 0) {
        tournament.phases.forEach((phase, phaseIndex) => {
          if (phase.matches && phase.matches.length > 0) {
            phase.matches.forEach(match => {
              matches.push({
                ...match.toObject(),
                phaseIndex,
                phaseName: phase.name || `Phase ${phaseIndex + 1}`
              });
            });
          }
        });
      }

      res.json({
        success: true,
        data: {
          matches,
          tournament: {
            id: tournament._id,
            name: tournament.name,
            status: tournament.status
          }
        }
      });

    } catch (error) {
      console.error('Error getting tournament matches:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tournament matches',
        details: error.message
      });
    }
  };

  /**
   * Validate outcome data structure and values
   * @param {Object} outcome - Match outcome data
   * @returns {Object} - Validation result
   */
  validateOutcomeData(outcome) {
    const errors = [];
    const warnings = [];

    // Check required fields
    if (!outcome.team1Score || !outcome.team2Score) {
      errors.push('Team scores are required');
    }

    // Validate score values
    if (outcome.team1Score) {
      if (outcome.team1Score.runs < 0) {
        errors.push('Team 1 runs cannot be negative');
      }
      if (outcome.team1Score.wickets < 0 || outcome.team1Score.wickets > 10) {
        errors.push('Team 1 wickets must be between 0 and 10');
      }
      if (outcome.team1Score.overs < 0) {
        errors.push('Team 1 overs cannot be negative');
      }
    }

    if (outcome.team2Score) {
      if (outcome.team2Score.runs < 0) {
        errors.push('Team 2 runs cannot be negative');
      }
      if (outcome.team2Score.wickets < 0 || outcome.team2Score.wickets > 10) {
        errors.push('Team 2 wickets must be between 0 and 10');
      }
      if (outcome.team2Score.overs < 0) {
        errors.push('Team 2 overs cannot be negative');
      }
    }

    // Check winner consistency
    if (outcome.winner && !['team1', 'team2'].includes(outcome.winner)) {
      errors.push('Winner must be either "team1" or "team2"');
    }

    // Check for logical inconsistencies
    if (outcome.team1Score && outcome.team2Score) {
      if (outcome.team1Score.runs === outcome.team2Score.runs && !outcome.isTie) {
        warnings.push('Scores are equal but match is not marked as tie');
      }
      if (outcome.team1Score.runs !== outcome.team2Score.runs && outcome.isTie) {
        warnings.push('Match is marked as tie but scores are different');
      }
    }

    // Check confidence score
    if (outcome.confidence !== undefined && (outcome.confidence < 0 || outcome.confidence > 1)) {
      warnings.push('Confidence score should be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

module.exports = new MatchOutcomeController();