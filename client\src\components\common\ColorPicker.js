import React from 'react';
import { Box, Typography, Grid, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';

const ColorSwatch = styled(Paper)(({ theme, selected }) => ({
  width: 40,
  height: 40,
  cursor: 'pointer',
  border: selected ? `2px solid ${theme.palette.primary.main}` : '2px solid transparent',
  borderRadius: '50%',
  transition: 'transform 0.2s',
  '&:hover': {
    transform: 'scale(1.1)',
  },
}));

/**
 * Color picker component for selecting colors
 * @param {Object} props - Component props
 * @param {Array} props.colors - Array of color objects with name and value properties
 * @param {string} props.selectedColor - Currently selected color value
 * @param {Function} props.onChange - Function to call when a color is selected
 * @param {string} props.label - Label for the color picker
 */
const ColorPicker = ({ colors, selectedColor, onChange, label }) => {
  return (
    <Box sx={{ mb: 2 }}>
      {label && (
        <Typography variant="subtitle2" gutterBottom>
          {label}
        </Typography>
      )}
      <Grid container spacing={1}>
        {colors.map((color) => (
          <Grid item key={color.name}>
            <ColorSwatch
              elevation={1}
              sx={{ bgcolor: color.value }}
              selected={selectedColor === color.value}
              onClick={() => onChange(color.value, color.name)}
              title={color.name}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default ColorPicker;
