# Setting Up ngrok for HTTPS Access

## What is ngrok?
ngrok is a tool that creates a secure tunnel to your local development server, making it accessible over HTTPS from anywhere, including mobile devices.

## Installation Steps

1. **Download ngrok**:
   - Go to https://ngrok.com/download
   - Download the version for your operating system
   - Extract the executable to a folder on your computer

2. **Create a free ngrok account**:
   - Go to https://dashboard.ngrok.com/signup
   - Sign up for a free account
   - Get your authtoken from the dashboard

3. **Configure ngrok with your authtoken**:
   ```
   ngrok config add-authtoken YOUR_AUTH_TOKEN
   ```

4. **Start a tunnel to your React development server**:
   ```
   ngrok http 3000
   ```

5. **Use the HTTPS URL provided by ngrok**:
   - ngrok will display a URL like `https://12345abcde.ngrok.io`
   - Use this URL on your mobile device to access your app securely

## Example Output

```
ngrok                                                                                                                                                                                                                                                                                                                   (Ctrl+C to quit)
                                                                                                                                                                                                                                                                                                                                        
Session Status                online                                                                                                                                                                                                                                                                                                    
Account                       Your Name (Plan: Free)                                                                                                                                                                                                                                                                                    
Version                       3.3.1                                                                                                                                                                                                                                                                                                     
Region                        United States (us)                                                                                                                                                                                                                                                                                        
Latency                       45ms                                                                                                                                                                                                                                                                                                      
Web Interface                 http://127.0.0.1:4040                                                                                                                                                                                                                                                                                     
Forwarding                    https://12345abcde.ngrok.io -> http://localhost:3000                                                                                                                                                                                                                                                      
                                                                                                                                                                                                                                                                                                                                        
Connections                   ttl     opn     rt1     rt5     p50     p90                                                                                                                                                                                                                                                               
                              0       0       0.00    0.00    0.00    0.00 
```

## Important Notes

1. **Free tier limitations**:
   - The free tier of ngrok assigns a random URL each time you start it
   - Sessions last for 2 hours before they need to be restarted
   - Limited to 4 tunnels per account

2. **Proxy considerations**:
   - If your React app makes API calls to a local backend, you'll need to configure CORS or proxy settings

3. **Security considerations**:
   - Anyone with the ngrok URL can access your local development server
   - Don't use ngrok for sensitive development work without proper authentication

## Alternative: local-ssl-proxy

If you prefer not to use ngrok, you can use local-ssl-proxy:

1. **Install local-ssl-proxy**:
   ```
   npm install -g local-ssl-proxy
   ```

2. **Generate self-signed certificates**:
   ```
   openssl req -x509 -newkey rsa:2048 -keyout key.pem -out cert.pem -days 365 -nodes
   ```

3. **Start the proxy**:
   ```
   local-ssl-proxy --source 3001 --target 3000 --cert cert.pem --key key.pem
   ```

4. **Access your app via HTTPS**:
   - Use `https://localhost:3001` to access your app
   - Note: You'll need to accept the self-signed certificate warning
