import React from 'react';
import { Box, Container, Typography, Link, useTheme as useMuiTheme, useMediaQuery } from '@mui/material';
import NavBar from './NavBar';
import DarkModeToggle from '../common/DarkModeToggle';
import { useTheme } from '../../context/ThemeContext';

// Footer component
const Footer = () => {
  const theme = useMuiTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      component="footer"
      sx={{
        py: { xs: 2, md: 3 },
        px: { xs: 2, md: 2 },
        mt: 'auto',
        backgroundColor: (theme) => theme.palette.mode === 'light'
          ? theme.palette.grey[100]
          : theme.palette.grey[900],
        borderTop: '1px solid',
        borderColor: (theme) => theme.palette.divider
      }}
    >
      <Container maxWidth="lg">
        <Typography
          variant="body2"
          color="text.secondary"
          align="center"
          sx={{ mb: 1 }}
        >
          {'© '}
          {new Date().getFullYear()}
          {' Big Ant Cricket 24 Tournament Management System'}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            flexDirection: isMobile ? 'column' : 'row',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Link
            color="inherit"
            href="#"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            Privacy Policy
          </Link>
          {!isMobile && <Typography variant="body2" color="text.secondary">•</Typography>}
          <Link
            color="inherit"
            href="#"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            Terms of Service
          </Link>
          {!isMobile && <Typography variant="body2" color="text.secondary">•</Typography>}
          <Link
            color="inherit"
            href="#"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline'
              }
            }}
          >
            Contact Us
          </Link>
        </Box>
      </Container>
    </Box>
  );
};

// Main layout component
const Layout = ({ children }) => {
  const { darkMode } = useTheme();
  const theme = useMuiTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: (theme) =>
          theme.palette.mode === 'light'
            ? theme.palette.grey[50]
            : theme.palette.background.default,
      }}
      className={darkMode ? 'dark-mode' : 'light-mode'}
    >
      <NavBar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          pt: { xs: 2, sm: 3, md: 4 },
          pb: { xs: 4, sm: 5, md: 6 },
          px: { xs: 1, sm: 2, md: 3 },
          overflow: 'auto'
        }}
      >
        {children}
      </Box>
      <Footer />
      <DarkModeToggle />
    </Box>
  );
};

export default Layout;