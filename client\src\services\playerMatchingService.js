import axios from 'axios';
import { API_URL } from '../config';

/**
 * Player Matching Service
 * 
 * Provides client-side integration with the robust player matching backend service.
 * Handles automatic player name matching from OCR data with confidence scoring,
 * fuzzy matching for OCR errors, and manual verification workflows.
 */

// Create API instance
const API = axios.create({
  baseURL: `${API_URL}/player-matching`
});

// Add auth token to requests
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * Match a single player name from OCR against the database
 * 
 * @param {string} ocrName - The player name extracted from OCR
 * @param {Object} options - Matching options
 * @param {string} options.teamFilter - Filter by specific team
 * @param {string} options.roleFilter - Filter by player role (batsman/bowler/allrounder)
 * @param {boolean} options.requireManualVerification - Force manual verification
 * @returns {Promise<Object>} - Match result with confidence and suggestions
 */
export const matchSinglePlayer = async (ocrName, options = {}) => {
  try {
    const response = await API.post('/match-single', {
      ocrName,
      ...options
    });
    return response.data;
  } catch (error) {
    console.error('Error matching single player:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Match multiple player names from OCR in batch
 * 
 * @param {Array<string>} ocrNames - Array of player names from OCR
 * @param {Object} options - Matching options
 * @param {string} options.teamFilter - Filter by specific team
 * @param {string} options.roleFilter - Filter by player role
 * @returns {Promise<Object>} - Batch matching results categorized by match type
 */
export const matchBatchPlayers = async (ocrNames, options = {}) => {
  try {
    const response = await API.post('/match-batch', {
      ocrNames,
      ...options
    });
    return response.data;
  } catch (error) {
    console.error('Error matching batch players:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Search players for manual matching (used in dropdowns/autocomplete)
 * 
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @param {number} options.limit - Maximum number of results
 * @param {string} options.teamFilter - Filter by team
 * @param {string} options.roleFilter - Filter by role
 * @returns {Promise<Array>} - Array of matching players
 */
export const searchPlayers = async (query, options = {}) => {
  try {
    const response = await API.get('/search-players', {
      params: {
        query,
        limit: options.limit || 10,
        teamFilter: options.teamFilter,
        roleFilter: options.roleFilter
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error searching players:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Confirm a manual player match
 * 
 * @param {string} ocrName - Original OCR name
 * @param {string} playerId - Selected player ID
 * @param {string} confidence - Confidence level (high/medium/low)
 * @returns {Promise<Object>} - Confirmation result
 */
export const confirmMatch = async (ocrName, playerId, confidence = 'medium') => {
  try {
    const response = await API.post('/confirm-match', {
      ocrName,
      playerId,
      confidence
    });
    return response.data;
  } catch (error) {
    console.error('Error confirming match:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Process OCR data and match all players automatically
 * 
 * @param {Object} ocrData - Complete OCR extraction result
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} - Enhanced OCR data with player matching results
 */
export const processOCRPlayerMatching = async (ocrData, options = {}) => {
  try {
    // Debug: Check authentication status
    const token = localStorage.getItem('token');
    console.log('Player matching debug:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      apiUrl: `${API_URL}/player-matching`
    });
    
    // Extract all player names from OCR data
    const allPlayerNames = [];
    
    // Collect batsmen names
    if (ocrData.team1Batsmen) {
      allPlayerNames.push(...ocrData.team1Batsmen.map(p => p.name));
    }
    if (ocrData.team2Batsmen) {
      allPlayerNames.push(...ocrData.team2Batsmen.map(p => p.name));
    }
    
    // Collect bowler names
    if (ocrData.team1Bowlers) {
      allPlayerNames.push(...ocrData.team1Bowlers.map(p => p.name));
    }
    if (ocrData.team2Bowlers) {
      allPlayerNames.push(...ocrData.team2Bowlers.map(p => p.name));
    }
    
    // Add player of the match if present
    if (ocrData.playerOfMatch) {
      allPlayerNames.push(ocrData.playerOfMatch);
    }
    
    // Remove duplicates
    const uniquePlayerNames = [...new Set(allPlayerNames.filter(name => name && name.trim()))];
    
    if (uniquePlayerNames.length === 0) {
      return {
        ...ocrData,
        playerMatching: {
          processed: true,
          totalPlayers: 0,
          results: {
            automaticMatches: [],
            manualVerificationRequired: [],
            noMatches: [],
            errors: []
          }
        }
      };
    }
    
    // Perform batch matching
    const matchingResults = await matchBatchPlayers(uniquePlayerNames, options);
    
    // Debug: Log the actual API response structure
    console.log('=== PLAYER MATCHING API RESPONSE DEBUG ===');
    console.log('Raw matchingResults:', matchingResults);
    console.log('matchingResults type:', typeof matchingResults);
    console.log('matchingResults keys:', Object.keys(matchingResults || {}));
    console.log('matchingResults.data:', matchingResults?.data);
    console.log('matchingResults.success:', matchingResults?.success);
    console.log('matchingResults.automaticMatches:', matchingResults?.automaticMatches);
    console.log('matchingResults.manualVerificationRequired:', matchingResults?.manualVerificationRequired);
    console.log('matchingResults.noMatches:', matchingResults?.noMatches);
    console.log('=== END DEBUG ===');
    
    // Extract the actual results from the API response
    const results = matchingResults.data;
    
    // Enhance OCR data with matching results
    const enhancedOcrData = {
      ...ocrData,
      playerMatching: {
        processed: true,
        totalPlayers: results.totalPlayers || uniquePlayerNames.length,
        results: results
      }
    };
    
    // Add matching metadata to individual player records
    enhancedOcrData.team1Batsmen = enhancePlayerRecords(ocrData.team1Batsmen, results);
    enhancedOcrData.team2Batsmen = enhancePlayerRecords(ocrData.team2Batsmen, results);
    enhancedOcrData.team1Bowlers = enhancePlayerRecords(ocrData.team1Bowlers, results);
    enhancedOcrData.team2Bowlers = enhancePlayerRecords(ocrData.team2Bowlers, results);
    
    return enhancedOcrData;
    
  } catch (error) {
    console.error('Error processing OCR player matching:', error);
    console.error('Error details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      statusText: error.response?.statusText
    });
    
    // Check if it's an authentication error
    const isAuthError = error.response?.status === 401 || 
                       error.response?.data?.msg?.includes('authorization denied');
    
    // Return original data with detailed error information
    return {
      ...ocrData,
      playerMatching: {
        processed: false,
        error: isAuthError ? 'Authentication required for player matching' : (error.message || 'Player matching failed'),
        isAuthError,
        totalPlayers: 0,
        results: {
          automaticMatches: [],
          manualVerificationRequired: [],
          noMatches: [],
          errors: []
        }
      }
    };
  }
};

/**
 * Enhance player records with matching metadata
 * 
 * @param {Array} playerRecords - Array of player records from OCR
 * @param {Object} matchingResults - Results from batch matching
 * @returns {Array} - Enhanced player records with matching info
 */
const enhancePlayerRecords = (playerRecords, matchingResults) => {
  if (!playerRecords || !Array.isArray(playerRecords)) {
    return playerRecords;
  }
  
  return playerRecords.map(player => {
    if (!player.name) return player;
    
    // Find matching result for this player
    const automaticMatch = matchingResults.automaticMatches?.find(m => m.ocrName === player.name);
    const manualMatch = matchingResults.manualVerificationRequired?.find(m => m.ocrName === player.name);
    const noMatch = matchingResults.noMatches?.find(m => m.ocrName === player.name);
    
    let matchingInfo = {
      status: 'no_match',
      confidence: 0,
      matchedPlayer: null,
      candidates: []
    };
    
    if (automaticMatch) {
      matchingInfo = {
        status: 'automatic',
        confidence: automaticMatch.confidence,
        matchedPlayer: automaticMatch.matchedPlayer,
        candidates: automaticMatch.candidates || []
      };
    } else if (manualMatch) {
      matchingInfo = {
        status: 'manual_verification',
        confidence: manualMatch.topCandidate?.confidence || 0,
        matchedPlayer: null,
        candidates: manualMatch.candidates || []
      };
    }
    
    return {
      ...player,
      matching: matchingInfo
    };
  });
};

/**
 * Get match status summary for UI display
 * 
 * @param {Object} playerMatchingResults - Results from processOCRPlayerMatching
 * @returns {Object} - Summary statistics for UI
 */
export const getMatchingSummary = (playerMatchingResults) => {
  if (!playerMatchingResults || !playerMatchingResults.results) {
    return {
      total: 0,
      automatic: 0,
      manual: 0,
      noMatch: 0,
      successRate: 0
    };
  }
  
  const results = playerMatchingResults.results;
  const automatic = results.automaticMatches?.length || 0;
  const manual = results.manualVerificationRequired?.length || 0;
  const noMatch = results.noMatches?.length || 0;
  const total = automatic + manual + noMatch;
  
  return {
    total,
    automatic,
    manual,
    noMatch,
    successRate: total > 0 ? Math.round((automatic / total) * 100) : 0
  };
};

export default {
  matchSinglePlayer,
  matchBatchPlayers,
  searchPlayers,
  confirmMatch,
  processOCRPlayerMatching,
  getMatchingSummary
};