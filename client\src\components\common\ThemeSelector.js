import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  useTheme
} from '@mui/material';
import {
  Palette as PaletteIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  SportsCricket as CricketIcon
} from '@mui/icons-material';
import { useEnhancedTheme } from '../../theme/EnhancedThemeProvider';

/**
 * Theme Selector Component
 * 
 * Provides a dropdown menu to select different themes
 */
const ThemeSelector = () => {
  const { currentTheme, setTheme, availableThemes, themeNames } = useEnhancedTheme();
  const theme = useTheme();
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  
  // Open menu handler
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  // Close menu handler
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  // Theme selection handler
  const handleThemeSelect = (themeName) => {
    setTheme(themeName);
    handleClose();
  };
  
  // Get icon for theme
  const getThemeIcon = (themeName) => {
    switch (themeName) {
      case 'light':
        return <LightModeIcon />;
      case 'dark':
        return <DarkModeIcon />;
      case 'cricket':
        return <CricketIcon />;
      default:
        return <LightModeIcon />;
    }
  };
  
  return (
    <Box>
      <Tooltip title="Change theme">
        <IconButton
          onClick={handleClick}
          size="medium"
          aria-controls={open ? 'theme-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          color="inherit"
        >
          <PaletteIcon />
        </IconButton>
      </Tooltip>
      
      <Menu
        id="theme-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'theme-button',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            overflow: 'visible',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 14,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {availableThemes.map((themeName) => (
          <MenuItem 
            key={themeName}
            selected={themeName === currentTheme}
            onClick={() => handleThemeSelect(themeName)}
            sx={{
              borderRadius: 1,
              mx: 1,
              my: 0.5,
              '&.Mui-selected': {
                backgroundColor: theme.palette.mode === 'dark' 
                  ? 'rgba(255, 255, 255, 0.08)'
                  : 'rgba(0, 0, 0, 0.04)',
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.12)'
                    : 'rgba(0, 0, 0, 0.08)',
                }
              }
            }}
          >
            <ListItemIcon>
              {getThemeIcon(themeName)}
            </ListItemIcon>
            <ListItemText>{themeNames[themeName]}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default ThemeSelector;
