/**
 * Clean OCR Service
 *
 * A simple, robust OCR service using PaddleOCR for cricket scorecard processing
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const FormData = require('form-data');
const sharp = require('sharp');
const { enhanceWithBowlingFigures } = require('../utils/bowlingFigureParser');
const { normalizePaddleOutput, normalizeInJS } = require('../utils/paddleNormalizer');
const GoogleVisionService = require('./googleVisionService');
const { matchPlayerNames } = require('../utils/playerNameMatcher');

// Create OCR output directories if they don't exist
const ocrOutputDir = path.join(__dirname, '..', 'ocr-output');
const extractedDir = path.join(ocrOutputDir, 'extracted');
const processedDir = path.join(ocrOutputDir, 'processed');
const templatesDir = path.join(ocrOutputDir, 'templates');

[ocrOutputDir, extractedDir, processedDir, templatesDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created OCR output directory: ${dir}`);
  }
});

const OCRSettings = require('../models/OCRSettings');

class OCRService {
  constructor() {
    this.pythonPath = process.env.PYTHON_PATH || 'python';
    this.scriptPath = path.join(__dirname, '../scripts/cricket_scorecard_parser_generalizable.py');
    this.googleVisionService = new GoogleVisionService();
    this.useGoogleVisionVerification = true; // Enable Google Vision verification
    this.settings = null;
    this.lastSettingsLoad = 0;
  }

  static async initialize() {
    const service = new OCRService();
    await service.loadSettings();
    return service;
  }

  async loadSettings() {
    try {
      // Add sort by lastUpdated to ensure we get the most recent settings
      let settings = await OCRSettings.findOne().sort({ lastUpdated: -1 });
      if (!settings) {
        settings = await OCRSettings.create({});
      }
      this.settings = settings;
      this.lastSettingsLoad = Date.now();
      console.log(`OCR settings loaded: primaryMethod=${settings.primaryMethod}, fallbackMethod=${settings.fallbackMethod}`);
    } catch (error) {
      console.error('Error loading OCR settings:', error);
      // Use defaults if settings can't be loaded
      this.settings = {
        primaryMethod: 'ocrspace',
        fallbackMethod: 'googlevision',
        ocrSpaceTimeout: 60000
      };
    }
  }

  /**
   * Enhance image for better OCR accuracy
   * @param {string} imagePath - Path to the original image
   * @returns {Promise<string>} - Path to the enhanced image
   */
  async enhanceImageForOCR(imagePath) {
    try {
      console.log(`Enhancing image for OCR: ${imagePath}`);

      // Create enhanced image filename
      const imageDir = path.dirname(imagePath);
      const imageBasename = path.basename(imagePath, path.extname(imagePath));
      const imageExt = path.extname(imagePath);
      const enhancedPath = path.join(imageDir, `${imageBasename}_enhanced${imageExt}`);

      // Apply image enhancements for better OCR (optimized for file size)
      await sharp(imagePath)
        .grayscale()                    // Convert to grayscale - removes color confusion
        .normalize()                    // Auto-enhance contrast - makes text clearer
        .sharpen({ sigma: 1.0 })       // Sharpen text edges - improves character recognition
        .jpeg({
          quality: 85,                  // High quality but compressed
          progressive: false,           // Baseline JPEG for better OCR compatibility
          mozjpeg: true                 // Use mozjpeg encoder for better compression
        })
        .toFile(enhancedPath.replace('.png', '.jpg'));

      const finalEnhancedPath = enhancedPath.replace('.png', '.jpg');
      console.log(`Enhanced image saved to: ${finalEnhancedPath}`);
      return finalEnhancedPath;

    } catch (error) {
      console.error('Image enhancement failed:', error.message);
      console.log('Falling back to original image...');
      return imagePath; // Return original if enhancement fails
    }
  }

  /**
   * Verify player scores that might have 6/9 confusion using Google Vision
   * @param {string} imagePath - Path to the image file
   * @param {Array} players - Array of player objects with name, runs, and balls
   * @returns {Promise<Array>} - Array of verified player objects
   */
  async verifyPlayerScores(imagePath, players) {
    if (!this.useGoogleVisionVerification) {
      return players; // Skip verification if disabled
    }
    
    try {
      console.log('Verifying player scores with Google Vision...');
      
      const verifiedPlayers = [...players];
      const playersToVerify = players.filter(player => {
        // Check for potential 6/9 confusion
        // First, handle cases with * for not out
        const runsStr = player.runs ? player.runs.toString() : '';
        const ballsStr = player.balls ? player.balls.toString() : '';
        
        // Extract numeric part from runs (removing * if present)
        const runsNumeric = runsStr.replace('*', '');
        
        // Check if runs or balls contain 6 or 9
        const hasRunsWith6or9 = runsNumeric.includes('6') || runsNumeric.includes('9');
        const hasBallsWith6or9 = ballsStr.includes('6') || ballsStr.includes('9');
        
        // Return true if either runs or balls contain 6 or 9
        return hasRunsWith6or9 || hasBallsWith6or9;
      });
      
      if (playersToVerify.length === 0) {
        console.log('No players need verification');
        return players;
      }
      
      console.log(`Found ${playersToVerify.length} players that need verification:`);
      playersToVerify.forEach(player => {
        console.log(`- ${player.name}: ${player.runs}(${player.balls})`);
      });
      
      // Verify each player
      for (const player of playersToVerify) {
        console.log(`Verifying ${player.name}'s score...`);
        const verifiedData = await this.googleVisionService.verifyPlayerScore(imagePath, player.name);
        
        if (verifiedData.found && verifiedData.runs !== null) {
          console.log(`Google Vision detected ${player.name}'s score as: ${verifiedData.runs}(${verifiedData.balls})`);
          console.log(`Original score was: ${player.runs}(${player.balls})`);
          
          // If Google Vision disagrees with the original OCR
          if (verifiedData.runs !== player.runs || verifiedData.balls !== player.balls) {
            console.log(`⚠️ Score mismatch detected for ${player.name}!`);
            console.log(`Original: ${player.runs}(${player.balls}), Google Vision: ${verifiedData.runs}(${verifiedData.balls})`);
            
            // Update the player's score in the verified array
            const playerIndex = verifiedPlayers.findIndex(p => p.name === player.name);
            if (playerIndex !== -1) {
              // Preserve the not out indicator (*) if present in the original runs
              const isNotOut = player.runs && player.runs.toString().includes('*');
              const numericRuns = verifiedData.runs ? verifiedData.runs.toString().replace('*', '') : '';
              
              verifiedPlayers[playerIndex].runs = isNotOut ? `${numericRuns}*` : verifiedData.runs;
              verifiedPlayers[playerIndex].balls = verifiedData.balls;
              verifiedPlayers[playerIndex].verified = true;
              verifiedPlayers[playerIndex].originalRuns = player.runs;
              verifiedPlayers[playerIndex].originalBalls = player.balls;
            }
          } else {
            console.log(`✅ Google Vision confirms ${player.name}'s score: ${player.runs}(${player.balls})`);
          }
        } else {
          console.log(`❌ Google Vision could not find ${player.name}`);
        }
      }
      
      return verifiedPlayers;
    } catch (error) {
      console.error('Error verifying player scores:', error);
      return players; // Return original players on error
    }
  }

  /**
   * Process an image with OCR.Space API
   * @param {string} imagePath - Path to the image file
   * @param {Object} options - OCR options
   * @returns {Promise<Object>} - OCR results
   */
  async processImageWithOCRSpace(imagePath, options = {}) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const imageBasename = path.basename(imagePath, path.extname(imagePath));
    let cricketData = null;

    try {
      console.log(`Processing image with OCR.Space: ${imagePath}`);

      // Check if image exists
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // STEP 1: Enhance image for better OCR accuracy
      console.log('🔧 Enhancing image for better OCR accuracy...');
      const enhancedImagePath = await this.enhanceImageForOCR(imagePath);
      console.log(`✅ Using enhanced image: ${enhancedImagePath}`);

      // OCR.Space API configuration
      const apiKey = 'K83396245188957';
      const apiUrl = 'https://api.ocr.space/parse/image';

      // STEP 2: Create form data with enhanced image
      const formData = new FormData();
      formData.append('file', fs.createReadStream(enhancedImagePath));
      formData.append('apikey', apiKey);
      formData.append('language', 'eng');
      formData.append('isOverlayRequired', 'true'); // Enable coordinate overlay
      formData.append('detectOrientation', 'true');
      formData.append('scale', 'true');
      formData.append('isTable', 'false');
      formData.append('OCREngine', '2'); // Engine 2 has better overall accuracy
      formData.append('isCreateSearchablePdf', 'false');
      formData.append('isSearchablePdfHideTextLayer', 'false');
      formData.append('filetype', 'auto'); // Auto-detect file type

      console.log('Calling OCR.Space API...');

      // Call OCR.Space API with configured timeout
      const response = await axios.post(apiUrl, formData, {
        headers: {
          ...formData.getHeaders(),
        },
        timeout: this.settings.ocrSpaceTimeout // Use timeout from settings
      });

      console.log('OCR.Space API response received');

      // Save raw API response for debugging
      const rawOutputFile = path.join(extractedDir, `${timestamp}_${imageBasename}_ocrspace_raw.json`);
      fs.writeFileSync(rawOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        apiResponse: response.data
      }, null, 2));
      console.log(`Saved OCR.Space raw response to: ${rawOutputFile}`);

      // Process OCR.Space response
      const ocrSpaceData = this.parseOCRSpaceResponse(response.data);

      // Extract cricket-specific data using coordinates if available
      let cricketData;
      if (ocrSpaceData.hasOverlay && ocrSpaceData.textOverlay) {
        console.log('Using coordinate-based extraction...');
        cricketData = await this.extractCricketDataFromCoordinates(ocrSpaceData.fullText, ocrSpaceData.textOverlay);
      } else {
        console.log('Using text-based extraction (fallback)...');
        cricketData = await this.extractCricketDataFromText(ocrSpaceData.fullText);
      }

      // Save final processed cricket data
      const processedOutputFile = path.join(processedDir, `${timestamp}_${imageBasename}_ocrspace_cricket.json`);
      fs.writeFileSync(processedOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        cricketData: cricketData,
        metadata: {
          processingSteps: ['ocrspace_api', 'text_extraction', 'cricket_parsing'],
          success: cricketData.success,
          ocrStatus: cricketData.ocrStatus,
          engine: 'OCR.Space'
        }
      }, null, 2));
      console.log(`Saved OCR.Space cricket data to: ${processedOutputFile}`);

      console.log('OCR.Space processing completed successfully');
      return cricketData;

    } catch (error) {
      console.error('OCR.Space processing failed:', error.message);

      // Save error information
      try {
        const errorFile = path.join(extractedDir, `${timestamp}_${imageBasename}_ocrspace_error.json`);
        fs.writeFileSync(errorFile, JSON.stringify({
          timestamp: new Date().toISOString(),
          imagePath: imagePath,
          error: error.message,
          errorStack: error.stack,
          step: 'ocrspace_processing_failed',
          errorType: error.constructor.name
        }, null, 2));
        console.log(`Saved OCR.Space error to: ${errorFile}`);
      } catch (saveError) {
        console.error('Failed to save error file:', saveError.message);
      }

      // Check if error is a timeout
      const isTimeout = error.code === 'ECONNABORTED' || error.message.includes('timeout');
      
      // Try Google Vision as fallback
      console.log(`OCR.Space failed due to ${isTimeout ? 'timeout' : 'error'}. Attempting fallback to Google Vision API...`);
      try {
        cricketData = await this.googleVisionService.processImage(imagePath);
        if (cricketData && cricketData.success) {
          console.log('Successfully processed with Google Vision fallback');
          cricketData.ocrStatus = 'success_fallback';
          cricketData.ocrMessage = 'Processed using Google Vision API (fallback)';
          cricketData.engine = 'Google Vision';
          return cricketData;
        } else {
          throw new Error('Google Vision processing failed to extract valid cricket data');
        }
      } catch (fallbackError) {
        console.error('Google Vision fallback also failed:', fallbackError.message);
        // Re-throw the original error if it was a timeout, otherwise combine both errors
        if (isTimeout) {
          throw error;
        } else {
          throw new Error(`OCR.Space failed: ${error.message}. Google Vision fallback failed: ${fallbackError.message}`);
        }
      }

      // If both OCR services fail, return error data
      return {
        success: false,
        error: error.message,
        errorStack: error.stack,
        errorLocation: 'OCRService.processImageWithOCRSpace',
        team1: 'Team 1',
        team2: 'Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        playerOfMatch: '',
        resultText: '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: '',
        ocrStatus: 'failed_all',
        ocrMessage: `Both OCR.Space and Google Vision failed. Please enter match details manually.`,
        debugInfo: {
          errorType: error.constructor.name,
          errorMessage: error.message,
          timestamp: new Date().toISOString(),
          engine: 'OCR.Space, Google Vision'
        }
      };
    }
  }

  /**
   * Process an image with PaddleOCR using coordinates
   * @param {string} imagePath - Path to the image file
   * @param {Object} options - OCR options
   * @returns {Promise<Object>} - OCR results
   */
  async processImageWithPaddleOCRCoordinates(imagePath, options = {}) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const imageBasename = path.basename(imagePath, path.extname(imagePath));

    try {
      console.log(`Processing image with PaddleOCR coordinates: ${imagePath}`);

      // Check if image exists
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // Use the fixed coordinate extractor script
      const scriptPath = path.join(__dirname, '../scripts/paddle_coordinate_extractor_fixed.py');

      console.log('Calling PaddleOCR coordinate extractor...');

      // Run the coordinate extraction script
      const result = await new Promise((resolve, reject) => {
        const spawn = require('child_process').spawn;
        const pythonProcess = spawn(this.pythonPath, [scriptPath, imagePath]);

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        pythonProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        pythonProcess.on('close', (code) => {
          if (code === 0) {
            try {
              const result = JSON.parse(stdout);
              resolve(result);
            } catch (parseError) {
              reject(new Error(`Failed to parse PaddleOCR output: ${parseError.message}`));
            }
          } else {
            reject(new Error(`PaddleOCR process failed with code ${code}: ${stderr}`));
          }
        });

        pythonProcess.on('error', (error) => {
          reject(new Error(`Failed to start PaddleOCR process: ${error.message}`));
        });
      });

      console.log('PaddleOCR coordinate extraction completed');

      // Normalize PaddleOCR output to make it more similar to OCR.Space format
      console.log('Normalizing PaddleOCR output...');
      const normalizedResult = await normalizePaddleOutput(result);
      console.log(`Normalized ${normalizedResult.text_elements?.length || 0} text elements`);

      // Save raw coordinate data for debugging
      const rawOutputFile = path.join(extractedDir, `${timestamp}_${imageBasename}_paddle_coordinates.json`);
      fs.writeFileSync(rawOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        coordinateData: result,
        normalizedData: normalizedResult
      }, null, 2));
      console.log(`Saved PaddleOCR coordinate data to: ${rawOutputFile}`);

      // Extract cricket-specific data using coordinates
      let cricketData;
      if (normalizedResult.success && normalizedResult.has_coordinates && normalizedResult.text_elements) {
        console.log('Using coordinate-based extraction with normalized PaddleOCR data...');
        cricketData = await this.extractCricketDataFromPaddleCoordinates(normalizedResult.full_text, normalizedResult.text_elements);
      } else {
        console.log('Using text-based extraction (fallback)...');
        cricketData = this.extractCricketDataFromText(normalizedResult.full_text || '');
      }

      // Save final processed cricket data
      const processedOutputFile = path.join(processedDir, `${timestamp}_${imageBasename}_paddle_cricket.json`);
      fs.writeFileSync(processedOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        cricketData: cricketData
      }, null, 2));
      console.log(`Saved PaddleOCR cricket data to: ${processedOutputFile}`);

      return cricketData;

    } catch (error) {
      console.error('PaddleOCR coordinate processing failed:', error);

      // Save error for debugging
      const errorOutputFile = path.join(extractedDir, `${timestamp}_${imageBasename}_paddle_error.json`);
      fs.writeFileSync(errorOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        error: error.message,
        stack: error.stack
      }, null, 2));
      console.log(`Saved PaddleOCR error to: ${errorOutputFile}`);

      // Return fallback data
      return {
        success: false,
        error: error.message,
        errorStack: error.stack,
        errorLocation: 'OCRService.processImageWithPaddleOCRCoordinates',
        team1: 'Team 1',
        team2: 'Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        playerOfMatch: '',
        resultText: '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: '',
        ocrStatus: 'failed',
        ocrMessage: `PaddleOCR coordinate extraction failed: ${error.message}. Please enter match details manually.`,
        debugInfo: {
          errorType: error.constructor.name,
          errorMessage: error.message,
          timestamp: new Date().toISOString(),
          engine: 'PaddleOCR-Coordinates'
        }
      };
    }
  }

  /**
   * Process an image with OCR
   * @param {string} imagePath - Path to the image file
   * @param {Object} options - OCR options
   * @returns {Promise<Object>} - OCR results
   */
  async processImage(imagePath, options = {}) {
    // Dynamic OCR method selection based on settings
    try {
      let result;
      
      // Refresh settings if they're stale (older than 5 seconds)
      const SETTINGS_REFRESH_INTERVAL = 5000; // 5 seconds
      if (Date.now() - this.lastSettingsLoad > SETTINGS_REFRESH_INTERVAL) {
        console.log('Refreshing OCR settings...');
        await this.loadSettings();
      }
      
      // Use the configured primary method
      console.log(`Using ${this.settings.primaryMethod} as primary OCR method`);
      switch (this.settings.primaryMethod) {
        case 'ocrspace':
          result = await this.processImageWithOCRSpace(imagePath, options);
          break;
        case 'googlevision':
          result = await this.googleVisionService.processImage(imagePath);
          break;
        case 'paddleocr':
          result = await this.processImageWithPaddleOCRCoordinates(imagePath, options);
          break;
        default:
          throw new Error(`Unknown primary OCR method: ${this.settings.primaryMethod}`);
      }
      
      // Verify player scores if needed
      if (this.useGoogleVisionVerification && result.team1Batsmen && result.team2Batsmen) {
        // Combine all batsmen for verification
        const allBatsmen = [...result.team1Batsmen, ...result.team2Batsmen];
        
        // Verify scores that might have 6/9 confusion
        const verifiedBatsmen = await this.verifyPlayerScores(imagePath, allBatsmen);
        
        // Update the results with verified scores
        if (verifiedBatsmen.length > 0) {
          // Split back into team1 and team2
          const team1Count = result.team1Batsmen.length;
          result.team1Batsmen = verifiedBatsmen.slice(0, team1Count);
          result.team2Batsmen = verifiedBatsmen.slice(team1Count);
          
          // Add verification info to result
          result.verification = {
            method: 'google-vision',
            timestamp: new Date().toISOString(),
            playersVerified: verifiedBatsmen.filter(p => p.verified).length
          };
        }
      }
      
      return result;

    } catch (error) {
      if (this.settings.fallbackMethod === 'none') {
        throw error; // No fallback configured, propagate the error
      }

      console.log(`${this.settings.primaryMethod} failed, falling back to ${this.settings.fallbackMethod}...`);
      console.log(`Error: ${error.message}`);

      // Use the configured fallback method
      let result;
      switch (this.settings.fallbackMethod) {
        case 'googlevision':
          result = await this.googleVisionService.processImage(imagePath);
          break;
        case 'paddleocr':
          result = await this.processImageWithPaddleOCRCoordinates(imagePath, options);
          break;
        default:
          throw new Error(`Unknown fallback OCR method: ${this.settings.fallbackMethod}`);
      }
      
      // Verify player scores if needed
      if (this.useGoogleVisionVerification && result.team1Batsmen && result.team2Batsmen) {
        // Combine all batsmen for verification
        const allBatsmen = [...result.team1Batsmen, ...result.team2Batsmen];
        
        // Verify scores that might have 6/9 confusion
        const verifiedBatsmen = await this.verifyPlayerScores(imagePath, allBatsmen);
        
        // Update the results with verified scores
        if (verifiedBatsmen.length > 0) {
          // Split back into team1 and team2
          const team1Count = result.team1Batsmen.length;
          result.team1Batsmen = verifiedBatsmen.slice(0, team1Count);
          result.team2Batsmen = verifiedBatsmen.slice(team1Count);
          
          // Add verification info to result
          result.verification = {
            method: 'google-vision',
            timestamp: new Date().toISOString(),
            playersVerified: verifiedBatsmen.filter(p => p.verified).length
          };
        }
      }
      
      return result;
    }

    // Create timestamp and filename for JSON files (do this first, before any errors)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const imageBasename = path.basename(imagePath, path.extname(imagePath));

    try {
      console.log(`Processing image with PaddleOCR: ${imagePath}`);

      // Check if image exists
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // Check if OCR script exists
      if (!fs.existsSync(this.scriptPath)) {
        throw new Error(`OCR script not found: ${this.scriptPath}`);
      }

      // Prepare command arguments
      const args = [
        this.scriptPath,
        '--image', imagePath,
        '--debug'
      ];

      console.log(`Running OCR command: ${this.pythonPath} ${args.join(' ')}`);

      // Save command info for debugging
      const commandInfoFile = path.join(extractedDir, `${timestamp}_${imageBasename}_command.json`);
      fs.writeFileSync(commandInfoFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        command: `${this.pythonPath} ${args.join(' ')}`,
        scriptPath: this.scriptPath,
        imageExists: fs.existsSync(imagePath),
        scriptExists: fs.existsSync(this.scriptPath),
        step: 'before_execution'
      }, null, 2));
      console.log(`Saved command info to: ${commandInfoFile}`);

      // Execute OCR script
      let result;
      try {
        result = await this.executeOCRScript(args);
      } catch (scriptError) {
        // Save script execution error
        const errorFile = path.join(extractedDir, `${timestamp}_${imageBasename}_script_error.json`);
        fs.writeFileSync(errorFile, JSON.stringify({
          timestamp: new Date().toISOString(),
          imagePath: imagePath,
          error: scriptError.message,
          errorStack: scriptError.stack,
          step: 'script_execution_failed'
        }, null, 2));
        console.log(`Saved script error to: ${errorFile}`);
        throw scriptError;
      }

      // Save raw OCR output for debugging
      const rawOutputFile = path.join(extractedDir, `${timestamp}_${imageBasename}_raw.json`);

      fs.writeFileSync(rawOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        rawOutput: result,
        command: `${this.pythonPath} ${args.join(' ')}`
      }, null, 2));
      console.log(`Saved raw OCR output to: ${rawOutputFile}`);

      // Parse and validate result
      const ocrData = this.parseOCRResult(result);

      // Save parsed OCR data
      const parsedOutputFile = path.join(extractedDir, `${timestamp}_${imageBasename}_parsed.json`);
      fs.writeFileSync(parsedOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        parsedData: ocrData
      }, null, 2));
      console.log(`Saved parsed OCR data to: ${parsedOutputFile}`);

      // Extract cricket-specific data
      const cricketData = await this.extractCricketData(ocrData);

      // Save final processed cricket data
      const processedOutputFile = path.join(processedDir, `${timestamp}_${imageBasename}_cricket.json`);
      fs.writeFileSync(processedOutputFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        imagePath: imagePath,
        cricketData: cricketData,
        metadata: {
          processingSteps: ['raw_ocr', 'parsed_data', 'cricket_extraction'],
          success: cricketData.success,
          ocrStatus: cricketData.ocrStatus
        }
      }, null, 2));
      console.log(`Saved processed cricket data to: ${processedOutputFile}`);

      console.log('OCR processing completed successfully');
      return cricketData;

    } catch (error) {
      console.error('OCR processing failed:', error.message);
      console.error('OCR error stack:', error.stack);

      // Save error information to JSON file for debugging
      try {
        const errorFile = path.join(extractedDir, `${timestamp}_${imageBasename}_main_error.json`);
        fs.writeFileSync(errorFile, JSON.stringify({
          timestamp: new Date().toISOString(),
          imagePath: imagePath,
          error: error.message,
          errorStack: error.stack,
          step: 'main_processing_failed',
          errorType: error.constructor.name
        }, null, 2));
        console.log(`Saved main error to: ${errorFile}`);
      } catch (saveError) {
        console.error('Failed to save error file:', saveError.message);
      }

      // Return detailed error information for debugging
      return {
        success: false,
        error: error.message,
        errorStack: error.stack,
        errorLocation: 'OCRService.processImage',
        team1: 'Team 1',
        team2: 'Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        playerOfMatch: '',
        resultText: '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: '',
        ocrStatus: 'failed',
        ocrMessage: `OCR failed: ${error.message}. Please enter match details manually.`,
        debugInfo: {
          errorType: error.constructor.name,
          errorMessage: error.message,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Execute the OCR Python script
   * @param {Array} args - Command arguments
   * @returns {Promise<string>} - Script output
   */
  executeOCRScript(args) {
    return new Promise((resolve, reject) => {
      const process = spawn(this.pythonPath, args);

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        console.log('OCR script exited with code', code);

        // Check for JSON output first, regardless of exit code
        if (stdout.includes('===JSON_OUTPUT_START===') && stdout.includes('===JSON_OUTPUT_END===')) {
          console.log('Found JSON output in stdout, using it...');
          resolve(stdout);
        } else if (code === 0) {
          resolve(stdout);
        } else {
          // Check if we have extracted data in stderr even though exit code is 1
          console.log('Checking stderr for extracted data...');

          if (stderr.includes('Team names:') && stderr.includes('Scores:')) {
            console.log('Found extracted data in stderr, parsing...');
            try {
              // Parse the extracted data from stderr
              const extractedData = this.parseExtractedDataFromStderr(stderr);
              resolve(JSON.stringify(extractedData));
            } catch (parseError) {
              console.error('Failed to parse extracted data:', parseError);
              reject(new Error(`OCR script failed with code ${code}: ${stderr}`));
            }
          } else {
            reject(new Error(`OCR script failed with code ${code}: ${stderr}`));
          }
        }
      });

      process.on('error', (error) => {
        reject(new Error(`Failed to start OCR script: ${error.message}`));
      });

      // Set timeout
      setTimeout(() => {
        process.kill();
        reject(new Error('OCR script timeout'));
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Parse extracted data from stderr output
   * @param {string} stderr - Error output containing extracted data
   * @returns {Object} - Parsed cricket data
   */
  parseExtractedDataFromStderr(stderr) {
    try {
      console.log('Parsing extracted data from stderr...');

      // Extract team names
      const teamNamesMatch = stderr.match(/Team names: (.+)/);
      const teamNames = teamNamesMatch ? teamNamesMatch[1].split(',').map(t => t.trim()) : ['Team 1', 'Team 2'];

      // Extract scores
      const scoresMatch = stderr.match(/Scores: (.+)/);
      let team1Score = { runs: 0, wickets: 0, overs: 0 };
      let team2Score = { runs: 0, wickets: 0, overs: 0 };

      if (scoresMatch) {
        try {
          // Parse the Python dict format: {'runs': 0, 'wickets': 0, 'overs': 0}, {'runs': 0, 'wickets': 12, 'overs': 7.5}
          const scoresText = scoresMatch[1];
          const scoreMatches = scoresText.match(/\{[^}]+\}/g);

          if (scoreMatches && scoreMatches.length >= 2) {
            // Parse first team score
            const score1Match = scoreMatches[0].match(/'runs': (\d+), 'wickets': (\d+), 'overs': ([\d.]+)/);
            if (score1Match) {
              team1Score = {
                runs: parseInt(score1Match[1]),
                wickets: parseInt(score1Match[2]),
                overs: parseFloat(score1Match[3])
              };
            }

            // Parse second team score
            const score2Match = scoreMatches[1].match(/'runs': (\d+), 'wickets': (\d+), 'overs': ([\d.]+)/);
            if (score2Match) {
              team2Score = {
                runs: parseInt(score2Match[1]),
                wickets: parseInt(score2Match[2]),
                overs: parseFloat(score2Match[3])
              };
            }
          }
        } catch (scoreParseError) {
          console.error('Error parsing scores:', scoreParseError);
        }
      }

      // Extract result
      const resultMatch = stderr.match(/Result: (.+)/);
      const resultText = resultMatch ? resultMatch[1].trim() : '';

      // Extract venue
      const venueMatch = stderr.match(/Venue: (.+)/);
      const venue = venueMatch ? venueMatch[1].trim() : 'Unknown Venue';

      // Extract player of match from result text
      const pomMatch = resultText.match(/POM: (.+)/);
      const playerOfMatch = pomMatch ? pomMatch[1].trim() : '';

      // Extract player statistics from stderr
      const team1Batsmen = this.extractPlayersFromStderr(stderr, 'team1', 'batsmen');
      const team1Bowlers = this.extractPlayersFromStderr(stderr, 'team1', 'bowlers');
      const team2Batsmen = this.extractPlayersFromStderr(stderr, 'team2', 'batsmen');
      const team2Bowlers = this.extractPlayersFromStderr(stderr, 'team2', 'bowlers');

      const extractedData = {
        success: true,
        team1: teamNames[0] || 'Team 1',
        team2: teamNames[1] || 'Team 2',
        venue: venue,
        team1Score: team1Score,
        team2Score: team2Score,
        playerOfMatch: playerOfMatch,
        resultText: resultText,
        team1Batsmen: team1Batsmen,
        team1Bowlers: team1Bowlers,
        team2Batsmen: team2Batsmen,
        team2Bowlers: team2Bowlers,
        rawText: stderr,
        ocrStatus: 'success',
        ocrMessage: 'Successfully extracted cricket data using advanced parser.'
      };

      console.log('Parsed extracted data:', extractedData);
      return extractedData;

    } catch (error) {
      console.error('Error parsing extracted data from stderr:', error);
      throw error;
    }
  }

  /**
   * Extract player statistics from stderr debug output
   * @param {string} stderr - Error output containing player data
   * @param {string} team - Team identifier ('team1' or 'team2')
   * @param {string} type - Player type ('batsmen' or 'bowlers')
   * @returns {Array} - Array of player objects
   */
  extractPlayersFromStderr(stderr, team, type) {
    try {
      const players = [];

      // Look for team-specific extraction patterns in stderr
      const teamSectionPattern = team === 'team1'
        ? /Extracting (batsmen|bowlers) from section with \d+ items[\s\S]*?Extracted \d+ \1/g
        : /Extracting (batsmen|bowlers) from section with \d+ items[\s\S]*?Extracted \d+ \1/g;

      // Find all team sections
      const teamSections = stderr.match(teamSectionPattern) || [];

      // Filter sections by type (batsmen or bowlers)
      const relevantSections = teamSections.filter(section =>
        section.includes(`Extracting ${type}`)
      );

      // For team assignment, use section order:
      // First batsmen/bowlers section = team1
      // Second batsmen/bowlers section = team2
      const sectionIndex = team === 'team1' ? 0 : 1;
      const targetSection = relevantSections[sectionIndex];

      if (targetSection) {
        // Extract players from the specific team section
        const playerPattern = type === 'batsmen'
          ? /Added batsman: \{[^}]+\}/g
          : /Added bowler: \{[^}]+\}/g;

        const matches = targetSection.match(playerPattern);

        if (matches) {
          for (const match of matches) {
            try {
              // Extract the JSON-like object from the match
              const jsonMatch = match.match(/\{[^}]+\}/);
              if (jsonMatch) {
                // Parse the Python dict format
                const playerText = jsonMatch[0];

                // Extract player data using regex
                const nameMatch = playerText.match(/'name': '([^']+)'/);
                const runsMatch = playerText.match(/'runs': (\d+)/);
                const ballsMatch = playerText.match(/'balls': (\d+)/);
                const wicketsMatch = playerText.match(/'wickets': (\d+)/);
                const confidenceMatch = playerText.match(/'ocrConfidence': '([^']+)'/);

                if (nameMatch) {
                  const player = {
                    name: nameMatch[1],
                    ocrConfidence: confidenceMatch ? confidenceMatch[1] : 'medium'
                  };

                  if (type === 'batsmen') {
                    player.runs = runsMatch ? parseInt(runsMatch[1]) : 0;
                    player.balls = ballsMatch ? parseInt(ballsMatch[1]) : 0;
                  } else {
                    player.wickets = wicketsMatch ? parseInt(wicketsMatch[1]) : 0;
                    player.runs = runsMatch ? parseInt(runsMatch[1]) : 0;
                  }

                  players.push(player);
                }
              }
            } catch (parseError) {
              console.error(`Error parsing ${type} player:`, parseError);
            }
          }
        }
      }

      console.log(`Extracted ${players.length} ${team} ${type} from stderr (section ${sectionIndex})`);
      return players;

    } catch (error) {
      console.error(`Error extracting ${team} ${type} from stderr:`, error);
      return [];
    }
  }

  /**
   * Parse OCR script result
   * @param {string} result - Raw script output
   * @returns {Object} - Parsed OCR data
   */
  parseOCRResult(result) {
    try {
      console.log('Raw OCR script output:', result);

      // Check for JSON markers from cricket_scorecard_parser.py
      const jsonStartMarker = '===JSON_OUTPUT_START===';
      const jsonEndMarker = '===JSON_OUTPUT_END===';

      if (result.includes(jsonStartMarker) && result.includes(jsonEndMarker)) {
        // Extract JSON between the LAST occurrence of markers (in case of duplicates)
        const lastStartIndex = result.lastIndexOf(jsonStartMarker) + jsonStartMarker.length;
        const lastEndIndex = result.lastIndexOf(jsonEndMarker);
        const jsonString = result.substring(lastStartIndex, lastEndIndex).trim();

        console.log('Extracted JSON string:', jsonString);
        const ocrData = JSON.parse(jsonString);

        if (!ocrData.success) {
          throw new Error(ocrData.error || 'OCR processing failed');
        }

        return ocrData;
      } else {
        // Fallback: try to find any JSON in the output
        const jsonMatch = result.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No JSON found in OCR output');
        }

        const ocrData = JSON.parse(jsonMatch[0]);

        if (!ocrData.success) {
          throw new Error(ocrData.error || 'OCR processing failed');
        }

        return ocrData;
      }
    } catch (error) {
      console.error('Error parsing OCR result:', error);
      console.error('Raw result was:', result);
      throw new Error(`Failed to parse OCR result: ${error.message}`);
    }
  }

  /**
   * Extract cricket-specific data from OCR results
   * @param {Object} ocrData - Raw OCR data
   * @returns {Object} - Structured cricket data
   */
  async extractCricketData(ocrData) {
    try {
      console.log('Processing OCR data:', ocrData);
      console.log('OCR data type:', typeof ocrData);
      console.log('OCR data keys:', Object.keys(ocrData || {}));

      // Check if this is already processed cricket data from the advanced parser
      if (ocrData.team1 && ocrData.team2 && ocrData.team1Score && ocrData.team2Score) {
        console.log('Using advanced cricket parser data');

        // Enhance with bowling figure parsing
        const enhancedData = enhanceWithBowlingFigures(ocrData);

        // Extract all player names for matching
        const allPlayerNames = [
          ...(enhancedData.team1Batsmen || []).map(p => p.name),
          ...(enhancedData.team1Bowlers || []).map(p => p.name),
          ...(enhancedData.team2Batsmen || []).map(p => p.name),
          ...(enhancedData.team2Bowlers || []).map(p => p.name)
        ].filter(name => name && name.trim());

        // Match player names with database
        let playerMatches = null;
        try {
          if (allPlayerNames.length > 0) {
            console.log(`Matching ${allPlayerNames.length} player names with database (advanced parser)...`);
            const matchResult = await matchPlayerNames(allPlayerNames);
            if (matchResult.success) {
              playerMatches = matchResult.matches;
              console.log(`Successfully matched players (advanced). Exact matches: ${matchResult.matches.filter(m => m.matchType === 'exact').length}, Fuzzy matches: ${matchResult.matches.filter(m => m.matchType === 'fuzzy').length}, No matches: ${matchResult.matches.filter(m => m.matchType === 'none').length}`);
            } else {
              console.warn('Player matching failed (advanced):', matchResult.error);
            }
          }
        } catch (error) {
          console.error('Error during player matching (advanced):', error);
        }

        return {
          success: true,
          team1: enhancedData.team1,
          team2: enhancedData.team2,
          venue: enhancedData.venue || 'Unknown Venue',
          team1Score: enhancedData.team1Score,
          team2Score: enhancedData.team2Score,
          playerOfMatch: enhancedData.playerOfMatch || '',
          resultText: enhancedData.resultText || '',
          team1Batsmen: enhancedData.team1Batsmen || [],
          team1Bowlers: enhancedData.team1Bowlers || [],
          team2Batsmen: enhancedData.team2Batsmen || [],
          team2Bowlers: enhancedData.team2Bowlers || [],
          rawText: enhancedData.fullText || '',
          ocrStatus: 'success',
          ocrMessage: `Successfully extracted cricket data using advanced parser with bowling figure enhancement.`,
          // Include bowling figure parsing results
          bowlingFiguresParsed: {
            team1: enhancedData.team1_bowler_figures_parsed || [],
            team2: enhancedData.team2_bowler_figures_parsed || []
          },
          playerMatches // Add player matching results
        };
      }
    } catch (error) {
      console.error('Error in extractCricketData (advanced parser section):', error);
      throw error;
    }

    try {
      // Fallback to basic extraction for simple OCR data
      const { texts = [], fullText = '' } = ocrData;

      console.log(`Extracting cricket data from ${texts.length} text regions`);
      console.log('Texts array type:', Array.isArray(texts));
      console.log('First few texts:', texts.slice(0, 3));

      // Convert texts to string array if they are objects
      const textStrings = texts.map(item => {
        if (typeof item === 'string') {
          return item;
        } else if (typeof item === 'object' && item.text) {
          return item.text;
        } else {
          return String(item);
        }
      }).filter(text => text && text.length > 0);

      console.log(`Converted to ${textStrings.length} text strings for processing`);
      console.log('First few text strings:', textStrings.slice(0, 3));

      // Basic cricket data extraction
      const cricketData = {
        success: true,
        team1: this.extractTeamName(textStrings, 0) || 'Team 1',
        team2: this.extractTeamName(textStrings, 1) || 'Team 2',
        venue: this.extractVenue(textStrings) || 'Unknown Venue',
        team1Score: this.extractScore(textStrings, 0),
        team2Score: this.extractScore(textStrings, 1),
        playerOfMatch: this.extractPlayerOfMatch(textStrings) || '',
        resultText: this.extractResult(textStrings) || '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: fullText,
        ocrStatus: 'success',
        ocrMessage: `Successfully extracted ${textStrings.length} text regions from scorecard.`
      };

      console.log('Cricket data extraction completed:', {
        team1: cricketData.team1,
        team2: cricketData.team2,
        venue: cricketData.venue,
        textRegions: textStrings.length
      });

      return cricketData;
    } catch (error) {
      console.error('Error in extractCricketData (basic extraction section):', error);
      console.error('Error stack:', error.stack);

      // Create a detailed error with context
      const detailedError = new Error(`extractCricketData failed: ${error.message}`);
      detailedError.originalError = error;
      detailedError.location = 'extractCricketData.basicExtraction';
      detailedError.ocrDataKeys = Object.keys(ocrData || {});
      detailedError.textsLength = (ocrData?.texts || []).length;

      throw detailedError;
    }
  }

  /**
   * Extract team name from texts
   * @param {Array} texts - Array of text strings
   * @param {number} index - Team index (0 or 1)
   * @returns {string} - Team name
   */
  extractTeamName(texts, index) {
    try {
      // Ensure texts is an array
      if (!Array.isArray(texts)) {
        console.log('extractTeamName: texts is not an array:', typeof texts);
        return null;
      }

      // Look for team names (usually in caps)
      const teamNames = texts.filter(text => {
        // Ensure text is a string and not null/undefined
        if (typeof text !== 'string' || !text) {
          console.log('extractTeamName: skipping non-string text:', typeof text, text);
          return false;
        }

        try {
          return text.length > 2 &&
            text === text.toUpperCase() &&
            !text.match(/^\d+$/) &&
            !text.includes('OVER') &&
            !text.includes('WICKET');
        } catch (err) {
          console.error('extractTeamName: error processing text:', text, err);
          return false;
        }
      });

      return teamNames[index] || null;
    } catch (error) {
      console.error('extractTeamName: unexpected error:', error);
      return null;
    }
  }

  /**
   * Extract venue from texts
   * @param {Array} texts - Array of text strings
   * @returns {string} - Venue name
   */
  extractVenue(texts) {
    try {
      // Ensure texts is an array
      if (!Array.isArray(texts)) {
        console.log('extractVenue: texts is not an array:', typeof texts);
        return null;
      }

      // Look for venue keywords
      const venueKeywords = ['GROUND', 'STADIUM', 'OVAL', 'PARK', 'FIELD'];

      for (const text of texts) {
        // Ensure text is a string and not null/undefined
        if (typeof text !== 'string' || !text) {
          console.log('extractVenue: skipping non-string text:', typeof text, text);
          continue;
        }

        try {
          if (venueKeywords.some(keyword => text.toUpperCase().includes(keyword))) {
            return text;
          }
        } catch (err) {
          console.error('extractVenue: error processing text:', text, err);
          continue;
        }
      }

      return null;
    } catch (error) {
      console.error('extractVenue: unexpected error:', error);
      return null;
    }
  }

  /**
   * Extract score from texts
   * @param {Array} texts - Array of text strings
   * @param {number} index - Team index (0 or 1)
   * @returns {Object} - Score object
   */
  extractScore(texts, index) {
    // Look for score patterns like "150/8" or "150-8"
    const scorePattern = /(\d+)[\/\-](\d+)/;
    const scores = [];

    for (const text of texts) {
      // Ensure text is a string
      if (typeof text !== 'string') continue;

      const match = text.match(scorePattern);
      if (match) {
        scores.push({
          runs: parseInt(match[1]),
          wickets: parseInt(match[2]),
          overs: 0
        });
      }
    }

    return scores[index] || { runs: 0, wickets: 0, overs: 0 };
  }

  /**
   * Extract player of the match
   * @param {Array} texts - Array of text strings
   * @returns {string} - Player name
   */
  extractPlayerOfMatch(texts) {
    try {
      // Ensure texts is an array
      if (!Array.isArray(texts)) {
        console.log('extractPlayerOfMatch: texts is not an array:', typeof texts);
        return null;
      }

      // Look for player of match indicators
      const pomKeywords = ['PLAYER OF THE MATCH', 'MAN OF THE MATCH'];

      for (let i = 0; i < texts.length; i++) {
        // Ensure text is a string and not null/undefined
        if (typeof texts[i] !== 'string' || !texts[i]) {
          console.log('extractPlayerOfMatch: skipping non-string text:', typeof texts[i], texts[i]);
          continue;
        }

        try {
          const text = texts[i].toUpperCase();
          if (pomKeywords.some(keyword => text.includes(keyword))) {
            // Return next text as player name (ensure it's also a string)
            const nextText = texts[i + 1];
            return (typeof nextText === 'string') ? nextText : null;
          }
        } catch (err) {
          console.error('extractPlayerOfMatch: error processing text:', texts[i], err);
          continue;
        }
      }

      return null;
    } catch (error) {
      console.error('extractPlayerOfMatch: unexpected error:', error);
      return null;
    }
  }

  /**
   * Extract match result
   * @param {Array} texts - Array of text strings
   * @returns {string} - Result text
   */
  extractResult(texts) {
    try {
      // Ensure texts is an array
      if (!Array.isArray(texts)) {
        console.log('extractResult: texts is not an array:', typeof texts);
        return null;
      }

      // Look for result keywords
      const resultKeywords = ['WON BY', 'VICTORY', 'DEFEATED'];

      for (const text of texts) {
        // Ensure text is a string and not null/undefined
        if (typeof text !== 'string' || !text) {
          console.log('extractResult: skipping non-string text:', typeof text, text);
          continue;
        }

        try {
          const upperText = text.toUpperCase();
          if (resultKeywords.some(keyword => upperText.includes(keyword))) {
            return text;
          }
        } catch (err) {
          console.error('extractResult: error processing text:', text, err);
          continue;
        }
      }

      return null;
    } catch (error) {
      console.error('extractResult: unexpected error:', error);
      return null;
    }
  }

  /**
   * Parse OCR.Space API response
   * @param {Object} response - OCR.Space API response
   * @returns {Object} - Parsed text data
   */
  parseOCRSpaceResponse(response) {
    try {
      console.log('Parsing OCR.Space response...');

      if (!response || response.OCRExitCode !== 1) {
        throw new Error(`OCR.Space API error: ${response?.ErrorMessage || 'Unknown error'}`);
      }

      if (!response.ParsedResults || response.ParsedResults.length === 0) {
        throw new Error('No text found in image');
      }

      // Extract text from the first parsed result
      const parsedResult = response.ParsedResults[0];
      const fullText = parsedResult.ParsedText || '';

      // Extract coordinate data if overlay is available
      const textOverlay = parsedResult.TextOverlay;
      const hasOverlay = textOverlay && textOverlay.HasOverlay;

      console.log(`OCR.Space extracted ${fullText.length} characters of text`);
      console.log(`Coordinate overlay available: ${hasOverlay}`);

      return {
        success: true,
        fullText: fullText,
        textOverlay: textOverlay,
        hasOverlay: hasOverlay,
        confidence: hasOverlay ? 'high' : 'medium',
        engine: 'OCR.Space'
      };

    } catch (error) {
      console.error('Error parsing OCR.Space response:', error);
      throw error;
    }
  }

  /**
   * Extract cricket data from raw text using improved universal parsing
   * @param {string} fullText - Full extracted text
   * @returns {Object} - Cricket match data
   */
  async extractCricketDataFromText(fullText) {
    try {
      console.log('Extracting cricket data from OCR.Space text using improved parsing...');

      // Clean and prepare text
      const cleanText = this.cleanExtractedText(fullText);
      const lines = cleanText.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      console.log(`Processing ${lines.length} clean text lines`);

      // Extract basic match information using universal patterns
      const venue = this.extractVenueUniversal(cleanText);
      const playerOfMatch = this.extractPlayerOfMatchUniversal(cleanText);
      const overs = this.extractOversUniversal(cleanText);
      const scores = this.extractScoresUniversal(cleanText);

      // Use universal Team 1/Team 2 labels (no team name detection needed)
      const teamNames = { team1: "Team 1", team2: "Team 2" };

      // Extract player statistics
      const playerStats = await this.extractPlayerStatsUniversal(lines, teamNames);

      // Build cricket data object
      const cricketData = {
        success: true,
        team1: teamNames.team1,
        team2: teamNames.team2,
        venue: venue,
        team1Score: {
          runs: scores.team1Runs,
          wickets: scores.team1Wickets,
          overs: overs.team1Overs
        },
        team2Score: {
          runs: scores.team2Runs,
          wickets: scores.team2Wickets,
          overs: overs.team2Overs
        },
        playerOfMatch: playerOfMatch,
        resultText: '', // We don't use result text as per user requirement
        team1Batsmen: playerStats.team1Batsmen,
        team1Bowlers: playerStats.team1Bowlers,
        team2Batsmen: playerStats.team2Batsmen,
        team2Bowlers: playerStats.team2Bowlers,
        rawText: fullText,
        ocrStatus: 'success',
        ocrMessage: `Successfully extracted cricket data using improved OCR.Space parsing. Found ${lines.length} text lines.`,
        extractionMethod: 'OCR.Space API - Universal Parser',
        confidence: this.calculateExtractionConfidence(teamNames, venue, scores, playerStats)
      };

      console.log('OCR.Space improved cricket data extraction completed:', {
        team1: cricketData.team1,
        team2: cricketData.team2,
        venue: cricketData.venue,
        team1Score: cricketData.team1Score,
        team2Score: cricketData.team2Score,
        playerOfMatch: cricketData.playerOfMatch,
        confidence: cricketData.confidence,
        linesProcessed: lines.length
      });

      return cricketData;

    } catch (error) {
      console.error('Error extracting cricket data from text:', error);
      return {
        success: false,
        error: error.message,
        team1: 'Team 1',
        team2: 'Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        playerOfMatch: '',
        resultText: '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: fullText || '',
        ocrStatus: 'failed',
        ocrMessage: `Failed to extract cricket data: ${error.message}`,
        extractionMethod: 'OCR.Space API - Universal Parser',
        confidence: 'low'
      };
    }
  }

  /**
   * Extract cricket data using coordinate-based analysis
   * @param {string} fullText - Full extracted text
   * @param {Object} textOverlay - OCR.Space text overlay with coordinates
   * @returns {Object} - Cricket match data
   */
  async extractCricketDataFromCoordinates(fullText, textOverlay) {
    try {
      console.log('Starting coordinate-based cricket data extraction...');

      // Parse text elements with coordinates
      const textElements = this.parseTextElements(textOverlay);
      console.log(`Parsed ${textElements.length} text elements with coordinates`);

      // Extract basic match information using universal patterns (same as before)
      const venue = this.extractVenueUniversal(fullText);
      const playerOfMatch = this.extractPlayerOfMatchUniversal(fullText);
      const overs = this.extractOversUniversal(fullText);

      // Use coordinate-based score extraction for position accuracy
      const scores = this.extractScoresFromCoordinates(textElements);

      // Extract actual team names using coordinate analysis
      const teamNames = this.extractTeamNamesFromCoordinates(textElements);

      // Extract player statistics using spatial analysis
      const playerStats = this.extractPlayerStatsFromCoordinates(textElements, teamNames);

      // Build cricket data object
      const cricketData = {
        success: true,
        team1: teamNames.team1,
        team2: teamNames.team2,
        venue: venue,
        team1Score: {
          runs: scores.team1Runs,
          wickets: scores.team1Wickets,
          overs: overs.team1Overs
        },
        team2Score: {
          runs: scores.team2Runs,
          wickets: scores.team2Wickets,
          overs: overs.team2Overs
        },
        playerOfMatch: playerOfMatch,
        resultText: '', // We don't use result text as per user requirement
        team1Batsmen: playerStats.team1Batsmen,
        team1Bowlers: playerStats.team1Bowlers,
        team2Batsmen: playerStats.team2Batsmen,
        team2Bowlers: playerStats.team2Bowlers,
        rawText: fullText,
        ocrStatus: 'success',
        ocrMessage: `Successfully extracted cricket data using coordinate-based OCR.Space parsing. Found ${textElements.length} text elements.`,
        extractionMethod: 'OCR.Space API - Coordinate-Based Parser',
        confidence: this.calculateExtractionConfidence(teamNames, venue, scores, playerStats),
        coordinateData: {
          totalElements: textElements.length,
          hasOverlay: true
        }
      };

      console.log('Coordinate-based cricket data extraction completed:', {
        team1: cricketData.team1,
        team2: cricketData.team2,
        venue: cricketData.venue,
        team1Score: cricketData.team1Score,
        team2Score: cricketData.team2Score,
        playerOfMatch: cricketData.playerOfMatch,
        confidence: cricketData.confidence,
        elementsProcessed: textElements.length
      });

      return cricketData;

    } catch (error) {
      console.error('Error in coordinate-based extraction, falling back to text-based:', error);
      // Fallback to text-based extraction
      return await this.extractCricketDataFromText(fullText);
    }
  }

  /**
   * Extract cricket data using PaddleOCR coordinate data
   * @param {string} fullText - Full extracted text
   * @param {Array} textElements - PaddleOCR text elements with coordinates
   * @returns {Object} - Cricket match data
   */
  async extractCricketDataFromPaddleCoordinates(fullText, textElements) {
    try {
      console.log('Starting PaddleOCR coordinate-based cricket data extraction...');

      // Filter out any empty or low-confidence text elements
      const filteredElements = textElements.filter(element => {
        // Skip elements with empty text
        if (!element.text || element.text.trim() === '') {
          return false;
        }
        
        // Skip elements with very low confidence (likely noise)
        if (element.confidence && element.confidence < 0.2) {
          return false;
        }
        
        // Skip single character elements that are likely noise
        if (element.text.length === 1 && 
            !element.text.match(/[0-9WwXxOoBb]/) && 
            element.confidence < 0.5) {
          return false;
        }
        
        return true;
      });
      
      console.log(`Filtered ${textElements.length - filteredElements.length} low-quality elements`);

      // Convert PaddleOCR format to OCR.Space-compatible format
      const convertedElements = filteredElements.map(element => ({
        text: element.text,
        x: element.x,
        y: element.y,
        width: element.width,
        height: element.height,
        lineIndex: 0,
        wordIndex: 0,
        confidence: element.confidence
      }));

      console.log(`Converted ${convertedElements.length} PaddleOCR elements to coordinate format`);

      // Extract basic match information using universal patterns (same as before)
      const venue = this.extractVenueUniversal(fullText);
      const playerOfMatch = this.extractPlayerOfMatchUniversal(fullText);
      const overs = this.extractOversUniversal(fullText);

      // Use coordinate-based score extraction for position accuracy
      const scores = this.extractScoresFromCoordinates(convertedElements);

      // Extract actual team names using coordinate analysis
      const teamNames = this.extractTeamNamesFromCoordinates(convertedElements);

      // Extract player statistics using spatial analysis
      const playerStats = this.extractPlayerStatsFromCoordinates(convertedElements, teamNames);

      // Build cricket data object
      const cricketData = {
        success: true,
        team1: teamNames.team1,
        team2: teamNames.team2,
        venue: venue,
        team1Score: {
          runs: scores.team1Runs,
          wickets: scores.team1Wickets,
          overs: overs.team1Overs
        },
        team2Score: {
          runs: scores.team2Runs,
          wickets: scores.team2Wickets,
          overs: overs.team2Overs
        },
        playerOfMatch: playerOfMatch,
        resultText: '', // We don't use result text as per user requirement
        team1Batsmen: playerStats.team1Batsmen,
        team1Bowlers: playerStats.team1Bowlers,
        team2Batsmen: playerStats.team2Batsmen,
        team2Bowlers: playerStats.team2Bowlers,
        rawText: fullText,
        ocrStatus: 'success',
        ocrMessage: `Successfully extracted cricket data using PaddleOCR coordinate-based parsing. Found ${convertedElements.length} text elements.`,
        extractionMethod: 'PaddleOCR - Coordinate-Based Parser',
        confidence: this.calculateExtractionConfidence(teamNames, venue, scores, playerStats),
        coordinateData: {
          totalElements: convertedElements.length,
          hasOverlay: true,
          engine: 'PaddleOCR'
        }
      };

      console.log('PaddleOCR coordinate-based cricket data extraction completed:', {
        team1: cricketData.team1,
        team2: cricketData.team2,
        venue: cricketData.venue,
        team1Score: cricketData.team1Score,
        team2Score: cricketData.team2Score,
        playerOfMatch: cricketData.playerOfMatch,
        confidence: cricketData.confidence,
        elementsProcessed: convertedElements.length
      });

      return cricketData;

    } catch (error) {
      console.error('Error in PaddleOCR coordinate-based extraction, falling back to text-based:', error);
      // Fallback to text-based extraction
      return await this.extractCricketDataFromText(fullText);
    }
  }

  /**
   * Parse text elements from OCR.Space text overlay
   * @param {Object} textOverlay - OCR.Space text overlay
   * @returns {Array} - Array of text elements with coordinates
   */
  parseTextElements(textOverlay) {
    try {
      const textElements = [];

      if (!textOverlay || !textOverlay.Lines) {
        console.log('No text overlay lines found');
        return textElements;
      }

      textOverlay.Lines.forEach((line, lineIndex) => {
        // Add line-level elements (for patterns like "(40)", "3-21")
        if (line.LineText && line.LineText.trim()) {
          // Use first word's coordinates for line-level elements since MinLeft/MinTop aren't available
          const firstWord = line.Words && line.Words[0];
          const x = firstWord ? firstWord.Left : 0;
          const y = firstWord ? firstWord.Top : (line.MinTop || 0);

          textElements.push({
            text: line.LineText.trim(),
            x: x,
            y: y,
            width: line.MaxWidth || 0,
            height: line.MaxHeight || 0,
            lineIndex: lineIndex,
            wordIndex: -1, // Indicates this is a line-level element
            isLineText: true
          });
        }

        // Add word-level elements
        if (line.Words && Array.isArray(line.Words)) {
          line.Words.forEach((word, wordIndex) => {
            textElements.push({
              text: word.WordText || '',
              x: word.Left || 0,
              y: word.Top || 0,
              width: word.Width || 0,
              height: word.Height || 0,
              lineIndex: lineIndex,
              wordIndex: wordIndex,
              isLineText: false
            });
          });
        }
      });

      console.log(`Parsed ${textElements.length} text elements from overlay (including line-level elements)`);
      return textElements;

    } catch (error) {
      console.error('Error parsing text elements:', error);
      return [];
    }
  }

  /**
   * Extract team section labels using OVERS markers as boundaries
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {Object} - Team labels
   */
  extractTeamNamesFromCoordinates(textElements) {
    try {
      console.log('Using Team 1/Team 2 approach with OVERS markers as section boundaries...');

      // Find OVERS markers to identify team sections
      const oversElements = textElements.filter(el =>
        /OVERS/i.test(el.text) && /\d+\.?\d*/.test(el.text)
      );

      console.log(`Found ${oversElements.length} OVERS markers:`,
        oversElements.map(el => `"${el.text}" at (${el.x}, ${el.y})`));

      // Always return Team 1 and Team 2 - users will map actual teams in UI
      const team1 = "Team 1";
      const team2 = "Team 2";

      console.log(`Using section-based approach - Team 1: "${team1}", Team 2: "${team2}"`);
      console.log('Users will map actual team names in the Add Match Result form');

      return { team1, team2 };

    } catch (error) {
      console.error('Error in team section detection:', error);
      return { team1: "Team 1", team2: "Team 2" };
    }
  }

  /**
   * Clean extracted text by removing unnecessary elements
   * @param {string} text - Raw extracted text
   * @returns {string} - Cleaned text
   */
  cleanExtractedText(text) {
    try {
      // Remove unnecessary UI elements as per user requirements
      const unnecessaryTexts = [
        'PROCEED', 'BACK', 'PHOENT', 'VS', '=', 'CHANGE SCORECARD',
        'Player of the Match:', // We'll extract this separately
      ];

      let cleanText = text;

      // Remove unnecessary texts but keep the structure
      unnecessaryTexts.forEach(unnecessaryText => {
        const regex = new RegExp(`\\b${unnecessaryText}\\b`, 'gi');
        cleanText = cleanText.replace(regex, '');
      });

      // Clean up extra whitespace and empty lines
      cleanText = cleanText.replace(/\n\s*\n/g, '\n').trim();

      return cleanText;
    } catch (error) {
      console.error('Error cleaning extracted text:', error);
      return text; // Return original text if cleaning fails
    }
  }

  /**
   * Check if a text element is from result text that should be ignored
   * @param {string} elementText - Text element to check
   * @param {string} fullText - Full extracted text
   * @returns {boolean} - True if element is from result text
   */
  isFromResultText(elementText, fullText) {
    try {
      // Cricket result patterns to ignore
      const resultPatterns = [
        /\bWON BY \d+ RUNS?\b/gi,
        /\bWON BY \d+ WICKETS?\b/gi,
        /\bWON BY \d+ BALLS?\b/gi,
        /\bMATCH TIED\b/gi,
        /\bNO RESULT\b/gi,
        /\bABANDONED\b/gi,
        /\bDRAW\b/gi
      ];

      // Check if any result pattern exists in the full text
      for (const pattern of resultPatterns) {
        const matches = fullText.match(pattern);
        if (matches) {
          // Check if the element text is part of any result text match
          for (const match of matches) {
            if (match.includes(elementText)) {
              console.log(`Excluding result text element: "${elementText}" from "${match}"`);
              return true;
            }
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking result text:', error);
      return false;
    }
  }

  /**
   * Extract venue using universal pattern (text after "AT")
   * @param {string} text - Full text
   * @returns {string} - Venue name
   */
  extractVenueUniversal(text) {
    try {
      console.log('Starting priority-based venue extraction...');

      // Split text into lines for analysis
      const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      // PRIORITY 1: Look for "AT [VENUE]" pattern (handles cases like "T20 Day AT BLUNDSTONE ARENA")
      for (const line of lines) {
        const atMatch = line.match(/\bAT\s+([A-Z][A-Z\s]+(?:STADIUM|OVAL|ARENA|GROUND|PARK|CENTRE|CENTER|FIELD|GARDENS|CLUB))/i);
        if (atMatch) {
          const venue = atMatch[1].trim();
          console.log(`Found venue with AT pattern: "${venue}" from line: "${line}"`);
          return venue;
        }
      }

      // PRIORITY 1.5: Look for "AT [VENUE]" pattern with common venue names (more flexible)
      for (const line of lines) {
        const atMatch = line.match(/\bAT\s+(.+?)(?:\s|$)/i);
        if (atMatch) {
          const potentialVenue = atMatch[1].trim();
          // Check if it's a known venue or contains venue keywords
          const commonVenues = [
            'THE GABBA', 'MCG', 'SCG', 'WACA', 'LORDS', 'EDEN GARDENS', 'CHINNASWAMY',
            'CHEPAUK', 'FEROZ SHAH KOTLA', 'RAJIV GANDHI', 'BRABOURNE', 'WANKHEDE',
            'NEWLANDS', 'WANDERERS', 'KINGSMEAD', 'SUPERSPORT PARK', 'CENTURION',
            'TRENT BRIDGE', 'OLD TRAFFORD', 'HEADINGLEY', 'THE OVAL', 'EDGBASTON',
            'ADELAIDE OVAL', 'KIA OVAL', 'BLUNDSTONE ARENA'
          ];

          const hasVenueKeywords = /(?:STADIUM|OVAL|ARENA|GROUND|PARK|CENTRE|CENTER|FIELD|GARDENS|CLUB)/i.test(potentialVenue);
          const isCommonVenue = commonVenues.some(venue => potentialVenue.toUpperCase().includes(venue));

          if (hasVenueKeywords || isCommonVenue) {
            console.log(`Found venue with flexible AT pattern: "${potentialVenue}" from line: "${line}"`);
            return potentialVenue;
          }
        }
      }

      // PRIORITY 2: Standalone venue names (works for most scorecards)
      for (const line of lines) {
        // Look for lines that end with venue keywords
        if (/(?:STADIUM|OVAL|ARENA|GROUND|PARK|CENTRE|CENTER|FIELD|GARDENS|CLUB)$/i.test(line) &&
            !line.includes('ARMY') && // Exclude team names like "BHARAT ARMY"
            !line.includes('BHARAT') && // Exclude team names
            line.length > 3 &&
            line.length < 50) {
          console.log(`Found standalone venue: "${line}"`);
          return line;
        }
      }

      // PRIORITY 3: Fallback - look for any venue keywords (less strict)
      for (const line of lines) {
        if (/(?:STADIUM|OVAL|ARENA|GROUND|PARK|CENTRE|CENTER|FIELD|GARDENS|CLUB)/i.test(line) &&
            !line.includes('ARMY') && // Exclude team names
            line.length > 3 &&
            line.length < 50) {
          console.log(`Found fallback venue: "${line}"`);
          return line;
        }
      }

      // PRIORITY 4: Look for common cricket venue names (specific venues)
      const commonVenues = [
        'MCG', 'SCG', 'GABBA', 'WACA', 'LORDS', 'EDEN GARDENS', 'CHINNASWAMY',
        'CHEPAUK', 'FEROZ SHAH KOTLA', 'RAJIV GANDHI', 'BRABOURNE', 'WANKHEDE',
        'NEWLANDS', 'WANDERERS', 'KINGSMEAD', 'SUPERSPORT PARK', 'CENTURION',
        'TRENT BRIDGE', 'OLD TRAFFORD', 'HEADINGLEY', 'THE OVAL', 'EDGBASTON'
      ];

      for (const line of lines) {
        // First check if this line contains "AT" pattern - if so, extract only the venue part
        const atMatch = line.match(/\bAT\s+(.+)$/i);
        if (atMatch && commonVenues.some(venue => line.toUpperCase().includes(venue))) {
          const venueOnly = atMatch[1].trim();
          console.log(`Found common venue with AT pattern: "${venueOnly}" from line: "${line}"`);
          return venueOnly;
        }
        // Otherwise, check for standalone venue names
        else if (commonVenues.some(venue => line.toUpperCase().includes(venue))) {
          console.log(`Found standalone common venue: "${line}"`);
          return line;
        }
      }

      console.log('No venue found using any pattern');
      return 'Unknown Venue';
    } catch (error) {
      console.error('Error extracting venue:', error);
      return 'Unknown Venue';
    }
  }

  /**
   * Extract player of match using universal pattern
   * @param {string} text - Full text
   * @returns {string} - Player name
   */
  extractPlayerOfMatchUniversal(text) {
    try {
      // Pattern: Extract name after "Player of the Match:"
      const pomMatch = text.match(/Player of the Match:\s*(.+?)(?:\n|$)/i);
      if (pomMatch && pomMatch[1]) {
        const playerName = pomMatch[1].trim();
        console.log(`Extracted player of match: "${playerName}"`);
        return playerName;
      }

      console.log('No player of match found');
      return '';
    } catch (error) {
      console.error('Error extracting player of match:', error);
      return '';
    }
  }

  /**
   * Extract overs using universal pattern
   * @param {string} text - Full text
   * @returns {Object} - Overs data
   */
  extractOversUniversal(text) {
    try {
      // Pattern: Extract "OVERS: X.X" format
      const oversMatches = text.match(/OVERS:\s*(\d+\.?\d*)/g);

      if (oversMatches && oversMatches.length >= 2) {
        const team1Overs = parseFloat(oversMatches[0].match(/(\d+\.?\d*)/)[1]);
        const team2Overs = parseFloat(oversMatches[1].match(/(\d+\.?\d*)/)[1]);

        console.log(`Extracted overs - Team 1: ${team1Overs}, Team 2: ${team2Overs}`);
        return { team1Overs, team2Overs };
      } else if (oversMatches && oversMatches.length === 1) {
        const overs = parseFloat(oversMatches[0].match(/(\d+\.?\d*)/)[1]);
        console.log(`Extracted single overs value: ${overs}`);
        return { team1Overs: overs, team2Overs: 0 };
      }

      console.log('No overs found using OVERS pattern');
      return { team1Overs: 0, team2Overs: 0 };
    } catch (error) {
      console.error('Error extracting overs:', error);
      return { team1Overs: 0, team2Overs: 0 };
    }
  }

  /**
   * Extract scores using position-based detection with coordinate data
   * @param {string} text - Full text
   * @returns {Object} - Scores data
   */
  extractScoresUniversal(text) {
    try {
      console.log('Using position-based score extraction...');

      // This method will be called from coordinate-based extraction
      // For now, return basic extraction and let coordinate method handle position
      const allNumbers = text.match(/\b\d+\b/g) || [];
      const dashPatterns = text.match(/\b(\d+)-(\d+)\b/g) || [];

      console.log(`Found ${allNumbers.length} numbers and ${dashPatterns.length} dash patterns`);

      // Collect all potential team scores (both dash and standalone)
      const allPotentialScores = [];

      // Add dash patterns
      for (const pattern of dashPatterns) {
        const [runs, wickets] = pattern.split('-').map(Number);
        allPotentialScores.push({ runs, wickets, format: 'dash', pattern });
      }

      // Add standalone numbers (allout detection)
      for (const numStr of allNumbers) {
        const num = parseInt(numStr);
        if (num >= 50 && num <= 500) {
          const isPartOfDash = dashPatterns.some(dash => dash.includes(numStr));
          const isFromResult = this.isFromResultText(numStr, text);

          if (!isPartOfDash && !isFromResult) {
            allPotentialScores.push({ runs: num, wickets: 10, format: 'standalone', pattern: numStr });
            console.log(`Allout detected: ${num} runs (10 wickets lost)`);
          }
        }
      }

      // For text-based extraction, use simple assignment
      let team1Runs = 0, team1Wickets = 0, team2Runs = 0, team2Wickets = 0;

      if (allPotentialScores.length >= 2) {
        // Take first two scores found
        team1Runs = allPotentialScores[0].runs;
        team1Wickets = allPotentialScores[0].wickets;
        team2Runs = allPotentialScores[1].runs;
        team2Wickets = allPotentialScores[1].wickets;
      } else if (allPotentialScores.length === 1) {
        team1Runs = allPotentialScores[0].runs;
        team1Wickets = allPotentialScores[0].wickets;
      }

      console.log(`All potential scores: ${allPotentialScores.map(s => `${s.runs}-${s.wickets}`).join(', ')}`);
      console.log(`Text-based assignment - Team 1: ${team1Runs}-${team1Wickets}, Team 2: ${team2Runs}-${team2Wickets}`);

      return { team1Runs, team1Wickets, team2Runs, team2Wickets };

    } catch (error) {
      console.error('Error extracting scores:', error);
      return { team1Runs: 0, team1Wickets: 0, team2Runs: 0, team2Wickets: 0 };
    }
  }

  /**
   * Extract scores using coordinate-based position detection
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {Object} - Scores data
   */
  extractScoresFromCoordinates(textElements) {
    try {
      console.log('Extracting scores using coordinate-based position detection...');

      // Find all potential score elements with coordinates
      const scoreElements = [];

      // Look for ALL dash patterns (XXX-X format) - pure position-based detection
      textElements.forEach(element => {
        const dashMatch = element.text.match(/^(\d+)-(\d+)$/);
        if (dashMatch) {
          const runs = parseInt(dashMatch[1]);
          const wickets = parseInt(dashMatch[2]);

          // PURE POSITION-BASED: Add ALL dash patterns without any filtering
          // We'll rely entirely on Y-coordinate sorting for team assignment
          scoreElements.push({
            runs,
            wickets,
            format: 'dash',
            pattern: element.text,
            x: element.x,
            y: element.y,
            element
          });
          console.log(`Score element: ${runs}-${wickets} at (${element.x}, ${element.y})`);
        }
      });

      // Look for standalone numbers (allout scores) using relative coordinate detection
      const imageWidth = this.getImageWidth(textElements);
      const imageHeight = this.getImageHeight(textElements);

      console.log(`Image dimensions detected: ${imageWidth} x ${imageHeight}`);

      textElements.forEach(element => {
        const numMatch = element.text.match(/^(\d+)$/);
        if (numMatch) {
          const num = parseInt(numMatch[1]);

          // Check if this number is not part of any dash pattern
          const isPartOfDash = scoreElements.some(score =>
            score.pattern.includes(element.text)
          );

          if (!isPartOfDash) {
            // Use relative coordinate detection for team score areas
            if (this.isInTeamScoreArea(element, imageWidth, imageHeight)) {
              scoreElements.push({
                runs: num,
                wickets: 10, // Allout
                format: 'standalone',
                pattern: element.text,
                x: element.x,
                y: element.y,
                element
              });
              const xPercent = ((element.x / imageWidth) * 100).toFixed(1);
              const yPercent = ((element.y / imageHeight) * 100).toFixed(1);
              console.log(`Team score detected at (${element.x}, ${element.y}): ${num} runs [${xPercent}%, ${yPercent}%]`);
            } else {
              console.log(`Standalone number ${num} at (${element.x}, ${element.y}) not in team score area - skipped`);
            }
          }
        }
      });

      console.log(`Found ${scoreElements.length} score elements with coordinates`);
      scoreElements.forEach(score => {
        console.log(`Score: ${score.runs}-${score.wickets} at (${score.x}, ${score.y})`);
      });

      // Get OVERS markers to define team sections
      const overs = this.extractOversUniversal(textElements.map(el => el.text).join(' '));
      console.log(`OVERS data: Team 1: ${overs.team1Overs}, Team 2: ${overs.team2Overs}`);

      // Find OVERS markers coordinates for section boundaries
      const oversMarkers = textElements.filter(el =>
        el.text.includes('OVERS:') || el.text.match(/OVERS:\s*\d+\.\d+/)
      );

      console.log(`Found ${oversMarkers.length} OVERS markers:`);
      oversMarkers.forEach(marker => {
        console.log(`"${marker.text}" at (${marker.x}, ${marker.y})`);
      });

      let team1Runs = 0, team1Wickets = 0, team2Runs = 0, team2Wickets = 0;

      if (oversMarkers.length >= 2) {
        // Get smart boundaries that adapt to any image dimension
        const boundaries = this.getSmartBoundaries(oversMarkers, imageHeight);

        console.log(`Smart boundaries calculated:`);
        console.log(`Team 1 score section: Y 0-${boundaries.team1ScoreEnd}`);
        console.log(`Team 2 score section: Y ${boundaries.team2ScoreStart}-${imageHeight}`);

        // Find scores within each team section using smart boundaries
        const team1Scores = scoreElements.filter(score => score.y <= boundaries.team1ScoreEnd);
        const team2Scores = scoreElements.filter(score => score.y >= boundaries.team2ScoreStart);

        console.log(`Team 1 scores in section: ${team1Scores.map(s => `${s.runs}-${s.wickets}`).join(', ')}`);
        console.log(`Team 2 scores in section: ${team2Scores.map(s => `${s.runs}-${s.wickets}`).join(', ')}`);

        // Prioritize actual team scores over bowling figures in each section
        if (team1Scores.length > 0) {
          const team1TeamScore = this.selectBestTeamScore(team1Scores);
          team1Runs = team1TeamScore.runs;
          team1Wickets = team1TeamScore.wickets;
          console.log(`Team 1 score: ${team1Runs}-${team1Wickets} from section (${team1TeamScore.format})`);
        }

        if (team2Scores.length > 0) {
          const team2TeamScore = this.selectBestTeamScore(team2Scores);
          team2Runs = team2TeamScore.runs;
          team2Wickets = team2TeamScore.wickets;
          console.log(`Team 2 score: ${team2Runs}-${team2Wickets} from section (${team2TeamScore.format})`);
        }

      } else {
        // Fallback: Use global position-based assignment
        console.log('Fallback: Using global position-based assignment');
        scoreElements.sort((a, b) => a.y - b.y);

        if (scoreElements.length >= 2) {
          team1Runs = scoreElements[0].runs;
          team1Wickets = scoreElements[0].wickets;
          team2Runs = scoreElements[1].runs;
          team2Wickets = scoreElements[1].wickets;
        } else if (scoreElements.length === 1) {
          team1Runs = scoreElements[0].runs;
          team1Wickets = scoreElements[0].wickets;
        }
      }

      return { team1Runs, team1Wickets, team2Runs, team2Wickets };

    } catch (error) {
      console.error('Error extracting scores from coordinates:', error);
      return { team1Runs: 0, team1Wickets: 0, team2Runs: 0, team2Wickets: 0 };
    }
  }

  /**
   * Select the best team score from a list of scores in a section
   * Prioritizes standalone team scores over bowling figures
   * @param {Array} scores - Array of score objects
   * @returns {Object} - Best team score object
   */
  selectBestTeamScore(scores) {
    try {
      console.log(`Selecting best team score from: ${scores.map(s => `${s.runs}-${s.wickets} (${s.format})`).join(', ')}`);

      // Priority 1: Standalone team scores (allout scores)
      const standaloneScores = scores.filter(score => score.format === 'standalone');
      if (standaloneScores.length > 0) {
        console.log(`Found ${standaloneScores.length} standalone scores, selecting first: ${standaloneScores[0].runs}-${standaloneScores[0].wickets}`);
        return standaloneScores[0];
      }

      // Priority 2: Dash format scores that look like team scores (higher runs)
      const dashScores = scores.filter(score => score.format === 'dash');
      if (dashScores.length > 0) {
        // Sort by runs (descending) to get the highest score (more likely to be team total)
        const sortedDashScores = dashScores.sort((a, b) => b.runs - a.runs);
        console.log(`Found ${dashScores.length} dash scores, selecting highest: ${sortedDashScores[0].runs}-${sortedDashScores[0].wickets}`);
        return sortedDashScores[0];
      }

      // Fallback: Return first score
      console.log(`No standalone/dash scores found, returning first score: ${scores[0].runs}-${scores[0].wickets}`);
      return scores[0];

    } catch (error) {
      console.error('Error selecting best team score:', error);
      return scores[0] || { runs: 0, wickets: 0, format: 'unknown' };
    }
  }

  /**
   * Get smart boundaries that adapt to any image dimension using OVERS markers
   * @param {Array} oversMarkers - Array of OVERS marker elements with coordinates
   * @param {number} imageHeight - Image height in pixels
   * @returns {Object} - Boundary coordinates for different sections
   */
  getSmartBoundaries(oversMarkers, imageHeight) {
    try {
      if (oversMarkers.length < 2) {
        // Fallback boundaries if not enough OVERS markers
        return {
          team1ScoreEnd: imageHeight * 0.25,
          team2ScoreStart: imageHeight * 0.55,
          team1PlayerStart: imageHeight * 0.15,
          team1PlayerEnd: imageHeight * 0.55,
          team2PlayerStart: imageHeight * 0.55,
          team2PlayerEnd: imageHeight * 0.85
        };
      }

      const firstOversY = oversMarkers[0].y;
      const secondOversY = oversMarkers[1].y;

      // Calculate relative distances for adaptive boundaries
      const oversDistance = secondOversY - firstOversY;
      const bufferSize = Math.max(20, oversDistance * 0.08); // 8% of distance between OVERS, min 20px

      console.log(`OVERS markers at Y=${firstOversY}, Y=${secondOversY}, distance=${oversDistance}, buffer=${bufferSize.toFixed(1)}`);

      return {
        // Score boundaries (tight around OVERS markers)
        team1ScoreEnd: firstOversY + bufferSize,
        team2ScoreStart: secondOversY - bufferSize,

        // Player boundaries (wide between OVERS markers)
        team1PlayerStart: firstOversY,
        team1PlayerEnd: secondOversY,
        team2PlayerStart: secondOversY,
        team2PlayerEnd: imageHeight
      };

    } catch (error) {
      console.error('Error calculating smart boundaries:', error);
      // Fallback to percentage-based boundaries
      return {
        team1ScoreEnd: imageHeight * 0.25,
        team2ScoreStart: imageHeight * 0.55,
        team1PlayerStart: imageHeight * 0.15,
        team1PlayerEnd: imageHeight * 0.55,
        team2PlayerStart: imageHeight * 0.55,
        team2PlayerEnd: imageHeight * 0.85
      };
    }
  }

  /**
   * Get image width from text elements (estimate from max X coordinate)
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {number} - Estimated image width
   */
  getImageWidth(textElements) {
    try {
      const maxX = Math.max(...textElements.map(el => el.x + (el.width || 50)));
      // Add some padding to account for elements near the edge
      return Math.ceil(maxX * 1.05);
    } catch (error) {
      console.error('Error detecting image width:', error);
      return 2048; // Default fallback
    }
  }

  /**
   * Get image height from text elements (estimate from max Y coordinate)
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {number} - Estimated image height
   */
  getImageHeight(textElements) {
    try {
      const maxY = Math.max(...textElements.map(el => el.y + (el.height || 30)));
      // Add some padding to account for elements near the edge
      return Math.ceil(maxY * 1.05);
    } catch (error) {
      console.error('Error detecting image height:', error);
      return 1152; // Default fallback
    }
  }

  /**
   * Check if an element is in a team score area using relative coordinates
   * @param {Object} element - Text element with x, y coordinates
   * @param {number} imageWidth - Image width in pixels
   * @param {number} imageHeight - Image height in pixels
   * @returns {boolean} - True if element is in a team score area
   */
  isInTeamScoreArea(element, imageWidth, imageHeight) {
    try {
      // Convert element coordinates to percentages
      const xPercent = (element.x / imageWidth) * 100;
      const yPercent = (element.y / imageHeight) * 100;

      // Define team score areas as percentages (based on Big Ant Cricket layout analysis)
      const TEAM_SCORE_AREAS = [
        {
          name: "Team 1 Score Area",
          xMinPercent: 80,   // 80-98% from left (right side of scorecard)
          xMaxPercent: 98,
          yMinPercent: 5,    // 5-40% from top (upper section)
          yMaxPercent: 40
        },
        {
          name: "Team 2 Score Area",
          xMinPercent: 80,   // 80-98% from left (right side of scorecard)
          xMaxPercent: 98,
          yMinPercent: 35,   // 35-80% from top (lower section)
          yMaxPercent: 80
        }
      ];

      // Check if element is in any team score area
      const inArea = TEAM_SCORE_AREAS.some(area =>
        xPercent >= area.xMinPercent &&
        xPercent <= area.xMaxPercent &&
        yPercent >= area.yMinPercent &&
        yPercent <= area.yMaxPercent
      );

      if (inArea) {
        console.log(`Element "${element.text}" at ${xPercent.toFixed(1)}%, ${yPercent.toFixed(1)}% is in team score area`);
      }

      return inArea;

    } catch (error) {
      console.error('Error checking team score area:', error);
      return false;
    }
  }

  /**
   * Extract team names using universal position-based logic
   * @param {Array} lines - Array of text lines
   * @returns {Object} - Team names
   */
  extractTeamNamesUniversal(lines) {
    try {
      // Find all potential team names (uppercase, reasonable length, not numbers/scores)
      const potentialTeamNames = [];

      for (let i = 0; i < Math.min(20, lines.length); i++) {
        const line = lines[i].trim();

        if (this.isLikelyTeamName(line)) {
          potentialTeamNames.push({ name: line, position: i });
        }
      }

      // Remove duplicates while preserving order
      const uniqueTeamNames = [];
      const seen = new Set();

      for (const team of potentialTeamNames) {
        if (!seen.has(team.name.toUpperCase())) {
          seen.add(team.name.toUpperCase());
          uniqueTeamNames.push(team);
        }
      }

      // Use context-based selection for team names
      let team1 = 'Team 1';
      let team2 = 'Team 2';

      if (uniqueTeamNames.length >= 2) {
        // Use first two unique team names found in text order
        team1 = uniqueTeamNames[0].name;
        team2 = uniqueTeamNames[1].name;
      } else if (uniqueTeamNames.length === 1) {
        team1 = uniqueTeamNames[0].name;
      }

      console.log(`Extracted team names - Team 1: "${team1}", Team 2: "${team2}"`);
      console.log(`Found ${uniqueTeamNames.length} unique team names from ${potentialTeamNames.length} candidates`);

      return { team1, team2 };

    } catch (error) {
      console.error('Error extracting team names:', error);
      return { team1: 'Team 1', team2: 'Team 2' };
    }
  }

  /**
   * Check if a line is likely a team name using improved universal patterns
   * @param {string} line - Text line
   * @returns {boolean} - True if likely a team name
   */
  isLikelyTeamName(line) {
    if (!line || line.length < 4 || line.length > 25) return false;

    // Skip pure numbers
    if (/^\d+$/.test(line)) return false;

    // Skip numbers with asterisk (batting scores)
    if (/^\d+\*?$/.test(line)) return false;

    // Skip numbers in parentheses
    if (/^\(\d+\)$/.test(line)) return false;

    // Skip score patterns
    if (/^\d+-\d+$/.test(line)) return false;

    // Skip overs patterns
    if (/^OVERS:/i.test(line)) return false;

    // Skip time patterns
    if (/^\d+:\d+[AP]M$/i.test(line)) return false;

    // Skip known non-team keywords
    const nonTeamKeywords = [
      'PLAYER OF THE MATCH', 'WON BY', 'RUNS', 'WICKETS', 'OVERS',
      'PROCEED', 'BACK', 'VS', 'CHANGE SCORECARD', 'AT', 'COPY'
    ];

    if (nonTeamKeywords.some(keyword => line.toUpperCase().includes(keyword))) return false;

    // Must contain letters
    if (!/[A-Za-z]/.test(line)) return false;

    // Should look like a proper name (letters, spaces, maybe hyphens)
    if (!/^[A-Za-z\s\-]+$/.test(line)) return false;

    // Team names are typically all uppercase in scorecards
    const upperCaseRatio = (line.match(/[A-Z]/g) || []).length / line.replace(/\s/g, '').length;
    if (upperCaseRatio < 0.7) return false; // At least 70% uppercase letters

    // Team names often have multiple words or are longer single words
    const words = line.split(/\s+/);

    // Allow well-known team name patterns (like PHOENIX, EAGLES, etc.)
    // Single word team names should be at least 6 characters (reduced from 8 to include PHOENIX)
    if (words.length === 1 && line.length < 6) return false;

    return true;
  }

  /**
   * Check if a text is an obvious player name that should not be considered a team name
   * @param {string} text - Text to check
   * @returns {boolean} - True if it's obviously a player name
   */
  isObviousPlayerName(text) {
    if (!text) return false;

    // Common cricket player name patterns that are NOT team names
    const playerNamePatterns = [
      // Full player names (first + last)
      /^(YOUNIS KHAN|ALI AKBAR|NAVJOT SINGH SIDHU|KEITH MILLER|JOHN REID|VAIBHAV BANGA|BRENDON MCCULLUM|ALAN MELVILLE|JAMES FAULKNER|MATT POTTS|MITCHELL JOHNSON|NAVEEN-UL-HAQ MURID|USAMA MIR|TRENT BOULT)$/i,
      // First names that are clearly not team names
      /^(YOUNIS|NAVJOT|VAIBHAV|BRENDON|KEITH|ALAN|JAMES|MATT|MITCHELL|NAVEEN|USAMA|TRENT)$/i,
      // Last names that are clearly not team names
      /^(KHAN|AKBAR|SINGH|SIDHU|MILLER|BANGA|MCCULLUM|MELVILLE|FAULKNER|POTTS|REID|JOHNSON|MURID|BOULT)$/i,
      // Common cricket names
      /^(VIRAT|ROHIT|DHONI|KOHLI|SHARMA|PATEL|KUMAR|SINGH|YADAV|PANDYA)$/i
    ];

    // Check if text matches any player name pattern
    return playerNamePatterns.some(pattern => pattern.test(text));
  }

  /**
   * Extract player statistics using universal parsing
   * @param {Array} lines - Array of text lines
   * @param {Object} teamNames - Team names object
   * @returns {Object} - Player statistics
   */
  async extractPlayerStatsUniversal(lines, teamNames) {
    try {
      console.log('Extracting player statistics...');

      // Initialize player arrays
      const team1Batsmen = [];
      const team1Bowlers = [];
      const team2Batsmen = [];
      const team2Bowlers = [];

      // Find batting statistics (runs and balls)
      const battingStats = this.findBattingStats(lines);

      // Find bowling statistics (wickets-runs format)
      const bowlingStats = this.findBowlingStats(lines);

      console.log(`Found ${battingStats.length} batting stats, ${bowlingStats.length} bowling stats`);

      // Group players by teams using cricket scorecard structure
      const playerGroups = this.groupPlayersByTeams(lines, teamNames);

      // Assign batting statistics to players (flexible count 1-4)
      this.assignBattingStats(playerGroups.team1Batsmen, battingStats.slice(0, 4), team1Batsmen);
      this.assignBattingStats(playerGroups.team2Batsmen, battingStats.slice(4, 8), team2Batsmen);

      // Assign bowling statistics to players (dynamic count based on actual bowlers)
      const team2BowlingCount = Math.min(playerGroups.team2Bowlers.length, bowlingStats.length);
      const team1BowlingCount = Math.min(playerGroups.team1Bowlers.length, bowlingStats.length - team2BowlingCount);

      this.assignBowlingStats(playerGroups.team2Bowlers, bowlingStats.slice(0, team2BowlingCount), team2Bowlers);
      this.assignBowlingStats(playerGroups.team1Bowlers, bowlingStats.slice(team2BowlingCount, team2BowlingCount + team1BowlingCount), team1Bowlers);

      console.log(`Assigned stats - Team 1 Batsmen: ${team1Batsmen.length}, Team 1 Bowlers: ${team1Bowlers.length}, Team 2 Batsmen: ${team2Batsmen.length}, Team 2 Bowlers: ${team2Bowlers.length}`);

      // Extract all player names for matching
      const allPlayerNames = [
        ...team1Batsmen.map(p => p.name),
        ...team1Bowlers.map(p => p.name),
        ...team2Batsmen.map(p => p.name),
        ...team2Bowlers.map(p => p.name)
      ].filter(name => name && name.trim());

      // Match player names with database
      let playerMatches = null;
      try {
        console.log(`Matching ${allPlayerNames.length} player names with database...`);
        const matchResult = await matchPlayerNames(allPlayerNames);
        if (matchResult.success) {
          playerMatches = matchResult.matches;
          console.log(`Successfully matched players. Exact matches: ${matchResult.matches.filter(m => m.matchType === 'exact').length}, Fuzzy matches: ${matchResult.matches.filter(m => m.matchType === 'fuzzy').length}, No matches: ${matchResult.matches.filter(m => m.matchType === 'none').length}`);
        } else {
          console.warn('Player matching failed:', matchResult.error);
        }
      } catch (error) {
        console.error('Error during player matching:', error);
      }

      return {
        team1Batsmen,
        team1Bowlers,
        team2Batsmen,
        team2Bowlers,
        playerMatches // Add player matching results
      };

    } catch (error) {
      console.error('Error extracting player statistics:', error);
      return {
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: []
      };
    }
  }

  /**
   * Find player names in text lines with improved filtering
   * @param {Array} lines - Array of text lines
   * @param {Object} teamNames - Team names to exclude
   * @returns {Array} - Array of player names
   */
  findPlayerNames(lines, teamNames = {}) {
    try {
      const playerNames = [];
      const excludeNames = new Set();

      // Add team names to exclusion list
      if (teamNames.team1) excludeNames.add(teamNames.team1.toUpperCase());
      if (teamNames.team2) excludeNames.add(teamNames.team2.toUpperCase());

      for (const line of lines) {
        const trimmedLine = line.trim();

        // Check if line looks like a player name and is not a team name
        if (this.isPlayerName(trimmedLine) && !excludeNames.has(trimmedLine.toUpperCase())) {
          playerNames.push(trimmedLine);
        }
      }

      console.log(`Found ${playerNames.length} potential player names (excluding team names)`);
      console.log(`Player names: ${playerNames.join(', ')}`);
      return playerNames;

    } catch (error) {
      console.error('Error finding player names:', error);
      return [];
    }
  }

  /**
   * Check if a line is a player name using improved universal patterns
   * @param {string} line - Text line
   * @returns {boolean} - True if likely a player name
   */
  isPlayerName(line) {
    const trimmedLine = line.trim();

    // Debug code removed for cleaner output

    // Skip empty lines
    if (!trimmedLine) return false;

    // Skip pure numbers
    if (/^\d+$/.test(trimmedLine)) return false;

    // Skip numbers with asterisk (batting scores)
    if (/^\d+\*?$/.test(trimmedLine)) return false;

    // Skip numbers in parentheses (balls faced)
    if (/^\(\d+\)$/.test(trimmedLine)) return false;

    // Skip score patterns (bowling figures or team scores)
    if (/^\d+-\d+$/.test(trimmedLine)) return false;

    // Skip overs patterns
    if (/^OVERS:/i.test(trimmedLine)) return false;

    // Skip time patterns
    if (/^\d+:\d+[AP]M$/i.test(trimmedLine)) return false;

    // Skip known non-player keywords (no hardcoded team names)
    const nonPlayerKeywords = [
      'PLAYER OF THE MATCH', 'WON BY', 'RUNS', 'WICKETS', 'OVERS',
      'PROCEED', 'BACK', 'VS', 'CHANGE SCORECARD', 'COPY'
    ];

    // Check for exact matches or whole word matches
    const upperLine = trimmedLine.toUpperCase();
    const containsKeyword = nonPlayerKeywords.some(keyword => {
      // Exact match
      if (upperLine === keyword) return true;
      // Whole word match (surrounded by word boundaries)
      const regex = new RegExp(`\\b${keyword}\\b`);
      return regex.test(upperLine);
    });

    if (containsKeyword) return false;

    // Must contain letters
    if (!/[A-Za-z]/.test(trimmedLine)) return false;

    // Reasonable length for a player name
    if (trimmedLine.length < 4 || trimmedLine.length > 35) return false;

    // Player names should have multiple words or be reasonably long single names
    const words = trimmedLine.split(/\s+/);
    if (words.length === 1 && trimmedLine.length < 6) return false;

    // Should look like a proper name (letters, spaces, hyphens, dots)
    if (!/^[A-Za-z\s\-\.]+$/.test(trimmedLine)) return false;

    // Should have reasonable uppercase ratio (names are often in caps in scorecards)
    const upperCaseRatio = (trimmedLine.match(/[A-Z]/g) || []).length / trimmedLine.replace(/\s/g, '').length;
    if (upperCaseRatio < 0.3) return false; // At least 30% uppercase

    return true;
  }

  /**
   * Find batting statistics in text lines
   * @param {Array} lines - Array of text lines
   * @returns {Array} - Array of batting stats
   */
  findBattingStats(lines) {
    try {
      const battingStats = [];
      const runs = [];
      const balls = [];

      // Find runs (numbers, possibly with *)
      for (const line of lines) {
        const runsMatch = line.match(/^(\d+\*?)$/);
        if (runsMatch) {
          runs.push(runsMatch[1]);
        }
      }

      // Find balls (numbers in parentheses)
      for (const line of lines) {
        const ballsMatch = line.match(/^\((\d+)\)$/);
        if (ballsMatch) {
          balls.push(parseInt(ballsMatch[1]));
        }
      }

      // Combine runs and balls
      const minLength = Math.min(runs.length, balls.length);
      for (let i = 0; i < minLength; i++) {
        battingStats.push({
          runs: runs[i],
          balls: balls[i]
        });
      }

      console.log(`Found batting stats: ${battingStats.map(s => `${s.runs}(${s.balls})`).join(', ')}`);
      return battingStats;

    } catch (error) {
      console.error('Error finding batting stats:', error);
      return [];
    }
  }

  /**
   * Find bowling statistics in text lines with improved filtering
   * @param {Array} lines - Array of text lines
   * @returns {Array} - Array of bowling stats
   */
  findBowlingStats(lines) {
    try {
      const bowlingStats = [];

      for (const line of lines) {
        // Look for bowling figures pattern (WICKETS-RUNS)
        // Wickets: 0-9 (single digit), Runs: 0-999+ (any number)
        const bowlingMatch = line.match(/^([0-9])-(\d+)$/);
        if (bowlingMatch) {
          const wickets = parseInt(bowlingMatch[1]);
          const runs = parseInt(bowlingMatch[2]);

          // Universal bowling figures criteria:
          // - Wickets: 0-9 (single digit as per cricket rules)
          // - Runs: 0-999+ (any number, can be high in bad bowling performances)
          // - Exclude obvious team scores by checking wickets range
          if (wickets >= 0 && wickets <= 9) {
            bowlingStats.push({
              wickets: wickets,
              runs: runs,
              figure: `${wickets}-${runs}`
            });
          } else {
            // Wickets > 9 is not possible for individual bowler, likely team score
            console.log(`Skipping invalid bowling figure (wickets > 9): ${wickets}-${runs}`);
          }
        }
      }

      console.log(`Found ${bowlingStats.length} bowling figures: ${bowlingStats.map(s => s.figure).join(', ')}`);
      return bowlingStats;

    } catch (error) {
      console.error('Error finding bowling stats:', error);
      return [];
    }
  }

  /**
   * Group players by teams using cricket scorecard structure
   * @param {Array} lines - Array of text lines
   * @param {Object} teamNames - Team names object
   * @returns {Object} - Grouped players with batting and bowling sections
   */
  groupPlayersByTeams(lines, teamNames) {
    try {
      console.log('Grouping players using cricket scorecard structure...');

      // Find structural markers in the scorecard (no team names needed)
      const structure = this.findScorecardStructure(lines);

      // Extract players by sections using flexible counts (1-4 players max)
      const team1Batsmen = this.extractPlayersInSection(lines, structure.team1BatsmenStart, structure.team1BatsmenEnd, 4);
      const team2Batsmen = this.extractPlayersInSectionWithDebug(lines, structure.team2BatsmenStart, structure.team2BatsmenEnd, 4, "Team 2 Batsmen");
      const team2Bowlers = this.extractPlayersInSection(lines, structure.team2BowlersStart, structure.team2BowlersEnd, 4);
      const team1Bowlers = this.extractPlayersInSection(lines, structure.team1BowlersStart, structure.team1BowlersEnd, 4);

      console.log(`Extracted players by structure:`);
      console.log(`Team 1 Batsmen (${team1Batsmen.length}): ${team1Batsmen.join(', ')}`);
      console.log(`Team 2 Batsmen (${team2Batsmen.length}): ${team2Batsmen.join(', ')}`);
      console.log(`Team 2 Bowlers (${team2Bowlers.length}): ${team2Bowlers.join(', ')}`);
      console.log(`Team 1 Bowlers (${team1Bowlers.length}): ${team1Bowlers.join(', ')}`);

      return {
        team1Batsmen,
        team2Batsmen,
        team2Bowlers,
        team1Bowlers
      };

    } catch (error) {
      console.error('Error grouping players by teams:', error);
      return {
        team1Batsmen: [],
        team2Batsmen: [],
        team2Bowlers: [],
        team1Bowlers: []
      };
    }
  }

  /**
   * Find cricket scorecard structure using universal position-based logic
   * @param {Array} lines - Array of text lines
   * @returns {Object} - Structure boundaries
   */
  findScorecardStructure(lines) {
    try {
      const structure = {
        team1BatsmenStart: -1,
        team1BatsmenEnd: -1,
        team2BatsmenStart: -1,
        team2BatsmenEnd: -1,
        team2BowlersStart: -1,
        team2BowlersEnd: -1,
        team1BowlersStart: -1,
        team1BowlersEnd: -1
      };

      // Find OVERS markers (key structural elements)
      const oversPositions = this.findOversMarkers(lines);

      // Find potential team name positions (any uppercase text that could be team names)
      const potentialTeamPositions = this.findPotentialTeamPositions(lines);

      console.log(`Structure markers - Potential teams: [${potentialTeamPositions.join(', ')}], Overs: [${oversPositions.join(', ')}]`);

      // Use cricket scorecard structure logic:
      // Team 1 batsmen -> Team 2 batsmen -> OVERS -> Team 2 bowlers -> OVERS -> Team 1 bowlers

      if (potentialTeamPositions.length >= 2 && oversPositions.length >= 2) {
        // Standard structure with clear markers
        const team1Pos = potentialTeamPositions[0];
        const team2Pos = potentialTeamPositions[1];

        structure.team1BatsmenStart = team1Pos + 1;
        structure.team1BatsmenEnd = team2Pos;

        structure.team2BatsmenStart = team2Pos + 1;
        structure.team2BatsmenEnd = oversPositions[0];

        // Find actual player sections within bowling areas
        const team2BowlersSection = this.findPlayersInBowlingSection(lines, oversPositions[0] + 1, oversPositions[1]);
        const team1BowlersSection = this.findPlayersInBowlingSection(lines, oversPositions[1] + 1, lines.length);

        structure.team2BowlersStart = team2BowlersSection.start;
        structure.team2BowlersEnd = team2BowlersSection.end;

        structure.team1BowlersStart = team1BowlersSection.start;
        structure.team1BowlersEnd = team1BowlersSection.end;

      } else if (oversPositions.length >= 2) {
        // Fallback: use OVERS markers to estimate structure
        const firstOvers = oversPositions[0];
        const secondOvers = oversPositions[1];

        // Estimate team sections based on OVERS positions
        structure.team1BatsmenStart = 0;
        structure.team1BatsmenEnd = Math.max(0, firstOvers - 8); // ~8 lines before first OVERS

        structure.team2BatsmenStart = Math.max(0, firstOvers - 8);
        structure.team2BatsmenEnd = firstOvers;

        structure.team2BowlersStart = firstOvers + 1;
        structure.team2BowlersEnd = secondOvers;

        structure.team1BowlersStart = secondOvers + 1;
        structure.team1BowlersEnd = lines.length;
      }

      console.log(`Final structure boundaries:`, structure);
      return structure;

    } catch (error) {
      console.error('Error finding scorecard structure:', error);
      return {
        team1BatsmenStart: -1, team1BatsmenEnd: -1,
        team2BatsmenStart: -1, team2BatsmenEnd: -1,
        team2BowlersStart: -1, team2BowlersEnd: -1,
        team1BowlersStart: -1, team1BowlersEnd: -1
      };
    }
  }

  /**
   * Find potential team name positions using improved cricket scorecard logic
   * @param {Array} lines - Array of text lines
   * @returns {Array} - Array of line indices
   */
  findPotentialTeamPositions(lines) {
    try {
      const positions = [];

      // Look for team names that are followed by player names
      for (let i = 0; i < Math.min(15, lines.length); i++) {
        const line = lines[i].trim();

        // Check if this line could be a team name
        if (this.isLikelyTeamName(line)) {
          // Verify by checking if followed by player names
          const followingPlayerCount = this.countFollowingPlayers(lines, i);

          // A team name should be followed by 1-4 player names
          if (followingPlayerCount >= 1 && followingPlayerCount <= 4) {
            positions.push(i);
            console.log(`Found potential team at line ${i}: "${line}" (followed by ${followingPlayerCount} players)`);
          }
        }
      }

      // If we found too many or too few, use fallback logic
      if (positions.length !== 2) {
        console.log(`Found ${positions.length} potential teams, using fallback detection`);
        return this.findTeamPositionsFallback(lines);
      }

      return positions;
    } catch (error) {
      console.error('Error finding potential team positions:', error);
      return [];
    }
  }

  /**
   * Count how many player names follow a potential team name
   * @param {Array} lines - Array of text lines
   * @param {number} startIndex - Starting line index
   * @returns {number} - Count of following player names
   */
  countFollowingPlayers(lines, startIndex) {
    try {
      let playerCount = 0;

      // Check next 6 lines for player names
      for (let i = startIndex + 1; i < Math.min(startIndex + 7, lines.length); i++) {
        const line = lines[i].trim();

        // Stop if we hit another team name or OVERS marker
        if (this.isLikelyTeamName(line) || /^OVERS:/i.test(line)) {
          break;
        }

        // Count if it's a player name
        if (this.isPlayerName(line)) {
          playerCount++;
        }
      }

      return playerCount;
    } catch (error) {
      console.error('Error counting following players:', error);
      return 0;
    }
  }

  /**
   * Fallback method to find team positions using OVERS markers
   * @param {Array} lines - Array of text lines
   * @returns {Array} - Array of line indices
   */
  findTeamPositionsFallback(lines) {
    try {
      const oversPositions = this.findOversMarkers(lines);

      if (oversPositions.length >= 2) {
        // Estimate team positions based on OVERS structure
        const firstOvers = oversPositions[0];

        // Team 1 is usually around line 2-4
        // Team 2 is usually 4-8 lines before first OVERS
        const estimatedTeam1 = 2;
        const estimatedTeam2 = Math.max(6, firstOvers - 8); // Adjusted to capture more Team 2 players

        console.log(`Using fallback team positions: Team1=${estimatedTeam1}, Team2=${estimatedTeam2} (firstOvers=${firstOvers})`);
        return [estimatedTeam1, estimatedTeam2];
      }

      // Last resort: use fixed positions
      console.log('Using last resort fixed positions');
      return [2, 7]; // Common positions in cricket scorecards

    } catch (error) {
      console.error('Error in fallback team position detection:', error);
      return [2, 7];
    }
  }

  /**
   * Find players in bowling section with improved boundary detection
   * @param {Array} lines - Array of text lines
   * @param {number} startIndex - Start line index
   * @param {number} endIndex - End line index
   * @returns {Object} - Section boundaries with start and end
   */
  findPlayersInBowlingSection(lines, startIndex, endIndex) {
    try {
      let actualStart = startIndex;
      let actualEnd = endIndex;

      // Skip non-player lines at the beginning
      for (let i = startIndex; i < endIndex; i++) {
        const line = lines[i].trim();
        if (this.isPlayerName(line)) {
          actualStart = i;
          break;
        }
      }

      // Find the last player in the section
      let lastPlayerIndex = actualStart;
      let playerCount = 0;

      for (let i = actualStart; i < endIndex && playerCount < 4; i++) {
        const line = lines[i].trim();
        if (this.isPlayerName(line)) {
          lastPlayerIndex = i;
          playerCount++;
        }
      }

      actualEnd = Math.min(lastPlayerIndex + 1, endIndex);

      console.log(`Bowling section refined: ${startIndex}-${endIndex} -> ${actualStart}-${actualEnd} (${playerCount} players)`);

      return {
        start: actualStart,
        end: actualEnd
      };

    } catch (error) {
      console.error('Error finding players in bowling section:', error);
      return {
        start: startIndex,
        end: endIndex
      };
    }
  }

  /**
   * Find team name position in lines
   * @param {Array} lines - Array of text lines
   * @param {string} teamName - Team name to find
   * @returns {number} - Line index or -1 if not found
   */
  findTeamNamePosition(lines, teamName) {
    try {
      if (!teamName) return -1;

      for (let i = 0; i < lines.length; i++) {
        if (lines[i].trim().toUpperCase() === teamName.toUpperCase()) {
          return i;
        }
      }

      return -1;
    } catch (error) {
      console.error('Error finding team name position:', error);
      return -1;
    }
  }

  /**
   * Find OVERS markers in lines
   * @param {Array} lines - Array of text lines
   * @returns {Array} - Array of line indices with OVERS markers
   */
  findOversMarkers(lines) {
    try {
      const oversPositions = [];

      for (let i = 0; i < lines.length; i++) {
        if (/^OVERS:\s*\d+\.?\d*$/i.test(lines[i].trim())) {
          oversPositions.push(i);
        }
      }

      return oversPositions;
    } catch (error) {
      console.error('Error finding overs markers:', error);
      return [];
    }
  }

  /**
   * Extract players in a specific section with flexible count (1-4 max)
   * @param {Array} lines - Array of text lines
   * @param {number} startIndex - Start line index
   * @param {number} endIndex - End line index
   * @param {number} maxPlayers - Maximum players to extract (4)
   * @returns {Array} - Array of player names
   */
  extractPlayersInSection(lines, startIndex, endIndex, maxPlayers = 4) {
    try {
      if (startIndex < 0 || endIndex < 0 || startIndex >= endIndex) {
        return [];
      }

      const players = [];

      for (let i = startIndex; i < Math.min(endIndex, lines.length) && players.length < maxPlayers; i++) {
        const line = lines[i].trim();

        // Check if line is a player name
        if (this.isPlayerName(line)) {
          players.push(line);
        }
      }

      // Validate player count (1-4 is normal, 5+ is error)
      if (players.length > maxPlayers) {
        console.warn(`Found ${players.length} players in section - taking first ${maxPlayers}`);
        return players.slice(0, maxPlayers);
      }

      return players;

    } catch (error) {
      console.error('Error extracting players in section:', error);
      return [];
    }
  }

  /**
   * Extract players in a section with detailed debugging
   * @param {Array} lines - Array of text lines
   * @param {number} startIndex - Start line index
   * @param {number} endIndex - End line index
   * @param {number} maxPlayers - Maximum players to extract
   * @param {string} sectionName - Name of section for debugging
   * @returns {Array} - Array of player names
   */
  extractPlayersInSectionWithDebug(lines, startIndex, endIndex, maxPlayers, sectionName) {
    try {
      console.log(`\n=== ${sectionName} Debug ===`);
      console.log(`Section boundaries: ${startIndex} to ${endIndex}`);

      if (startIndex < 0 || endIndex < 0 || startIndex >= endIndex) {
        console.log(`Invalid boundaries for ${sectionName}`);
        return [];
      }

      const players = [];

      for (let i = startIndex; i < Math.min(endIndex, lines.length) && players.length < maxPlayers; i++) {
        const line = lines[i].trim();
        console.log(`Line ${i}: "${line}" - isPlayer: ${this.isPlayerName(line)}`);

        // Check if line is a player name
        if (this.isPlayerName(line)) {
          players.push(line);
          console.log(`  -> Added player: ${line}`);
        }
      }

      console.log(`${sectionName} final result: ${players.length} players found`);
      console.log(`=== End ${sectionName} Debug ===\n`);

      // Validate player count (1-4 is normal, 5+ is error)
      if (players.length > maxPlayers) {
        console.warn(`Found ${players.length} players in ${sectionName} - taking first ${maxPlayers}`);
        return players.slice(0, maxPlayers);
      }

      return players;

    } catch (error) {
      console.error(`Error extracting players in ${sectionName}:`, error);
      return [];
    }
  }

  /**
   * Assign batting statistics to players
   * @param {Array} players - Array of player names
   * @param {Array} stats - Array of batting stats
   * @param {Array} result - Result array to populate
   */
  assignBattingStats(players, stats, result) {
    try {
      const minLength = Math.min(players.length, stats.length);

      for (let i = 0; i < minLength; i++) {
        result.push({
          name: players[i],
          runs: stats[i].runs,
          balls: stats[i].balls,
          strikeRate: stats[i].balls > 0 ? ((parseInt(stats[i].runs.replace('*', '')) / stats[i].balls) * 100).toFixed(2) : '0.00'
        });
      }

    } catch (error) {
      console.error('Error assigning batting stats:', error);
    }
  }

  /**
   * Assign bowling statistics to players
   * @param {Array} players - Array of player names
   * @param {Array} stats - Array of bowling stats
   * @param {Array} result - Result array to populate
   */
  assignBowlingStats(players, stats, result) {
    try {
      const minLength = Math.min(players.length, stats.length);

      for (let i = 0; i < minLength; i++) {
        result.push({
          name: players[i],
          wickets: stats[i].wickets,
          runs: stats[i].runs,
          economy: stats[i].runs > 0 ? (stats[i].runs / 4).toFixed(2) : '0.00', // Assuming 4 overs
          figure: stats[i].figure
        });
      }

    } catch (error) {
      console.error('Error assigning bowling stats:', error);
    }
  }

  /**
   * Calculate extraction confidence
   * @param {Object} teamNames - Team names
   * @param {string} venue - Venue
   * @param {Object} scores - Scores
   * @param {Object} playerStats - Player statistics
   * @returns {string} - Confidence level
   */
  calculateExtractionConfidence(teamNames, venue, scores, playerStats) {
    try {
      let confidence = 0;
      let maxPoints = 0;

      // Team names (20 points)
      maxPoints += 20;
      if (teamNames.team1 !== 'Team 1' && teamNames.team2 !== 'Team 2') {
        confidence += 20;
      } else if (teamNames.team1 !== 'Team 1' || teamNames.team2 !== 'Team 2') {
        confidence += 10;
      }

      // Venue (10 points)
      maxPoints += 10;
      if (venue !== 'Unknown Venue') {
        confidence += 10;
      }

      // Scores (20 points)
      maxPoints += 20;
      if (scores.team1Runs > 0 && scores.team2Runs > 0) {
        confidence += 20;
      } else if (scores.team1Runs > 0 || scores.team2Runs > 0) {
        confidence += 10;
      }

      // Player statistics (50 points)
      maxPoints += 50;
      const totalPlayers = playerStats.team1Batsmen.length + playerStats.team1Bowlers.length +
                          playerStats.team2Batsmen.length + playerStats.team2Bowlers.length;
      confidence += Math.min(50, totalPlayers * 3); // 3 points per player, max 50

      const confidencePercentage = (confidence / maxPoints) * 100;

      if (confidencePercentage >= 80) return 'high';
      if (confidencePercentage >= 60) return 'medium';
      return 'low';

    } catch (error) {
      console.error('Error calculating confidence:', error);
      return 'low';
    }
  }

  /**
   * Group text elements by Y-coordinate
   * @param {Array} elements - Array of text elements
   * @param {number} tolerance - Y-coordinate tolerance in pixels
   * @returns {Array} - Array of grouped elements
   */
  groupElementsByY(elements, tolerance = 30) {
    try {
      const groups = [];
      const sortedElements = [...elements].sort((a, b) => a.y - b.y);

      for (const element of sortedElements) {
        // Find existing group within tolerance
        let foundGroup = false;
        for (const group of groups) {
          if (Math.abs(group[0].y - element.y) <= tolerance) {
            group.push(element);
            foundGroup = true;
            break;
          }
        }

        // Create new group if not found
        if (!foundGroup) {
          groups.push([element]);
        }
      }

      return groups;
    } catch (error) {
      console.error('Error grouping elements by Y:', error);
      return [];
    }
  }

  /**
   * Find players below a team name using coordinates
   * @param {Array} textElements - Array of all text elements
   * @param {Object} teamElement - Team name element
   * @returns {Array} - Array of player elements below the team
   */
  findPlayersBelow(textElements, teamElement) {
    try {
      const playersBelow = textElements.filter(el => {
        // Must be below the team name (Y > team.y)
        const isBelow = el.y > teamElement.y;

        // Must be within reasonable distance (not too far below)
        const isNearby = el.y < teamElement.y + 200;

        // Must be horizontally aligned (similar X coordinate)
        const isAligned = Math.abs(el.x - teamElement.x) < 100;

        // Must look like a player name
        const isPlayer = this.isPlayerName(el.text);

        return isBelow && isNearby && isAligned && isPlayer;
      });

      // Sort by Y-coordinate and take first 4
      return playersBelow.sort((a, b) => a.y - b.y).slice(0, 4);

    } catch (error) {
      console.error('Error finding players below team:', error);
      return [];
    }
  }

  /**
   * Extract player statistics using spatial coordinate analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @param {Object} teamNames - Team names object
   * @returns {Object} - Player statistics
   */
  extractPlayerStatsFromCoordinates(textElements, teamNames) {
    try {
      console.log('Extracting player statistics using coordinate analysis...');
      console.log(`Looking for teams: "${teamNames.team1}" and "${teamNames.team2}"`);

      // Find OVERS markers to identify team sections (look for any text containing OVERS and numbers)
      const oversElements = textElements.filter(el =>
        /OVERS/i.test(el.text) && /\d+\.?\d*/.test(el.text)
      );
      console.log(`Found ${oversElements.length} OVERS markers:`, oversElements.map(el => `"${el.text}" at (${el.x}, ${el.y})`));

      // Use OVERS markers as section boundaries instead of team name elements
      let team1BatsmenElements = [];
      let team2BatsmenElements = [];

      // Get smart boundaries for all player extraction (calculate once, use everywhere)
      const imageHeight = this.getImageHeight(textElements);
      // Store image height in instance for use in isInBattingArea method
      this._cachedImageHeight = imageHeight;
      console.log(`Image height detected: ${imageHeight}px`);
      let boundaries = null;

      if (oversElements.length >= 2) {
        // Sort OVERS markers by Y-coordinate (top to bottom)
        oversElements.sort((a, b) => a.y - b.y);

        const firstOversY = oversElements[0].y;
        const secondOversY = oversElements[1].y;

        // Correct boundaries based on actual scorecard layout:
        // Team 1 batting: Between first OVERS (Y=277) and second OVERS (Y=595)
        // Team 2 batting: After second OVERS (Y=595) until end

        // Get smart boundaries for player extraction (wider than score boundaries)
        boundaries = this.getSmartBoundaries(oversElements, imageHeight);

        // Team 1 batsmen: Use wider player boundaries (left side)
        team1BatsmenElements = this.findPlayersBetweenYCoordinates(textElements, boundaries.team1PlayerStart, boundaries.team1PlayerEnd);

        // Team 2 batsmen: Use wider player boundaries (left side)
        team2BatsmenElements = this.findPlayersBetweenYCoordinates(textElements, boundaries.team2PlayerStart, boundaries.team2PlayerEnd);

        console.log(`Using smart OVERS-relative boundaries:`);
        console.log(`- Team 1 batsmen: Y ${boundaries.team1PlayerStart}-${boundaries.team1PlayerEnd} (X < 500)`);
        console.log(`- Team 2 batsmen: Y ${boundaries.team2PlayerStart}-${boundaries.team2PlayerEnd} (X < 500)`);
      } else {
        console.log('Not enough OVERS markers found, using fallback method');
        // Fallback: split players by Y-coordinate midpoint
        const allPlayers = textElements.filter(el => this.isPlayerName(el.text));
        allPlayers.sort((a, b) => a.y - b.y);
        const midpoint = allPlayers.length / 2;
        team1BatsmenElements = allPlayers.slice(0, midpoint);
        team2BatsmenElements = allPlayers.slice(midpoint);
      }

      // Extract bowlers using OVERS markers as boundaries (right side, X > 500)
      let team2BowlerElements = [];
      let team1BowlerElements = [];

      if (oversElements.length >= 2 && boundaries) {
        // Use the same smart boundaries for bowlers (right side)
        // Team 2 bowlers: Use team1 player boundaries (right side)
        team2BowlerElements = this.findBowlersBetweenYCoordinates(textElements, boundaries.team1PlayerStart, boundaries.team1PlayerEnd);
        // Team 1 bowlers: Use team2 player boundaries (right side)
        team1BowlerElements = this.findBowlersBetweenYCoordinates(textElements, boundaries.team2PlayerStart, boundaries.team2PlayerEnd);

        console.log(`- Team 2 bowlers: Y ${boundaries.team1PlayerStart}-${boundaries.team1PlayerEnd} (X > 500)`);
        console.log(`- Team 1 bowlers: Y ${boundaries.team2PlayerStart}-${boundaries.team2PlayerEnd} (X > 500)`);
      }

      console.log(`Extracted players - Team 1 Batsmen: ${team1BatsmenElements.length}, Team 2 Batsmen: ${team2BatsmenElements.length}`);
      console.log(`Team 1 Batsmen: [${team1BatsmenElements.map(p => p.text).join(', ')}]`);
      console.log(`Team 2 Batsmen: [${team2BatsmenElements.map(p => p.text).join(', ')}]`);

      // Find bowling statistics
      const bowlingStats = this.findBowlingStatsFromCoordinates(textElements);
      console.log(`Found ${bowlingStats.length} bowling stats:`, bowlingStats.map(s => s.figure));

      // Match batting stats to players using spatial proximity
      const team1BatsmenWithStats = this.matchBattingStatsToPlayers(team1BatsmenElements, textElements);
      const team2BatsmenWithStats = this.matchBattingStatsToPlayers(team2BatsmenElements, textElements);

      // Assign bowling statistics to players using spatial proximity
      const team2BowlersWithStats = this.matchBowlingStatsToPlayers(team2BowlerElements, textElements);
      const team1BowlersWithStats = this.matchBowlingStatsToPlayers(team1BowlerElements, textElements);

      console.log(`Coordinate-based player extraction completed:`, {
        team1Batsmen: team1BatsmenWithStats.length,
        team2Batsmen: team2BatsmenWithStats.length,
        team2Bowlers: team2BowlersWithStats.length,
        team1Bowlers: team1BowlersWithStats.length
      });

      return {
        team1Batsmen: team1BatsmenWithStats,
        team1Bowlers: team1BowlersWithStats,
        team2Batsmen: team2BatsmenWithStats,
        team2Bowlers: team2BowlersWithStats
      };

    } catch (error) {
      console.error('Error extracting player stats from coordinates:', error);
      return {
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: []
      };
    }
  }

  /**
   * Find batsmen between Y coordinates (left side only, X < 500)
   * @param {Array} textElements - Array of text elements
   * @param {number} startY - Start Y coordinate
   * @param {number} endY - End Y coordinate
   * @returns {Array} - Array of batsman elements
   */
  findPlayersBetweenYCoordinates(textElements, startY, endY) {
    try {
      const players = textElements.filter(el => {
        const isInYRange = el.y > startY && el.y < endY;
        const isPlayer = this.isPlayerName(el.text);
        const isNotDuplicate = !this.isDuplicatePlayerName(el.text, textElements);
        const isLeftSide = el.x < 500; // Only batsmen on left side (X < 500)
        return isInYRange && isPlayer && isNotDuplicate && isLeftSide;
      });

      // Sort by Y-coordinate and take first 4 players
      const sortedPlayers = players.sort((a, b) => a.y - b.y);
      const uniquePlayers = this.deduplicatePlayerNames(sortedPlayers);

      console.log(`Found ${uniquePlayers.length} batsmen between Y ${startY}-${endY} (X < 500):`,
        uniquePlayers.map(p => `"${p.text}" at (${p.x}, ${p.y})`));

      // Apply intelligent filtering to remove team names and non-players
      const validPlayers = this.filterRealPlayersIntelligently(uniquePlayers, textElements);
      
      // Special handling for Team 2 batsmen (lower half of image)
      const isTeam2Area = startY > (this._cachedImageHeight / 2);
      
      if (isTeam2Area && validPlayers.length === 0) {
        console.log(`⚠️ No valid Team 2 batsmen found after filtering - using pre-filtered players`);
        // For Team 2, if no valid players after filtering, use pre-filtered players
        return uniquePlayers.slice(0, 4);
      }

      return validPlayers.slice(0, 4); // Max 4 players per team (cricket standard)

    } catch (error) {
      console.error('Error finding batsmen between Y coordinates:', error);
      return [];
    }
  }

  /**
   * Find bowlers between Y coordinates (right side only, X > 500)
   * @param {Array} textElements - Array of text elements
   * @param {number} startY - Start Y coordinate
   * @param {number} endY - End Y coordinate
   * @returns {Array} - Array of bowler elements
   */
  findBowlersBetweenYCoordinates(textElements, startY, endY) {
    try {
      const bowlers = textElements.filter(el => {
        const isInYRange = el.y > startY && el.y < endY;
        const isPlayer = this.isPlayerName(el.text);
        const isNotDuplicate = !this.isDuplicatePlayerName(el.text, textElements);
        const isRightSide = el.x > 500; // Only bowlers on right side (X > 500)
        return isInYRange && isPlayer && isNotDuplicate && isRightSide;
      });

      // Sort by Y-coordinate and take first 4 players
      const sortedBowlers = bowlers.sort((a, b) => a.y - b.y);
      const uniqueBowlers = this.deduplicatePlayerNames(sortedBowlers);

      return uniqueBowlers.slice(0, 4); // Max 4 bowlers per team (cricket standard)

    } catch (error) {
      console.error('Error finding bowlers between Y coordinates:', error);
      return [];
    }
  }

  /**
   * Check if a player name is a duplicate (shorter version of a longer name)
   * @param {string} playerName - Player name to check
   * @param {Array} textElements - All text elements
   * @returns {boolean} - True if this is a duplicate
   */
  isDuplicatePlayerName(playerName, textElements) {
    try {
      // Find all player names
      const allPlayerNames = textElements
        .filter(el => this.isPlayerName(el.text))
        .map(el => el.text);

      // Check if there's a longer name that contains this name
      return allPlayerNames.some(name =>
        name !== playerName &&
        name.includes(playerName) &&
        name.length > playerName.length
      );

    } catch (error) {
      console.error('Error checking duplicate player name:', error);
      return false;
    }
  }

  /**
   * Remove duplicate player names (keep longer versions)
   * @param {Array} playerElements - Array of player elements
   * @returns {Array} - Deduplicated player elements
   */
  deduplicatePlayerNames(playerElements) {
    try {
      const result = [];
      const seen = new Set();

      // Sort by name length (longest first) to prefer full names
      const sortedByLength = [...playerElements].sort((a, b) => b.text.length - a.text.length);

      for (const player of sortedByLength) {
        // Check if we've already added a name that contains this name
        const isDuplicate = Array.from(seen).some(seenName =>
          seenName.includes(player.text) || player.text.includes(seenName)
        );

        if (!isDuplicate) {
          result.push(player);
          seen.add(player.text);
        }
      }

      return result.sort((a, b) => a.y - b.y); // Sort back by Y-coordinate

    } catch (error) {
      console.error('Error deduplicating player names:', error);
      return playerElements;
    }
  }

  /**
   * Extract batsmen by coordinates (players below team name)
   * @param {Array} textElements - Array of text elements
   * @param {Object} teamElement - Team name element
   * @returns {Array} - Array of batsman names
   */
  extractBatsmenByCoordinates(textElements, teamElement) {
    try {
      if (!teamElement) return [];

      const batsmen = this.findPlayersBelow(textElements, teamElement);
      return batsmen.map(player => player.text);

    } catch (error) {
      console.error('Error extracting batsmen by coordinates:', error);
      return [];
    }
  }

  /**
   * Extract bowlers by coordinates (players between OVERS markers)
   * @param {Array} textElements - Array of text elements
   * @param {Object} startMarker - Start OVERS element
   * @param {Object} endMarker - End OVERS element (can be null)
   * @returns {Array} - Array of bowler names
   */
  extractBowlersByCoordinates(textElements, startMarker, endMarker) {
    try {
      if (!startMarker) return [];

      const startY = startMarker.y;
      const endY = endMarker ? endMarker.y : startY + 200;

      const bowlers = textElements.filter(el => {
        const isBetween = el.y > startY && el.y < endY;
        const isPlayer = this.isPlayerName(el.text);
        return isBetween && isPlayer;
      });

      // Sort by Y-coordinate and take first 4
      return bowlers.sort((a, b) => a.y - b.y).map(player => player.text); // Dynamic count based on actual bowlers

    } catch (error) {
      console.error('Error extracting bowlers by coordinates:', error);
      return [];
    }
  }

  /**
   * Find batting statistics from coordinates
   * @param {Array} textElements - Array of text elements
   * @returns {Array} - Array of batting stats
   */
  findBattingStatsFromCoordinates(textElements) {
    try {
      // Look for runs in both word-level and line-level elements
      const runs = textElements.filter(el => /^\d+\*?$/.test(el.text));

      // Look for balls specifically in line-level elements (where "(40)" is properly combined)
      const balls = textElements.filter(el =>
        el.isLineText && /^\(\d+\)$/.test(el.text)
      );

      console.log(`Found ${runs.length} runs elements:`, runs.map(r => `"${r.text}" at (${r.x}, ${r.y})`));
      console.log(`Found ${balls.length} balls elements:`, balls.map(b => `"${b.text}" at (${b.x}, ${b.y})`));

      const battingStats = [];

      // Sort both arrays by Y coordinate to match them properly
      runs.sort((a, b) => a.y - b.y);
      balls.sort((a, b) => a.y - b.y);

      const minLength = Math.min(runs.length, balls.length);

      for (let i = 0; i < minLength; i++) {
        battingStats.push({
          runs: runs[i].text,
          balls: parseInt(balls[i].text.replace(/[()]/g, ''))
        });
      }

      return battingStats;

    } catch (error) {
      console.error('Error finding batting stats from coordinates:', error);
      return [];
    }
  }

  /**
   * Find bowling statistics from coordinates
   * @param {Array} textElements - Array of text elements
   * @returns {Array} - Array of bowling stats
   */
  findBowlingStatsFromCoordinates(textElements) {
    try {
      // Look for bowling figures in line-level elements (where "3-21" is properly combined)
      const bowlingFigures = textElements.filter(el => {
        // Prefer line-level elements for bowling figures
        if (!el.isLineText) return false;

        const match = el.text.match(/^([0-9])-(\d+)$/);
        if (match) {
          const wickets = parseInt(match[1]);
          const runs = parseInt(match[2]);
          return wickets >= 0 && wickets <= 9; // Valid bowling figures
        }
        return false;
      });

      console.log(`Found ${bowlingFigures.length} bowling figures:`, bowlingFigures.map(b => `"${b.text}" at (${b.x}, ${b.y})`));

      return bowlingFigures.map(el => {
        const [wickets, runs] = el.text.split('-').map(Number);
        return {
          wickets,
          runs,
          figure: el.text
        };
      });

    } catch (error) {
      console.error('Error finding bowling stats from coordinates:', error);
      return [];
    }
  }

  /**
   * Match batting statistics to players using spatial proximity
   * @param {Array} playerElements - Array of player elements with coordinates
   * @param {Array} textElements - Array of all text elements
   * @returns {Array} - Array of players with matched stats
   */
  matchBattingStatsToPlayers(playerElements, textElements) {
    try {
      const result = [];

      for (const playerElement of playerElements) {
        console.log(`\n=== Matching stats for ${playerElement.text} at (${playerElement.x}, ${playerElement.y}) ===`);

        // Debug: Show all elements in the same row
        const sameRowElements = textElements.filter(el => Math.abs(el.y - playerElement.y) <= 15);
        console.log(`All elements in same row:`, sameRowElements.map(el => `"${el.text}" at (${el.x}, ${el.y})`));

        // Find runs near this player using improved spatial intelligence
        const nearbyRuns = textElements.filter(el => {
          const isRuns = /^\d+\*?$/.test(el.text);
          const isSameRow = Math.abs(el.y - playerElement.y) <= 15; // Same row tolerance
          const isToTheRight = el.x > playerElement.x; // Runs should be to the right of player name

          // Improved spatial filtering: Exclude balls format and bowling stats
          const isNotBallsFormat = !(/^\(\d+\)$/.test(el.text)); // Exclude (40) format
          const isNotBowlingStats = el.x < playerElement.x + 600; // Exclude far-right bowling columns

          // Prefer runs in the expected runs column (closer to player name)
          const isInRunsColumn = el.x < playerElement.x + 400; // Runs usually within 400px of name

          return isRuns && isSameRow && isToTheRight && isNotBallsFormat && isNotBowlingStats && isInRunsColumn;
        });

        // Find balls near this player (same row, within Y tolerance)
        const nearbyBalls = textElements.filter(el => {
          const isBalls = /^\(\d+\)$/.test(el.text); // Balls in parentheses format
          const isSameRow = Math.abs(el.y - playerElement.y) <= 15; // Same row tolerance
          const isToTheRight = el.x > playerElement.x; // Balls should be to the right of player name
          const isInBallsColumn = el.x < playerElement.x + 1600; // Much wider range for balls detection
          return isBalls && isSameRow && isToTheRight && isInBallsColumn;
        });

        console.log(`Found ${nearbyRuns.length} runs elements:`, nearbyRuns.map(r => `"${r.text}" at (${r.x}, ${r.y})`));
        console.log(`Found ${nearbyBalls.length} balls elements:`, nearbyBalls.map(b => `"${b.text}" at (${b.x}, ${b.y})`));

        // STEP 1: Extract balls first (more reliable in OCR)
        const balls = nearbyBalls.length > 0 ? parseInt(nearbyBalls[0].text.replace(/[()]/g, '')) : 0;
        
        // STEP 2: Find runs using multiple approaches
        let runsOptions = [];
        
        // Approach 1: Standard runs detection (most reliable)
        if (nearbyRuns.length > 0) {
          runsOptions.push({
            value: nearbyRuns[0].text,
            confidence: 0.9,
            method: "standard"
          });
          console.log(`Standard method found runs: ${nearbyRuns[0].text}`);
        }
        
        // Approach 2: Broader search for runs
        const fallbackRuns = textElements.filter(el => {
          const isRuns = /^\d+\*?$/.test(el.text);
          const isSameRow = Math.abs(el.y - playerElement.y) <= 15;
          const isToTheRight = el.x > playerElement.x;
          const isNotBallsFormat = !(/^\(\d+\)$/.test(el.text));
          return isRuns && isSameRow && isToTheRight && isNotBallsFormat;
        });
        
        if (fallbackRuns.length > 0 && (!nearbyRuns.length || fallbackRuns[0].text !== nearbyRuns[0].text)) {
          runsOptions.push({
            value: fallbackRuns[0].text,
            confidence: 0.7,
            method: "fallback"
          });
          console.log(`Fallback method found runs: ${fallbackRuns[0].text}`);
        }
        
        // Approach 3: Look for individual digits that might be runs
        // This helps with OCR that splits "9" into separate elements
        const individualDigits = textElements.filter(el => {
          const isDigit = /^\d$/.test(el.text);
          const isSameRow = Math.abs(el.y - playerElement.y) <= 15;
          const isToTheRight = el.x > playerElement.x;
          const isBeforeBalls = nearbyBalls.length > 0 ? el.x < nearbyBalls[0].x : true;
          const isInRunsColumn = el.x < playerElement.x + 400;
          return isDigit && isSameRow && isToTheRight && isBeforeBalls && isInRunsColumn;
        });
        
        if (individualDigits.length > 0) {
          // Sort by X position to get correct order
          const sortedDigits = individualDigits.sort((a, b) => a.x - b.x);
          const combinedValue = sortedDigits.map(d => d.text).join('');
          
          if (combinedValue.length <= 3) { // Reasonable runs limit
            runsOptions.push({
              value: combinedValue,
              confidence: 0.6,
              method: "individual-digits"
            });
            console.log(`Individual digits method found runs: ${combinedValue}`);
          }
        }
        
        // Approach 4: Use image analysis to distinguish between 6 and 9
        // For digits that are commonly confused (6/9, 1/7, 0/8)
        if (runsOptions.length > 0) {
          const primaryValue = runsOptions[0].value;
          const numericValue = parseInt(primaryValue.replace('*', ''));
          const isNotOut = primaryValue.includes('*');
          
          // We've removed the digit shape analysis
          // Using raw OCR values without correction
        }
        
        // Approach 5: Use strike rate analysis for validation
        if (balls > 0 && runsOptions.length > 0) {
          const primaryValue = parseInt(runsOptions[0].value.replace('*', ''));
          const strikeRate = (primaryValue / balls) * 100;
          
          // If strike rate is implausible, suggest corrections
          if (strikeRate > 300 || strikeRate < 30) {
            // For very high strike rates, 6 might be 9 and vice versa
            let correctedValue;
            
            if (primaryValue === 6 && strikeRate > 300 && balls <= 3) {
              correctedValue = 9;
            } else if (primaryValue === 9 && strikeRate < 30 && balls >= 15) {
              correctedValue = 6;
            } else if (primaryValue === 1 && strikeRate < 30 && balls >= 5) {
              correctedValue = 7;
            } else if (primaryValue === 0 && balls >= 10) {
              correctedValue = 8;
            }
            
            if (correctedValue) {
              const isNotOut = runsOptions[0].value.includes('*');
              runsOptions.push({
                value: correctedValue + (isNotOut ? '*' : ''),
                confidence: 0.75,
                method: "strike-rate-analysis"
              });
              console.log(`Strike rate analysis suggests ${primaryValue} might be ${correctedValue}`);
            }
          }
        }
        
        // STEP 3: Select the best runs value from all options
        let runs = '0';
        if (runsOptions.length > 0) {
          // Sort by confidence (highest first)
          runsOptions.sort((a, b) => b.confidence - a.confidence);
          runs = runsOptions[0].value;
          
          // Log all options for debugging
          console.log(`All runs options:`, runsOptions.map(o => 
            `${o.value} (${o.method}, confidence: ${o.confidence.toFixed(2)})`
          ));
          console.log(`Selected runs: ${runs} using method: ${runsOptions[0].method}`);
        }
        
        // We're no longer applying automatic digit corrections
        // Let the OCR results stand as they are
        console.log(`Using original OCR value: ${runs}`);
        // No corrections applied
        
        console.log(`Final match: ${playerElement.text} -> ${runs}(${balls})`);
        
        // Calculate strike rate
        const runsValue = parseInt(runs.replace('*', ''));
        const strikeRate = balls > 0 ? ((runsValue / balls) * 100).toFixed(2) : '0.00';
        
        result.push({
          name: playerElement.text,
          runs: runs,
          balls: balls,
          strikeRate: strikeRate
        });
      }

      return result;

    } catch (error) {
      console.error('Error matching batting stats to players:', error);
      return [];
    }
  }

  /**
   * Match bowling statistics to players using spatial proximity
   * @param {Array} playerElements - Array of player elements with coordinates
   * @param {Array} textElements - Array of all text elements
   * @returns {Array} - Array of players with matched bowling stats
   */
  matchBowlingStatsToPlayers(playerElements, textElements) {
    try {
      const result = [];

      for (const playerElement of playerElements) {
        console.log(`\n=== Matching bowling stats for ${playerElement.text} at (${playerElement.x}, ${playerElement.y}) ===`);

        // Find bowling figures near this player (same row, within Y tolerance)
        const nearbyFigures = textElements.filter(el => {
          const isBowlingFigure = /^\d+-\d+$/.test(el.text);
          const isSameRow = Math.abs(el.y - playerElement.y) <= 15; // Same row tolerance
          const isToTheRight = el.x > playerElement.x; // Figures should be to the right of player name
          return isBowlingFigure && isSameRow && isToTheRight;
        });

        console.log(`Found ${nearbyFigures.length} bowling figures:`, nearbyFigures.map(f => `"${f.text}" at (${f.x}, ${f.y})`));

        // Only assign bowling figures if we found actual figures (no phantom 0-0)
        if (nearbyFigures.length > 0) {
          const figure = nearbyFigures[0].text;
          const [wickets, runs] = figure.split('-').map(Number);

          console.log(`Matched: ${playerElement.text} -> ${figure}`);

          result.push({
            name: playerElement.text,
            wickets: wickets || 0,
            runs: runs || 0,
            economy: runs > 0 ? (runs / 4).toFixed(2) : '0.00', // Assuming 4 overs
            figure: figure
          });
        } else {
          console.log(`No bowling figure found for ${playerElement.text}, skipping (not a bowler)`);
        }
      }

      return result;

    } catch (error) {
      console.error('Error matching bowling stats to players:', error);
      return [];
    }
  }

  /**
   * Intelligently filter real players from team names and non-players
   * @param {Array} detectedPlayers - Array of detected player elements
   * @param {Array} allElements - All text elements for context analysis
   * @returns {Array} - Filtered array of real players
   */
  filterRealPlayersIntelligently(detectedPlayers, allElements) {
    try {
      console.log('🧠 Applying intelligent player filtering...');

      const validPlayers = detectedPlayers.filter(player => {
        // Method 1: Check if player has associated batting stats
        const hasAssociatedStats = this.hasAssociatedBattingStats(player, allElements);

        // Method 2: Check if appears isolated (team names often appear alone)
        const appearsIsolated = this.appearsIsolatedFromStats(player, allElements);

        // Method 3: Check if in proper batting area (not header/footer)
        const inBattingArea = this.isInBattingArea(player);

        // Check if player is in lower half (Team 2 area)
        const isLowerHalf = player.y > (this._cachedImageHeight / 2);
        
        // Determine decision based on position in image
        const decision = isLowerHalf ? 
          (hasAssociatedStats ? 'KEEP' : 'FILTER') : 
          (hasAssociatedStats && inBattingArea ? 'KEEP' : 'FILTER');
        
        console.log(`Analyzing "${player.text}":`, {
          hasStats: hasAssociatedStats,
          notIsolated: !appearsIsolated,
          inBattingArea: inBattingArea,
          isTeam2Area: isLowerHalf,
          decision: decision
        });

        // For players in the lower half of the image (Team 2 area), be more lenient
        
        if (isLowerHalf) {
          // For Team 2 area, only require stats (don't filter by batting area)
          return hasAssociatedStats;
        } else {
          // For Team 1 area, require both stats and proper batting area
          return hasAssociatedStats && inBattingArea;
        }
      });

      console.log(`🧠 Intelligent filtering: ${detectedPlayers.length} → ${validPlayers.length} players`);
      return validPlayers;

    } catch (error) {
      console.error('Error in intelligent player filtering:', error);
      return detectedPlayers; // Fallback to original list
    }
  }

  /**
   * Check if a player has associated batting statistics nearby
   * @param {Object} player - Player element with position
   * @param {Array} allElements - All text elements
   * @returns {boolean} - True if player has associated stats
   */
  hasAssociatedBattingStats(player, allElements) {
    // Look for elements in the same row (within 15px Y tolerance)
    const sameRowElements = allElements.filter(el =>
      Math.abs(el.y - player.y) <= 15 && // Same row
      el.x > player.x // To the right of player name
    );

    // Check for runs pattern (numbers, possibly with *)
    const hasRuns = sameRowElements.some(el => /^\d+\*?$/.test(el.text));

    // Check for balls pattern (numbers in parentheses)
    const hasBalls = sameRowElements.some(el => /^\(\d+\)$/.test(el.text));
    
    // Check if player is in lower half (Team 2 area)
    const isLowerHalf = player.y > (this._cachedImageHeight / 2);
    
    if (isLowerHalf) {
      // For Team 2 area, be more lenient - only require runs OR some numeric value
      const hasAnyNumeric = sameRowElements.some(el => /\d+/.test(el.text));
      return hasRuns || hasAnyNumeric;
    } else {
      // For Team 1 area, require both runs and balls
      return hasRuns && hasBalls;
    }
  }

  /**
   * Check if a player appears isolated from batting statistics
   * @param {Object} player - Player element with position
   * @param {Array} allElements - All text elements
   * @returns {boolean} - True if player appears isolated
   */
  appearsIsolatedFromStats(player, allElements) {
    // Look for any statistical elements near the player
    const nearbyElements = allElements.filter(el =>
      Math.abs(el.y - player.y) <= 25 && // Slightly larger tolerance
      Math.abs(el.x - player.x) <= 200   // Within reasonable horizontal distance
    );

    // Check if there are any statistical patterns nearby
    const hasNearbyStats = nearbyElements.some(el =>
      /^\d+\*?$/.test(el.text) ||     // Runs
      /^\(\d+\)$/.test(el.text) ||    // Balls
      /^\d+-\d+$/.test(el.text)       // Bowling figures
    );

    return !hasNearbyStats;
  }

  /**
   * Check if element is in proper batting area (not header/footer)
   * @param {Object} player - Player element with position
   * @returns {boolean} - True if in batting area
   */
  isInBattingArea(player) {
    // Get all text elements to calculate image height (cached for performance)
    if (!this._cachedImageHeight) {
      // Use a very large value to ensure we don't exclude valid players
      this._cachedImageHeight = 2000; 
    }
    
    // Simple heuristic: batting areas are typically in middle sections
    // Headers are usually at top (Y < 100), footers at bottom (Y > 95% of height)
    // This can be refined based on scorecard structure
    const minY = 100; // Skip header area
    const maxY = this._cachedImageHeight * 0.95; // Allow almost full height
    
    const isInArea = player.y > minY && player.y < maxY;
    
    // Debug log for Team 2 batting area issue
    if (player.y > 900) {
      console.log(`Player "${player.text}" at Y=${player.y} is ${isInArea ? 'IN' : 'OUT OF'} batting area (max Y=${maxY})`);
    }
    
    return isInArea;
  }
  
  // We've removed the enhancedDigitCorrection method
  // We're now using the raw OCR results without attempting to correct them
  
  /**
   * Analyze digit shape and context to distinguish between commonly confused digits
   * @param {Array} textElements - All text elements
   * @param {Object} playerElement - Player element
   * @param {number} detectedValue - Detected digit value
   * @param {number} balls - Balls faced
   * @returns {number|null} - Corrected digit or null if no correction
   */
  // We've removed the analyzeDigitShape method
  // We're now using the raw OCR results without attempting to correct them

  /**
   * Assign batting statistics to players
   * @param {Array} players - Array of player names
   * @param {Array} stats - Array of batting stats
   * @returns {Array} - Array of players with stats
   */
  assignBattingStatsToPlayers(players, stats) {
    try {
      const result = [];
      const minLength = Math.min(players.length, stats.length);

      for (let i = 0; i < minLength; i++) {
        result.push({
          name: players[i],
          runs: stats[i].runs,
          balls: stats[i].balls,
          strikeRate: stats[i].balls > 0 ?
            ((parseInt(stats[i].runs.replace('*', '')) / stats[i].balls) * 100).toFixed(2) : '0.00'
        });
      }

      return result;

    } catch (error) {
      console.error('Error assigning batting stats to players:', error);
      return [];
    }
  }

  /**
   * Assign bowling statistics to players
   * @param {Array} players - Array of player names
   * @param {Array} stats - Array of bowling stats
   * @returns {Array} - Array of players with stats
   */
  assignBowlingStatsToPlayers(players, stats) {
    try {
      const result = [];
      const minLength = Math.min(players.length, stats.length);

      for (let i = 0; i < minLength; i++) {
        result.push({
          name: players[i],
          wickets: stats[i].wickets,
          runs: stats[i].runs,
          economy: stats[i].runs > 0 ? (stats[i].runs / 4).toFixed(2) : '0.00', // Assuming 4 overs
          figure: stats[i].figure
        });
      }

      return result;

    } catch (error) {
      console.error('Error assigning bowling stats to players:', error);
      return [];
    }
  }
}

module.exports = OCRService;