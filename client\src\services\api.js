import axios from 'axios';
import { API_URL } from '../config';

// API base URL for the training server - try to get from localStorage or use default
const getTrainingApiBaseUrl = () => {
  const savedUrl = localStorage.getItem('training_api_url');
  // Try to use the same host as the main app but with a different port
  const currentHost = window.location.hostname;
  return savedUrl || `http://${currentHost}:5001/api`;
};

// API base URL for the training server
const TRAINING_API_BASE_URL = getTrainingApiBaseUrl();

// Create API instance for the main server
const API = axios.create({
  baseURL: API_URL
});

// Create API instance for the training server
const TRAINING_API = axios.create({
  baseURL: TRAINING_API_BASE_URL
});

// Add auth token to requests
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export { API, TRAINING_API };
