const MatchOutcomeService = require('../services/matchOutcomeService');
const Tournament = require('../models/Tournament');
const Team = require('../models/Team');
const mongoose = require('mongoose');

/**
 * Match Outcome Service Tests
 * 
 * Tests for match outcome calculation, tournament standings updates,
 * and related functionality.
 */

describe('MatchOutcomeService', () => {
  let matchOutcomeService;
  let mockTournament;
  let mockTeam1;
  let mockTeam2;

  beforeAll(async () => {
    // Initialize the service
    matchOutcomeService = new MatchOutcomeService();
    await matchOutcomeService.initialize();
  });

  beforeEach(() => {
    // Mock tournament data
    mockTournament = {
      _id: new mongoose.Types.ObjectId(),
      name: 'Test Tournament',
      pointsForWin: 2,
      pointsForTie: 1,
      pointsForNoResult: 1,
      phases: [{
        name: 'Group Stage',
        standings: []
      }]
    };

    // Mock team data
    mockTeam1 = {
      _id: new mongoose.Types.ObjectId(),
      teamName: 'Team Alpha'
    };

    mockTeam2 = {
      _id: new mongoose.Types.ObjectId(),
      teamName: 'Team Beta'
    };
  });

  describe('calculateMatchOutcome', () => {
    test('should calculate winner correctly for team1 victory', async () => {
      const ocrData = {
        team1: 'Team Alpha',
        team2: 'Team Beta',
        team1Score: { runs: 180, wickets: 5, overs: 20 },
        team2Score: { runs: 165, wickets: 8, overs: 20 },
        resultText: 'Team Alpha won by 15 runs'
      };

      const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData);

      expect(outcome.winner).toBe('team1');
      expect(outcome.margin.type).toBe('runs');
      expect(outcome.margin.value).toBe(15);
      expect(outcome.team1Score.runs).toBe(180);
      expect(outcome.team2Score.runs).toBe(165);
      expect(outcome.confidence).toBeGreaterThan(0.8);
    });

    test('should calculate winner correctly for team2 victory', async () => {
      const ocrData = {
        team1: 'Team Alpha',
        team2: 'Team Beta',
        team1Score: { runs: 145, wickets: 10, overs: 18.3 },
        team2Score: { runs: 146, wickets: 6, overs: 19.2 },
        resultText: 'Team Beta won by 4 wickets'
      };

      const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData);

      expect(outcome.winner).toBe('team2');
      expect(outcome.margin.type).toBe('wickets');
      expect(outcome.margin.value).toBe(4);
      expect(outcome.team1Score.runs).toBe(145);
      expect(outcome.team2Score.runs).toBe(146);
    });

    test('should handle tie matches correctly', async () => {
      const ocrData = {
        team1: 'Team Alpha',
        team2: 'Team Beta',
        team1Score: { runs: 150, wickets: 8, overs: 20 },
        team2Score: { runs: 150, wickets: 9, overs: 20 },
        resultText: 'Match tied'
      };

      const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData);

      expect(outcome.winner).toBe('tie');
      expect(outcome.isTie).toBe(true);
      expect(outcome.team1Score.runs).toBe(150);
      expect(outcome.team2Score.runs).toBe(150);
    });

    test('should extract player performances correctly', async () => {
      const ocrData = {
        team1: 'Team Alpha',
        team2: 'Team Beta',
        team1Score: { runs: 180, wickets: 5, overs: 20 },
        team2Score: { runs: 165, wickets: 8, overs: 20 },
        team1Batsmen: [
          { name: 'Player A', runs: 65, balls: 45, fours: 8, sixes: 2 },
          { name: 'Player B', runs: 42, balls: 38, fours: 4, sixes: 1 }
        ],
        team1Bowlers: [
          { name: 'Player C', overs: 4, runs: 28, wickets: 2 },
          { name: 'Player D', overs: 4, runs: 35, wickets: 1 }
        ],
        playerOfMatch: 'Player A'
      };

      const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData);

      expect(outcome.playerPerformances).toBeDefined();
      expect(outcome.playerPerformances.batting).toHaveLength(2);
      expect(outcome.playerPerformances.bowling).toHaveLength(2);
      expect(outcome.playerOfMatch).toBe('Player A');
    });

    test('should handle missing or invalid data gracefully', async () => {
      const ocrData = {
        team1: 'Team Alpha',
        team2: 'Team Beta'
        // Missing scores
      };

      const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData);

      expect(outcome.confidence).toBeLessThan(0.5);
      expect(outcome.errors).toBeDefined();
      expect(outcome.errors.length).toBeGreaterThan(0);
    });
  });

  describe('calculateNetRunRate', () => {
    test('should calculate net run rate correctly', () => {
      const runsScored = 180;
      const oversPlayed = 20;
      const runsConceded = 165;
      const oversBowled = 20;

      const nrr = matchOutcomeService.calculateNetRunRate(
        runsScored, oversPlayed, runsConceded, oversBowled
      );

      expect(nrr).toBeCloseTo(0.75, 2); // (180/20) - (165/20) = 9 - 8.25 = 0.75
    });

    test('should handle zero overs correctly', () => {
      const nrr = matchOutcomeService.calculateNetRunRate(100, 0, 80, 15);
      expect(nrr).toBe(0); // Should return 0 when overs played is 0
    });
  });

  describe('extractPlayerPerformances', () => {
    test('should extract batting performances correctly', () => {
      const ocrData = {
        team1Batsmen: [
          { name: 'Player A', runs: 65, balls: 45, fours: 8, sixes: 2, strikeRate: 144.44 },
          { name: 'Player B', runs: 42, balls: 38, fours: 4, sixes: 1, strikeRate: 110.53 }
        ],
        team2Batsmen: [
          { name: 'Player C', runs: 38, balls: 42, fours: 3, sixes: 0, strikeRate: 90.48 }
        ]
      };

      const performances = matchOutcomeService.extractPlayerPerformances(ocrData);

      expect(performances.batting).toHaveLength(3);
      expect(performances.batting[0].playerName).toBe('Player A');
      expect(performances.batting[0].runs).toBe(65);
      expect(performances.batting[0].balls).toBe(45);
      expect(performances.batting[0].strikeRate).toBeCloseTo(144.44, 2);
    });

    test('should extract bowling performances correctly', () => {
      const ocrData = {
        team1Bowlers: [
          { name: 'Player D', overs: 4, runs: 28, wickets: 2, economy: 7.0 },
          { name: 'Player E', overs: 4, runs: 35, wickets: 1, economy: 8.75 }
        ],
        team2Bowlers: [
          { name: 'Player F', overs: 3.2, runs: 22, wickets: 3, economy: 6.6 }
        ]
      };

      const performances = matchOutcomeService.extractPlayerPerformances(ocrData);

      expect(performances.bowling).toHaveLength(3);
      expect(performances.bowling[0].playerName).toBe('Player D');
      expect(performances.bowling[0].overs).toBe(4);
      expect(performances.bowling[0].wickets).toBe(2);
      expect(performances.bowling[0].economy).toBeCloseTo(7.0, 1);
    });
  });

  describe('determineWinner', () => {
    test('should determine winner by runs correctly', () => {
      const team1Score = { runs: 180, wickets: 5 };
      const team2Score = { runs: 165, wickets: 8 };

      const result = matchOutcomeService.determineWinner(team1Score, team2Score);

      expect(result.winner).toBe('team1');
      expect(result.margin.type).toBe('runs');
      expect(result.margin.value).toBe(15);
    });

    test('should determine winner by wickets correctly', () => {
      const team1Score = { runs: 145, wickets: 10 };
      const team2Score = { runs: 146, wickets: 6 };

      const result = matchOutcomeService.determineWinner(team1Score, team2Score);

      expect(result.winner).toBe('team2');
      expect(result.margin.type).toBe('wickets');
      expect(result.margin.value).toBe(4);
    });

    test('should identify tie correctly', () => {
      const team1Score = { runs: 150, wickets: 8 };
      const team2Score = { runs: 150, wickets: 9 };

      const result = matchOutcomeService.determineWinner(team1Score, team2Score);

      expect(result.winner).toBe('tie');
      expect(result.isTie).toBe(true);
    });
  });

  describe('parseMarginFromText', () => {
    test('should parse runs margin correctly', () => {
      const texts = [
        'Team Alpha won by 15 runs',
        'Team Alpha won by 15 runs with 2 balls remaining',
        'Team Alpha beat Team Beta by 15 runs'
      ];

      texts.forEach(text => {
        const margin = matchOutcomeService.parseMarginFromText(text);
        expect(margin.type).toBe('runs');
        expect(margin.value).toBe(15);
      });
    });

    test('should parse wickets margin correctly', () => {
      const texts = [
        'Team Beta won by 4 wickets',
        'Team Beta won by 4 wickets with 8 balls remaining',
        'Team Beta beat Team Alpha by 4 wickets'
      ];

      texts.forEach(text => {
        const margin = matchOutcomeService.parseMarginFromText(text);
        expect(margin.type).toBe('wickets');
        expect(margin.value).toBe(4);
      });
    });

    test('should identify tie from text', () => {
      const texts = [
        'Match tied',
        'The match was tied',
        'Match ended in a tie'
      ];

      texts.forEach(text => {
        const margin = matchOutcomeService.parseMarginFromText(text);
        expect(margin.type).toBe('tie');
      });
    });

    test('should return unknown for unparseable text', () => {
      const text = 'Some random text without margin info';
      const margin = matchOutcomeService.parseMarginFromText(text);
      expect(margin.type).toBe('unknown');
    });
  });

  describe('getStats', () => {
    test('should return service statistics', () => {
      const stats = matchOutcomeService.getStats();
      
      expect(stats).toHaveProperty('totalCalculations');
      expect(stats).toHaveProperty('successfulCalculations');
      expect(stats).toHaveProperty('averageConfidence');
      expect(stats).toHaveProperty('lastCalculation');
      expect(typeof stats.totalCalculations).toBe('number');
    });
  });
});