/**
 * Player Name Matcher Utility
 * 
 * This utility provides functions to match player names from OCR results with the database
 * using the PlayerMatchingService.
 */

const PlayerMatchingService = require('../services/playerMatchingService');

// Initialize the player matching service
let playerMatchingService;
(async () => {
  try {
    playerMatchingService = new PlayerMatchingService();
    await playerMatchingService.initialize();
    console.log('Player matching service initialized successfully');
  } catch (error) {
    console.error('Failed to initialize player matching service:', error);
  }
})();

/**
 * Match player names from OCR results with database players
 * @param {Array<string>} playerNames - Array of player names from OCR
 * @param {Object} options - Matching options
 * @returns {Promise<Object>} - Matching results
 */
exports.matchPlayerNames = async (playerNames, options = {}) => {
  try {
    // Ensure service is initialized
    if (!playerMatchingService) {
      console.log('Player matching service not initialized, initializing now...');
      playerMatchingService = new PlayerMatchingService();
      await playerMatchingService.initialize();
    }

    // Match the batch of player names
    const matchResults = await playerMatchingService.matchMultiplePlayers(playerNames);
    
    console.log(`Matched ${matchResults.length} player names with database`);
    
    return {
      success: true,
      matches: matchResults,
      stats: playerMatchingService.getMatchingStats()
    };
  } catch (error) {
    console.error('Error matching player names:', error);
    return {
      success: false,
      error: error.message,
      matches: [],
      stats: {}
    };
  }
};

/**
 * Match a single player name from OCR with database players
 * @param {string} playerName - Player name from OCR
 * @param {Object} options - Matching options
 * @returns {Promise<Object>} - Matching result
 */
exports.matchSinglePlayerName = async (playerName, options = {}) => {
  try {
    // Ensure service is initialized
    if (!playerMatchingService) {
      console.log('Player matching service not initialized, initializing now...');
      playerMatchingService = new PlayerMatchingService();
      await playerMatchingService.initialize();
    }

    // Match the single player name
    const matchResult = await playerMatchingService.matchSinglePlayer(playerName);
    
    return {
      success: true,
      match: matchResult,
      stats: playerMatchingService.getMatchingStats()
    };
  } catch (error) {
    console.error('Error matching single player name:', error);
    return {
      success: false,
      error: error.message,
      match: null,
      stats: {}
    };
  }
};

/**
 * Confirm a manual player match and optionally create an alias
 * @param {string} ocrName - Original OCR player name
 * @param {string} playerId - Database player ID to match with
 * @param {boolean} createAlias - Whether to create an alias for future matching
 * @returns {Promise<Object>} - Confirmation result
 */
exports.confirmPlayerMatch = async (ocrName, playerId, createAlias = false) => {
  try {
    // Ensure service is initialized
    if (!playerMatchingService) {
      console.log('Player matching service not initialized, initializing now...');
      playerMatchingService = new PlayerMatchingService();
      await playerMatchingService.initialize();
    }

    // Confirm the match
    const result = await playerMatchingService.confirmMatch(ocrName, playerId, createAlias);
    
    return {
      success: true,
      result
    };
  } catch (error) {
    console.error('Error confirming player match:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = exports;