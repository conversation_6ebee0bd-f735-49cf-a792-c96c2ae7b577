/**
 * Test script for PaddleOCR normalizer
 */

const { normalizeInJS } = require('../utils/paddleNormalizer');
const fs = require('fs');
const path = require('path');

// Load the PaddleOCR output
const paddleOutputPath = path.join(__dirname, '..', 'uploads', 'paddle_scorecard1_output.json');
const paddleOutput = JSON.parse(fs.readFileSync(paddleOutputPath, 'utf8'));

// Normalize the output
const normalized = normalizeInJS(paddleOutput);

// Print stats
console.log('Normalization successful!');
console.log(`Original elements: ${paddleOutput.text_elements.length}`);
console.log(`Normalized elements: ${normalized.text_elements.length}`);

// Check for fixed names
const fixedNames = [
    'JAMES FAULKNER',
    'MATT POTTS',
    'ALI AKBAR',
    'BRENDON MCCULLUM',
    'NAVEEN-UL-HAQ MURID',
    'Player of the Match: <PERSON> Akbar'
];

console.log('\nChecking for fixed names:');
fixedNames.forEach(name => {
    const found = normalized.text_elements.some(el => el.text === name);
    console.log(`- ${name}: ${found ? 'Found' : 'Not found'}`);
});

// Check for (L) to (1) conversion
const originalL = paddleOutput.text_elements.some(el => el.text === '(L)');
const normalizedOne = normalized.text_elements.some(el => el.text === '(1)');
console.log(`\n(L) to (1) conversion: ${originalL && normalizedOne ? 'Fixed' : 'Not fixed'}`);

// Save the normalized output
const normalizedOutputPath = path.join(__dirname, '..', 'uploads', 'paddle_scorecard1_js_normalized.json');
fs.writeFileSync(normalizedOutputPath, JSON.stringify(normalized, null, 2), 'utf8');
console.log(`\nNormalized output saved to: ${normalizedOutputPath}`);