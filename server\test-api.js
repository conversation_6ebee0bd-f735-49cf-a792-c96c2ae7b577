const fs = require('fs');
const path = require('path');
const axios = require('axios');
const FormData = require('form-data');

async function testOcrComparisonApi() {
  try {
    // Find an image file in the server/uploads/temp directory
    const tempDir = path.join(__dirname, 'uploads/temp');
    const files = fs.readdirSync(tempDir);
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'].includes(ext);
    });

    if (imageFiles.length === 0) {
      console.error('No image files found in uploads/temp directory');
      return;
    }

    // Use the first image file
    const imagePath = path.join(tempDir, imageFiles[0]);
    console.log('Using image:', imagePath);

    // Create form data
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    formData.append('methods', JSON.stringify(['direct']));
    
    const settings = {
      det_db_thresh: 0.3,
      det_db_box_thresh: 0.4,
      det_db_unclip_ratio: 1.5,
      drop_score: 0.5
    };
    
    formData.append('settings', JSON.stringify(settings));

    // Send request to the API
    console.log('Sending request to API...');
    const response = await axios.post('http://localhost:5000/api/ocr-comparison/compare', formData, {
      headers: {
        ...formData.getHeaders(),
        'Accept': 'application/json'
      }
    });

    // Log the response
    console.log('API Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Error testing API:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Run the test
testOcrComparisonApi();
