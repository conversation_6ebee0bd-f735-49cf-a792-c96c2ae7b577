const GoogleVisionClient = require('../utils/googleVisionClient');
const path = require('path');
const fs = require('fs');

// Configuration constants
const CONFIG = {
  ROW_THRESHOLD: 20, // Pixel threshold for grouping elements into rows
  MIN_CONFIDENCE: 0.8, // Minimum confidence score for OCR results
  TEAM_PATTERNS: {
    PHOENIX: /PHOENIX/i,
    INVINCIBLES: /INVINCIBLES/i
  },
  SCORE_PATTERN: /^\d+-\d+$/,
  OVERS_PATTERN: /OVERS:\s*\d+(\.\d+)?/i,
  OUTPUT_DIR: '../ocr-output/processed',
  // Cricket-specific patterns
  CRICKET_PATTERNS: {
    RUNS: /^\d+$/,
    WICKETS: /^\d+$/,
    OVERS: /^\d+(\.\d+)?$/,
    EXTRAS: /^\(?(b\s*\d+,?\s*)?(lb\s*\d+,?\s*)?(w\s*\d+,?\s*)?(nb\s*\d+)?\)?$/i,
    PLAYER_SCORE: /^\d+\*?$/,  // Handles not out (*) notation
    BOWLING_FIGURES: /^\d+-\d+-\d+-\d+$/  // overs-maidens-runs-wickets
  },
  // Cricket-specific validation ranges
  VALIDATION_RANGES: {
    MAX_RUNS_PER_INNINGS: 500,
    MAX_WICKETS: 10,
    MAX_OVERS: 50,
    MAX_INDIVIDUAL_SCORE: 300,
    MAX_BOWLING_OVERS: 10
  },
  // Common cricket terms for text normalization
  CRICKET_TERMS: {
    DISMISSALS: ['c', 'b', 'lbw', 'run out', 'st', 'hit wicket', 'caught', 'bowled'],
    EXTRAS: ['b', 'lb', 'w', 'nb', 'byes', 'leg byes', 'wides', 'no balls'],
    STATS: ['runs', 'wickets', 'overs', 'extras', 'total']
  }
};

/**
 * Custom error class for OCR-related errors
 */
class OCRError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = 'OCRError';
    this.details = details;
  }
}

/**
 * Google Vision OCR Service
 * Used as a fallback and verification mechanism for OCR.space and PaddleOCR
 */
class GoogleVisionService {
  constructor() {
    this.visionClient = new GoogleVisionClient();
  }

  /**
   * Validates if two elements should be joined based on cricket-specific rules
   * @param {Object} elem1 - First element
   * @param {Object} elem2 - Second element
   * @returns {boolean} - Whether elements should be joined
   */
  isCricketSpecificJoin(elem1, elem2) {
    const combinedText = `${elem1.text}${elem2.text}`;
    
    // Check for common cricket patterns that might be split
    return (
      // Bowling figures (e.g., "4" + "-2-15-3")
      CONFIG.CRICKET_PATTERNS.BOWLING_FIGURES.test(combinedText) ||
      // Extras notation (e.g., "(b 4" + ", lb 2)")
      CONFIG.CRICKET_PATTERNS.EXTRAS.test(combinedText) ||
      // Player dismissal (e.g., "c Smith" + "b Jones")
      this.isDismissalNotation(combinedText)
    );
  }

  /**
   * Checks if text matches cricket dismissal notation
   * @param {string} text - Text to check
   * @returns {boolean} - Whether text is dismissal notation
   */
  isDismissalNotation(text) {
    const dismissalTerms = CONFIG.CRICKET_TERMS.DISMISSALS;
    return dismissalTerms.some(term => text.toLowerCase().includes(term));
  }

  /**
   * Validates cricket-specific data
   * @param {Object} data - Extracted cricket data
   * @returns {Object} - Validated cricket data
   * @throws {OCRError} - If validation fails
   */
  validateCricketData(data) {
    const ranges = CONFIG.VALIDATION_RANGES;

    // Validate runs
  }

  /**
   * Initialize cricket data structure
   * @returns {Object} - Empty cricket data structure
   */
  initializeCricketData() {
    return {
      success: false,
      team1: '',
      team2: '',
      team1Score: { runs: 0, wickets: 0, overs: 0 },
      team2Score: { runs: 0, wickets: 0, overs: 0 },
      team1Batsmen: [],
      team2Batsmen: [],
      team1Bowlers: [],
      team2Bowlers: [],
      venue: '',
      playerOfMatch: '',
      rawText: '',
      extractionMethod: '',
      confidence: 0
    };
  }

  /**
   * Extract cricket data from OCR results
   * @param {Object} ocrResults - OCR results from Google Vision
   * @returns {Object} - Extracted cricket data
   */
  extractCricketData(ocrResults) {
    try {
      // Extract text elements with coordinates
      const textElements = this.extractTextElements(ocrResults);
      const fullText = textElements.map(element => element.text).join(' ');

      // Initialize cricket data structure
      const cricketData = this.initializeCricketData();

      // Extract team information using coordinate analysis
      const teamInfo = this.extractTeamsFromCoordinates(textElements);

      cricketData.team1 = teamInfo.team1;
      cricketData.team2 = teamInfo.team2;

      // Extract scores using position-based analysis
      const scores = this.extractScoresFromCoordinates(textElements);
      cricketData.team1Score = scores.team1Score;
      cricketData.team2Score = scores.team2Score;

      // Extract player statistics using spatial analysis
      const playerStats = this.extractPlayerStatsFromCoordinates(textElements, teamInfo);
      cricketData.team1Batsmen = playerStats.team1Batsmen;
      cricketData.team2Batsmen = playerStats.team2Batsmen;
      cricketData.team1Bowlers = playerStats.team1Bowlers;
      cricketData.team2Bowlers = playerStats.team2Bowlers;

      // Extract other information
      cricketData.venue = this.extractVenueFromCoordinates(textElements);
      cricketData.playerOfMatch = this.extractPlayerOfMatchFromCoordinates(textElements);

      // Add metadata
      cricketData.rawText = fullText;
      cricketData.extractionMethod = 'google-vision-coordinate';
      cricketData.confidence = this.calculateExtractionConfidence(textElements, teamInfo, scores, playerStats);

      // Filter and sort elements by coordinates
      const validElements = textElements.filter(el => 
        el && el.boundingBox && el.boundingBox.topLeft && 
        typeof el.boundingBox.topLeft.x === 'number' && 
        typeof el.boundingBox.topLeft.y === 'number'
      );

      if (validElements.length === 0) {
        throw new Error('No valid text elements with coordinates found');
      }

      // Sort by y-coordinate first, then x-coordinate
      validElements.sort((a, b) => {
        const yDiff = Math.abs(a.boundingBox.topLeft.y - b.boundingBox.topLeft.y);
        return yDiff < CONFIG.ROW_THRESHOLD ? 
          a.boundingBox.topLeft.x - b.boundingBox.topLeft.x : 
          a.boundingBox.topLeft.y - b.boundingBox.topLeft.y;
      });

      // Group elements by approximate rows
      const rows = this.groupElementsByVerticalPosition(validElements);

      // Process each row to find player data
      rows.forEach(row => {
        this.processRowForPlayerData(row, cricketData);
      });

      // Post-process the extracted data
      this.postProcessCricketData(cricketData);

      return cricketData;
    } catch (error) {
      console.error('Error in extractCricketData:', error);
      throw error;
    }
  }

  /**
   * Calculate confidence score for the extraction
   * @param {Array} textElements - Extracted text elements
   * @param {Object} teamInfo - Team information
   * @param {Object} scores - Score information
   * @param {Object} playerStats - Player statistics
   * @returns {number} - Confidence score between 0 and 1
   */
  calculateExtractionConfidence(textElements, teamInfo, scores, playerStats) {
    let confidence = 0;
    let totalChecks = 0;

    // Check team names
    if (teamInfo.team1 && teamInfo.team2) {
      confidence += 1;
    }
    totalChecks += 1;

    // Check scores
    if (scores.team1Score.runs > 0 && scores.team2Score.runs > 0) {
      confidence += 1;
    }
    totalChecks += 1;

    // Check player stats
    if (playerStats.team1Batsmen.length > 0 && playerStats.team2Batsmen.length > 0) {
      confidence += 1;
    }
    if (playerStats.team1Bowlers.length > 0 && playerStats.team2Bowlers.length > 0) {
      confidence += 1;
    }
    totalChecks += 2;

    return confidence / totalChecks;
  }

  /**
   * Process a row of text elements for player data
   * @param {Array} row - Array of text elements in a row
   * @param {Object} cricketData - Cricket data object to update
   */
  processRowForPlayerData(row, cricketData) {
    const rowText = row.map(el => el.text).join(' ').toLowerCase();

    // Skip header rows and empty rows
    if (!rowText || rowText.match(/^(batting|bowling|bowler|innings|total|extras)/i)) {
      return;
    }

    // Process based on context (batting or bowling)
    if (this.isBattingRow(rowText)) {
      this.processBattingRow(row, cricketData);
    } else if (this.isBowlingRow(rowText)) {
      this.processBowlingRow(row, cricketData);
    }
  }

  /**
   * Post-process extracted cricket data
   * @param {Object} cricketData - Cricket data to process
   */
  postProcessCricketData(cricketData) {
    // Remove duplicate players
    cricketData.team1Batsmen = this.removeDuplicatePlayers(cricketData.team1Batsmen);
    cricketData.team2Batsmen = this.removeDuplicatePlayers(cricketData.team2Batsmen);
    cricketData.team1Bowlers = this.removeDuplicatePlayers(cricketData.team1Bowlers);
    cricketData.team2Bowlers = this.removeDuplicatePlayers(cricketData.team2Bowlers);

    // Sort batsmen by runs (descending)
    cricketData.team1Batsmen.sort((a, b) => b.runs - a.runs);
    cricketData.team2Batsmen.sort((a, b) => b.runs - a.runs);

    // Sort bowlers by wickets (descending) and then economy rate
    cricketData.team1Bowlers.sort((a, b) => b.wickets - a.wickets || this.calculateEconomy(a) - this.calculateEconomy(b));
    cricketData.team2Bowlers.sort((a, b) => b.wickets - a.wickets || this.calculateEconomy(a) - this.calculateEconomy(b));
  }

  /**
   * Calculate economy rate for a bowler
   * @param {Object} bowler - Bowler statistics
   * @returns {number} - Economy rate
   */
  calculateEconomy(bowler) {
    return bowler.overs > 0 ? bowler.runs / bowler.overs : 999;
  }

  /**
   * Remove duplicate players from an array
   * @param {Array} players - Array of player objects
   * @returns {Array} - Array with duplicates removed
   */
  removeDuplicatePlayers(players) {
    return players.filter((player, index, self) =>
      index === self.findIndex(p => p.name === player.name)
    );
  }

  /**
   * Check if a row contains batting information
   * @param {string} rowText - Text content of the row
   * @returns {boolean} - Whether the row contains batting information
   */
  isBattingRow(rowText) {
    // Check for typical batting row patterns
    return (
      rowText.match(/\d+\s*\(\d+\)/) || // Runs (balls)
      rowText.match(/c\s+[a-z]+\s+b\s+[a-z]+/i) || // Caught and bowled
      rowText.match(/lbw\s+b\s+[a-z]+/i) || // LBW
      rowText.match(/b\s+[a-z]+/i) // Bowled
    );
  }

  /**
   * Check if a row contains bowling information
   * @param {string} rowText - Text content of the row
   * @returns {boolean} - Whether the row contains bowling information
   */
  isBowlingRow(rowText) {
    // Check for typical bowling row patterns (overs-maidens-runs-wickets)
    return rowText.match(/\d+(?:\.\d+)?-\d+-\d+-\d+/);
  }

  /**
   * Process a batting row and update cricket data
   * @param {Array} row - Array of text elements in the row
   * @param {Object} cricketData - Cricket data to update
   */
  processBattingRow(row, cricketData) {
    const sortedElements = row.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
    const playerName = sortedElements[0]?.text || '';
    const stats = sortedElements.slice(1).map(el => el.text).join(' ');

    // Extract runs and balls
    const runsMatch = stats.match(/(\d+)\s*\((\d+)\)/);
    const runs = runsMatch ? parseInt(runsMatch[1]) : 0;
    const balls = runsMatch ? parseInt(runsMatch[2]) : 0;

    const batsmanData = {
      name: playerName,
      runs,
      balls,
      dismissal: this.extractDismissalInfo(stats)
    };

    // Determine which team's batsman based on position in the document
    if (row[0].boundingBox.topLeft.y < cricketData.team2Score.y) {
      cricketData.team1Batsmen.push(batsmanData);
    } else {
      cricketData.team2Batsmen.push(batsmanData);
    }
  }

  /**
   * Process a bowling row and update cricket data
   * @param {Array} row - Array of text elements in the row
   * @param {Object} cricketData - Cricket data to update
   */
  processBowlingRow(row, cricketData) {
    const sortedElements = row.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
    const bowlerName = sortedElements[0]?.text || '';
    const stats = sortedElements.slice(1).map(el => el.text).join(' ');

    // Extract bowling figures (overs-maidens-runs-wickets)
    const figuresMatch = stats.match(/(\d+(?:\.\d+)?)-?(\d+)?-?(\d+)?-?(\d+)?/);
    if (!figuresMatch) return;

    const bowlerData = {
      name: bowlerName,
      overs: parseFloat(figuresMatch[1]) || 0,
      maidens: parseInt(figuresMatch[2]) || 0,
      runs: parseInt(figuresMatch[3]) || 0,
      wickets: parseInt(figuresMatch[4]) || 0
    };

    // Determine which team's bowler based on position in the document
    if (row[0].boundingBox.topLeft.y < cricketData.team2Score.y) {
      cricketData.team2Bowlers.push(bowlerData);
    } else {
      cricketData.team1Bowlers.push(bowlerData);
    }
  }

  validateCricketData(data) {
    const ranges = CONFIG.VALIDATION_RANGES;

    // Validate runs
    if (data.runs && data.runs > ranges.MAX_RUNS_PER_INNINGS) {
      throw new OCRError('Invalid runs value', { value: data.runs, max: ranges.MAX_RUNS_PER_INNINGS });
    }

    // Validate wickets
    if (data.wickets && data.wickets > ranges.MAX_WICKETS) {
      throw new OCRError('Invalid wickets value', { value: data.wickets, max: ranges.MAX_WICKETS });
    }

    // Validate overs
    if (data.overs) {
      const [overs, balls] = data.overs.toString().split('.');
      if (parseInt(overs) > ranges.MAX_OVERS || (balls && parseInt(balls) >= 6)) {
        throw new OCRError('Invalid overs value', { value: data.overs, max: ranges.MAX_OVERS });
      }
    }

    // Validate individual scores
    if (data.scores) {
      data.scores.forEach(score => {
        if (score > ranges.MAX_INDIVIDUAL_SCORE) {
          throw new OCRError('Invalid individual score', { value: score, max: ranges.MAX_INDIVIDUAL_SCORE });
        }
      });
    }

    return data;
  }

  /**
   * Process an image with Google Cloud Vision and extract cricket data
   * @param {string} imagePath - Path to the image file
   * @returns {Promise<Object>} - Extracted cricket data
   * @throws {OCRError} - If OCR processing fails
   */
  async processImage(imagePath) {
    if (!fs.existsSync(imagePath)) {
      throw new OCRError('Image file not found', { imagePath });
    }

    const startTime = Date.now();
    console.log(`[GoogleVision] Processing image: ${imagePath}`);

    try {
      // Get OCR results from Google Vision
      const visionResults = await this.visionClient.detectText(imagePath);
      
      // Validate OCR results structure
      if (!visionResults) {
        throw new OCRError('No results returned from Google Vision');
      }
      
      // Validate and log full text annotation
      const hasFullText = !!visionResults?.fullTextAnnotation?.pages && Array.isArray(visionResults.fullTextAnnotation.pages);
      const fullTextPages = hasFullText ? visionResults.fullTextAnnotation.pages.length : 0;
      console.log(`[GoogleVision] Full text annotation: ${hasFullText ? 'present' : 'missing'}, pages: ${fullTextPages}`);
      
      // Validate and log text annotations
      const hasTextAnnotations = !!visionResults?.textAnnotations && Array.isArray(visionResults.textAnnotations) && visionResults.textAnnotations.length > 0;
      const textElementCount = hasTextAnnotations ? visionResults.textAnnotations.length : 0;
      console.log(`[GoogleVision] Text annotations: ${hasTextAnnotations ? 'present' : 'missing'}, elements: ${textElementCount}`);

      // Validate and log text elements
      const hasTextElements = !!visionResults?.textElements && Array.isArray(visionResults.textElements) && visionResults.textElements.length > 0;
      const textElements = hasTextElements ? visionResults.textElements : [];
      console.log(`[GoogleVision] Text elements: ${hasTextElements ? 'present' : 'missing'}, count: ${textElements.length}`);
      
      if (!hasFullText && !hasTextAnnotations && !hasTextElements) {
        throw new OCRError('Missing required OCR data structures', {
          hasFullText,
          hasTextAnnotations,
          hasTextElements,
          fullTextPages,
          textElementCount,
          textElementsCount: textElements.length,
          availableKeys: Object.keys(visionResults)
        });
      }
      
      // Validate text content
      if (hasTextAnnotations && textElementCount < 2) { // First element is usually the complete text
        throw new OCRError('Insufficient text elements detected', {
          textElementCount,
          minRequired: 2
        });
      }

      // Extract cricket data from OCR results
      const cricketData = this.extractCricketData(visionResults);
      
      // Validate and enhance extracted data
      this.validateAndEnhanceData(cricketData);
      
      // Save results to file
      const outputPath = this.saveResults(imagePath, cricketData);
      
      const processingTime = Date.now() - startTime;
      console.log(`[GoogleVision] Processing completed in ${processingTime}ms`, {
        success: cricketData.success,
        ocrStatus: cricketData.ocrStatus,
        extractedBatsmen: cricketData.team1Batsmen.length + cricketData.team2Batsmen.length,
        processingTime
      });
      
      return cricketData;
    } catch (error) {
      const errorDetails = {
        imagePath,
        processingTime: Date.now() - startTime,
        errorType: error.name,
        errorMessage: error.message
      };
      
      console.error('[GoogleVision] Processing failed:', errorDetails);
      
      if (error instanceof OCRError) {
        throw error;
      }
      throw new OCRError('Google Vision processing failed', errorDetails);
    }
  }

  /**
   * Validate and enhance the extracted cricket data
   * @param {Object} cricketData - The cricket data to validate and enhance
   */
  validateAndEnhanceData(cricketData) {
    // Validate essential data
    const hasTeams = cricketData.team1 && cricketData.team2;
    const hasScores = (cricketData.team1Score && cricketData.team1Score.runs >= 0) || 
                     (cricketData.team2Score && cricketData.team2Score.runs >= 0);
    const hasPlayers = (cricketData.team1Batsmen && cricketData.team1Batsmen.length > 0) || 
                      (cricketData.team2Batsmen && cricketData.team2Batsmen.length > 0);
    
    // Update success and status
    cricketData.success = hasTeams && hasScores && hasPlayers;
    cricketData.ocrStatus = cricketData.success ? 'success' : 'failed';
    cricketData.ocrMessage = cricketData.success 
      ? 'Successfully processed with Google Vision API'
      : `Failed to extract cricket data: ${!hasTeams ? 'missing teams' : !hasScores ? 'missing scores' : 'missing players'}`;
    cricketData.engine = 'Google Vision';
    cricketData.processedAt = new Date().toISOString();
  }

  /**
   * Save the processed results to a file
   * @param {string} imagePath - Original image path
   * @param {Object} cricketData - Processed cricket data
   * @returns {string} - Path to the saved file
   */
  saveResults(imagePath, cricketData) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const imageBasename = path.basename(imagePath, path.extname(imagePath));
    const outputPath = path.join(__dirname, CONFIG.OUTPUT_DIR, `${timestamp}_${imageBasename}_google_cricket.json`);
    
    fs.writeFileSync(outputPath, JSON.stringify(cricketData, null, 2));
    console.log(`[GoogleVision] Results saved to: ${outputPath}`);
    
    return outputPath;
  }
  
  /**
   * Extract cricket data from OCR results
   * @param {Object} ocrResults - OCR results from Google Vision
   * @returns {Object} - Extracted cricket data
   * @throws {OCRError} - If text extraction fails
   */
  /**
   * Initialize the cricket data structure
   * @returns {Object} - Empty cricket data structure
   */
  initializeCricketData() {
    return {
      success: false,
      team1: '',
      team2: '',
      venue: '',
      team1Score: { runs: 0, wickets: 0, overs: 0 },
      team2Score: { runs: 0, wickets: 0, overs: 0 },
      playerOfMatch: '',
      team1Batsmen: [],
      team2Batsmen: [],
      team1Bowlers: [],
      team2Bowlers: [],
      extractionMethod: 'google-vision',
      confidence: 'medium',
      metadata: {
        processedAt: new Date().toISOString(),
        version: '2.0.0'
      }
    };
  }

  /**
   * Extract and normalize text elements from OCR results
   * @param {Object} ocrResults - OCR results from Google Vision
   * @returns {Array} - Normalized text elements
   */
  extractTextElements(ocrResults) {
    let textElements = [];

    try {
      // Validate OCR results structure
      if (!ocrResults || typeof ocrResults !== 'object') {
        throw new OCRError('Invalid OCR results object', {
          type: typeof ocrResults,
          value: ocrResults
        });
      }

      // Try textElements first if available
      if (Array.isArray(ocrResults.textElements) && ocrResults.textElements.length > 0) {
        console.log('[GoogleVision] Using provided textElements');
        textElements = ocrResults.textElements
          .filter(element => {
            // Validate required properties
            if (!element || typeof element !== 'object') return false;
            if (!element.text || typeof element.text !== 'string') return false;
            if (!element.boundingBox || !element.boundingBox.topLeft) return false;
            if (typeof element.boundingBox.topLeft.x !== 'number' || 
                typeof element.boundingBox.topLeft.y !== 'number') return false;
            return true;
          })
          .map(element => ({
            text: element.text.trim(),
            confidence: element.confidence || CONFIG.MIN_CONFIDENCE,
            boundingBox: element.boundingBox,
            type: 'element'
          }));
      }
      // Try fullTextAnnotation next
      else if (ocrResults.fullTextAnnotation?.pages) {
        console.log('[GoogleVision] Extracting from fullTextAnnotation');
        textElements = this.extractFromFullText(ocrResults.fullTextAnnotation);
      }
      // Fall back to textAnnotations if other methods failed or are not available
      else if (Array.isArray(ocrResults.textAnnotations) && ocrResults.textAnnotations.length > 0) {
        console.log('[GoogleVision] Falling back to textAnnotations');
        textElements = this.extractFromTextAnnotations(ocrResults.textAnnotations);
      } else {
        throw new OCRError('Invalid OCR results format', {
          hasTextElements: Array.isArray(ocrResults.textElements),
          textElementsLength: ocrResults.textElements?.length,
          hasFullText: !!ocrResults.fullTextAnnotation,
          hasPages: !!ocrResults.fullTextAnnotation?.pages,
          hasTextAnnotations: Array.isArray(ocrResults.textAnnotations),
          textAnnotationsLength: ocrResults.textAnnotations?.length
        });
      }

      // Validate extracted elements
      if (!Array.isArray(textElements) || textElements.length === 0) {
        throw new OCRError('No text elements extracted', {
          elementsType: typeof textElements,
          isArray: Array.isArray(textElements),
          length: textElements?.length
        });
      }

      console.log(`[GoogleVision] Successfully extracted ${textElements.length} text elements`);
      return this.normalizeTextElements(textElements);
    } catch (error) {
      throw new OCRError('Failed to extract text elements', {
        cause: error,
        method: error.message.includes('fullTextAnnotation') ? 'fullTextAnnotation' : 'textAnnotations'
      });
    }
  }

  /**
   * Extract teams from text elements using coordinate analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {Object} - Team information
   */
  extractTeamsFromCoordinates(textElements) {
    // Filter and sort elements by y-coordinate
    const validElements = textElements
      .filter(el => el.boundingBox && el.boundingBox.topLeft && el.text && typeof el.text === 'string')
      .sort((a, b) => a.boundingBox.topLeft.y - b.boundingBox.topLeft.y);

    // Check if we have any valid elements
    if (!validElements.length) {
      return { team1: '', team2: '' };
    }

    // Look at top 15 elements for better coverage
    const topElements = validElements.slice(0, 15);

    // Group elements by similar y-coordinates
    const groups = [];
    let currentGroup = [topElements[0]];

    for (let i = 1; i < topElements.length; i++) {
      const current = topElements[i];
      const previous = topElements[i - 1];
      const yDiff = Math.abs(current.boundingBox.topLeft.y - previous.boundingBox.topLeft.y);

      if (yDiff < CONFIG.ROW_THRESHOLD) {
        currentGroup.push(current);
      } else {
        groups.push(currentGroup);
        currentGroup = [current];
      }
    }
    groups.push(currentGroup);

    // Process each group to find team names
    const potentialTeams = groups.map(group => {
      // Sort by x-coordinate within group
      group.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
      
      // Join text within group
      const text = group
        .map(el => el.text.trim())
        .filter(text => 
          text.length > 2 && // Team names are usually longer
          !text.match(/^\d+$/) && // Exclude pure numbers
          !text.match(/^(at|in|on|vs\.?|venue|ground|stadium|match|innings|total|score|overs|wickets)$/i) // Exclude common words
        )
        .join(' ');

      return text.length > 0 ? text : null;
    }).filter(Boolean);

    // Find the first two valid team names
    const teams = potentialTeams
      .filter(team => team.length >= 3 && !team.match(/^[\d\s]*$/));

    return {
      team1: teams[0] || '',
      team2: teams[1] || ''
    };
  }

  /**
   * Extract scores using coordinate-based analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {Object} - Score information
   */
  extractScoresFromCoordinates(textElements) {
    // Group elements by their vertical position (similar y-coordinates)
    const yGroups = this.groupElementsByVerticalPosition(textElements);

    // Look for score patterns in each group
    const scorePatterns = yGroups.map(group => {
      // Sort elements within group by x-coordinate
      group.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
      
      // Join text and look for various score patterns
      const text = group.map(el => el.text.trim()).join(' ');
      
      // Try different score patterns
      const patterns = [
        // Pattern: "123-4" or "123/4"
        /(?:^|\s)(\d+)[-\/](\d+)(?:\s|$)/,
        // Pattern: "123 all out"
        /(?:^|\s)(\d+)\s+(?:all\s*out|a\.?o\.?)(?:\s|$)/i,
        // Pattern: "123 runs"
        /(?:^|\s)(\d+)\s+(?:runs?)(?:\s|$)/i
      ];

      for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) {
          const runs = parseInt(match[1]);
          const wickets = pattern === patterns[0] ? parseInt(match[2]) : 10;
          
          // Validate the extracted values
          if (runs >= 0 && runs <= 1000 && wickets >= 0 && wickets <= 10) {
            return {
              runs,
              wickets,
              y: group[0].boundingBox.topLeft.y,
              elements: group,
              text
            };
          }
        }
      }
      return null;
    }).filter(Boolean);

    // Sort by vertical position and validate scores
    scorePatterns.sort((a, b) => a.y - b.y);

    // Extract overs for valid scores
    const team1Overs = scorePatterns[0] ? this.extractOversNearScore(textElements, scorePatterns[0]) : 0;
    const team2Overs = scorePatterns[1] ? this.extractOversNearScore(textElements, scorePatterns[1]) : 0;

    return {
      team1Score: {
        runs: scorePatterns[0]?.runs || 0,
        wickets: scorePatterns[0]?.wickets || 0,
        overs: team1Overs
      },
      team2Score: {
        runs: scorePatterns[1]?.runs || 0,
        wickets: scorePatterns[1]?.wickets || 0,
        overs: team2Overs
      }
    };
  }

  /**
   * Extract player statistics using coordinate-based analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @param {Object} teamInfo - Team information
   * @returns {Object} - Player statistics
   */
  extractPlayerStatsFromCoordinates(textElements, teamInfo) {
    // Group elements into rows based on y-coordinates
    const rows = this.groupElementsByVerticalPosition(textElements);

    // Find batting and bowling sections using team names as markers
    const sections = this.identifyStatsSections(rows, teamInfo);

    return {
      team1Batsmen: this.extractBatsmenStats(sections.team1Batting),
      team2Batsmen: this.extractBatsmenStats(sections.team2Batting),
      team1Bowlers: this.extractBowlerStats(sections.team1Bowling),
      team2Bowlers: this.extractBowlerStats(sections.team2Bowling)
    };
  }

  /**
   * Group elements by their vertical position
   * @param {Array} elements - Array of text elements
   * @returns {Array} - Grouped elements
   */
  groupElementsByVerticalPosition(elements) {
    const ROW_THRESHOLD = 5; // pixels
    const groups = [];
    let currentGroup = [];

    // Sort elements by y-coordinate
    const sorted = elements
      .filter(el => el.boundingBox)
      .sort((a, b) => a.boundingBox.topLeft.y - b.boundingBox.topLeft.y);

    sorted.forEach(element => {
      if (currentGroup.length === 0) {
        currentGroup.push(element);
      } else {
        const lastElement = currentGroup[currentGroup.length - 1];
        const yDiff = Math.abs(element.boundingBox.topLeft.y - lastElement.boundingBox.topLeft.y);

        if (yDiff <= ROW_THRESHOLD) {
          currentGroup.push(element);
        } else {
          groups.push([...currentGroup]);
          currentGroup = [element];
        }
      }
    });

    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    return groups;
  }

  /**
   * Extract overs information near a score
   * @param {Array} textElements - All text elements
   * @param {Object} scorePattern - Score pattern with position info
   * @returns {number} - Number of overs
   */
  extractOversNearScore(textElements, scorePattern) {
    if (!scorePattern || !scorePattern.elements || !scorePattern.elements[0]) return 0;

    const scoreElement = scorePattern.elements[0];
    if (!scoreElement.boundingBox) return 0;

    // Look for elements near the score
    const nearbyElements = textElements.filter(el => {
      if (!el.boundingBox) return false;

      // Check if element is on the same line or just below/above
      const yDiff = Math.abs(
        el.boundingBox.topLeft.y - 
        scoreElement.boundingBox.topLeft.y
      );
      
      // Check if element is to the right of the score
      const xDiff = el.boundingBox.topLeft.x - 
                    (scoreElement.boundingBox.topLeft.x + scoreElement.boundingBox.width);

      // Elements should be within 100 pixels horizontally after the score
      // and within 15 pixels vertically (same line or adjacent)
      return xDiff >= 0 && xDiff < 100 && yDiff < 15;
    });

    // Sort by distance from score
    nearbyElements.sort((a, b) => {
      const aXDiff = a.boundingBox.topLeft.x - scoreElement.boundingBox.topLeft.x;
      const bXDiff = b.boundingBox.topLeft.x - scoreElement.boundingBox.topLeft.x;
      return aXDiff - bXDiff;
    });

    // Look for various over patterns
    const patterns = [
      /(\d+(?:\.\d+)?)\s*(?:ov|overs|ovs)(?:\s|$)/i,  // "20.4 ov" or "20.4 overs"
      /\((\d+(?:\.\d+)?)\)/,  // "(20.4)"
      /\b(\d+(?:\.\d+)?)\b/   // Just the number, if it's right after the score
    ];

    for (const el of nearbyElements) {
      for (const pattern of patterns) {
        const match = el.text.match(pattern);
        if (match) {
          const overs = parseFloat(match[1]);
          // Validate overs (typically between 0 and 50)
          if (overs >= 0 && overs <= 50) {
            return overs;
          }
        }
      }
    }

    return 0;
  }

  /**
   * Identify batting and bowling sections using team names
   * @param {Array} rows - Grouped text elements by row
   * @param {Object} teamInfo - Team information
   * @returns {Object} - Sections for batting and bowling
   */
  identifyStatsSections(rows, teamInfo) {
    let sections = {
      team1Batting: [],
      team2Batting: [],
      team1Bowling: [],
      team2Bowling: []
    };

    let currentSection = null;
    let team1Found = false;
    let team2Found = false;

    rows.forEach(row => {
      const rowText = row.map(el => el.text).join(' ').toLowerCase();
      
      // Check for team names or batting/bowling headers
      if (rowText.includes(teamInfo.team1.toLowerCase())) {
        team1Found = true;
        currentSection = rowText.includes('bowling') ? 'team1Bowling' : 'team1Batting';
      } else if (rowText.includes(teamInfo.team2.toLowerCase())) {
        team2Found = true;
        currentSection = rowText.includes('bowling') ? 'team2Bowling' : 'team2Batting';
      } else if (rowText.match(/bowling|bowler/i)) {
        if (team1Found && !team2Found) {
          currentSection = 'team2Bowling';
        } else {
          currentSection = 'team1Bowling';
        }
      }

      if (currentSection && !rowText.match(/^(batting|bowling|bowler|innings)/i)) {
        sections[currentSection].push(row);
      }
    });

    return sections;
  }

  /**
   * Extract batsmen statistics from rows
   * @param {Array} rows - Array of rows containing batsmen data
   * @returns {Array} - Extracted batsmen statistics
   */
  extractBatsmenStats(rows) {
    return rows.map(row => {
      // Filter out elements without valid bounding boxes and sort by x-coordinate
      const sortedElements = row
        .filter(el => el.boundingBox && el.text.trim().length > 0)
        .sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);

      if (sortedElements.length < 2) return null; // Need at least name and runs

      // Extract player name (first element)
      const playerName = sortedElements[0].text.trim();
      if (playerName.match(/^(extras|total|sundries|byes|leg\s+byes|wide|no\s+ball)/i)) return null;

      // Join remaining elements for stats
      const stats = sortedElements.slice(1).map(el => el.text.trim()).join(' ');

      // Look for runs and balls patterns
      const patterns = [
        /(\d+)\s*\((\d+)\)/, // "45 (32)"
        /(\d+)\s+runs?\s*\((\d+)\)/, // "45 runs (32)"
        /(\d+)\s*\((\d+)\s+balls?\)/, // "45 (32 balls)"
        /^(\d+)(?!\.)\b/ // Just the runs, if no balls info
      ];

      let runs = 0, balls = 0;
      for (const pattern of patterns) {
        const match = stats.match(pattern);
        if (match) {
          runs = parseInt(match[1]);
          balls = match[2] ? parseInt(match[2]) : 0;
          break;
        }
      }

      // Validate runs and balls
      if (runs < 0 || runs > 500 || balls < 0 || balls > 300) return null;

      return {
        name: playerName,
        runs,
        balls,
        dismissal: this.extractDismissalInfo(stats)
      };
    }).filter(Boolean);
  }

  /**
   * Extract bowler statistics from rows
   * @param {Array} rows - Array of rows containing bowler data
   * @returns {Array} - Extracted bowler statistics
   */
  extractBowlerStats(rows) {
    return rows.map(row => {
      // Filter out elements without valid bounding boxes and sort by x-coordinate
      const sortedElements = row
        .filter(el => el.boundingBox && el.text.trim().length > 0)
        .sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);

      if (sortedElements.length < 2) return null; // Need at least name and stats

      // Extract bowler name (first element)
      const bowlerName = sortedElements[0].text.trim();
      if (!bowlerName || bowlerName.match(/^(total|extras|bowling)/i)) return null;

      // Join remaining elements for stats
      const stats = sortedElements.slice(1).map(el => el.text.trim()).join(' ');

      // Try different bowling stats patterns
      const patterns = [
        /(\d+(?:\.\d+)?)\s*-\s*(\d+)\s*-\s*(\d+)\s*-\s*(\d+)/, // "4.2-0-23-2"
        /(\d+(?:\.\d+)?)\s+(?:ov|overs)\s*(\d+)\s*(?:m|md)\s*(\d+)\s*(?:r|runs)\s*(\d+)\s*(?:w|wkt)/, // "4.2 ov 0 m 23 r 2 w"
        /(\d+(?:\.\d+)?)\s+(\d+)\s+(\d+)\s+(\d+)/ // "4.2 0 23 2"
      ];

      let overs = 0, maidens = 0, runs = 0, wickets = 0;
      for (const pattern of patterns) {
        const match = stats.match(pattern);
        if (match) {
          overs = parseFloat(match[1]);
          maidens = parseInt(match[2]);
          runs = parseInt(match[3]);
          wickets = parseInt(match[4]);
          break;
        }
      }

      // Validate bowling figures
      if (overs < 0 || overs > 50 || maidens < 0 || maidens > 50 ||
          runs < 0 || runs > 200 || wickets < 0 || wickets > 10) return null;

      return {
        name: bowlerName,
        overs,
        maidens,
        runs,
        wickets
      };
    }).filter(Boolean);
  }

  /**
   * Extract venue information using coordinate analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {string} - Extracted venue name
   */
  extractVenueFromCoordinates(textElements) {
    // Look for venue patterns in the top portion of the image
    const topElements = textElements.filter(el => {
      if (!el.boundingBox) return false;
      // Consider elements in top 20% of the image
      return el.boundingBox.topLeft.y < textElements[0].boundingBox.topLeft.y + 200;
    });

    // Sort by y-coordinate to process from top to bottom
    topElements.sort((a, b) => a.boundingBox.topLeft.y - b.boundingBox.topLeft.y);

    // Look for venue indicators
    for (let i = 0; i < topElements.length; i++) {
      const text = topElements[i].text.toLowerCase();
      if (text.includes('at ') || text.includes('venue:') || text.match(/stadium|ground|arena|oval|park/i)) {
        // If venue indicator found, combine with nearby text
        const nearbyElements = topElements.filter(el => {
          const yDiff = Math.abs(el.boundingBox.topLeft.y - topElements[i].boundingBox.topLeft.y);
          return yDiff < 10; // Elements on same line
        });
        
        // Sort by x-coordinate and combine
        nearbyElements.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
        const venueText = nearbyElements.map(el => el.text).join(' ');
        
        // Clean up venue text
        return venueText.replace(/^at |venue:|ground:|stadium:?/i, '').trim();
      }
    }

    return null;
  }

  /**
   * Extract player of match information using coordinate analysis
   * @param {Array} textElements - Array of text elements with coordinates
   * @returns {string} - Player of match name
   */
  extractPlayerOfMatchFromCoordinates(textElements) {
    // Look for player of match indicators
    for (let i = 0; i < textElements.length; i++) {
      const text = textElements[i].text.toLowerCase();
      if (text.match(/player of.*match|man of.*match|motm/i)) {
        // Find nearby elements (within 50 pixels horizontally and 10 pixels vertically)
        const nearbyElements = textElements.filter(el => {
          if (!el.boundingBox || !textElements[i].boundingBox) return false;
          const xDiff = Math.abs(el.boundingBox.topLeft.x - textElements[i].boundingBox.topLeft.x);
          const yDiff = Math.abs(el.boundingBox.topLeft.y - textElements[i].boundingBox.topLeft.y);
          return xDiff < 50 && yDiff < 10;
        });

        // Sort by x-coordinate and combine
        nearbyElements.sort((a, b) => a.boundingBox.topLeft.x - b.boundingBox.topLeft.x);
        const playerText = nearbyElements.map(el => el.text).join(' ');

        // Extract player name after the indicator
        const match = playerText.match(/(?:player of.*match|man of.*match|motm):?\s*([^\d]+)/i);
        return match ? match[1].trim() : null;
      }
    }

    return null;
  }

  /**
   * Extract bowler statistics from sorted text elements
   * @param {Array} sortedElements - Array of text elements sorted by position
   * @returns {Array} - Array of bowler statistics
   */
  extractBowlerStats(sortedElements) {
    return sortedElements.map(row => {
      const bowlerName = row[0].text;
      const stats = row.slice(1).map(el => el.text).join(' ');

      // Extract overs, maidens, runs, wickets
      const statsMatch = stats.match(/(\d+(?:\.\d+)?)[^\d]+(\d+)[^\d]+(\d+)[^\d]+(\d+)/);
      
      return {
        name: bowlerName,
        overs: statsMatch ? parseFloat(statsMatch[1]) : 0,
        maidens: statsMatch ? parseInt(statsMatch[2]) : 0,
        runs: statsMatch ? parseInt(statsMatch[3]) : 0,
        wickets: statsMatch ? parseInt(statsMatch[4]) : 0
      };
    }).filter(bowler => bowler.name && !bowler.name.match(/total/i));
  }

  /**
   * Extract dismissal information from stats text
   * @param {string} stats - Statistics text
   * @returns {string} - Dismissal information
   */
  extractDismissalInfo(stats) {
    // Common dismissal patterns
    const patterns = [
      /c .*b .*/i,      // Caught
      /b .*/i,          // Bowled
      /lbw .*/i,        // LBW
      /run out.*/i,     // Run out
      /st .*/i,         // Stumped
      /hit wicket.*/i,  // Hit wicket
      /not out/i        // Not out
    ];

    for (const pattern of patterns) {
      const match = stats.match(pattern);
      if (match) return match[0];
    }

    return '';
  }

  /**
   * Extract text elements from fullTextAnnotation
   * @param {Object} fullTextAnnotation - Full text annotation from Google Vision
   * @returns {Array} - Extracted text elements
   */
  extractFromFullText(fullTextAnnotation) {
    if (!fullTextAnnotation || !Array.isArray(fullTextAnnotation.pages)) {
      throw new OCRError('Invalid fullTextAnnotation structure', {
        hasPages: !!fullTextAnnotation?.pages,
        pagesType: typeof fullTextAnnotation?.pages
      });
    }

    const elements = [];
    
    try {
      fullTextAnnotation.pages.forEach((page, pageIndex) => {
        if (!Array.isArray(page?.blocks)) {
          throw new OCRError(`Invalid page structure at index ${pageIndex}`);
        }

        page.blocks.forEach((block, blockIndex) => {
          if (!Array.isArray(block?.paragraphs)) {
            throw new OCRError(`Invalid block structure at page ${pageIndex}, block ${blockIndex}`);
          }

          block.paragraphs.forEach((paragraph, paragraphIndex) => {
            if (!Array.isArray(paragraph?.words)) {
              throw new OCRError(`Invalid paragraph structure at page ${pageIndex}, block ${blockIndex}, paragraph ${paragraphIndex}`);
            }

            paragraph.words.forEach((word, wordIndex) => {
              if (!word.boundingBox?.vertices || !Array.isArray(word.symbols)) {
                throw new OCRError(`Invalid word structure at page ${pageIndex}, block ${blockIndex}, paragraph ${paragraphIndex}, word ${wordIndex}`);
              }

              const vertices = word.boundingBox.vertices;
              const text = word.symbols.map(symbol => symbol.text).join('');
              const trimmedText = text.trim();
              
              if (trimmedText && vertices.length === 4) {
                // Validate vertices have proper coordinates
                const validVertices = vertices.every(v => 
                  typeof v.x === 'number' && 
                  typeof v.y === 'number' && 
                  !isNaN(v.x) && 
                  !isNaN(v.y)
                );

                if (validVertices) {
                  elements.push({
                    text: trimmedText,
                    confidence: word.confidence || CONFIG.MIN_CONFIDENCE,
                    boundingBox: {
                      topLeft: { x: vertices[0].x, y: vertices[0].y },
                      topRight: { x: vertices[1].x, y: vertices[1].y },
                      bottomRight: { x: vertices[2].x, y: vertices[2].y },
                      bottomLeft: { x: vertices[3].x, y: vertices[3].y }
                    },
                    type: 'word',
                    pageIndex,
                    blockIndex,
                    paragraphIndex,
                    wordIndex,
                    // Additional metadata for better text analysis
                    isNumeric: /^\d+$/.test(trimmedText),
                    isAllCaps: trimmedText === trimmedText.toUpperCase(),
                    length: trimmedText.length,
                    // Calculate center point for easier positioning analysis
                    center: {
                      x: (vertices[0].x + vertices[1].x + vertices[2].x + vertices[3].x) / 4,
                      y: (vertices[0].y + vertices[1].y + vertices[2].y + vertices[3].y) / 4
                    }
                  });
                }
              }
            });
          });
        });
      });

      return elements;
    } catch (error) {
      if (error instanceof OCRError) {
        throw error;
      }
      throw new OCRError('Failed to process fullTextAnnotation', { cause: error });
    }
  }

  /**
   * Extract text elements from textAnnotations
   * @param {Array} textAnnotations - Text annotations from Google Vision
   * @returns {Array} - Extracted text elements
   */
  extractFromTextAnnotations(textAnnotations) {
    if (!Array.isArray(textAnnotations) || textAnnotations.length === 0) {
      throw new OCRError('Invalid textAnnotations structure', {
        isArray: Array.isArray(textAnnotations),
        length: textAnnotations?.length
      });
    }

    try {
      // Skip the first element as it contains the entire text
      return textAnnotations.slice(1).map((annotation, index) => {
        if (!annotation.description || !annotation.boundingPoly?.vertices) {
          throw new OCRError(`Invalid annotation structure at index ${index}`, {
            hasDescription: !!annotation.description,
            hasBoundingPoly: !!annotation.boundingPoly,
            hasVertices: !!annotation.boundingPoly?.vertices
          });
        }

        const vertices = annotation.boundingPoly.vertices;
        if (!Array.isArray(vertices) || vertices.length !== 4) {
          throw new OCRError(`Invalid vertices at index ${index}`, {
            verticesLength: vertices?.length
          });
        }

        return {
          text: annotation.description.trim(),
          x: Math.min(...vertices.map(v => v.x || 0)),
          y: Math.min(...vertices.map(v => v.y || 0)),
          confidence: annotation.confidence || CONFIG.MIN_CONFIDENCE,
          type: 'annotation',
          index
        };
      });
    } catch (error) {
      if (error instanceof OCRError) {
        throw error;
      }
      throw new OCRError('Failed to process textAnnotations', { cause: error });
    }
  }

  /**
   * Normalize and sort text elements
   * @param {Array} elements - Raw text elements
   * @returns {Array} - Normalized and sorted text elements
   */
  normalizeTextElements(elements) {
    return elements
      .filter(el => el.text.length > 0)
      .sort((a, b) => {
        const yDiff = Math.abs(a.y - b.y);
        return yDiff < CONFIG.ROW_THRESHOLD ? a.x - b.x : a.y - b.y;
      });
  }

  /**
   * Extract cricket data from OCR results using coordinate-based analysis
   * @param {Object} ocrResults - OCR results from Google Vision
   * @returns {Object} - Extracted cricket data
   * @throws {OCRError} - If text extraction fails
   */
  extractCricketData(ocrResults) {
    try {
      // Extract text elements with coordinates
      const textElements = this.extractTextElements(ocrResults);
      const fullText = textElements.map(element => element.text).join(' ');

      // Initialize cricket data structure
      const cricketData = this.initializeCricketData();

      // Extract team information using coordinate analysis
      const teamInfo = this.extractTeamsFromCoordinates(textElements);

      cricketData.team1 = teamInfo.team1;
      cricketData.team2 = teamInfo.team2;

      // Extract scores using position-based analysis
      const scores = this.extractScoresFromCoordinates(textElements);
      cricketData.team1Score = scores.team1Score;
      cricketData.team2Score = scores.team2Score;

      // Extract player statistics using spatial analysis
      const playerStats = this.extractPlayerStatsFromCoordinates(textElements, teamInfo);
      cricketData.team1Batsmen = playerStats.team1Batsmen;
      cricketData.team2Batsmen = playerStats.team2Batsmen;
      cricketData.team1Bowlers = playerStats.team1Bowlers;
      cricketData.team2Bowlers = playerStats.team2Bowlers;

      // Extract other information
      cricketData.venue = this.extractVenueFromCoordinates(textElements);
      cricketData.playerOfMatch = this.extractPlayerOfMatchFromCoordinates(textElements);

      // Add metadata
      cricketData.rawText = fullText;
      cricketData.extractionMethod = 'google-vision-coordinate';
      cricketData.confidence = this.calculateExtractionConfidence(textElements, teamInfo, scores, playerStats);

      // Filter out elements without valid bounding boxes
      const validElements = textElements.filter(el => el.boundingBox && el.boundingBox.topLeft);
      
      // Sort elements by y-coordinate first, then x-coordinate
      const sortedElements = validElements.sort((a, b) => {
        const yDiff = Math.abs(a.boundingBox.topLeft.y - b.boundingBox.topLeft.y);
        return yDiff < CONFIG.ROW_THRESHOLD 
          ? a.boundingBox.topLeft.x - b.boundingBox.topLeft.x 
          : a.boundingBox.topLeft.y - b.boundingBox.topLeft.y;
      });

      // Group elements by approximate rows
      const rows = this.groupElementsByRows(sortedElements);

      // Process each row to find player data
      rows.forEach(row => {
        this.processRowForPlayerData(row, cricketData);
      });

      // Post-process the extracted data
      this.postProcessCricketData(cricketData);

      return cricketData;
    } catch (error) {
      console.error('Error extracting cricket data:', error);
      const cricketData = this.initializeCricketData();
      cricketData.error = error.message;
      cricketData.ocrMessage = `Error during extraction: ${error.message}`;
      return cricketData;
    }
  }
  
  /**
   * Group OCR elements by rows based on y-coordinates
   * @param {Array} elements - OCR text elements
   * @returns {Array} - Array of rows, each containing elements in that row
   */
  /**
   * Group OCR elements into rows based on y-coordinates
   * @param {Array} elements - OCR text elements
   * @returns {Array} - Array of rows, each containing elements in that row
   */
  groupElementsByRows(elements) {
    const rows = [];
    let currentRow = [];
    let currentY = 0;
    
    // Sort elements by y-coordinate
    const sortedElements = [...elements].sort((a, b) => a.y - b.y);
    
    for (const element of sortedElements) {
      if (currentRow.length === 0) {
        // Start a new row
        currentRow = [element];
        currentY = element.y;
      } else if (Math.abs(element.y - currentY) < CONFIG.ROW_THRESHOLD) {
        // Add to current row if within threshold
        currentRow.push(element);
      } else {
        // Sort current row by x-coordinate and add to rows
        rows.push(this.processRow([...currentRow]));
        // Start new row
        currentRow = [element];
        currentY = element.y;
      }
    }
    
    // Add final row if exists
    if (currentRow.length > 0) {
      rows.push(this.processRow([...currentRow]));
    }
    
    return rows;
  }

  /**
   * Process a row of elements to enhance data extraction with cricket-specific rules
   * @param {Array} row - Array of elements in a row
   * @returns {Array} - Processed row of elements
   */
  processRow(row) {
    // Sort by x-coordinate
    const sortedRow = row.sort((a, b) => a.x - b.x);
    
    // Join adjacent elements that might be split incorrectly
    const processedRow = [];
    let currentElement = null;
    
    for (const element of sortedRow) {
      if (!currentElement) {
        currentElement = { ...element };
      } else {
        const gap = element.x - (currentElement.x + currentElement.text.length * 8); // Approximate character width
        
        // Enhanced joining logic for cricket-specific cases
        if (gap < 10 && (this.shouldJoinElements(currentElement, element) || this.isCricketSpecificJoin(currentElement, element))) {
          // Join elements
          currentElement.text += element.text;
          currentElement.confidence = Math.min(currentElement.confidence, element.confidence);
        } else {
          processedRow.push(currentElement);
          currentElement = { ...element };
        }
      }
    }
    
    if (currentElement) {
      processedRow.push(currentElement);
    }
    
    return processedRow;
  }

  /**
   * Determine if two elements should be joined
   * @param {Object} elem1 - First element
   * @param {Object} elem2 - Second element
   * @returns {boolean} - True if elements should be joined
   */
  shouldJoinElements(elem1, elem2) {
    // Join if they form a number
    if (/^\d+$/.test(elem1.text) && /^\d+$/.test(elem2.text)) return true;
    
    // Join if they form a player name
    if (/^[A-Z]/.test(elem1.text) && /^[a-z]/.test(elem2.text)) return true;
    
    // Join if they form a score (e.g., '1' + '/' + '2')
    if (/^\d+$/.test(elem1.text) && elem2.text === '/') return true;
    if (elem1.text === '/' && /^\d+$/.test(elem2.text)) return true;
    
    return false;
  }
  
  /**
   * Process a row of OCR elements to extract player data
   * @param {Array} row - Array of OCR elements in a row
   * @param {Object} cricketData - Cricket data object to update
   */
  processRowForPlayerData(row, cricketData) {
    // Extract team names and scores
    if (row.some(el => /PHOENIX|INVINCIBLES/i.test(el.text))) {
      const teamNameEl = row.find(el => /PHOENIX|INVINCIBLES/i.test(el.text));
      const scoreEl = row.find(el => /^\d+-\d+$/.test(el.text));
      const oversEl = row.find(el => /OVERS:\s*\d+(\.\d+)?/i.test(el.text));

      if (teamNameEl && scoreEl) {
        const [runs, wickets] = scoreEl.text.split('-').map(Number);
        const overs = oversEl ? parseFloat(oversEl.text.match(/\d+(\.\d+)?/)[0]) : 0;

        if (teamNameEl.text.includes('PHOENIX')) {
          cricketData.team1 = 'BIRMINGHAM PHOENIX';
          cricketData.team1Score = { runs, wickets, overs };
        } else {
          cricketData.team2 = 'INVINCIBLES';
          cricketData.team2Score = { runs, wickets, overs };
        }
      }
    }

    // Extract venue
    const venueMatch = row.find(el => /AT\s+([A-Z\s]+)/i.test(el.text));
    if (venueMatch) {
      cricketData.venue = venueMatch.text.match(/AT\s+([A-Z\s]+)/i)[1].trim();
    }

    // Extract player of the match
    const pomMatch = row.find(el => /Player of the Match:/i.test(el.text));
    if (pomMatch) {
      const pomNameEl = row.find(el => el.x > pomMatch.x && /[A-Za-z\s]+/.test(el.text));
      if (pomNameEl) {
        cricketData.playerOfMatch = pomNameEl.text.trim();
      }
    }

    // Extract player statistics
    const nameElements = row.filter(el => 
      el.x < 500 && // Player names are typically on the left
      /^[A-Z\-]+$/i.test(el.text.replace(/\s/g, '')) && // Allow hyphens for names like NAVEEN-UL-HAQ
      el.text.length > 2 // At least 3 characters
    );

    if (nameElements.length > 0) {
      // Combine name elements
      const playerName = nameElements.map(el => el.text).join(' ').trim();

      // Look for batting stats
      const runsElement = row.find(el => /^\d+\*?$/.test(el.text));
      const ballsElement = row.find(el => /^\(\d+\)$/.test(el.text));
      const strikeRateElement = row.find(el => /^\d+\.\d+$/.test(el.text));

      // Look for bowling stats
      const figureElement = row.find(el => /^\d+-\d+$/.test(el.text));
      const economyElement = row.find(el => /^\d+\.\d+$/.test(el.text));

      if (runsElement && (ballsElement || strikeRateElement)) {
        // Batting stats found
        const player = {
          name: playerName,
          runs: runsElement.text,
          balls: ballsElement ? parseInt(ballsElement.text.replace(/[()]/g, ''), 10) : 0,
          strikeRate: strikeRateElement ? strikeRateElement.text : '0.00'
        };

        // Determine team based on position in scorecard
        if (cricketData.team1Batsmen.length < 4) {
          cricketData.team1Batsmen.push(player);
        } else {
          cricketData.team2Batsmen.push(player);
        }
      } else if (figureElement) {
        // Bowling stats found
        const [wickets, runs] = figureElement.text.split('-').map(Number);
        const bowler = {
          name: playerName,
          wickets,
          runs,
          economy: economyElement ? economyElement.text : '0.00',
          figure: figureElement.text
        };

        // Determine team based on position in scorecard
        if (cricketData.team1Bowlers.length < 4) {
          cricketData.team1Bowlers.push(bowler);
        } else {
          cricketData.team2Bowlers.push(bowler);
        }
      }
    }
  }

  /**
   * Post-process and validate the extracted cricket data
   * @param {Object} cricketData - The extracted cricket data to process
   */
  postProcessCricketData(cricketData) {
    // Calculate strike rates for batsmen if missing
    const calculateStrikeRate = (runs, balls) => {
      if (!balls) return '0.00';
      const runsNum = parseInt(runs.replace('*', ''));
      return ((runsNum / balls) * 100).toFixed(2);
    };

    // Process team 1 batsmen
    cricketData.team1Batsmen = cricketData.team1Batsmen.map(batsman => ({
      ...batsman,
      strikeRate: batsman.strikeRate || calculateStrikeRate(batsman.runs, batsman.balls)
    }));

    // Process team 2 batsmen
    cricketData.team2Batsmen = cricketData.team2Batsmen.map(batsman => ({
      ...batsman,
      strikeRate: batsman.strikeRate || calculateStrikeRate(batsman.runs, batsman.balls)
    }));

    // Calculate economy rates for bowlers if missing
    const calculateEconomy = (runs, overs) => {
      if (!overs) return '0.00';
      return (runs / overs).toFixed(2);
    };

    // Process team 1 bowlers
    cricketData.team1Bowlers = cricketData.team1Bowlers.map(bowler => ({
      ...bowler,
      economy: bowler.economy || calculateEconomy(bowler.runs, Math.floor(bowler.overs || 4))
    }));

    // Process team 2 bowlers
    cricketData.team2Bowlers = cricketData.team2Bowlers.map(bowler => ({
      ...bowler,
      economy: bowler.economy || calculateEconomy(bowler.runs, Math.floor(bowler.overs || 4))
    }));

    // Set result text if missing
    if (!cricketData.resultText && cricketData.team1Score.runs && cricketData.team2Score.runs) {
      const runDiff = Math.abs(cricketData.team1Score.runs - cricketData.team2Score.runs);
      if (cricketData.team1Score.runs > cricketData.team2Score.runs) {
        cricketData.resultText = `${cricketData.team1} WON BY ${runDiff} RUNS`;
      } else if (cricketData.team2Score.runs > cricketData.team1Score.runs) {
        cricketData.resultText = `${cricketData.team2} WON BY ${runDiff} RUNS`;
      }
    }

    // Update confidence based on data completeness
    const hasCompleteData = (
      cricketData.team1 !== 'Team 1' &&
      cricketData.team2 !== 'Team 2' &&
      cricketData.venue !== 'Unknown Venue' &&
      cricketData.team1Batsmen.length > 0 &&
      cricketData.team2Batsmen.length > 0 &&
      cricketData.team1Bowlers.length > 0 &&
      cricketData.team2Bowlers.length > 0
    );

    cricketData.confidence = hasCompleteData ? 'high' : 'medium';
  }

  /**
   * Verify a specific player's score using Google Vision
   * @param {string} imagePath - Path to the image file
   * @param {string} playerName - Player name to verify
   * @returns {Promise<Object>} - Verified player data
   */
  async verifyPlayerScore(imagePath, playerName) {
    try {
      console.log(`Verifying ${playerName}'s score with Google Vision: ${imagePath}`);
      
      // Get OCR results from Google Vision
      const visionResults = await this.visionClient.detectText(imagePath);
      console.log(`Google Vision detected ${visionResults.textElements.length} text elements`);
      
      // Find the player in the OCR results
      const textElements = visionResults.textElements || [];
      
      // First attempt: Try to find the exact player name
      const exactMatch = this.findPlayerByExactName(textElements, playerName);
      if (exactMatch.found) {
        return exactMatch;
      }
      
      // Second attempt: Try to find by name parts
      const namePartsMatch = this.findPlayerByNameParts(textElements, playerName);
      if (namePartsMatch.found) {
        return namePartsMatch;
      }
      
      // Third attempt: Try to find by approximate name matching
      const approximateMatch = this.findPlayerByApproximateName(textElements, playerName);
      if (approximateMatch.found) {
        return approximateMatch;
      }
      
      // If all attempts fail, try a more aggressive approach
      const aggressiveMatch = this.findPlayerAggressively(textElements, playerName);
      if (aggressiveMatch.found) {
        return aggressiveMatch;
      }
      
      // If we still can't find the player, return not found
      console.log(`❌ Google Vision could not find ${playerName}`);
      return {
        name: playerName,
        found: false
      };
    } catch (error) {
      console.error(`Error verifying ${playerName}'s score:`, error);
      return {
        name: playerName,
        found: false,
        error: error.message
      };
    }
  }
  
  /**
   * Find a player by exact name match
   * @param {Array} textElements - OCR text elements
   * @param {string} playerName - Player name to find
   * @returns {Object} - Player data
   */
  findPlayerByExactName(textElements, playerName) {
    // Look for the exact player name
    const exactNameElement = textElements.find(el => 
      el.text.toUpperCase() === playerName.toUpperCase()
    );
    
    if (exactNameElement) {
      return this.extractPlayerStats(textElements, exactNameElement, playerName);
    }
    
    return { name: playerName, found: false };
  }
  
  /**
   * Find a player by matching name parts
   * @param {Array} textElements - OCR text elements
   * @param {string} playerName - Player name to find
   * @returns {Object} - Player data
   */
  findPlayerByNameParts(textElements, playerName) {
    // Split the player name into parts
    const nameParts = playerName.split(' ');
    
    // Find elements matching the player name parts
    const nameElements = textElements.filter(el => 
      nameParts.some(part => 
        el.text.toUpperCase() === part.toUpperCase()
      )
    );
    
    if (nameElements.length > 0) {
      // Group name elements that are in the same row
      const nameElementsByRow = this.groupElementsByRows(nameElements);
      
      // Find the row with the most name parts
      let bestRow = [];
      let maxParts = 0;
      
      nameElementsByRow.forEach(row => {
        const partsInRow = nameParts.filter(part => 
          row.some(el => el.text.toUpperCase() === part.toUpperCase())
        ).length;
        
        if (partsInRow > maxParts) {
          maxParts = partsInRow;
          bestRow = row;
        }
      });
      
      if (bestRow.length > 0) {
        // Sort by x-coordinate
        bestRow.sort((a, b) => a.x - b.x);
        return this.extractPlayerStats(textElements, bestRow[0], playerName);
      }
    }
    
    return { name: playerName, found: false };
  }
  
  /**
   * Find a player by approximate name matching
   * @param {Array} textElements - OCR text elements
   * @param {string} playerName - Player name to find
   * @returns {Object} - Player data
   */
  findPlayerByApproximateName(textElements, playerName) {
    // Try to find elements that contain parts of the player name
    const nameParts = playerName.split(' ');
    const lastNamePart = nameParts[nameParts.length - 1];
    
    // Look for elements containing the last name
    const lastNameElements = textElements.filter(el => 
      el.text.toUpperCase().includes(lastNamePart.toUpperCase())
    );
    
    if (lastNameElements.length > 0) {
      return this.extractPlayerStats(textElements, lastNameElements[0], playerName);
    }
    
    // Try with the first name
    const firstNamePart = nameParts[0];
    const firstNameElements = textElements.filter(el => 
      el.text.toUpperCase().includes(firstNamePart.toUpperCase())
    );
    
    if (firstNameElements.length > 0) {
      return this.extractPlayerStats(textElements, firstNameElements[0], playerName);
    }
    
    return { name: playerName, found: false };
  }
  
  /**
   * Find a player aggressively by looking for any name-like text
   * @param {Array} textElements - OCR text elements
   * @param {string} playerName - Player name to find
   * @returns {Object} - Player data
   */
  findPlayerAggressively(textElements, playerName) {
    // Group elements by rows
    const rows = this.groupElementsByRows(textElements);
    
    // Look for rows that have a name-like element on the left and numbers on the right
    for (const row of rows) {
      // Sort by x-coordinate
      row.sort((a, b) => a.x - b.x);
      
      // Check if this row has a name-like element on the left
      const nameElement = row.find(el => 
        el.x < 500 && // Names are typically on the left
        /^[A-Za-z]+$/.test(el.text) && // Only alphabetic characters
        el.text.length > 2 // At least 3 characters
      );
      
      if (nameElement) {
        // Check if this row has number elements
        const hasNumbers = row.some(el => /\d/.test(el.text));
        
        if (hasNumbers) {
          // This row likely contains a player's stats
          // Extract the stats and return them
          return this.extractPlayerStats(textElements, nameElement, playerName);
        }
      }
    }
    
    return { name: playerName, found: false };
  }
  
  /**
   * Extract player statistics from a row containing the player
   * @param {Array} textElements - All OCR text elements
   * @param {Object} nameElement - The element containing the player name
   * @param {string} playerName - Original player name
   * @returns {Object} - Player data
   */
  extractPlayerStats(textElements, nameElement, playerName) {
    // Find all elements in the same row
    const sameRowElements = textElements.filter(el => 
      Math.abs(el.y - nameElement.y) < 20
    );
    
    // Sort by x-coordinate
    sameRowElements.sort((a, b) => a.x - b.x);
    
    // Find all number elements in the row
    const numberElements = sameRowElements.filter(el => /\d/.test(el.text));
    
    // Look for runs (a number, possibly with * for not out)
    const runsElement = numberElements.find(el => 
      /^\d+\*?$/.test(el.text) && // Number, possibly with * for not out
      el.x > nameElement.x // Must be to the right of the name
    );
    
    // Look for balls (a number in parentheses)
    const ballsElement = sameRowElements.find(el => /^\(\d+\)$/.test(el.text));
    
    // If balls are split into separate elements: (, digit, )
    let ballsValue = null;
    if (!ballsElement) {
      const openParen = sameRowElements.find(el => el.text === '(');
      const closeParen = sameRowElements.find(el => el.text === ')');
      
      if (openParen && closeParen) {
        const ballsDigit = sameRowElements.find(el => 
          /^\d+$/.test(el.text) && 
          el.x > openParen.x && 
          el.x < closeParen.x
        );
        
        if (ballsDigit) {
          ballsValue = parseInt(ballsDigit.text, 10);
        }
      }
    } else {
      // Extract the number from (n)
      ballsValue = parseInt(ballsElement.text.replace(/[()]/g, ''), 10);
    }
    
    // If we couldn't find runs or balls in the standard format,
    // try to infer them from the available numbers
    if (!runsElement && numberElements.length >= 2) {
      // Assume the first two numbers are runs and balls
      const runs = numberElements[0].text;
      ballsValue = parseInt(numberElements[1].text, 10);
      
      return {
        name: playerName,
        runs: runs,
        balls: ballsValue,
        found: true,
        elements: sameRowElements.map(el => `"${el.text}" at (${el.x}, ${el.y})`)
      };
    }
    
    // Return the verified player data
    return {
      name: playerName,
      runs: runsElement ? runsElement.text : null,
      balls: ballsValue,
      found: runsElement !== undefined || ballsValue !== null,
      elements: sameRowElements.map(el => `"${el.text}" at (${el.x}, ${el.y})`)
    };
  }
}

module.exports = GoogleVisionService;