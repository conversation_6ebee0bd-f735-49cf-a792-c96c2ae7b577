/* Cricket Player Card Styles - FIFA Inspired */
.cricket-player-card {
  position: relative;
  width: 300px;
  height: 485px;
  /* Using a gradient background instead of an image for better compatibility */
  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 3.8rem 0;
  z-index: 2;
  transition: 200ms ease-in;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  font-family: 'Saira Semi Condensed', sans-serif;
}

/* Enhanced gold card background with shine effect */
.cricket-player-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);
  border-radius: 15px;
  z-index: -1;
}

/* Add shine effect */
.cricket-player-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  z-index: 1;
  pointer-events: none;
}

.cricket-player-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* Selection checkbox */
.cricket-player-card .player-selection {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.cricket-player-card .player-selection:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Top section of the card */
.cricket-player-card .player-card-top {
  position: relative;
  display: flex;
  color: #e9cc74;
  padding: 0 1.5rem;
}

.cricket-player-card .player-card-top .player-master-info {
  position: absolute;
  line-height: 2.2rem;
  font-weight: 300;
  padding: 1.5rem 0;
  text-transform: uppercase;
}

.cricket-player-card .player-card-top .player-master-info .player-rating {
  font-size: 2.2rem;
  font-weight: 700;
  text-shadow: 2px 2px #111;
}

.cricket-player-card .player-card-top .player-master-info .player-position {
  font-size: 1.8rem;
  margin-top: 0.3rem;
}

.cricket-player-card .player-card-top .player-master-info .player-nation {
  display: block;
  width: 3.2rem;
  height: 2rem;
  margin: 0.5rem 0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
}

.cricket-player-card .player-card-top .player-master-info .player-nation img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 2px;
}

.cricket-player-card .player-card-top .player-master-info .player-team {
  display: block;
  width: 2.5rem;
  height: 40px;
  margin-top: 0.5rem;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cricket-player-card .player-card-top .player-master-info .player-team img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Player picture */
.cricket-player-card .player-card-top .player-picture {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
}

.cricket-player-card .player-card-top .player-picture img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  position: relative;
  right: -1.5rem;
  bottom: 0;
  transition: transform 0.3s ease;
}

.cricket-player-card:hover .player-card-top .player-picture img {
  transform: scale(1.05);
}

.cricket-player-card .player-card-top .player-picture .player-extra {
  position: absolute;
  right: 0;
  bottom: -0.5rem;
  overflow: hidden;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  width: 100%;
  height: 2rem;
  padding: 0 1.5rem;
  text-align: right;
  background: none;
}

.cricket-player-card .player-card-top .player-picture .player-extra span {
  margin-left: 0.6rem;
  text-shadow: 2px 2px #333;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Bottom section of the card */
.cricket-player-card .player-card-bottom {
  position: relative;
}

.cricket-player-card .player-card-bottom .player-info {
  display: block;
  padding: 0.3rem 0;
  color: #e9cc74;
  width: 90%;
  margin: 0 auto;
  height: auto;
  position: relative;
  z-index: 2;
}

.cricket-player-card .player-card-bottom .player-info .player-name {
  width: 100%;
  display: block;
  text-align: center;
  font-size: 1.6rem;
  text-transform: uppercase;
  border-bottom: 2px solid rgba(233, 204, 116, 0.1);
  padding-bottom: 0.3rem;
  overflow: hidden;
  letter-spacing: 1px;
}

.cricket-player-card .player-card-bottom .player-info .player-name span {
  display: block;
  text-shadow: 2px 2px #111;
  font-weight: 700;
}

.cricket-player-card .player-card-bottom .player-info .player-features {
  margin: 0.5rem auto;
  display: flex;
  justify-content: center;
}

.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {
  border-right: 2px solid rgba(233, 204, 116, 0.1);
  padding: 0 2.3rem;
}

.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {
  display: flex;
  font-size: 1.2rem;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
}

.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-value {
  margin-right: 0.3rem;
  font-weight: 700;
  text-shadow: 1px 1px #111;
}

.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-title {
  font-weight: 300;
  opacity: 0.9;
}

.cricket-player-card .player-card-bottom .player-info .player-features .player-features-col:last-child {
  border: 0;
}

/* Action buttons */
.cricket-player-card .player-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  display: flex;
  gap: 8px;
}

.cricket-player-card .player-actions .edit-button,
.cricket-player-card .player-actions .delete-button {
  color: white;
  background-color: rgba(0, 0, 0, 0.6);
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cricket-player-card .player-actions .edit-button:hover {
  background-color: #2196f3;
  transform: translateY(-2px);
}

.cricket-player-card .player-actions .delete-button:hover {
  background-color: #f44336;
  transform: translateY(-2px);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .cricket-player-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  }

  .cricket-player-card .player-card-top .player-picture .player-extra span {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .cricket-player-card .player-card-bottom .player-info .player-name span {
    text-shadow: 2px 2px #000;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span .player-feature-value {
    text-shadow: 1px 1px #000;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cricket-player-card {
    width: 280px;
    height: 455px;
  }

  .cricket-player-card .player-card-top .player-master-info .player-rating {
    font-size: 2rem;
  }

  .cricket-player-card .player-card-top .player-picture {
    width: 180px;
    height: 180px;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {
    padding: 0 2rem;
  }
}

@media (max-width: 576px) {
  .cricket-player-card {
    width: 260px;
    height: 425px;
  }

  .cricket-player-card .player-card-top .player-master-info .player-rating {
    font-size: 1.8rem;
  }

  .cricket-player-card .player-card-top .player-picture {
    width: 160px;
    height: 160px;
  }

  .cricket-player-card .player-card-bottom .player-info .player-name {
    font-size: 1.4rem;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {
    padding: 0 1.8rem;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {
    font-size: 1rem;
  }
}

/* Mobile portrait mode */
@media (max-width: 375px) {
  .cricket-player-card {
    width: 240px;
    height: 390px;
  }

  .cricket-player-card .player-card-top .player-master-info .player-rating {
    font-size: 1.6rem;
  }

  .cricket-player-card .player-card-top .player-master-info .player-position {
    font-size: 1.4rem;
  }

  .cricket-player-card .player-card-top .player-picture {
    width: 140px;
    height: 140px;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col {
    padding: 0 1.4rem;
  }

  .cricket-player-card .player-card-bottom .player-info .player-features .player-features-col span {
    font-size: 0.9rem;
  }
}
