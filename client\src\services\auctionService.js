import axios from 'axios';
import { getAuthToken } from '../utils/auth';

// Create an axios instance for auction API calls
const AuctionAPI = axios.create({
  baseURL: (process.env.REACT_APP_API_URL || 'http://localhost:5000/api') + '/auctions',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
AuctionAPI.interceptors.request.use(
  config => {
    const token = getAuthToken();
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

/**
 * Get all auctions with filtering options
 * @param {Object} filters - Filter options (status, search, playerId, page, limit)
 * @returns {Promise} Auctions data with pagination
 */
export const getAuctions = async (filters = {}) => {
  try {
    const response = await AuctionAPI.get('/', { params: filters });
    return response.data;
  } catch (error) {
    console.error('Error fetching auctions:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get a specific auction by ID
 * @param {string} auctionId - Auction ID
 * @returns {Promise} Auction data
 */
export const getAuctionById = async (auctionId) => {
  try {
    const response = await AuctionAPI.get(`/${auctionId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching auction ${auctionId}:`, error);
    throw error.response?.data || error.message;
  }
};

/**
 * Create a new auction (admin only)
 * @param {Object} auctionData - Auction data
 * @returns {Promise} Created auction
 */
export const createAuction = async (auctionData) => {
  try {
    const response = await AuctionAPI.post('/', auctionData);
    return response.data;
  } catch (error) {
    console.error('Error creating auction:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Update an auction (admin only)
 * @param {string} auctionId - Auction ID
 * @param {Object} auctionData - Updated auction data
 * @returns {Promise} Updated auction
 */
export const updateAuction = async (auctionId, auctionData) => {
  try {
    const response = await AuctionAPI.put(`/${auctionId}`, auctionData);
    return response.data;
  } catch (error) {
    console.error(`Error updating auction ${auctionId}:`, error);
    throw error.response?.data || error.message;
  }
};

/**
 * Delete an auction (admin only)
 * @param {string} auctionId - Auction ID
 * @returns {Promise} Success message
 */
export const deleteAuction = async (auctionId) => {
  try {
    const response = await AuctionAPI.delete(`/${auctionId}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting auction ${auctionId}:`, error);
    throw error.response?.data || error.message;
  }
};

/**
 * Place a bid on an auction
 * @param {string} auctionId - Auction ID
 * @param {number} amount - Bid amount
 * @returns {Promise} Updated auction with bid
 */
export const placeBid = async (auctionId, amount) => {
  try {
    console.log(`Placing bid of ${amount} on auction ${auctionId}`);

    // Use axios directly instead of the AuctionAPI instance
    const baseURL = 'http://localhost:5000/api';
    const url = `${baseURL}/auctions/${auctionId}/bid`;
    console.log(`Full URL: ${url}`);

    const token = getAuthToken();
    console.log(`Auth token present: ${!!token}`);

    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['x-auth-token'] = token;
    }

    console.log('Request headers:', headers);
    console.log('Request body:', JSON.stringify({ amount }));

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify({ amount })
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      let errorData;
      try {
        // Try to parse as JSON first
        const errorText = await response.text();
        console.error('Error response text:', errorText);

        try {
          errorData = JSON.parse(errorText);
        } catch (parseError) {
          // If it's not valid JSON, use the text as is
          errorData = { msg: errorText };
        }

        // Create an error object with the response data
        const error = new Error(`HTTP error! status: ${response.status}, message: ${errorData.msg || 'Unknown error'}`);
        error.status = response.status;
        error.response = { data: errorData };
        error.errorCode = errorData.errorCode;
        throw error;
      } catch (parseError) {
        // If we can't parse the response at all, throw a generic error
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }

    const data = await response.json();
    console.log('Bid response:', data);
    return data;
  } catch (error) {
    console.error(`Error placing bid on auction ${auctionId}:`, error);
    throw error;
  }
};

/**
 * Get auctions the current user has bid on
 * @returns {Promise} List of auctions with user bids
 */
export const getMyBids = async () => {
  try {
    console.log('Fetching my bids from API...');
    const response = await AuctionAPI.get('/my-bids');
    console.log('My bids API response:', response.data);
    
    // Log each auction with its status
    if (Array.isArray(response.data)) {
      response.data.forEach((auction, index) => {
        console.log(`API Auction ${index}:`, {
          id: auction._id,
          player: auction.player?.name,
          currentBid: auction.currentBid,
          currentBidder: auction.currentBidder?._id,
          status: auction.status,
          startTime: auction.startTime,
          endTime: auction.endTime
        });
      });
    }
    
    return response.data;
  } catch (error) {
    console.error('Error fetching user bids:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get active bids where the user is the highest bidder
 * @param {string} userId - User ID
 * @returns {Promise} List of active auctions where user is highest bidder
 */
export const getMyActiveBids = async (userId) => {
  try {
    console.log('Getting active bids for user ID:', userId);
    
    if (!userId) {
      console.error('No user ID provided to getMyActiveBids');
      return { activeBids: [], total: 0 };
    }
    
    // First get all bids
    const allBids = await getMyBids();
    console.log(`Retrieved ${allBids.length} total bids for filtering`);
    
    // Then filter for active bids where user is highest bidder
    const now = new Date();
    
    const activeBids = allBids.filter(auction => {
      if (!auction || !auction.endTime || !auction.startTime || !auction.currentBidder) {
        return false;
      }
      
      const endTime = new Date(auction.endTime);
      const startTime = new Date(auction.startTime);
      
      // Check if auction is live (between start and end time)
      const isLive = now >= startTime && now < endTime;
      
      // Check if user is the current highest bidder
      // Log the bidder ID and user ID for comparison
      const bidderId = auction.currentBidder._id;
      console.log(`Comparing bidder ID: ${bidderId} with user ID: ${userId}`);
      
      // Compare as strings to handle potential object ID vs string comparison issues
      const isHighestBidder = String(bidderId) === String(userId);
      
      const shouldCount = isLive && isHighestBidder;
      
      console.log(`Checking auction ${auction._id} (${auction.player?.name}):`);
      console.log(`- Is live: ${isLive}, Is highest bidder: ${isHighestBidder}, Should count: ${shouldCount}`);
      
      return shouldCount;
    });
    
    console.log(`Found ${activeBids.length} active bids for user ${userId}`);
    
    // Calculate total
    const total = activeBids.reduce((sum, auction) => sum + (auction?.currentBid || 0), 0);
    console.log(`Total active bids amount: ${total}`);
    
    return {
      activeBids,
      total
    };
  } catch (error) {
    console.error('Error getting active bids:', error);
    throw error;
  }
};

// Test function for bidding
export const testBid = async (amount) => {
  try {
    console.log(`Testing bid of ${amount}`);

    const baseURL = 'http://localhost:5000/api';
    const url = `${baseURL}/auctions/test-bid`;
    console.log(`Test URL: ${url}`);

    const token = getAuthToken();
    console.log(`Auth token present: ${!!token}`);

    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['x-auth-token'] = token;
    }

    console.log('Request headers:', headers);
    console.log('Request body:', JSON.stringify({ amount }));

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify({ amount })
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log('Test bid response:', data);
    return data;
  } catch (error) {
    console.error(`Error testing bid:`, error);
    throw error;
  }
};

const auctionService = {
  getAuctions,
  getAuctionById,
  createAuction,
  updateAuction,
  deleteAuction,
  placeBid,
  getMyBids,
  getMyActiveBids,
  testBid
};

export default auctionService;
