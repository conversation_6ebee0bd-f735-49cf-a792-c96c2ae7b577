/**
 * Role-based authorization middleware
 * @param {Array} roles - Array of allowed roles for the route
 * @returns {Function} Middleware function
 */
const roleAuth = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ msg: 'User not authenticated' });
    }

    // Convert single role to array if needed
    const allowedRoles = typeof roles === 'string' ? [roles] : roles;
    
    // Check if user's role is in the allowed roles
    if (allowedRoles.length && !allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ msg: 'You do not have permission to perform this action' });
    }
    
    next();
  };
};

module.exports = roleAuth;