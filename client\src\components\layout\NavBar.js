import React, { useState } from 'react';
import {
  AppBar,
  Box,
  Toolbar,
  IconButton,
  Typography,
  Menu,
  Container,
  Avatar,
  Button,
  Tooltip,
  MenuItem,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  useTheme,
  useMediaQuery,
  alpha
} from '@mui/material';
import {
  Menu as MenuIcon,
  SportsCricket as SportsCricketIcon,
  Dashboard as DashboardIcon,
  EmojiEvents as TrophyIcon,
  CardMembership as CardIcon,
  AttachMoney as MarketIcon,
  Leaderboard as LeaderboardIcon,
  AdminPanelSettings as AdminIcon,
  Person as ProfileIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Home as HomeIcon,
  Groups as TeamIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const NavBar = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [mobileOpen, setMobileOpen] = useState(false);
  const [notificationsAnchor, setNotificationsAnchor] = useState(null);

  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleOpenNotifications = (event) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleCloseNotifications = () => {
    setNotificationsAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleCloseUserMenu();
    navigate('/');
  };

  const toggleDrawer = (open) => (event) => {
    if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }
    setMobileOpen(open);
  };

  // Navigation items with icons
  const navItems = [
    { name: 'Home', path: '/', icon: <HomeIcon /> },
    { name: 'Dashboard', path: '/dashboard', icon: <DashboardIcon />, authRequired: true },
    { name: 'Tournaments', path: '/tournaments', icon: <TrophyIcon />, authRequired: true },
    { name: 'Player Cards', path: '/player-cards', icon: <CardIcon />, authRequired: true },
    { name: 'Transfer Market', path: '/market', icon: <MarketIcon />, authRequired: true },
    { name: 'Leaderboards', path: '/leaderboards', icon: <LeaderboardIcon /> }
  ];

  // Admin-only items
  const adminItems = [
    { name: 'Admin Dashboard', path: '/admin', icon: <AdminIcon /> },
    { name: 'Player Management', path: '/admin/players', icon: <CardIcon /> },
    { name: 'Tournament Control', path: '/admin/tournaments', icon: <TrophyIcon /> },
    { name: 'User Management', path: '/admin/users', icon: <ProfileIcon /> },
    { name: 'Template Builder', path: '/admin/templates', icon: <DashboardIcon /> },
    { name: 'Randomizer Tool', path: '/admin/randomizer', icon: <DashboardIcon /> },
    { name: 'OCR Comparison', path: '/admin/ocr-comparison', icon: <DashboardIcon /> }
  ];

  // User menu items
  const userMenuItems = [
    { name: 'Profile', action: () => navigate('/profile'), icon: <ProfileIcon /> },
    { name: 'My Team', action: () => navigate('/my-team'), icon: <TeamIcon /> },
    { name: 'Settings', action: () => navigate('/settings'), icon: <SettingsIcon /> },
    { name: 'Logout', action: handleLogout, icon: <LogoutIcon /> }
  ];

  // Sample notifications
  const notifications = [
    { id: 1, text: 'New tournament starting in 2 days', read: false },
    { id: 2, text: 'Player transfer offer received', read: false },
    { id: 3, text: 'Match result updated', read: true },
  ];

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.read).length;

  // Is the current path active
  const isPathActive = (path) => {
    if (path === '/') {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  // Mobile drawer content with icons
  const drawer = (
    <Box sx={{ width: 280 }}>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <SportsCricketIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h6" component={Link} to="/"
          sx={{
            textDecoration: 'none',
            color: 'inherit',
            fontWeight: 600
          }}>
          Cricket 24
        </Typography>
      </Box>
      <Divider />

      {/* User info in drawer */}
      {user && (
        <>
          <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
            <Avatar
              src={user.profilePicture || '/static/images/avatar/default.jpg'}
              alt={user.username}
              sx={{ width: 40, height: 40, mr: 2 }}
            />
            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                {user.username}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {user.role === 'team_owner' ? user.teamName :
                 user.role === 'admin' ? 'Administrator' : 'Viewer'}
              </Typography>
            </Box>
          </Box>
          <Divider />
        </>
      )}

      {/* Main navigation */}
      <List component="nav" sx={{ p: 1 }}>
        {navItems.map((item) => (
          (!item.authRequired || user) && (
            <ListItem key={item.name} disablePadding>
              <ListItemButton
                component={Link}
                to={item.path}
                selected={isPathActive(item.path)}
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  '&.Mui-selected': {
                    backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                    '&:hover': {
                      backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.15),
                    }
                  }
                }}
              >
                <ListItemIcon sx={{
                  minWidth: 40,
                  color: isPathActive(item.path) ? 'primary.main' : 'inherit'
                }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.name}
                  primaryTypographyProps={{
                    fontWeight: isPathActive(item.path) ? 'medium' : 'regular'
                  }}
                />
              </ListItemButton>
            </ListItem>
          )
        ))}
      </List>

      {/* Admin section */}
      {user && user.role === 'admin' && (
        <>
          <Divider sx={{ my: 1 }}>
            <Typography variant="caption" color="text.secondary">Admin Controls</Typography>
          </Divider>
          <List sx={{ p: 1 }}>
            {adminItems.map((item) => (
              <ListItem key={item.name} disablePadding>
                <ListItemButton
                  component={Link}
                  to={item.path}
                  selected={isPathActive(item.path)}
                  sx={{
                    borderRadius: 1,
                    mb: 0.5,
                    '&.Mui-selected': {
                      backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                      '&:hover': {
                        backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.15),
                      }
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 40,
                    color: isPathActive(item.path) ? 'primary.main' : 'inherit'
                  }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name}
                    primaryTypographyProps={{
                      fontWeight: isPathActive(item.path) ? 'medium' : 'regular'
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </>
      )}

      {/* User related actions */}
      {user && (
        <>
          <Divider sx={{ my: 1 }}>
            <Typography variant="caption" color="text.secondary">User</Typography>
          </Divider>
          <List sx={{ p: 1 }}>
            {userMenuItems.map((item) => (
              <ListItem key={item.name} disablePadding>
                <ListItemButton
                  onClick={item.action}
                  sx={{
                    borderRadius: 1,
                    mb: 0.5,
                    color: item.name === 'Logout' ? 'error.main' : 'inherit'
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 40,
                    color: item.name === 'Logout' ? 'error.main' : 'inherit'
                  }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.name} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </>
      )}

      {/* Guest actions */}
      {!user && (
        <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Button
            variant="outlined"
            fullWidth
            component={Link}
            to="/login"
          >
            Login
          </Button>
          <Button
            variant="contained"
            fullWidth
            component={Link}
            to="/register"
          >
            Register
          </Button>
        </Box>
      )}
    </Box>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={1}
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === 'light' ? 'white' : theme.palette.background.paper,
          color: (theme) => theme.palette.text.primary
        }}
      >
        <Container maxWidth="xl">
          <Toolbar disableGutters sx={{ minHeight: { xs: 64, sm: 70 } }}>
            {/* Mobile: Menu button */}
            <Box sx={{ display: { xs: 'flex', md: 'none' }, mr: 1 }}>
              <IconButton
                size="large"
                aria-label="menu"
                edge="start"
                onClick={toggleDrawer(true)}
                color="inherit"
              >
                <MenuIcon />
              </IconButton>
            </Box>

            {/* Logo for all sizes */}
            <SportsCricketIcon sx={{ mr: 1, color: 'primary.main', display: 'flex' }} />
            <Typography
              variant="h6"
              noWrap
              component={Link}
              to="/"
              sx={{
                mr: 2,
                fontWeight: 700,
                color: 'inherit',
                textDecoration: 'none',
                display: 'flex'
              }}
            >
              Cricket 24
            </Typography>

            {/* Desktop: Navigation Links */}
            {!isMobile && (
              <Box sx={{ flexGrow: 1, display: 'flex', ml: 1 }}>
                {navItems.map((item) => (
                  (!item.authRequired || user) && (
                    <Button
                      key={item.name}
                      component={Link}
                      to={item.path}
                      startIcon={item.icon}
                      sx={{
                        mx: 0.5,
                        color: isPathActive(item.path) ? 'primary.main' : 'inherit',
                        fontWeight: isPathActive(item.path) ? 'medium' : 'regular',
                        borderBottom: isPathActive(item.path) ? 2 : 0,
                        borderColor: 'primary.main',
                        borderRadius: 0,
                        '&:hover': {
                          backgroundColor: 'transparent',
                          opacity: 0.8
                        }
                      }}
                    >
                      {item.name}
                    </Button>
                  )
                ))}
                {user && user.role === 'admin' && (
                  <Button
                    component={Link}
                    to="/admin"
                    startIcon={<AdminIcon />}
                    sx={{
                      mx: 0.5,
                      color: isPathActive('/admin') ? 'primary.main' : 'inherit',
                      fontWeight: isPathActive('/admin') ? 'medium' : 'regular',
                      borderBottom: isPathActive('/admin') ? 2 : 0,
                      borderColor: 'primary.main',
                      borderRadius: 0,
                      '&:hover': {
                        backgroundColor: 'transparent',
                        opacity: 0.8
                      }
                    }}
                  >
                    Admin
                  </Button>
                )}
              </Box>
            )}

            {/* Spacer for mobile */}
            <Box sx={{ flexGrow: 1, display: { md: 'none' } }} />

            {/* Notification Bell for logged in users */}
            {user && (
              <Box sx={{ mr: 2 }}>
                <Tooltip title="Notifications">
                  <IconButton
                    onClick={handleOpenNotifications}
                    size="large"
                    color="inherit"
                  >
                    <Badge badgeContent={unreadCount} color="error">
                      <NotificationsIcon />
                    </Badge>
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={notificationsAnchor}
                  open={Boolean(notificationsAnchor)}
                  onClose={handleCloseNotifications}
                  PaperProps={{
                    elevation: 3,
                    sx: {
                      width: 320,
                      maxHeight: 400,
                      overflow: 'auto',
                      mt: 1.5,
                      '& .MuiList-root': {
                        py: 0
                      }
                    }
                  }}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                >
                  <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1" fontWeight="medium">Notifications</Typography>
                  </Box>
                  <List sx={{ py: 0 }}>
                    {notifications.length > 0 ? (
                      notifications.map((notification) => (
                        <ListItem
                          key={notification.id}
                          sx={{
                            py: 1.5,
                            px: 2,
                            backgroundColor: notification.read ? 'transparent' : alpha(theme.palette.primary.light, 0.1),
                            borderBottom: 1,
                            borderColor: 'divider'
                          }}
                        >
                          <ListItemText
                            primary={notification.text}
                            primaryTypographyProps={{
                              fontSize: '0.9rem',
                              fontWeight: notification.read ? 'regular' : 'medium'
                            }}
                          />
                        </ListItem>
                      ))
                    ) : (
                      <ListItem sx={{ py: 2 }}>
                        <ListItemText
                          primary="No new notifications"
                          primaryTypographyProps={{
                            textAlign: 'center',
                            color: 'text.secondary'
                          }}
                        />
                      </ListItem>
                    )}
                  </List>
                  {notifications.length > 0 && (
                    <Box sx={{ p: 1.5, textAlign: 'center', borderTop: 1, borderColor: 'divider' }}>
                      <Button size="small">View all notifications</Button>
                    </Box>
                  )}
                </Menu>
              </Box>
            )}

            {/* User Menu or Login/Register */}
            <Box sx={{ flexGrow: 0 }}>
              {user ? (
                <>
                  <Tooltip title={`Logged in as ${user.username}`}>
                    <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                      <Avatar
                        alt={user.username}
                        src={user.profilePicture || '/static/images/avatar/default.jpg'}
                        sx={{
                          width: 40,
                          height: 40,
                          border: '2px solid',
                          borderColor: 'primary.main'
                        }}
                      />
                    </IconButton>
                  </Tooltip>
                  <Menu
                    id="menu-appbar"
                    anchorEl={anchorElUser}
                    open={Boolean(anchorElUser)}
                    onClose={handleCloseUserMenu}
                    PaperProps={{
                      elevation: 3,
                      sx: {
                        width: 250,
                        mt: 1.5
                      }
                    }}
                    transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                    anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                  >
                    {/* User info in dropdown */}
                    <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={user.profilePicture || '/static/images/avatar/default.jpg'}
                          alt={user.username}
                          sx={{ width: 40, height: 40, mr: 2 }}
                        />
                        <Box>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                            {user.username}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {user.role === 'team_owner' ? user.teamName :
                             user.role === 'admin' ? 'Administrator' : 'Viewer'}
                          </Typography>
                        </Box>
                      </Box>
                      {user.role === 'team_owner' && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <MarketIcon fontSize="small" sx={{ mr: 0.5, color: 'success.main' }} />
                          <Typography variant="body2" color="text.secondary">
                            {user.virtualCurrency?.toLocaleString() || '0'} Credits
                          </Typography>
                        </Box>
                      )}
                    </Box>
                    {/* Menu options */}
                    {userMenuItems.map((item) => (
                      <MenuItem
                        key={item.name}
                        onClick={item.action}
                        sx={{
                          py: 1.5,
                          color: item.name === 'Logout' ? 'error.main' : 'inherit'
                        }}
                      >
                        <ListItemIcon sx={{
                          color: item.name === 'Logout' ? 'error.main' : 'inherit'
                        }}>
                          {item.icon}
                        </ListItemIcon>
                        <Typography textAlign="left">{item.name}</Typography>
                      </MenuItem>
                    ))}
                  </Menu>
                </>
              ) : (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    component={Link}
                    to="/login"
                    size={isMobile ? 'small' : 'medium'}
                    sx={{
                      fontWeight: 'medium',
                      borderRadius: '20px'
                    }}
                  >
                    Login
                  </Button>
                  <Button
                    variant="contained"
                    component={Link}
                    to="/register"
                    size={isMobile ? 'small' : 'medium'}
                    sx={{
                      fontWeight: 'medium',
                      borderRadius: '20px'
                    }}
                  >
                    Register
                  </Button>
                </Box>
              )}
            </Box>
          </Toolbar>
        </Container>
      </AppBar>

      {/* Mobile Drawer */}
      <Drawer
        variant="temporary"
        anchor="left"
        open={mobileOpen}
        onClose={toggleDrawer(false)}
        ModalProps={{ keepMounted: true }}
        sx={{
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            borderRadius: '0 15px 15px 0'
          },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default NavBar;