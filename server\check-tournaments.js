const mongoose = require('mongoose');
const Tournament = require('./models/Tournament');
const Team = require('./models/Team');

async function checkTournaments() {
  try {
    await mongoose.connect('mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Check all tournaments first
    const allTournaments = await Tournament.find();
    console.log(`Found ${allTournaments.length} tournaments total`);
    
    if (allTournaments.length > 0) {
      console.log('Tournament names:');
      allTournaments.forEach(t => console.log(`  - ${t.name}`));
    }
    
    // Now get detailed data
    const tournaments = await Tournament.find().populate('phases.standings.team');
    
    if (tournaments.length === 0) {
      console.log('No tournaments found with populated data');
    } else {
      tournaments.forEach(t => {
        console.log(`\nTournament: ${t.name}`);
        t.phases.forEach(p => {
          console.log(`  Phase: ${p.name}`);
          console.log(`  Standings:`);
          p.standings.forEach(s => {
            console.log(`    Team: ${s.team?.teamName || 'Unknown'}`);
            console.log(`    Played: ${s.played}`);
            console.log(`    Wins: ${s.won}`);
            console.log(`    Losses: ${s.lost}`);
            console.log(`    Points: ${s.points}`);
            console.log(`    NRR: ${s.netRunRate}`);
            console.log(`    Total Runs Scored: ${s.totalRunsScored || 'undefined'}`);
            console.log(`    Total Overs Played: ${s.totalOversPlayed || 'undefined'}`);
            console.log(`    ---`);
          });
        });
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkTournaments();