# Google Cloud Vision API Setup

This directory contains the credentials for the Google Cloud Vision API used for OCR processing of cricket scorecards.

## Setup Instructions

1. **Create a Google Cloud Account and Project**
   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Sign in with your Google account or create a new one
   - Create a new project:
     - Click on the project dropdown at the top of the page
     - Click "New Project"
     - Enter a name for your project (e.g., "Cricket OCR")
     - Click "Create"

2. **Enable the Vision API**
   - In the Google Cloud Console, go to "APIs & Services" > "Library"
   - Search for "Vision API"
   - Click on "Cloud Vision API"
   - Click "Enable"

3. **Create Service Account and Download Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "Service Account"
   - Enter a name for your service account (e.g., "cricket-ocr-service")
   - Click "Create and Continue"
   - For the role, select "Project" > "Owner" (or use a more restricted role if preferred)
   - Click "Continue" and then "Done"
   - Find your new service account in the list and click on it
   - Go to the "Keys" tab
   - Click "Add Key" > "Create new key"
   - Select "JSON" and click "Create"
   - The key file will be downloaded to your computer

4. **Add Credentials to the Project**
   - Rename the downloaded JSON file to `google-cloud-credentials.json`
   - Place the file in this directory (`server/config/`)
   - Make sure the path in the `.env` file matches the location of your credentials file:
     ```
     GOOGLE_APPLICATION_CREDENTIALS=./config/google-cloud-credentials.json
     ```

5. **Restart the Server**
   - Restart the server to apply the changes
   - The server should now be able to use the Google Cloud Vision API for OCR processing

## Troubleshooting

If you encounter issues with the Google Cloud Vision API:

1. **Check the credentials file**
   - Make sure the file exists at the path specified in the `.env` file
   - Verify that the JSON file is valid and contains all required fields

2. **Check API enablement**
   - Go to the Google Cloud Console
   - Navigate to "APIs & Services" > "Dashboard"
   - Make sure the Cloud Vision API is enabled

3. **Check billing**
   - The Vision API requires billing to be enabled
   - Go to the Google Cloud Console
   - Navigate to "Billing"
   - Make sure billing is set up for your project

4. **Check quotas**
   - The Vision API has usage limits
   - Go to the Google Cloud Console
   - Navigate to "APIs & Services" > "Quotas & System Limits"
   - Check your current usage and limits

## Security Notes

- **IMPORTANT**: The credentials file contains sensitive information
- Do not commit this file to version control
- Keep the file secure and restrict access to it
- Consider using environment variables or a secrets manager in production
