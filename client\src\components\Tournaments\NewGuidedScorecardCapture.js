import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  Paper,
  Grid,
  Slider,
  FormControlLabel,
  Switch,
  IconButton,
  CircularProgress,
  Alert,
  Divider,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell
} from '@mui/material';
import CameraIcon from '@mui/icons-material/Camera';
import CameraswitchIcon from '@mui/icons-material/Cameraswitch';
import FlashOnIcon from '@mui/icons-material/FlashOn';
import FlashOffIcon from '@mui/icons-material/FlashOff';
import EditIcon from '@mui/icons-material/Edit';
import CheckIcon from '@mui/icons-material/Check';

/**
 * A new implementation of the guided scorecard capture component
 * Based on the working SimplifiedGuidedCapture component
 */
const NewGuidedScorecardCapture = ({
  open,
  onClose,
  onUpload,
  onSwitchToTraditional
}) => {
  // Refs
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const captureCanvasRef = useRef(null);
  const animationFrameId = useRef(null);

  // Camera states
  const [stream, setStream] = useState(null);
  const [facingMode, setFacingMode] = useState('environment');
  const [flashMode, setFlashMode] = useState(false);
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [autoCaptureEnabled, setAutoCaptureEnabled] = useState(true);

  // UI states
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [boundaryDetected, setBoundaryDetected] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [extractedData, setExtractedData] = useState(null);
  const [showDataEditor, setShowDataEditor] = useState(false);

  // Initialize camera
  const initCamera = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Requesting camera access');

      // Use the simplest possible constraints - this worked in SimplifiedGuidedCapture
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: facingMode === 'user' ? true : { facingMode: 'environment' }
      });

      console.log('Camera access granted, stream obtained');

      if (videoRef.current) {
        console.log('Setting video source');
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);

        // Add event listeners
        videoRef.current.onloadedmetadata = () => {
          console.log(`Video metadata loaded: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
          startBoundaryDetection();
        };
      } else {
        console.log('Video ref not available');
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (err) {
      console.error(`Error accessing camera: ${err.message}`);
      setError(`Error accessing camera: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (stream) {
      console.log('Stopping camera stream');
      stream.getTracks().forEach(track => track.stop());
      setStream(null);

      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
        animationFrameId.current = null;
      }
    }
  };

  // Switch between front and back camera
  const toggleCamera = () => {
    setFacingMode(prevMode => prevMode === 'environment' ? 'user' : 'environment');
  };

  // Toggle flash mode (if available)
  const toggleFlash = async () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        try {
          const capabilities = videoTrack.getCapabilities();
          if (capabilities.torch) {
            const newFlashMode = !flashMode;
            await videoTrack.applyConstraints({
              advanced: [{ torch: newFlashMode }]
            });
            setFlashMode(newFlashMode);
          } else {
            setError('Flash not available on this device');
          }
        } catch (err) {
          console.error('Error toggling flash:', err);
          setError('Failed to toggle flash');
        }
      }
    }
  };

  // Start boundary detection
  const startBoundaryDetection = () => {
    if (!canvasRef.current || !videoRef.current) {
      console.error('Canvas or video ref not available');
      return;
    }

    const detectBoundaries = () => {
      try {
        if (!canvasRef.current || !videoRef.current) return;

        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // Make sure video is playing
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          // Set canvas dimensions to match video
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          // Draw video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Apply image adjustments
          if (brightness !== 100 || contrast !== 100) {
            try {
              const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
              const data = imageData.data;

              const brightnessValue = brightness / 100;
              const contrastValue = contrast / 100;

              for (let i = 0; i < data.length; i += 4) {
                // Apply brightness
                data[i] = data[i] * brightnessValue;
                data[i + 1] = data[i + 1] * brightnessValue;
                data[i + 2] = data[i + 2] * brightnessValue;

                // Apply contrast
                data[i] = ((data[i] - 128) * contrastValue) + 128;
                data[i + 1] = ((data[i + 1] - 128) * contrastValue) + 128;
                data[i + 2] = ((data[i + 2] - 128) * contrastValue) + 128;
              }

              ctx.putImageData(imageData, 0, 0);
            } catch (adjustError) {
              console.error('Error applying image adjustments:', adjustError);
            }
          }

          // Simple boundary detection (just for demonstration)
          // In a real implementation, this would use more sophisticated image processing
          const detected = Math.random() > 0.7; // Random detection for demo

          // Draw guide overlay
          drawGuideOverlay(ctx, canvas.width, canvas.height, detected);

          // Update state if boundary detection status changed
          if (detected !== boundaryDetected) {
            setBoundaryDetected(detected);

            // Auto-capture if enabled and boundaries detected
            if (detected && autoCaptureEnabled && !capturedImage) {
              captureImage();
            }
          }
        }

        // Continue detection loop
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      } catch (err) {
        console.error('Error in boundary detection:', err);
        // Try to continue despite errors
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      }
    };

    // Start detection loop
    detectBoundaries();
  };

  // Draw guide overlay
  const drawGuideOverlay = (ctx, width, height, detected) => {
    // Draw rectangle guide
    const padding = 50;
    const rectWidth = width - (padding * 2);
    const rectHeight = height - (padding * 2);

    // Set style based on detection status
    ctx.strokeStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.lineWidth = 4;

    // Draw rectangle
    ctx.beginPath();
    ctx.rect(padding, padding, rectWidth, rectHeight);
    ctx.stroke();

    // Add corner markers
    const cornerSize = 30;

    // Top-left corner
    ctx.beginPath();
    ctx.moveTo(padding, padding + cornerSize);
    ctx.lineTo(padding, padding);
    ctx.lineTo(padding + cornerSize, padding);
    ctx.stroke();

    // Top-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, padding);
    ctx.lineTo(width - padding, padding);
    ctx.lineTo(width - padding, padding + cornerSize);
    ctx.stroke();

    // Bottom-left corner
    ctx.beginPath();
    ctx.moveTo(padding, height - padding - cornerSize);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(padding + cornerSize, height - padding);
    ctx.stroke();

    // Bottom-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.lineTo(width - padding, height - padding - cornerSize);
    ctx.stroke();

    // Add text guide
    ctx.fillStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';

    if (detected) {
      ctx.fillText('Scorecard Detected! Capturing...', width / 2, height - 20);
    } else {
      ctx.fillText('Align scorecard within boundaries', width / 2, height - 20);
    }
  };

  // Capture current frame as image
  const captureImage = useCallback(() => {
    if (!videoRef.current || capturedImage || processing) return;

    try {
      setProcessing(true);

      // Create a canvas for the captured image
      const video = videoRef.current;
      const canvas = captureCanvasRef.current;

      if (!canvas) return;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to image data URL
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setCapturedImage(imageDataUrl);

      // Process the image with OCR
      fetch(imageDataUrl)
        .then(r => r.blob())
        .then(blob => {
          // Import the OCR service
          const { processScorecardImage } = require('../../services/ocrService');

          // Process the image
          return processScorecardImage(blob, { debug: true });
        })
        .then(result => {
          console.log('OCR processing result:', result);
          setExtractedData(result);
          setProcessing(false);
        })
        .catch(err => {
          console.error('Error processing image with OCR:', err);
          setError(`Error processing image: ${err.message}`);
          setProcessing(false);

          // Fallback to basic data if OCR fails
          setExtractedData({
            homeTeam: {
              name: 'Home Team',
              score: {
                runs: 0,
                wickets: 0,
                overs: '0.0'
              },
              batsmen: [
                { name: "PLACEHOLDER 1", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 2", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 3", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 4", runs: 0, balls: 0 }
              ],
              bowlers: [
                { name: "PLACEHOLDER 1", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 2", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 3", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 4", wickets: 0, runs: 0 }
              ]
            },
            awayTeam: {
              name: 'Away Team',
              score: {
                runs: 0,
                wickets: 0,
                overs: '0.0'
              },
              batsmen: [
                { name: "PLACEHOLDER 1", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 2", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 3", runs: 0, balls: 0 },
                { name: "PLACEHOLDER 4", runs: 0, balls: 0 }
              ],
              bowlers: [
                { name: "PLACEHOLDER 1", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 2", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 3", wickets: 0, runs: 0 },
                { name: "PLACEHOLDER 4", wickets: 0, runs: 0 }
              ]
            },
            result: 'No result available',
            playerOfMatch: 'Not specified',
            extractionMethod: 'fallback'
          });
        });
    } catch (err) {
      console.error('Error capturing image:', err);
      setError(`Error capturing image: ${err.message}`);
      setProcessing(false);
    }
  }, [capturedImage, processing]);

  // Reset capture and go back to camera view
  const resetCapture = () => {
    setCapturedImage(null);
    setExtractedData(null);
    setShowDataEditor(false);
    setError(null);
    setSuccess(null);
  };

  // Toggle data editor view
  const toggleDataEditor = () => {
    setShowDataEditor(prev => !prev);
  };

  // Handle upload
  const handleUpload = async () => {
    if (!capturedImage || !extractedData) return;

    setUploading(true);

    try {
      // In a real implementation, this would call an API to upload the data
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSuccess('Scorecard uploaded successfully!');

      if (onUpload) {
        onUpload({
          image: capturedImage,
          data: extractedData
        });
      }

      // Close dialog after successful upload
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      console.error('Error uploading scorecard:', err);
      setError(`Error uploading scorecard: ${err.message}`);
    } finally {
      setUploading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    stopCamera();
    resetCapture();
    if (onClose) onClose();
  };

  // Initialize camera when component mounts
  useEffect(() => {
    if (open) {
      initCamera();
    }

    return () => {
      stopCamera();
    };
  }, [open, facingMode]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          height: { xs: '100%', sm: 'auto' },
          maxHeight: { xs: '100%', sm: '90vh' },
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle>
        Guided Scorecard Capture
      </DialogTitle>

      <DialogContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        {error && (
          <Alert severity="error" sx={{ mx: 2, mt: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mx: 2, mt: 2 }}>
            {success}
          </Alert>
        )}

        {!stream && !capturedImage && (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              Camera Access Required
            </Typography>

            <Typography variant="body1" paragraph>
              This feature requires access to your device camera to capture the scorecard.
            </Typography>

            <Typography variant="body2" paragraph>
              Please ensure:
              <ul>
                <li>You've granted camera permissions when prompted</li>
                <li>You're using a secure connection (HTTPS)</li>
                <li>If on iOS, Safari is the only browser that fully supports camera access</li>
              </ul>
            </Typography>

            <Box sx={{ mt: 3, mb: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={initCamera}
                size="large"
              >
                Try Again
              </Button>
            </Box>

            <Divider sx={{ my: 3 }}>OR</Divider>

            <Typography variant="body2" paragraph>
              If camera access doesn't work, you can use the traditional upload method instead:
            </Typography>

            <Button
              variant="outlined"
              color="primary"
              onClick={() => {
                if (onSwitchToTraditional) {
                  onSwitchToTraditional();
                } else {
                  onClose();
                }
              }}
            >
              Switch to Traditional Upload
            </Button>
          </Box>
        )}

        {stream && !capturedImage && (
          <Box sx={{ position: 'relative', flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            {/* Camera feed with overlay */}
            <Box sx={{
              position: 'relative',
              flexGrow: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              bgcolor: 'black',
              borderRadius: 1
            }}>
              {loading && (
                <CircularProgress sx={{ position: 'absolute', zIndex: 1 }} />
              )}

              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  transform: facingMode === 'user' ? 'scaleX(-1)' : 'none'
                }}
              />

              <canvas
                ref={canvasRef}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />

              {boundaryDetected && (
                <Box sx={{
                  position: 'absolute',
                  bottom: 16,
                  left: 0,
                  right: 0,
                  textAlign: 'center',
                  zIndex: 2
                }}>
                  <Button
                    variant="contained"
                    color="success"
                    size="large"
                    onClick={captureImage}
                    disabled={processing}
                  >
                    Capture Now
                  </Button>
                </Box>
              )}
            </Box>

            {/* Camera controls */}
            <Paper sx={{ p: 2 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" gutterBottom>
                    Brightness
                  </Typography>
                  <Slider
                    value={brightness}
                    onChange={(e, newValue) => setBrightness(newValue)}
                    min={50}
                    max={150}
                    valueLabelDisplay="auto"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" gutterBottom>
                    Contrast
                  </Typography>
                  <Slider
                    value={contrast}
                    onChange={(e, newValue) => setContrast(newValue)}
                    min={50}
                    max={150}
                    valueLabelDisplay="auto"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={autoCaptureEnabled}
                          onChange={(e) => setAutoCaptureEnabled(e.target.checked)}
                        />
                      }
                      label="Auto-capture"
                    />

                    <Box>
                      <IconButton onClick={toggleCamera} title="Switch Camera">
                        <CameraswitchIcon />
                      </IconButton>

                      <IconButton onClick={toggleFlash} title="Toggle Flash">
                        {flashMode ? <FlashOnIcon /> : <FlashOffIcon />}
                      </IconButton>

                      <Button
                        variant="contained"
                        color="primary"
                        onClick={captureImage}
                        startIcon={<CameraIcon />}
                        disabled={processing}
                      >
                        Capture
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Box>
        )}

        {capturedImage && (
          <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            {processing ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', flexGrow: 1 }}>
                <CircularProgress size={60} />
                <Typography variant="h6" sx={{ mt: 2 }}>
                  Processing Scorecard...
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Extracting match data from image
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {/* Captured image */}
                <Grid item xs={12} md={showDataEditor ? 6 : 12}>
                  <Paper sx={{ p: 2, height: '100%' }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Captured Scorecard
                    </Typography>
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <img
                        src={capturedImage}
                        alt="Captured scorecard"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '50vh',
                          objectFit: 'contain'
                        }}
                      />
                    </Box>
                  </Paper>
                </Grid>

                {/* Extracted data */}
                {extractedData && (
                  <Grid item xs={12} md={showDataEditor ? 6 : 12}>
                    <Paper sx={{ p: 2, height: '100%', overflow: 'auto' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle1">
                          Extracted Match Data
                        </Typography>
                        <Button
                          startIcon={<EditIcon />}
                          onClick={toggleDataEditor}
                          size="small"
                        >
                          {showDataEditor ? 'Hide Editor' : 'Edit Data'}
                        </Button>
                      </Box>

                      {showDataEditor ? (
                        <Typography variant="body2" color="text.secondary">
                          Data editor would be implemented here in the full version
                        </Typography>
                      ) : (
                        <>
                          <Grid container spacing={2}>
                            {/* Home team */}
                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">
                                {extractedData.homeTeam.name}
                              </Typography>
                              <Typography variant="h4">
                                {extractedData.homeTeam.score.runs}/{extractedData.homeTeam.score.wickets}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Overs: {extractedData.homeTeam.score.overs}
                              </Typography>
                            </Grid>

                            {/* Away team */}
                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">
                                {extractedData.awayTeam.name}
                              </Typography>
                              <Typography variant="h4">
                                {extractedData.awayTeam.score.runs}/{extractedData.awayTeam.score.wickets}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Overs: {extractedData.awayTeam.score.overs}
                              </Typography>
                            </Grid>

                            {/* Result */}
                            <Grid item xs={12}>
                              <Typography variant="body1" sx={{ mt: 2, fontWeight: 'bold' }}>
                                {extractedData.result}
                              </Typography>
                              <Typography variant="body2">
                                Player of the Match: {extractedData.playerOfMatch}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Venue: {extractedData.venue || "VENUE"}
                              </Typography>
                            </Grid>

                            {/* Player Statistics */}
                            <Grid item xs={12}>
                              <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
                                Player Statistics
                              </Typography>

                              {/* Home Team Batting */}
                              <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 1 }}>
                                - Batting
                              </Typography>
                              <Box sx={{ overflowX: 'auto' }}>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Batsman</TableCell>
                                      <TableCell align="right">Runs</TableCell>
                                      <TableCell align="right">Balls</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {extractedData.homeTeam.batsmen.map((batsman, index) => (
                                      <TableRow key={index}>
                                        <TableCell>{batsman.name}</TableCell>
                                        <TableCell align="right">{batsman.runs}</TableCell>
                                        <TableCell align="right">{batsman.balls}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>

                              {/* Home Team Bowling */}
                              <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                                - Bowling
                              </Typography>
                              <Box sx={{ overflowX: 'auto' }}>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Bowler</TableCell>
                                      <TableCell align="right">W</TableCell>
                                      <TableCell align="right">R</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {extractedData.homeTeam.bowlers.map((bowler, index) => (
                                      <TableRow key={index}>
                                        <TableCell>{bowler.name}</TableCell>
                                        <TableCell align="right">{bowler.wickets}</TableCell>
                                        <TableCell align="right">{bowler.runs}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>

                              {/* Away Team Batting */}
                              <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                                - Batting
                              </Typography>
                              <Box sx={{ overflowX: 'auto' }}>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Batsman</TableCell>
                                      <TableCell align="right">Runs</TableCell>
                                      <TableCell align="right">Balls</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {extractedData.awayTeam.batsmen.map((batsman, index) => (
                                      <TableRow key={index}>
                                        <TableCell>{batsman.name}</TableCell>
                                        <TableCell align="right">{batsman.runs}</TableCell>
                                        <TableCell align="right">{batsman.balls}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>

                              {/* Away Team Bowling */}
                              <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                                - Bowling
                              </Typography>
                              <Box sx={{ overflowX: 'auto' }}>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>Bowler</TableCell>
                                      <TableCell align="right">W</TableCell>
                                      <TableCell align="right">R</TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {extractedData.awayTeam.bowlers.map((bowler, index) => (
                                      <TableRow key={index}>
                                        <TableCell>{bowler.name}</TableCell>
                                        <TableCell align="right">{bowler.wickets}</TableCell>
                                        <TableCell align="right">{bowler.runs}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </Box>

                              <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
                                Extraction method: {extractedData.extractionMethod}
                              </Typography>
                            </Grid>
                          </Grid>
                        </>
                      )}
                    </Paper>
                  </Grid>
                )}
              </Grid>
            )}

            {/* Hidden canvas for capturing */}
            <canvas
              ref={captureCanvasRef}
              style={{ display: 'none' }}
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        {capturedImage ? (
          <>
            <Button onClick={resetCapture} disabled={uploading || processing}>
              Retake
            </Button>
            <Button
              onClick={handleUpload}
              variant="contained"
              color="primary"
              disabled={uploading || processing || !extractedData}
              startIcon={uploading ? <CircularProgress size={20} /> : <CheckIcon />}
            >
              {uploading ? 'Uploading...' : 'Confirm & Upload'}
            </Button>
          </>
        ) : (
          <Button onClick={handleClose} disabled={uploading || processing}>
            Cancel
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default NewGuidedScorecardCapture;
