import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Card,
  CardContent,
  Button,
  Divider,
  Avatar
} from '@mui/material';
import {
  PeopleAlt as UsersIcon,
  SportsEsports as TournamentIcon,
  SportsHandball as PlayerIcon,
  Casino as RandomizerIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  ViewModule as TemplateIcon,
  Scanner as OCRIcon,
  SportsCricket as MatchIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../theme/ThemeProvider';
import OCRSettings from '../../components/admin/OCRSettings';
import AdminMatchManagement from '../../components/AdminMatchManagement';

// TabPanel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const { darkMode } = useTheme();

  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="admin sections"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: darkMode ? 'background.paper' : 'grey.50',
            '& .MuiTab-root': {
              color: darkMode ? 'grey.400' : 'grey.700',
              '&.Mui-selected': {
                color: darkMode ? 'primary.light' : 'primary.main',
              },
              '&:hover': {
                color: darkMode ? 'primary.light' : 'primary.main',
              }
            }
          }}
        >
          <Tab label="Overview" icon={<DashboardIcon />} iconPosition="start" />
          <Tab
            label="Users & Teams"
            icon={<UsersIcon />}
            iconPosition="start"
          />
          <Tab
            label="Players"
            icon={<PlayerIcon />}
            iconPosition="start"
          />
          <Tab
            label="Tournaments"
            icon={<TournamentIcon />}
            iconPosition="start"
          />
          <Tab
            label="Match Management"
            icon={<MatchIcon />}
            iconPosition="start"
          />
          <Tab
            label="Templates"
            icon={<TemplateIcon />}
            iconPosition="start"
          />
          <Tab
            label="Manage OCR"
            icon={<OCRIcon />}
            iconPosition="start"
          />
          <Tab
            label="Randomizer"
            icon={<RandomizerIcon />}
            iconPosition="start"
          />
          <Tab
            label="Settings"
            icon={<SettingsIcon />}
            iconPosition="start"
          />
        </Tabs>

        <TabPanel value={tabValue} index={4}>
          <AdminMatchManagement />
        </TabPanel>

        <TabPanel value={tabValue} index={6}>
          <OCRSettings />
        </TabPanel>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ p: 3 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? 'primary.light' : 'primary.main',
                mb: 3
              }}
            >
              Admin Tools (4 Cards Layout)
            </Typography>

            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr',
                  sm: 'repeat(2, 1fr)',
                  md: 'repeat(4, 1fr)'
                },
                gap: 3,
                width: '100%'
              }}
            >
              {/* User Management */}
              <Box>
                <Card
                  elevation={darkMode ? 2 : 1}
                  sx={{
                    height: '100%',
                    minHeight: '350px',  // Add fixed minimum height
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: darkMode ? 'background.paper' : 'background.default',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: darkMode ? 8 : 4
                    }
                  }}
                >
                  <CardContent sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flexGrow: 1
                  }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        bgcolor: darkMode ? 'primary.dark' : 'primary.light',
                        mb: 2
                      }}
                    >
                      <UsersIcon />
                    </Avatar>

                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      User Management
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                      sx={{
                        mb: 3,
                        minHeight: '42px',
                        maxWidth: '85%'
                      }}
                    >
                      Manage users and team owners, update permissions, and handle account issues.
                    </Typography>

                    <Box sx={{ mt: 'auto', width: '100%' }}>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => navigate('/admin/users')}
                        sx={{
                          py: 1.5,
                          textTransform: 'none',
                          fontWeight: 500
                        }}
                      >
                        Manage Users
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Box>

              {/* Player Management */}
              <Box>
                <Card
                  elevation={darkMode ? 2 : 1}
                  sx={{
                    height: '100%',
                    minHeight: '350px',  // Add fixed minimum height
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: darkMode ? 'background.paper' : 'background.default',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: darkMode ? 8 : 4
                    }
                  }}
                >
                  <CardContent sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flexGrow: 1
                  }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        bgcolor: darkMode ? '#00796b' : '#4db6ac',
                        mb: 2
                      }}
                    >
                      <PlayerIcon />
                    </Avatar>

                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Player Management
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                      sx={{
                        mb: 3,
                        minHeight: '42px',
                        maxWidth: '85%'
                      }}
                    >
                      Create and manage player cards, update attributes, and handle player transfers.
                    </Typography>

                    <Box sx={{ mt: 'auto', width: '100%' }}>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => navigate('/admin/players')}
                        sx={{
                          py: 1.5,
                          textTransform: 'none',
                          fontWeight: 500,
                          bgcolor: darkMode ? '#00796b' : '#009688',
                          '&:hover': {
                            bgcolor: darkMode ? '#00695c' : '#00897b'
                          }
                        }}
                      >
                        Manage Players
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Box>

              {/* Tournament Control */}
              <Box>
                <Card
                  elevation={darkMode ? 2 : 1}
                  sx={{
                    height: '100%',
                    minHeight: '350px',  // Add fixed minimum height
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: darkMode ? 'background.paper' : 'background.default',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: darkMode ? 8 : 4
                    }
                  }}
                >
                  <CardContent sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flexGrow: 1
                  }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        bgcolor: darkMode ? '#5c6bc0' : '#9fa8da',
                        mb: 2
                      }}
                    >
                      <TournamentIcon />
                    </Avatar>

                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Tournament Control
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                      sx={{
                        mb: 3,
                        minHeight: '42px',
                        maxWidth: '85%'
                      }}
                    >
                      Create tournaments, set formats, manage registrations, and update standings.
                    </Typography>

                    <Box sx={{ mt: 'auto', width: '100%' }}>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => navigate('/admin/tournaments')}
                        sx={{
                          py: 1.5,
                          textTransform: 'none',
                          fontWeight: 500,
                          bgcolor: darkMode ? '#5c6bc0' : '#3f51b5',
                          '&:hover': {
                            bgcolor: darkMode ? '#3f51b5' : '#303f9f'
                          }
                        }}
                      >
                        Manage Tournaments
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Box>

              {/* Template Builder */}
              <Box>
                <Card
                  elevation={darkMode ? 2 : 1}
                  sx={{
                    height: '100%',
                    minHeight: '350px',
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: darkMode ? 'background.paper' : 'background.default',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: darkMode ? 8 : 4
                    }
                  }}
                >
                  <CardContent sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    flexGrow: 1
                  }}>
                    <Avatar
                      sx={{
                        width: 56,
                        height: 56,
                        bgcolor: darkMode ? '#7b1fa2' : '#ba68c8',
                        mb: 2
                      }}
                    >
                      <TemplateIcon />
                    </Avatar>

                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Template Builder
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                      sx={{
                        mb: 3,
                        minHeight: '42px',
                        maxWidth: '85%'
                      }}
                    >
                      Create and manage OCR templates for cricket scorecards to improve extraction accuracy.
                    </Typography>

                    <Box sx={{ mt: 'auto', width: '100%' }}>
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={() => navigate('/admin/templates')}
                        sx={{
                          py: 1.5,
                          textTransform: 'none',
                          fontWeight: 500,
                          bgcolor: darkMode ? '#7b1fa2' : '#9c27b0',
                          '&:hover': {
                            bgcolor: darkMode ? '#6a1b9a' : '#7b1fa2'
                          }
                        }}
                      >
                        Open Template Builder
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Box>
            </Box>
          </Box>
        </TabPanel>

        {/* Users & Teams Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? 'primary.light' : 'primary.main'
              }}
            >
              Users & Teams Management
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            This section will provide controls for managing users and teams. Functionality will be implemented in Phase 2.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/admin/users')}
            sx={{
              fontWeight: 500,
              boxShadow: darkMode ? '0 4px 12px rgba(30,136,229,0.5)' : '0 4px 12px rgba(30,136,229,0.2)',
            }}
          >
            Go to User Management
          </Button>
        </TabPanel>

        {/* Players Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? 'secondary.light' : 'secondary.main'
              }}
            >
              Player Card Management
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            This section will provide controls for managing player cards. Functionality will be implemented in Phase 3.
          </Typography>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => navigate('/admin/players')}
            sx={{
              fontWeight: 500,
              boxShadow: darkMode ? '0 4px 12px rgba(245,0,87,0.5)' : '0 4px 12px rgba(245,0,87,0.2)',
            }}
          >
            Go to Player Management
          </Button>
        </TabPanel>

        {/* Tournaments Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? '#5c6bc0' : '#3f51b5'
              }}
            >
              Tournament Management
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            This section will provide controls for managing tournaments. Functionality will be implemented in Phase 4.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/admin/tournaments')}
            sx={{
              fontWeight: 500,
              bgcolor: darkMode ? '#5c6bc0' : '#3f51b5',
              '&:hover': {
                bgcolor: darkMode ? '#3f51b5' : '#303f9f',
              }
            }}
          >
            Go to Tournament Management
          </Button>
        </TabPanel>

        {/* Templates Tab */}
        <TabPanel value={tabValue} index={5}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? '#7b1fa2' : '#9c27b0'
              }}
            >
              Template Builder
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Create and manage OCR templates for cricket scorecards. Map field regions on scorecard images to improve extraction accuracy.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/admin/templates')}
            sx={{
              fontWeight: 500,
              bgcolor: darkMode ? '#7b1fa2' : '#9c27b0',
              '&:hover': {
                bgcolor: darkMode ? '#6a1b9a' : '#7b1fa2',
              }
            }}
          >
            Open Template Builder
          </Button>
        </TabPanel>

        {/* Randomizer Tab */}
        <TabPanel value={tabValue} index={7}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? '#7b1fa2' : '#9c27b0'
              }}
            >
              Match Randomizer Configuration
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            This section will provide controls for configuring the match randomizer. Functionality will be implemented in Phase 5.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/admin/randomizer')}
            sx={{
              fontWeight: 500,
              bgcolor: darkMode ? '#7b1fa2' : '#9c27b0',
              '&:hover': {
                bgcolor: darkMode ? '#6a1b9a' : '#7b1fa2',
              }
            }}
          >
            Go to Randomizer Configuration
          </Button>
        </TabPanel>

        {/* Settings Tab */}
        <TabPanel value={tabValue} index={8}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: darkMode ? '#00695c' : '#009688'
              }}
            >
              System Settings
            </Typography>
            <Divider sx={{ flexGrow: 1, mx: 2 }} />
          </Box>
          <Typography variant="body1" sx={{ mb: 3 }}>
            This section will provide controls for managing system settings. Functionality will be added throughout development.
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/admin/settings')}
            sx={{
              fontWeight: 500,
              bgcolor: darkMode ? '#00695c' : '#009688',
              '&:hover': {
                bgcolor: darkMode ? '#004d40' : '#00796b',
              }
            }}
          >
            Go to System Settings
          </Button>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default AdminDashboard;