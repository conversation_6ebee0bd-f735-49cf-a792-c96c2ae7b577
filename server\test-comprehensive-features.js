/**
 * Comprehensive Test Script for RPL Features
 * 
 * Tests both:
 * 1. ✅ Robust Player Name Matching
 * 2. ✅ Match Outcome Calculation
 * 
 * Usage: node test-comprehensive-features.js [scorecard-filename]
 * Example: node test-comprehensive-features.js scorecard1.jpg
 */

const path = require('path');
const OCRService = require('./services/ocrService');
const PlayerMatchingService = require('./services/playerMatchingService');
const MatchOutcomeService = require('./services/matchOutcomeService');
const Player = require('./models/Player');
const Team = require('./models/Team');
const Tournament = require('./models/Tournament');

class ComprehensiveFeatureTest {
  constructor() {
    this.ocrService = new OCRService();
    this.playerMatchingService = new PlayerMatchingService();
    this.matchOutcomeService = new MatchOutcomeService();
    this.testResults = {
      ocrExtraction: null,
      playerMatching: null,
      matchOutcome: null,
      summary: {
        passed: 0,
        failed: 0,
        total: 3
      }
    };
  }

  /**
   * Run comprehensive tests on a scorecard
   */
  async runTests(scorecardName = 'scorecard1.jpg') {
    console.log('🚀 COMPREHENSIVE RPL FEATURE TEST');
    console.log('=================================');
    console.log(`Testing scorecard: ${scorecardName}\n`);

    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);
    
    try {
      // Test 1: OCR Extraction
      await this.testOCRExtraction(imagePath);
      
      // Test 2: Player Name Matching
      await this.testPlayerMatching();
      
      // Test 3: Match Outcome Calculation
      await this.testMatchOutcome();
      
      // Display final summary
      this.displaySummary();
      
    } catch (error) {
      console.error('❌ Test execution failed:', error.message);
    }
  }

  /**
   * Test 1: OCR Extraction with both services
   */
  async testOCRExtraction(imagePath) {
    console.log('📋 TEST 1: OCR EXTRACTION');
    console.log('=========================');
    
    try {
      console.log(`📸 Processing: ${path.basename(imagePath)}`);
      
      // Test OCR.Space extraction
      console.log('\n⏳ Testing OCR.Space extraction...');
      const ocrResult = await this.ocrService.processImageWithOCRSpace(imagePath);
      
      if (ocrResult.success) {
        console.log('✅ OCR.Space extraction successful');
        console.log(`   Team 1: "${ocrResult.team1}"`);
        console.log(`   Team 2: "${ocrResult.team2}"`);
        console.log(`   Team 1 Score: ${ocrResult.team1Score.runs}-${ocrResult.team1Score.wickets}`);
        console.log(`   Team 2 Score: ${ocrResult.team2Score.runs}-${ocrResult.team2Score.wickets}`);
        console.log(`   Batsmen found: ${ocrResult.team1Batsmen?.length || 0} + ${ocrResult.team2Batsmen?.length || 0}`);
        
        this.testResults.ocrExtraction = {
          success: true,
          data: ocrResult,
          message: 'OCR extraction completed successfully'
        };
        this.testResults.summary.passed++;
      } else {
        throw new Error(ocrResult.error || 'OCR extraction failed');
      }
      
    } catch (error) {
      console.log('❌ OCR extraction failed:', error.message);
      this.testResults.ocrExtraction = {
        success: false,
        error: error.message
      };
      this.testResults.summary.failed++;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
  }

  /**
   * Test 2: Robust Player Name Matching
   */
  async testPlayerMatching() {
    console.log('🔍 TEST 2: ROBUST PLAYER NAME MATCHING');
    console.log('======================================');
    
    try {
      if (!this.testResults.ocrExtraction?.success) {
        throw new Error('Cannot test player matching without successful OCR extraction');
      }
      
      const ocrData = this.testResults.ocrExtraction.data;
      const allPlayers = [];
      
      // Collect all player names from OCR data
      if (ocrData.team1Batsmen) {
        allPlayers.push(...ocrData.team1Batsmen.map(p => p.name));
      }
      if (ocrData.team2Batsmen) {
        allPlayers.push(...ocrData.team2Batsmen.map(p => p.name));
      }
      if (ocrData.team1Bowlers) {
        allPlayers.push(...ocrData.team1Bowlers.map(p => p.name));
      }
      if (ocrData.team2Bowlers) {
        allPlayers.push(...ocrData.team2Bowlers.map(p => p.name));
      }
      
      console.log(`Found ${allPlayers.length} players to match:\n`);
      
      const matchingResults = {
        totalPlayers: allPlayers.length,
        automaticMatches: 0,
        manualVerificationRequired: 0,
        noMatches: 0,
        details: []
      };
      
      // Test each player name
      for (let i = 0; i < Math.min(allPlayers.length, 10); i++) { // Limit to 10 for demo
        const playerName = allPlayers[i];
        console.log(`${i + 1}. Testing: "${playerName}"`);
        
        try {
          const matchResult = await this.playerMatchingService.matchPlayer(playerName);
          
          switch (matchResult.matchType) {
            case 'automatic':
              console.log(`   ✅ Automatic match: ${matchResult.player.name} (${matchResult.similarity.toFixed(2)})`);
              matchingResults.automaticMatches++;
              break;
            case 'manual_verification':
              console.log(`   ⚠️  Manual verification: ${matchResult.suggestions[0]?.name} (${matchResult.suggestions[0]?.similarity.toFixed(2)})`);
              matchingResults.manualVerificationRequired++;
              break;
            case 'no_match':
              console.log(`   ❌ No match found`);
              matchingResults.noMatches++;
              break;
          }
          
          matchingResults.details.push(matchResult);
          
        } catch (error) {
          console.log(`   ❌ Error matching: ${error.message}`);
          matchingResults.noMatches++;
        }
      }
      
      console.log('\n📊 PLAYER MATCHING SUMMARY:');
      console.log(`   Automatic matches: ${matchingResults.automaticMatches}`);
      console.log(`   Manual verification: ${matchingResults.manualVerificationRequired}`);
      console.log(`   No matches: ${matchingResults.noMatches}`);
      console.log(`   Success rate: ${((matchingResults.automaticMatches / matchingResults.totalPlayers) * 100).toFixed(1)}%`);
      
      this.testResults.playerMatching = {
        success: true,
        data: matchingResults,
        message: 'Player matching completed successfully'
      };
      this.testResults.summary.passed++;
      
    } catch (error) {
      console.log('❌ Player matching test failed:', error.message);
      this.testResults.playerMatching = {
        success: false,
        error: error.message
      };
      this.testResults.summary.failed++;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
  }

  /**
   * Test 3: Match Outcome Calculation
   */
  async testMatchOutcome() {
    console.log('🏆 TEST 3: MATCH OUTCOME CALCULATION');
    console.log('====================================');
    
    try {
      if (!this.testResults.ocrExtraction?.success) {
        throw new Error('Cannot test match outcome without successful OCR extraction');
      }
      
      const ocrData = this.testResults.ocrExtraction.data;
      
      // Create a mock tournament for testing
      const mockTournamentId = 'test-tournament-' + Date.now();
      
      console.log('⏳ Calculating match outcome...');
      
      // Test match outcome calculation
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(ocrData, mockTournamentId);
      
      console.log('\n📊 MATCH OUTCOME RESULTS:');
      console.log(`   Winner: ${outcome.winner}`);
      console.log(`   Margin: ${outcome.margin}`);
      console.log(`   Team 1 Points: ${outcome.team1Points}`);
      console.log(`   Team 2 Points: ${outcome.team2Points}`);
      
      if (outcome.team1Stats) {
        console.log(`   Team 1 NRR: ${outcome.team1Stats.netRunRate?.toFixed(3) || 'N/A'}`);
      }
      if (outcome.team2Stats) {
        console.log(`   Team 2 NRR: ${outcome.team2Stats.netRunRate?.toFixed(3) || 'N/A'}`);
      }
      
      // Test player performance calculations
      if (outcome.playerPerformances && outcome.playerPerformances.length > 0) {
        console.log('\n🏏 TOP PLAYER PERFORMANCES:');
        outcome.playerPerformances.slice(0, 5).forEach((perf, i) => {
          console.log(`   ${i + 1}. ${perf.playerName}: ${perf.runs} runs, ${perf.wickets || 0} wickets`);
        });
      }
      
      this.testResults.matchOutcome = {
        success: true,
        data: outcome,
        message: 'Match outcome calculation completed successfully'
      };
      this.testResults.summary.passed++;
      
    } catch (error) {
      console.log('❌ Match outcome calculation failed:', error.message);
      this.testResults.matchOutcome = {
        success: false,
        error: error.message
      };
      this.testResults.summary.failed++;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
  }

  /**
   * Display final test summary
   */
  displaySummary() {
    console.log('📋 FINAL TEST SUMMARY');
    console.log('=====================');
    console.log(`Tests Passed: ${this.testResults.summary.passed}/${this.testResults.summary.total}`);
    console.log(`Tests Failed: ${this.testResults.summary.failed}/${this.testResults.summary.total}`);
    console.log(`Success Rate: ${((this.testResults.summary.passed / this.testResults.summary.total) * 100).toFixed(1)}%\n`);
    
    // Individual test results
    console.log('📊 INDIVIDUAL TEST RESULTS:');
    console.log(`1. OCR Extraction: ${this.testResults.ocrExtraction?.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`2. Player Matching: ${this.testResults.playerMatching?.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`3. Match Outcome: ${this.testResults.matchOutcome?.success ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (this.testResults.summary.passed === this.testResults.summary.total) {
      console.log('\n🎉 ALL TESTS PASSED! Both features are working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Check the details above.');
    }
  }

  /**
   * Test multiple scorecards for comprehensive validation
   */
  async runBatchTests() {
    const testScoreCards = [
      'scorecard1.jpg',
      'scorecard9.png',
      'Super-Over-match.jpg'
    ];
    
    console.log('🚀 BATCH TESTING MULTIPLE SCORECARDS');
    console.log('====================================\n');
    
    for (const scorecard of testScoreCards) {
      console.log(`\n🔄 Testing ${scorecard}...`);
      console.log('-'.repeat(40));
      
      // Reset results for each test
      this.testResults = {
        ocrExtraction: null,
        playerMatching: null,
        matchOutcome: null,
        summary: { passed: 0, failed: 0, total: 3 }
      };
      
      await this.runTests(scorecard);
      
      console.log('\n' + '='.repeat(60) + '\n');
    }
  }
}

// Main execution
async function main() {
  const tester = new ComprehensiveFeatureTest();
  
  // Get scorecard name from command line arguments
  const scorecardName = process.argv[2];
  
  if (scorecardName === 'batch') {
    // Run batch tests on multiple scorecards
    await tester.runBatchTests();
  } else {
    // Run single scorecard test
    await tester.runTests(scorecardName);
  }
}

// Handle errors and run
main().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});