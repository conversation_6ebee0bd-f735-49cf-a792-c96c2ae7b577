const mongoose = require('mongoose');

// Define budget allocation schema
const budgetAllocationSchema = new mongoose.Schema({
  percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  }
});

// Define transaction schema
const transactionSchema = new mongoose.Schema({
  date: {
    type: Date,
    default: Date.now
  },
  description: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  category: {
    type: String,
    enum: ['playerAcquisition', 'playerDevelopment', 'teamOperations', 'marketing', 'reserve', 'other'],
    default: 'other'
  }
});

// Define team schema
const teamSchema = new mongoose.Schema({
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  teamName: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  logo: {
    type: String,
    default: '/uploads/teams/default-logo.png'
  },
  primaryColor: {
    type: String,
    default: '#1e88e5' // Default blue
  },
  secondaryColor: {
    type: String,
    default: '#bbdefb' // Light blue
  },
  colorScheme: {
    type: String,
    default: 'Blue'
  },
  homeGround: {
    type: String,
    default: ''
  },
  foundedYear: {
    type: Number,
    default: new Date().getFullYear()
  },
  slogan: {
    type: String,
    default: ''
  },
  budget: {
    totalBudget: {
      type: Number,
      default: 10000
    },
    allocations: {
      playerAcquisition: {
        type: budgetAllocationSchema,
        default: () => ({
          percentage: 60,
          amount: 6000
        })
      },
      playerDevelopment: {
        type: budgetAllocationSchema,
        default: () => ({
          percentage: 20,
          amount: 2000
        })
      },
      teamOperations: {
        type: budgetAllocationSchema,
        default: () => ({
          percentage: 10,
          amount: 1000
        })
      },
      marketing: {
        type: budgetAllocationSchema,
        default: () => ({
          percentage: 5,
          amount: 500
        })
      },
      reserve: {
        type: budgetAllocationSchema,
        default: () => ({
          percentage: 5,
          amount: 500
        })
      }
    },
    transactions: [transactionSchema]
  },
  statistics: {
    totalMatches: {
      type: Number,
      default: 0
    },
    wins: {
      type: Number,
      default: 0
    },
    losses: {
      type: Number,
      default: 0
    },
    draws: {
      type: Number,
      default: 0
    },
    winPercentage: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Calculate win percentage before saving
teamSchema.pre('save', function(next) {
  const stats = this.statistics;
  if (stats.totalMatches > 0) {
    stats.winPercentage = Math.round((stats.wins / stats.totalMatches) * 100);
  } else {
    stats.winPercentage = 0;
  }
  next();
});

const Team = mongoose.model('Team', teamSchema);

module.exports = Team;
