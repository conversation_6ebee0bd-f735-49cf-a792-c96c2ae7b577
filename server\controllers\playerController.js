const Player = require('../models/Player');
const User = require('../models/User');
const mongoose = require('mongoose');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const cheerio = require('cheerio');
const { generatePlayerProfile } = require('../utils/profileGenerator');

/**
 * Get all players
 * @route GET /api/players
 * @access Public
 */
exports.getAllPlayers = async (req, res) => {
  try {
    const { page = 1, limit = 20, search, type, rarity, nationality, sort, owner } = req.query;

    const query = {};

    // Add filters if provided
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }

    if (type) {
      query.type = type;
    }

    if (rarity) {
      query.rarity = rarity;
    }

    if (nationality) {
      query.nationality = { $regex: nationality, $options: 'i' };
    }

    if (owner) {
      if (owner === 'market') {
        query.isAvailableOnMarket = true;
      } else if (owner === 'free') {
        query.owner = null;
        query.isAvailableOnMarket = false;
      } else if (mongoose.Types.ObjectId.isValid(owner)) {
        query.owner = owner;
      }
    }

    // Handle sorting
    let sortOptions = { 'ratings.overall': -1 }; // Default sort by overall rating
    if (sort) {
      const [field, order] = sort.split(':');
      const sortOrder = order === 'asc' ? 1 : -1;

      if (field === 'marketValue') {
        sortOptions = { marketValue: sortOrder };
      } else if (field === 'name') {
        sortOptions = { name: sortOrder };
      } else if (field === 'age') {
        sortOptions = { age: sortOrder };
      } else if (field.startsWith('ratings.')) {
        sortOptions = { [field]: sortOrder };
      }
    }

    // Count total players matching the query
    const totalPlayers = await Player.countDocuments(query);

    // Get paginated players
    const players = await Player.find(query)
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('owner', 'username teamName');

    res.json({
      players,
      totalPages: Math.ceil(totalPlayers / limit),
      currentPage: Number(page),
      totalPlayers
    });
  } catch (err) {
    console.error('Error getting players:', err);
    res.status(500).send('Server error');
  }
};

/**
 * Get player by ID
 * @route GET /api/players/:id
 * @access Public
 */
exports.getPlayerById = async (req, res) => {
  try {
    const player = await Player.findById(req.params.id)
      .populate('owner', 'username teamName');

    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    res.json(player);
  } catch (err) {
    console.error('Error getting player:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Player not found' });
    }
    res.status(500).send('Server error');
  }
};

/**
 * Create new player
 * @route POST /api/players
 * @access Private (Admin only)
 */
exports.createPlayer = async (req, res) => {
  try {
    const {
      name,
      nationality,
      age,
      height,
      type,
      battingHand,
      bowlingHand,
      ratings,
      rarity,
      image
    } = req.body;

    // Normalize height
    const normalizedHeight = typeof height === 'string' && height.trim().toLowerCase().endsWith('cm')
      ? height.trim()
      : `${height} cm`;

    try {
      // Use default image if none provided (don't generate random one)
      const playerImage = image || '/uploads/players/default.png';

      // Create new player
      const player = new Player({
        name,
        nationality,
        age,
        height: normalizedHeight,
        type,
        battingHand,
        bowlingHand,
        rarity: rarity || 'Common',
        image: playerImage,
        createdBy: req.user.id,
        ...(ratings && { ratings })
      });

      await player.save();
      res.json(player);
    } catch (err) {
      console.error('Error saving player:', err);
      throw err;
    }
  } catch (err) {
    console.error('Error creating player:', err);
    return res.status(500).json({
      msg: 'Server error creating player',
      error: err.message
    });
  }
};

/**
 * Update player
 * @route PUT /api/players/:id
 * @access Private (Admin only)
 */
exports.updatePlayer = async (req, res) => {
  try {
    const {
      name,
      nationality,
      age,
      type,
      battingStyle,
      bowlingStyle,
      ratings,
      rarity,
      stats,
      image
    } = req.body;

    // Find player
    let player = await Player.findById(req.params.id);

    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Update player fields
    if (name) player.name = name;
    if (nationality) player.nationality = nationality;
    if (age) player.age = age;
    if (type) player.type = type;
    if (battingStyle) player.battingStyle = battingStyle;
    if (bowlingStyle) player.bowlingStyle = bowlingStyle;
    if (rarity) player.rarity = rarity;
    if (image) player.image = image;

    // Update ratings if provided
    if (ratings) {
      player.ratings = {
        ...player.ratings,
        ...ratings
      };
    }

    // Update stats if provided
    if (stats) {
      player.stats = {
        ...player.stats,
        ...stats
      };
    }

    await player.save();

    res.json(player);
  } catch (err) {
    console.error('Error updating player:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Player not found' });
    }
    res.status(500).send('Server error');
  }
};

/**
 * Delete player
 * @route DELETE /api/players/:id
 * @access Private (Admin only)
 */
/**
 * Transfer player ownership
 * @route PUT /api/players/:id/transfer
 * @access Private (Admin only)
 */
exports.transferPlayerOwnership = async (req, res) => {
  try {
    const { newOwnerId } = req.body;

    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ msg: 'Invalid player ID format' });
    }

    if (!mongoose.Types.ObjectId.isValid(newOwnerId)) {
      return res.status(400).json({ msg: 'Invalid new owner ID format' });
    }

    // Check if player exists
    const player = await Player.findById(req.params.id);
    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Check if new owner exists
    const newOwner = await User.findById(newOwnerId);
    if (!newOwner) {
      return res.status(404).json({ msg: 'New owner not found' });
    }

    // Update player ownership
    player.owner = newOwnerId;
    await player.save();

    res.json({ msg: 'Player ownership transferred successfully', player });
  } catch (err) {
    console.error('Error transferring player ownership:', err);
    res.status(500).json({
      msg: 'Server error transferring player ownership',
      error: err.message
    });
  }
};

/**
 * Remove player ownership
 * @route PUT /api/players/:id/remove-owner
 * @access Private (Admin only)
 */
exports.removePlayerOwnership = async (req, res) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ msg: 'Invalid player ID format' });
    }

    // Check if player exists
    const player = await Player.findById(req.params.id);
    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Remove ownership
    player.owner = null;
    player.isAvailableOnMarket = false; // Reset market status
    await player.save();

    res.json({ msg: 'Player ownership removed successfully', player });
  } catch (err) {
    console.error('Error removing player ownership:', err);
    res.status(500).json({
      msg: 'Server error removing player ownership',
      error: err.message
    });
  }
};

exports.deletePlayer = async (req, res) => {
  try {
    console.log('Delete player request for ID:', req.params.id);

    // Check if ID is valid
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      console.error('Invalid player ID format:', req.params.id);
      return res.status(400).json({ msg: 'Invalid player ID format' });
    }

    const player = await Player.findById(req.params.id);

    if (!player) {
      console.error('Player not found with ID:', req.params.id);
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Don't allow deletion of players that are owned by users
    if (player.owner) {
      console.error('Cannot delete player owned by a user:', player.owner);
      return res.status(400).json({ msg: 'Cannot delete player owned by a user' });
    }

    // Use deleteOne instead of remove (which is deprecated)
    const result = await Player.deleteOne({ _id: req.params.id });

    console.log('Delete result:', result);

    if (result.deletedCount === 0) {
      console.error('Player not deleted:', req.params.id);
      return res.status(500).json({ msg: 'Failed to delete player' });
    }

    console.log('Player deleted successfully:', req.params.id);
    res.json({ msg: 'Player deleted successfully' });
  } catch (err) {
    console.error('Error deleting player:', err);
    console.error('Error stack:', err.stack);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Player not found' });
    }

    res.status(500).json({
      msg: 'Server error deleting player',
      error: err.message
    });
  }
};

/**
 * Import players from Excel file
 * @route POST /api/players/import
 * @access Private (Admin only)
 */
exports.importPlayers = async (req, res) => {
  try {
    // Handle single player creation
    if (req.body.singlePlayer && req.body.playerData) {
      console.log('Creating single player with data:', req.body.playerData);
      const playerData = JSON.parse(req.body.playerData);

      // Validate required fields
      const requiredFields = ['name', 'nationality', 'height', 'type', 'battingHand'];
      const missingFields = requiredFields.filter(field => !playerData[field]);

      if (missingFields.length > 0) {
        console.log('Missing required fields:', missingFields);
        return res.status(400).json({
          msg: `Missing required fields: ${missingFields.join(', ')}`
        });
      }

      // Check if an image was uploaded
      let playerImage = playerData.image || '/uploads/players/default.png';

      // If there's an uploaded image file, use that instead
      if (req.file) {
        console.log('Image file uploaded:', req.file.path);
        playerImage = '/' + req.file.path.replace(/\\/g, '/');
      }

      // Create new player
      const player = new Player({
        name: playerData.name,
        nationality: playerData.nationality,
        age: playerData.age ? Number(playerData.age) : undefined, // Use age if provided, otherwise use model default
        height: playerData.height,
        type: playerData.type,
        battingHand: playerData.battingHand,
        bowlingHand: playerData.bowlingHand || 'None',
        image: playerImage,
        ratings: playerData.ratings || { overall: 70 },
        createdBy: req.user.id
      });

      try {
        await player.save();
      } catch (saveError) {
        // If the error is related to age validation, ignore it and try again without age
        if (saveError.name === 'ValidationError' && saveError.errors && saveError.errors.age) {
          console.log('Ignoring age validation error and retrying without age field');
          // Create a new player without the age field
          const playerWithoutAge = new Player({
            name: playerData.name,
            nationality: playerData.nationality,
            height: playerData.height,
            type: playerData.type,
            battingHand: playerData.battingHand,
            bowlingHand: playerData.bowlingHand || 'None',
            ratings: playerData.ratings || { overall: 70 },
            createdBy: req.user.id
          });
          await playerWithoutAge.save();
        } else {
          // If it's not an age-related error, rethrow it
          throw saveError;
        }
      }

      return res.json({
        success: true,
        playersImported: 1,
        errors: null,
        player
      });
    }

    // Handle batch import from IPL scraper
    if (req.body.batchImport && req.body.playerData) {
      console.log('Processing batch import from IPL scraper');

      try {
        const playersData = JSON.parse(req.body.playerData);
        const players = [];
        const errors = [];
        const skippedDuplicates = [];

        // Check for existing players to avoid duplicates
        const playerNames = playersData.map(p => p.name);
        const existingPlayers = await Player.find({ name: { $in: playerNames } });
        const existingPlayerNames = existingPlayers.map(p => p.name);

        console.log(`Found ${existingPlayerNames.length} existing players with matching names`);

        for (let i = 0; i < playersData.length; i++) {
          const playerData = playersData[i];

          try {
            // Skip if player with same name already exists
            if (existingPlayerNames.includes(playerData.name)) {
              console.log(`Skipping duplicate player: ${playerData.name}`);
              skippedDuplicates.push(`${playerData.name} - Player with this name already exists`);
              continue;
            }

            // Ensure height has 'cm' suffix
            let height = playerData.height;
            if (!height.toLowerCase().endsWith('cm')) {
              height = `${height} cm`;
            }

            // Create new player
            const player = new Player({
              name: playerData.name,
              nationality: playerData.nationality || 'India',
              height: height,
              type: playerData.type,
              battingHand: playerData.battingHand,
              bowlingHand: playerData.bowlingHand || 'None',
              image: playerData.image || playerData.imageUrl,
              ratings: playerData.ratings || { overall: 70 },
              stats: playerData.stats || {},
              createdBy: req.user.id
            });

            await player.save();
            players.push(player);
            console.log(`Imported IPL player: ${playerData.name}`);
          } catch (err) {
            console.error(`Error importing IPL player ${playerData.name}:`, err);
            errors.push(`${playerData.name}: ${err.message}`);
          }
        }

        return res.json({
          success: true,
          playersImported: players.length,
          errors: errors.length > 0 ? errors : null,
          errorCount: errors.length,
          skippedDuplicates: skippedDuplicates.length > 0 ? skippedDuplicates : null,
          skippedCount: skippedDuplicates.length
        });
      } catch (err) {
        console.error('Error processing batch import:', err);
        return res.status(400).json({
          success: false,
          message: 'Error processing batch import',
          error: err.message
        });
      }
    }

    // Handle Excel import
    if (!req.file) {
      return res.status(400).json({ msg: 'No file uploaded' });
    }

    // Read the file (Excel or CSV)
    console.log('Reading file:', req.file.path);
    console.log('File type:', req.file.mimetype);

    let data;

    try {
      // Try to read as Excel or CSV
      const workbook = xlsx.readFile(req.file.path);
      console.log('Workbook sheets:', workbook.SheetNames);

      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      // Log sheet data for debugging
      console.log('Sheet data sample:', JSON.stringify(sheet['!ref']));

      data = xlsx.utils.sheet_to_json(sheet);
      console.log('Parsed data sample:', JSON.stringify(data.slice(0, 2)));
      console.log('Data length:', data.length);
    } catch (readError) {
      console.error('Error reading file:', readError);
      throw new Error(`Failed to read the uploaded file: ${readError.message}`);
    }

    // Validate data
    if (!data || data.length === 0) {
      return res.status(400).json({ msg: 'No data found in the Excel file' });
    }

    // Process player data
    const players = [];
    const errors = [];
    const skippedDuplicates = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      try {
        console.log(`Processing row ${i + 1}:`, JSON.stringify(row));

        // Validate required fields (excluding height which is handled separately)
        const requiredFields = ['name', 'nationality', 'type', 'battingHand'];
        const missingFields = requiredFields.filter(field => !row[field]);

        if (missingFields.length > 0) {
          const errorMsg = `Row ${i + 1}: Missing required fields: ${missingFields.join(', ')}`;
          console.error(errorMsg);
          errors.push(errorMsg);
          continue;
        }

        // Check for existing player with same name, nationality, and type
        const existingPlayer = await Player.findOne({
          name: row.name,
          nationality: row.nationality,
          type: row.type
        });

        if (existingPlayer) {
          console.log(`Row ${i + 1}: Skipping duplicate player ${row.name} (${row.nationality}, ${row.type})`);
          skippedDuplicates.push(`Row ${i + 1}: ${row.name} (${row.nationality}, ${row.type})`);
          continue;
        }

        // Validate height format and existence
        if (!row.height) {
          const errorMsg = `Row ${i + 1}: Missing required field: height`;
          console.error(errorMsg);
          errors.push(errorMsg);
          continue;
        }

        // Convert height to string and normalize format
        const heightStr = row.height.toString().trim();
        if (!heightStr.toLowerCase().endsWith('cm')) {
          row.height = `${heightStr} cm`;
          console.log(`Row ${i + 1}: Normalized height to "${row.height}"`);
        } else {
          row.height = heightStr; // Ensure it's a string with proper formatting
        }

        // Create player - explicitly omit age field to avoid validation issues
        // Special handling for Wicket Keeper type
        let playerType = row.type;
        if (row.type === 'Wicket Keeper') {
          console.log(`Row ${i + 1}: Special handling for Wicket Keeper type`);
          // We'll keep the type as is, but log it for debugging
        }

        const playerData = {
          name: row.name,
          nationality: row.nationality,
          // age field is intentionally omitted to use the model default
          height: row.height || '175 cm',
          type: playerType,
          battingHand: row.battingHand,
          bowlingHand: row.bowlingHand || 'None',
          createdBy: req.user.id
        };

        console.log(`Row ${i + 1}: Creating player with data:`, JSON.stringify(playerData));

        const player = new Player(playerData);

        // Add ratings if provided
        if (row.overall) {
          player.ratings.overall = Number(row.overall);
          console.log(`Row ${i + 1}: Setting overall rating to ${player.ratings.overall}`);
        }

        // Validate player before saving
        const validationError = player.validateSync();
        if (validationError) {
          // Filter out age-related validation errors and handle player type validation
          const filteredErrors = {};
          for (const field in validationError.errors) {
            // Skip age-related errors
            if (field === 'age') {
              continue;
            }

            // Special handling for player type validation
            if (field === 'type' && row.type === 'Wicket Keeper') {
              console.log('Fixing Wicket Keeper type validation');
              player.type = 'Wicket Keeper';
              continue;
            }

            // Add other errors to the filtered list
            filteredErrors[field] = validationError.errors[field];
          }

          // If there are still errors after filtering
          if (Object.keys(filteredErrors).length > 0) {
            const validationErrors = Object.values(filteredErrors).map(e => e.message).join(', ');
            throw new Error(`Validation failed: ${validationErrors}`);
          }
        }

        await player.save();
        console.log(`Row ${i + 1}: Player saved successfully with ID ${player._id}`);
        players.push(player);
      } catch (err) {
        console.error(`Error processing row ${i + 1}:`, err);
        errors.push(`Row ${i + 1}: ${err.message}`);
      }
    }

    // Delete the uploaded file
    fs.unlinkSync(req.file.path);

    // Log summary
    console.log(`Import summary: ${players.length} players imported, ${errors.length} errors, ${skippedDuplicates.length} duplicates skipped`);
    if (errors.length > 0) {
      console.log('Import errors:', errors);
    }
    if (skippedDuplicates.length > 0) {
      console.log('Skipped duplicates:', skippedDuplicates);
    }

    res.json({
      success: true,
      playersImported: players.length,
      errors: errors.length > 0 ? errors : null,
      errorCount: errors.length,
      skippedDuplicates: skippedDuplicates.length > 0 ? skippedDuplicates : null,
      skippedCount: skippedDuplicates.length
    });
  } catch (err) {
    console.error('Error importing players:', err);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).send('Server error');
  }
};

/**
 * Get team's players by user ID
 * @route GET /api/players/team/:userId
 * @access Public
 */
exports.getPlayersByUserId = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // Validate user
    const user = await User.findById(req.params.userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Count total players
    const totalPlayers = await Player.countDocuments({ owner: req.params.userId });

    // Get paginated players
    const players = await Player.find({ owner: req.params.userId })
      .sort({ 'ratings.overall': -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    res.json({
      players,
      totalPages: Math.ceil(totalPlayers / limit),
      currentPage: Number(page),
      totalPlayers,
      team: {
        teamName: user.teamName,
        username: user.username
      }
    });
  } catch (err) {
    console.error('Error getting team players:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }
    res.status(500).send('Server error');
  }
};

/**
 * Get my team players
 * @route GET /api/players/myteam
 * @access Private
 */
exports.getMyTeamPlayers = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // Count total players
    const totalPlayers = await Player.countDocuments({ owner: req.user.id });

    // Get paginated players
    const players = await Player.find({ owner: req.user.id })
      .sort({ 'ratings.overall': -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    res.json({
      players,
      totalPages: Math.ceil(totalPlayers / limit),
      currentPage: Number(page),
      totalPlayers
    });
  } catch (err) {
    console.error('Error getting my team players:', err);
    res.status(500).send('Server error');
  }
};

/**
 * Scrape players from IPL website using headless browser
 * @route POST /api/players/scrape-ipl
 * @access Private (Admin only)
 */
exports.scrapeIplPlayers = async (req, res) => {
  try {
    const { url, useMock = false } = req.body;

    if (!url || !url.includes('iplt20.com/teams')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid IPL team URL'
      });
    }

    console.log(`Scraping players from: ${url}`);

    let players;

    // Use the scraper utility (which will fall back to mock data if needed)
    const { scrapeIplPlayers } = require('../utils/iplScraper');

    if (useMock) {
      // Explicitly use mock data if requested
      const mockIplTeams = require('../data/mockIplPlayers');

      // Extract team slug from URL
      let teamSlug = '';
      const urlParts = url.split('/');
      for (let i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'teams' && i + 1 < urlParts.length) {
          teamSlug = urlParts[i + 1].split('#')[0].split('?')[0];
          break;
        }
      }

      // Default to delhi-capitals if team not found
      if (!mockIplTeams[teamSlug]) {
        teamSlug = 'delhi-capitals';
      }

      players = mockIplTeams[teamSlug].players;
      console.log(`Using mock data: Found ${players.length} players for team: ${teamSlug}`);
    } else {
      // Try to use the headless browser scraper
      players = await scrapeIplPlayers(url);

      // Enhance player data with additional information if needed
      players = players.map(player => {
        // If the player already has all the required fields, return as is
        if (player.battingHand && player.bowlingHand && player.height && player.ratings && player.stats) {
          return player;
        }

        // Otherwise, add the missing fields
        return {
          ...player,
          battingHand: player.battingHand || (player.type === 'Bowler' ? 'RHB' : 'RHB'),
          bowlingHand: player.bowlingHand || (player.type === 'Bowler' || player.type === 'Allrounder' ? 'RFM' : 'None'),
          height: player.height || generateRandomHeight(),
          ratings: player.ratings || {
            overall: Math.floor(Math.random() * 25) + 70 // 70-95
          },
          stats: player.stats || generateRandomStats(player.type)
        };
      });
    }

    return res.status(200).json({
      success: true,
      message: `Found ${players.length} players`,
      data: players
    });
  } catch (error) {
    console.error('Error scraping IPL players:', error);
    return res.status(500).json({
      success: false,
      message: 'Error scraping IPL players',
      error: error.message
    });
  }
};

// Helper functions for the IPL scraper
function mapPlayerRole(role) {
  if (!role) return 'Batsman';

  const lowerRole = role.toLowerCase();
  if (lowerRole.includes('all-rounder')) {
    return 'Allrounder';
  } else if (lowerRole.includes('bowler')) {
    return 'Bowler';
  } else if (lowerRole.includes('wicket')) {
    return 'Wicket Keeper';
  } else {
    return 'Batsman';
  }
}

function mapBowlingHand(role) {
  if (!role) return 'None';

  const lowerRole = role.toLowerCase();
  if (lowerRole.includes('bowler') || lowerRole.includes('all-rounder')) {
    // 70% chance of right arm, 30% chance of left arm
    return Math.random() < 0.7 ? 'RFM' : 'LFM';
  } else {
    return 'None';
  }
}

function generateRating() {
  return Math.floor(Math.random() * 25) + 70; // 70-95
}

function generateRandomStats(role) {
  const lowerRole = role ? role.toLowerCase() : '';

  if (lowerRole.includes('bowler')) {
    return {
      battingAverage: (Math.random() * 15 + 5).toFixed(2),
      strikeRate: (Math.random() * 80 + 70).toFixed(2),
      wickets: Math.floor(Math.random() * 150 + 50),
      economy: (Math.random() * 3 + 5).toFixed(2),
      highScore: Math.floor(Math.random() * 40 + 10)
    };
  } else if (lowerRole.includes('all-rounder')) {
    return {
      battingAverage: (Math.random() * 25 + 20).toFixed(2),
      strikeRate: (Math.random() * 40 + 120).toFixed(2),
      wickets: Math.floor(Math.random() * 100 + 20),
      economy: (Math.random() * 2 + 6).toFixed(2),
      highScore: Math.floor(Math.random() * 60 + 40)
    };
  } else {
    return {
      battingAverage: (Math.random() * 30 + 30).toFixed(2),
      strikeRate: (Math.random() * 50 + 130).toFixed(2),
      wickets: Math.floor(Math.random() * 10),
      economy: (Math.random() * 3 + 7).toFixed(2),
      highScore: Math.floor(Math.random() * 80 + 70)
    };
  }
}

function generateRandomHeight() {
  return `${Math.floor(Math.random() * 20) + 170} cm`;
}