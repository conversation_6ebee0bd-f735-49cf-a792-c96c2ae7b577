import React, { createContext, useState, useContext, useEffect, useMemo } from 'react';
import { ThemeProvider as MuiThemeProvider, CssBaseline } from '@mui/material';
import createAppTheme from './index';

// Create theme context
const ThemeContext = createContext({
  mode: 'light',
  toggleMode: () => {},
});

/**
 * Custom hook to use the theme context
 */
export const useTheme = () => useContext(ThemeContext);

/**
 * Theme Provider Component
 * 
 * Provides theme context and MUI theme to the application
 * Handles theme mode persistence in localStorage
 */
export const ThemeProvider = ({ children }) => {
  // Get initial theme mode from localStorage or default to 'light'
  const [mode, setMode] = useState(() => {
    const savedMode = localStorage.getItem('themeMode');
    return savedMode || 'light';
  });

  // Create theme based on current mode
  const theme = useMemo(() => createAppTheme(mode), [mode]);

  // Toggle between light and dark mode
  const toggleMode = () => {
    setMode((prevMode) => {
      const newMode = prevMode === 'light' ? 'dark' : 'light';
      localStorage.setItem('themeMode', newMode);
      return newMode;
    });
  };

  // Update theme mode in localStorage when it changes
  useEffect(() => {
    localStorage.setItem('themeMode', mode);
    
    // Update body class for global CSS targeting
    document.body.classList.remove('light-mode', 'dark-mode');
    document.body.classList.add(`${mode}-mode`);
  }, [mode]);

  // Context value
  const contextValue = {
    mode,
    toggleMode,
    isDark: mode === 'dark',
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
