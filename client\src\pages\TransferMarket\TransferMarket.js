import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Chip,
  TextField,
  InputAdornment,
  Pagination,
  useTheme,
  useMediaQuery,
  Tooltip,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import InfoIcon from '@mui/icons-material/Info';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import { useAuth } from '../../hooks/useAuth';
import TeamRosterPlayerCard from '../../components/TeamRosterPlayerCard';

const TransferMarket = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [availablePlayers, setAvailablePlayers] = useState([]);
  const [totalPlayers, setTotalPlayers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    type: '',
    nationality: '',
    minRating: '',
    maxPrice: ''
  });
  
  const [showFilters, setShowFilters] = useState(false);
  
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [playerToBuy, setPlayerToBuy] = useState(null);
  
  const [teamBudget, setTeamBudget] = useState(10000);
  
  // Load available players
  useEffect(() => {
    setLoading(true);
    try {
      // Generate some placeholder players for the transfer market
      const generateMarketPlayers = () => {
        const playerTypes = ['Batsman', 'Bowler', 'All-Rounder', 'Wicket Keeper'];
        const nationalities = ['India', 'Australia', 'England', 'South Africa', 'New Zealand', 'West Indies'];
        const names = [
          'Virat Kohli', 'Rohit Sharma', 'Kane Williamson', 'Steve Smith', 'Joe Root',
          'Ben Stokes', 'Jasprit Bumrah', 'Pat Cummins', 'Kagiso Rabada', 'Trent Boult',
          'Babar Azam', 'Quinton de Kock', 'David Warner', 'Shakib Al Hasan', 'Rashid Khan',
          'Jos Buttler', 'Mitchell Starc', 'Jofra Archer', 'Hardik Pandya', 'Rishabh Pant',
          'KL Rahul', 'Faf du Plessis', 'Jonny Bairstow', 'Glenn Maxwell', 'Ravindra Jadeja',
          'Jofra Archer', 'Lockie Ferguson', 'Shai Hope', 'Nicholas Pooran', 'Shimron Hetmyer'
        ];
        
        return Array.from({ length: 30 }, (_, i) => {
          const name = names[i % names.length];
          const type = playerTypes[Math.floor(Math.random() * playerTypes.length)];
          const nationality = nationalities[Math.floor(Math.random() * nationalities.length)];
          const overall = Math.floor(Math.random() * 20) + 75; // 75-94
          const marketValue = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
          
          return {
            _id: `market_${i}`,
            name,
            type,
            nationality,
            image: `/uploads/players/placeholder_${i % 5 + 1}.jpg`,
            ratings: {
              overall,
              batting: Math.floor(Math.random() * 20) + 70,
              bowling: Math.floor(Math.random() * 20) + 70,
              fielding: Math.floor(Math.random() * 20) + 70
            },
            marketValue,
            battingHand: Math.random() > 0.7 ? 'Left' : 'Right',
            bowlingHand: Math.random() > 0.3 ? (Math.random() > 0.7 ? 'Left' : 'Right') : 'None'
          };
        });
      };
      
      const allPlayers = generateMarketPlayers();
      
      // Apply filters
      let filteredPlayers = allPlayers;
      
      // Search filter
      if (searchTerm) {
        filteredPlayers = filteredPlayers.filter(p => 
          p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.nationality.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }
      
      // Type filter
      if (filters.type) {
        filteredPlayers = filteredPlayers.filter(p => 
          p.type.toLowerCase() === filters.type.toLowerCase()
        );
      }
      
      // Nationality filter
      if (filters.nationality) {
        filteredPlayers = filteredPlayers.filter(p => 
          p.nationality.toLowerCase() === filters.nationality.toLowerCase()
        );
      }
      
      // Min rating filter
      if (filters.minRating) {
        filteredPlayers = filteredPlayers.filter(p => 
          p.ratings.overall >= parseInt(filters.minRating)
        );
      }
      
      // Max price filter
      if (filters.maxPrice) {
        filteredPlayers = filteredPlayers.filter(p => 
          p.marketValue <= parseInt(filters.maxPrice)
        );
      }
      
      // Paginate results
      const startIndex = (currentPage - 1) * 12;
      const endIndex = startIndex + 12;
      const paginatedPlayers = filteredPlayers.slice(startIndex, endIndex);
      
      setAvailablePlayers(paginatedPlayers);
      setTotalPlayers(filteredPlayers.length);
      setTotalPages(Math.ceil(filteredPlayers.length / 12));
      
      // Load team budget from localStorage
      const savedBudget = localStorage.getItem(`teamBudget_${user.id}`);
      if (savedBudget) {
        setTeamBudget(parseInt(savedBudget));
      }
    } catch (err) {
      console.error('Error loading transfer market:', err);
      setError('Failed to load transfer market. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [user, currentPage, searchTerm, filters]);
  
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };
  
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };
  
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setCurrentPage(1);
  };
  
  const clearFilters = () => {
    setFilters({
      type: '',
      nationality: '',
      minRating: '',
      maxPrice: ''
    });
    setCurrentPage(1);
  };
  
  const openBuyConfirmation = (player) => {
    setPlayerToBuy(player);
    setConfirmDialogOpen(true);
  };
  
  const handleBuyPlayer = async () => {
    if (!playerToBuy) return;
    
    try {
      // Check if player is affordable
      if (playerToBuy.marketValue > teamBudget) {
        setError(`Not enough budget to buy ${playerToBuy.name}. You need ${playerToBuy.marketValue - teamBudget} more credits.`);
        setConfirmDialogOpen(false);
        return;
      }
      
      // Get current roster from localStorage
      const savedRoster = localStorage.getItem(`teamRoster_${user.id}`);
      const currentRoster = savedRoster ? JSON.parse(savedRoster) : [];
      
      // Check if player is already in roster
      if (currentRoster.some(p => p.name === playerToBuy.name)) {
        setError(`${playerToBuy.name} is already in your team.`);
        setConfirmDialogOpen(false);
        return;
      }
      
      // Add player to roster
      const updatedRoster = [...currentRoster, playerToBuy];
      
      // Update budget
      const updatedBudget = teamBudget - playerToBuy.marketValue;
      
      // Save updated roster and budget to localStorage
      localStorage.setItem(`teamRoster_${user.id}`, JSON.stringify(updatedRoster));
      localStorage.setItem(`teamBudget_${user.id}`, updatedBudget.toString());
      
      // Update state
      setTeamBudget(updatedBudget);
      setSuccess(`Successfully purchased ${playerToBuy.name} for ${playerToBuy.marketValue} credits.`);
      setConfirmDialogOpen(false);
      
      // Remove purchased player from available players
      setAvailablePlayers(prev => prev.filter(p => p._id !== playerToBuy._id));
    } catch (err) {
      console.error('Error buying player:', err);
      setError('Failed to buy player. Please try again.');
    }
  };
  
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Transfer Market
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Buy players for your team
            </Typography>
          </Box>
          
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: { xs: 'flex-start', sm: 'flex-end' },
            mt: { xs: 2, sm: 0 }
          }}>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
              Budget: {teamBudget.toLocaleString()} Credits
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Use your budget wisely to build your team
            </Typography>
          </Box>
        </Box>
      </Paper>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
      
      {/* Search and Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <TextField
            placeholder="Search players by name, nationality, or type"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ flexGrow: 1, mr: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <Button
            variant="outlined"
            startIcon={showFilters ? <ClearIcon /> : <FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </Box>
        
        {showFilters && (
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Player Type</InputLabel>
                  <Select
                    name="type"
                    value={filters.type}
                    onChange={handleFilterChange}
                    label="Player Type"
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="Batsman">Batsman</MenuItem>
                    <MenuItem value="Bowler">Bowler</MenuItem>
                    <MenuItem value="All-Rounder">All-Rounder</MenuItem>
                    <MenuItem value="Wicket Keeper">Wicket Keeper</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Nationality</InputLabel>
                  <Select
                    name="nationality"
                    value={filters.nationality}
                    onChange={handleFilterChange}
                    label="Nationality"
                  >
                    <MenuItem value="">All Countries</MenuItem>
                    <MenuItem value="India">India</MenuItem>
                    <MenuItem value="Australia">Australia</MenuItem>
                    <MenuItem value="England">England</MenuItem>
                    <MenuItem value="South Africa">South Africa</MenuItem>
                    <MenuItem value="New Zealand">New Zealand</MenuItem>
                    <MenuItem value="West Indies">West Indies</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Min Rating</InputLabel>
                  <Select
                    name="minRating"
                    value={filters.minRating}
                    onChange={handleFilterChange}
                    label="Min Rating"
                  >
                    <MenuItem value="">Any Rating</MenuItem>
                    <MenuItem value="90">90+</MenuItem>
                    <MenuItem value="85">85+</MenuItem>
                    <MenuItem value="80">80+</MenuItem>
                    <MenuItem value="75">75+</MenuItem>
                    <MenuItem value="70">70+</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Max Price</InputLabel>
                  <Select
                    name="maxPrice"
                    value={filters.maxPrice}
                    onChange={handleFilterChange}
                    label="Max Price"
                  >
                    <MenuItem value="">Any Price</MenuItem>
                    <MenuItem value="1000">1,000 Credits</MenuItem>
                    <MenuItem value="2000">2,000 Credits</MenuItem>
                    <MenuItem value="3000">3,000 Credits</MenuItem>
                    <MenuItem value="4000">4,000 Credits</MenuItem>
                    <MenuItem value="5000">5,000 Credits</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
                disabled={!Object.values(filters).some(Boolean)}
              >
                Clear Filters
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
      
      {/* Player Listings */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Available Players ({totalPlayers})
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        {loading ? (
          <Box sx={{ width: '100%', mt: 2, mb: 2 }}>
            <LinearProgress />
          </Box>
        ) : availablePlayers.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              No players found matching your criteria.
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={clearFilters}
              sx={{ mt: 2 }}
              disabled={!Object.values(filters).some(Boolean) && !searchTerm}
            >
              Clear Filters
            </Button>
          </Box>
        ) : (
          <>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {availablePlayers.map((player) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={player._id}>
                  <Card sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    }
                  }}>
                    <TeamRosterPlayerCard 
                      player={player}
                      onRemove={() => {}} // Disabled for market view
                    />
                    
                    <Box sx={{ 
                      p: 2, 
                      backgroundColor: 'background.paper',
                      borderTop: '1px solid',
                      borderColor: 'divider'
                    }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6" color="primary" fontWeight="bold">
                          {player.marketValue.toLocaleString()} Credits
                        </Typography>
                        
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          startIcon={<ShoppingCartIcon />}
                          onClick={() => openBuyConfirmation(player)}
                          disabled={player.marketValue > teamBudget}
                        >
                          Buy
                        </Button>
                      </Box>
                      
                      {player.marketValue > teamBudget && (
                        <Typography variant="caption" color="error">
                          Not enough budget
                        </Typography>
                      )}
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
            
            {totalPages > 1 && (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                mt: 8, 
                mb: 3, 
                pt: 2,
                position: 'relative',
                zIndex: 1
              }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </Paper>
      
      {/* Confirm Purchase Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>
          Confirm Player Purchase
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to buy {playerToBuy?.name} for {playerToBuy?.marketValue.toLocaleString()} credits?
          </Typography>
          <Typography variant="body2">
            Your current budget: {teamBudget.toLocaleString()} credits
          </Typography>
          <Typography variant="body2">
            Remaining budget after purchase: {playerToBuy ? (teamBudget - playerToBuy.marketValue).toLocaleString() : 0} credits
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleBuyPlayer} color="primary" variant="contained">
            Confirm Purchase
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TransferMarket;
