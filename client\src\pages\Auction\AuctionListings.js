import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  LinearProgress,
  Pagination,
  Tooltip,
  IconButton,
  CircularProgress
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import GavelIcon from '@mui/icons-material/Gavel';
import MoneyIcon from '@mui/icons-material/AttachMoney';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useAuth } from '../../hooks/useAuth';
import { 
  getAuctions, 
  placeBid, 
  getMyBids, 
  getMyActiveBids, 
  updateAuction, 
  deleteAuction 
} from '../../services/auctionService';
import { getTeamBudget, createTeam, getTeamSettings } from '../../services/teamService';
import AuctionPlayerCard from '../../components/auction/AuctionPlayerCard';
import { formatDistanceToNow, differenceInSeconds } from 'date-fns';

// Auction card component
const AuctionCard = ({ auction, onBid, currentUser }) => {
  const [timeLeft, setTimeLeft] = useState('');
  const [status, setStatus] = useState({ text: '', color: '' });
  const timerRef = useRef(null);

  // Update time left and status
  const updateTimeLeft = useCallback(() => {
    const now = new Date();
    const startTime = new Date(auction.startTime);
    const endTime = new Date(auction.endTime);

    // Determine status based on auction status
    if (!auction.isActive) {
      setStatus({ text: 'Inactive', color: 'default' });
      setTimeLeft('Inactive');
      return;
    }

    if (auction.status === 'scheduled' || now < startTime) {
      setStatus({ text: 'Scheduled', color: 'info' });
      setTimeLeft(`Starts ${formatDistanceToNow(startTime, { addSuffix: true })}`);
      return;
    }

    if (auction.status === 'live' || (now >= startTime && now < endTime)) {
      setStatus({ text: 'Live', color: 'success' });

      // Calculate time left
      const secondsLeft = differenceInSeconds(endTime, now);
      const hours = Math.floor(secondsLeft / 3600);
      const minutes = Math.floor((secondsLeft % 3600) / 60);
      const seconds = secondsLeft % 60;

      setTimeLeft(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
      return;
    }

    if (auction.status === 'completed' || auction.status === 'cancelled' || now >= endTime) {
      if (auction.currentBidder) {
        setStatus({ text: 'Completed', color: 'primary' });
        setTimeLeft('Auction ended');
      } else {
        setStatus({ text: 'Expired', color: 'error' });
        setTimeLeft('No bids received');
      }
      return;
    }
  }, [auction, setStatus, setTimeLeft]);

  // Set up timer
  useEffect(() => {
    updateTimeLeft();

    // Update every second for live auctions
    timerRef.current = setInterval(() => {
      updateTimeLeft();
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [auction, updateTimeLeft]);

  // Check if current user is the highest bidder
  const isHighestBidder = auction?.currentBidder && auction?.currentBidder._id === currentUser?.id;

  // Calculate minimum bid
  const minimumBid = (auction?.currentBid || 0) + (auction?.minimumBidIncrement || 100);

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      transition: 'transform 0.2s',
      overflow: 'hidden',
      borderRadius: 2,
      minHeight: { xs: '280px', sm: '300px', md: '320px' }, // Minimum height but not maximum
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: 4
      }
    }}>
      <Box sx={{ position: 'relative' }}>
        <Box
          component="img"
          src={auction?.player?.image || '/default-player.png'}
          alt={auction?.player?.name || 'Player'}
          sx={{
            width: '100%',
            height: { xs: 120, sm: 130, md: 140, lg: 150 },
            objectFit: 'cover',
            objectPosition: 'center top',
            filter: status.text === 'Completed' || status.text === 'Expired' ? 'grayscale(0.5)' : 'none'
          }}
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = '/default-player.png';
          }}
        />

        <Chip
          label={status.text}
          color={status.color}
          size="small"
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            fontWeight: 'bold',
            height: 20,
            '& .MuiChip-label': { px: 1, fontSize: '0.7rem' }
          }}
        />

        {isHighestBidder && (
          <Chip
            label="Your Bid"
            color="success"
            size="small"
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              fontWeight: 'bold',
              height: 20,
              '& .MuiChip-label': { px: 1, fontSize: '0.7rem' }
            }}
          />
        )}

        <Box sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          bgcolor: 'rgba(0,0,0,0.7)',
          color: 'white',
          p: { xs: 0.75, md: 1 },
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Typography variant="subtitle2" fontWeight="bold" sx={{ fontSize: { xs: '0.8rem', md: '0.9rem' } }}>
            {auction?.player?.name || 'Player'}
          </Typography>
          <Typography variant="caption" sx={{ fontWeight: 'bold', bgcolor: 'primary.main', px: 0.75, py: 0.25, borderRadius: 1 }}>
            OVR {auction?.player?.ratings?.overall || '??'}
          </Typography>
        </Box>
      </Box>

      <CardContent sx={{
        flexGrow: 1,
        p: { xs: 1, sm: 1.25, md: 1.5 },
        minHeight: { xs: '110px', sm: '120px', md: '130px' } // Minimum height to ensure content fits
      }}>
        <Box sx={{ mb: 0.5 }}>
          <Typography variant="caption" color="text.secondary" sx={{ fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' } }}>
            {auction?.player?.type || 'Unknown'} • {auction?.player?.nationality || 'Unknown'}
          </Typography>
        </Box>

        <Box sx={{ mb: 1 }}>
          <Typography variant="caption" sx={{ display: 'block', mb: 0.25, fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' } }}>
            Current Bid
          </Typography>
          <Typography variant="subtitle1" color="primary" fontWeight="bold" sx={{ fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' } }}>
            {(auction?.currentBid || 0).toLocaleString()} ₹
          </Typography>
          {auction?.currentBidder && (
            <Typography
              variant="caption"
              color={isHighestBidder ? 'success.main' : 'text.secondary'}
              sx={{ display: 'block', fontSize: { xs: '0.6rem', sm: '0.65rem', md: '0.7rem' } }}
            >
              {isHighestBidder
                ? 'You are the highest bidder!'
                : auction?.bids && Array.isArray(auction?.bids) && auction?.bids.some(bid => bid?.bidder === currentUser?.id)
                  ? 'You have been outbid'
                  : `Highest: ${auction.currentBidder?.username || 'Unknown'}`
              }
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AccessTimeIcon sx={{ mr: 0.5, color: 'text.secondary', fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' } }} />
          <Typography variant="caption" color="text.secondary" sx={{ fontSize: { xs: '0.65rem', sm: '0.7rem', md: '0.75rem' } }}>
            {timeLeft}
          </Typography>
        </Box>
      </CardContent>

      <CardActions sx={{
        p: { xs: 0.75, sm: 1, md: 1.25 },
        pt: 0
      }}>
        <Button
          fullWidth
          variant="contained"
          color="primary"
          size="small"
          startIcon={<GavelIcon sx={{ fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' } }} />}
          onClick={() => onBid(auction, minimumBid)}
          disabled={status.text !== 'Live' || isHighestBidder}
          sx={{
            borderRadius: 1.5,
            textTransform: 'none',
            py: { xs: 0.5, sm: 0.75 },
            fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' }
          }}
        >
          {isHighestBidder ? 'Highest Bid' : `Bid ${minimumBid.toLocaleString()} ₹`}
        </Button>
      </CardActions>
    </Card>
  );
};

const AuctionListings = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [auctions, setAuctions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [bidDialogOpen, setBidDialogOpen] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState(null);
  const [bidAmount, setBidAmount] = useState(0);
  const [bidLoading, setBidLoading] = useState(false);

  const [teamBudget, setTeamBudget] = useState(10000);
  const [activeBidsTotal, setActiveBidsTotal] = useState(0);
  const [refreshingBudget, setRefreshingBudget] = useState(false);
  
  // Admin edit/delete functionality
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editAuction, setEditAuction] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteAuction, setDeleteAuction] = useState(null);
  const [editFormData, setEditFormData] = useState({
    startingPrice: 0,
    minimumBidIncrement: 100,
    isActive: true
  });

  // Function to refresh active bids total
  const refreshActiveBidsTotal = useCallback(async () => {
    setRefreshingBudget(true);
    try {
      // Log the entire user object to see its structure
      console.log('User object:', user);
      
      // Check if user exists and has an _id property (MongoDB uses _id)
      const userId = user?._id;
      console.log('Refreshing active bids total for user ID:', userId);
      
      if (!userId) {
        console.log('No user ID available, cannot refresh bids');
        return [];
      }
      
      // Use the new getMyActiveBids function with the correct user ID property
      const result = await getMyActiveBids(userId);
      
      // Update the state with the total
      setActiveBidsTotal(result.total);
      
      console.log('Refreshed active bids total:', result.total);
      console.log('Active bids count:', result.activeBids.length);
      
      return result.activeBids;
    } catch (error) {
      console.error('Error refreshing active bids total:', error);
      return [];
    } finally {
      setRefreshingBudget(false);
    }
  }, [user]);

  // Set up periodic refresh of active bids
  useEffect(() => {
    // Only refresh if we have a user
    if (user && user._id) {
      console.log('Setting up periodic refresh for user:', user._id);
      
      // Initial refresh
      refreshActiveBidsTotal();
      
      // Set up interval to refresh every 30 seconds
      const intervalId = setInterval(() => {
        refreshActiveBidsTotal();
      }, 30000); // 30 seconds
      
      // Clean up interval on unmount
      return () => clearInterval(intervalId);
    } else {
      console.log('No user available for periodic refresh');
    }
  }, [refreshActiveBidsTotal, user]);

  // Load auctions and team budget
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Get status based on tab
        let status = '';
        if (tabValue === 1) status = 'live';
        else if (tabValue === 2) status = 'scheduled';
        else if (tabValue === 4) status = 'completed';

        // Load auctions from API
        let auctionsData;
        if (tabValue === 3) {
          // My bids tab
          auctionsData = await getMyBids();
        } else {
          // Other tabs
          auctionsData = await getAuctions({
            status,
            search: searchTerm,
            page: currentPage,
            limit: 9
          });
        }

        // Get the auctions data based on tab
        let filteredAuctions = tabValue === 3 ? auctionsData : auctionsData.auctions;
        const now = new Date();

        // Filter auctions based on tab
        if (tabValue === 1) { // "LIVE NOW" tab
          // Only include auctions that haven't ended yet
          filteredAuctions = filteredAuctions.filter(auction => {
            const endTime = new Date(auction.endTime);
            return now < endTime;
          });
        } else if (tabValue === 3) { // "MY BIDS" tab
          // Update the status of auctions that have ended
          filteredAuctions = filteredAuctions.map(auction => {
            const endTime = new Date(auction.endTime);
            if (now >= endTime && auction.status === 'live') {
              // If the auction has ended but still has 'live' status, update it
              return {
                ...auction,
                status: 'completed'
              };
            }
            return auction;
          });
        }

        setAuctions(filteredAuctions);
        if (auctionsData.pagination) {
          setTotalPages(auctionsData.pagination.pages);
        }

        // Load team budget
        try {
          const budgetData = await getTeamBudget();
          if (budgetData && budgetData.budget) {
            setTeamBudget(budgetData.budget.totalBudget);
          }
        } catch (budgetErr) {
          console.error('Error loading team budget:', budgetErr);
          // Fallback to localStorage for now
          const savedBudget = localStorage.getItem(`teamBudget_${user.id}`);
          if (savedBudget) {
            setTeamBudget(parseInt(savedBudget));
          }
        }
        
        // Refresh active bids total
        await refreshActiveBidsTotal();

        // This code has been moved to the refreshActiveBidsTotal function
      } catch (err) {
        console.error('Error loading auction data:', err);
        setError('Failed to load auction data: ' + (err.msg || err.message));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user, tabValue, searchTerm, currentPage]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
  };



  // Open bid dialog
  const handleOpenBidDialog = async (auction, suggestedBid) => {
    setSelectedAuction(auction);
    setBidAmount(suggestedBid);
    setBidDialogOpen(true);
    
    // Refresh active bids total when opening the dialog
    if (user && user._id) {
      console.log('Refreshing bids when opening dialog for user:', user._id);
      await refreshActiveBidsTotal();
    } else {
      console.log('Cannot refresh bids when opening dialog - no user ID available');
    }
  };

  // Place bid
  const handlePlaceBid = async () => {
    if (!selectedAuction) return;

    setBidLoading(true);
    try {
      // Validate bid amount
      if (bidAmount <= (selectedAuction?.currentBid || 0)) {
        setError('Bid amount must be higher than the current bid');
        return;
      }

      if (bidAmount < (selectedAuction?.currentBid || 0) + (selectedAuction?.minimumBidIncrement || 100)) {
        setError(`Minimum bid increment is ${selectedAuction?.minimumBidIncrement || 100} Credits`);
        return;
      }

      // Get the latest active bids total using the correct user ID property
      if (!user || !user._id) {
        setError('User information not available. Please refresh the page and try again.');
        return;
      }
      
      console.log('Getting active bids for user ID:', user._id);
      const activeBidsResult = await getMyActiveBids(user._id);
      const latestActiveBidsTotal = activeBidsResult.total;
      
      // Update the state
      setActiveBidsTotal(latestActiveBidsTotal);
      
      // Calculate available budget (total budget minus active bids plus current bid if user is highest bidder)
      const availableBudget = teamBudget - latestActiveBidsTotal +
        (selectedAuction?.currentBidder && selectedAuction?.currentBidder._id === user?.id ? selectedAuction?.currentBid || 0 : 0);

      if (bidAmount > availableBudget) {
        setError(`You do not have enough available budget. Your available budget is ${availableBudget.toLocaleString()} Credits`);
        return;
      }

      // Place the bid

      // Place bid via API
      await placeBid(selectedAuction._id, bidAmount);

      // Close dialog
      setBidDialogOpen(false);
      setSuccess('Bid placed successfully');

      // Refresh active bids total
      await refreshActiveBidsTotal();

      // Refresh auctions
      const auctionsData = await getAuctions({
        status: 'live',
        page: currentPage,
        limit: 9
      });

      // Filter out auctions that have ended
      const now = new Date();
      const filteredAuctions = auctionsData.auctions.filter(auction => {
        const endTime = new Date(auction.endTime);
        return now < endTime; // Only include auctions that haven't ended yet
      });

      setAuctions(filteredAuctions);
      setTotalPages(auctionsData.pagination.pages);
    } catch (err) {
      console.error('Error placing bid:', err);

      // Check if this is a team missing error
      if ((err.message && err.message.includes('TEAM_MISSING')) ||
          (err.response && err.response.data && err.response.data.errorCode === 'TEAM_MISSING')) {
        setError('Team not found. Attempting to create team automatically...');

        // Automatically create team and retry bid
        try {
          await handleCreateTeam();

          // Retry the bid after team creation
          console.log('Retrying bid after team creation...');
          await placeBid(selectedAuction._id, bidAmount);

          // If we get here, the bid was successful
          setBidDialogOpen(false);
          setSuccess('Team created and bid placed successfully!');

          // Refresh active bids total
          await refreshActiveBidsTotal();

          // Refresh auctions
          const auctionsData = await getAuctions({
            status: 'live',
            page: currentPage,
            limit: 9
          });

          // Filter out auctions that have ended
          const now = new Date();
          const filteredAuctions = auctionsData.auctions.filter(auction => {
            const endTime = new Date(auction.endTime);
            return now < endTime; // Only include auctions that haven't ended yet
          });

          setAuctions(filteredAuctions);
          setTotalPages(auctionsData.pagination.pages);
          return;
        } catch (retryErr) {
          console.error('Error creating team and retrying bid:', retryErr);
          setError('Failed to create team and place bid: ' + (retryErr.message || 'Unknown error'));
        }
      } else {
        setError('Failed to place bid: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setBidLoading(false);
    }
  };

  // Handle opening the edit dialog
  const handleOpenEditDialog = (auction) => {
    setEditAuction(auction);
    setEditFormData({
      startingPrice: auction.startingPrice,
      minimumBidIncrement: auction.minimumBidIncrement,
      isActive: auction.isActive
    });
    setEditDialogOpen(true);
  };

  // Handle closing the edit dialog
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setEditAuction(null);
  };

  // Handle edit form input changes
  const handleEditFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditFormData({
      ...editFormData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle submitting the edit form
  const handleEditSubmit = async () => {
    try {
      setLoading(true);
      
      // Convert string values to numbers
      const formData = {
        ...editFormData,
        startingPrice: Number(editFormData.startingPrice),
        minimumBidIncrement: Number(editFormData.minimumBidIncrement)
      };
      
      const result = await updateAuction(editAuction._id, formData);
      
      if (result.success) {
        setSuccess('Auction updated successfully');
        
        // Update the auction in the local state
        setAuctions(auctions.map(auction => 
          auction._id === editAuction._id ? { ...auction, ...formData } : auction
        ));
        
        handleCloseEditDialog();
      } else {
        setError('Failed to update auction');
      }
    } catch (err) {
      console.error('Error updating auction:', err);
      setError('Failed to update auction: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the delete confirmation dialog
  const handleOpenDeleteDialog = (auction) => {
    setDeleteAuction(auction);
    setDeleteDialogOpen(true);
  };

  // Handle closing the delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteAuction(null);
  };

  // Handle deleting an auction
  const handleDeleteAuction = async () => {
    try {
      setLoading(true);
      
      const result = await deleteAuction(deleteAuction._id);
      
      if (result.success) {
        setSuccess('Auction deleted successfully');
        
        // Remove the auction from the local state
        setAuctions(auctions.filter(auction => auction._id !== deleteAuction._id));
        
        handleCloseDeleteDialog();
      } else {
        setError('Failed to delete auction');
      }
    } catch (err) {
      console.error('Error deleting auction:', err);
      setError('Failed to delete auction: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Private function to create or verify team when needed
  const handleCreateTeam = async () => {
    try {
      setLoading(true);

      // First try to get the team settings to check if team exists
      try {
        const teamSettings = await getTeamSettings();
        console.log('Team already exists, settings:', teamSettings);

        // Now get the budget
        try {
          const budgetData = await getTeamBudget();
          console.log('Team budget:', budgetData);
          if (budgetData && budgetData.budget) {
            setTeamBudget(budgetData.budget.totalBudget);
            setSuccess('Team verified successfully. Your budget: ' + budgetData.budget.totalBudget + ' credits');
          } else {
            setSuccess('Team verified successfully.');
          }
          return;
        } catch (budgetErr) {
          console.error('Error getting team budget:', budgetErr);
          setSuccess('Team verified, but could not retrieve budget.');
          return;
        }
      } catch (teamErr) {
        console.log('No existing team found, creating new team...', teamErr);
      }

      // If we get here, team doesn't exist, so create one
      const result = await createTeam();
      console.log('Create team result:', result);

      if (result && result.success) {
        setSuccess('Team created successfully: ' + result.team.teamName);

        // Refresh team budget
        try {
          const budgetData = await getTeamBudget();
          if (budgetData && budgetData.budget) {
            setTeamBudget(budgetData.budget.totalBudget);
          }
        } catch (budgetErr) {
          console.error('Error getting team budget after creation:', budgetErr);
        }
      } else {
        setError('Team creation returned an unexpected result');
      }
    } catch (err) {
      console.error('Team verification/creation error:', err);
      setError('Failed to verify/create team: ' + (err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 1, sm: 1.5, md: 2 } }}>
      {/* Header with budget info */}
      <Paper sx={{ p: { xs: 1.5, md: 2 }, mb: { xs: 1.5, md: 2 }, borderRadius: 2 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: { xs: 0.5, md: 1 }
        }}>
          <Typography variant={{ xs: 'h5', md: 'h4' }} sx={{ mb: { xs: 1, sm: 0 } }}>
            Player Auctions
          </Typography>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            bgcolor: 'primary.dark',
            color: 'white',
            px: 2,
            py: 0.75,
            borderRadius: 2
          }}>
            <Typography variant={{ xs: 'subtitle1', md: 'h6' }}>
              Budget: {teamBudget.toLocaleString()} $
            </Typography>
          </Box>
        </Box>
        <Typography variant="subtitle2" color="text.secondary">
          Bid on players to add them to your team
        </Typography>
      </Paper>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: { xs: 2, md: 3 }, borderRadius: 1 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      {/* Main content */}
      <Paper sx={{ p: { xs: 2, md: 3 }, mb: 3, borderRadius: 2 }}>
        {/* Budget Display */}
        <Box 
          sx={{ 
            mb: 3, 
            p: 2, 
            borderRadius: 1, 
            bgcolor: 'background.paper', 
            boxShadow: 1,
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" fontWeight="bold">
              Your Budget
            </Typography>
            <Tooltip title="Refresh budget information" arrow>
              <IconButton 
                size="small" 
                onClick={() => refreshActiveBidsTotal()}
                sx={{ p: 0.5 }}
                disabled={refreshingBudget}
                color="primary"
              >
                {refreshingBudget ? (
                  <CircularProgress size={16} thickness={5} />
                ) : (
                  <RefreshIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          </Box>
          
          <Grid container spacing={1}>
            {/* Total Budget */}
            <Grid item xs={12} sm={4}>
              <Tooltip title="Your total team budget for the season" arrow placement="top">
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    Total Budget
                  </Typography>
                  <Typography variant="subtitle1" color="primary" fontWeight="bold">
                    {teamBudget.toLocaleString()} $
                  </Typography>
                </Box>
              </Tooltip>
            </Grid>
            
            {/* Locked in Bids */}
            <Grid item xs={12} sm={4}>
              <Tooltip title="Amount currently committed in active bids. This will be returned if you are outbid." arrow placement="top">
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    Locked in Bids
                  </Typography>
                  {refreshingBudget ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <CircularProgress size={20} thickness={5} color="warning" />
                    </Box>
                  ) : (
                    <Typography variant="subtitle1" color="warning.main" fontWeight="bold">
                      {activeBidsTotal.toLocaleString()} $
                    </Typography>
                  )}
                </Box>
              </Tooltip>
            </Grid>
            
            {/* Available Budget */}
            <Grid item xs={12} sm={4}>
              <Tooltip title="Budget available for new bids" arrow placement="top">
                <Box sx={{ textAlign: 'center', p: 1 }}>
                  <Typography variant="caption" color="text.secondary">
                    Available Budget
                  </Typography>
                  {refreshingBudget ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <CircularProgress size={20} thickness={5} color="success" />
                    </Box>
                  ) : (
                    <Typography variant="subtitle1" color="success.main" fontWeight="bold">
                      {(teamBudget - activeBidsTotal).toLocaleString()} $
                    </Typography>
                  )}
                </Box>
              </Tooltip>
            </Grid>
          </Grid>
          
          {/* Budget Progress Bar */}
          <Box sx={{ mt: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.25 }}>
              <Box sx={{ width: '100%', mr: 0.5 }}>
                {refreshingBudget ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 0.5 }}>
                    <CircularProgress size={20} thickness={5} />
                  </Box>
                ) : (
                  <LinearProgress
                    variant="buffer"
                    value={(activeBidsTotal / teamBudget) * 100}
                    valueBuffer={100}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: 'warning.main',
                      },
                      '& .MuiLinearProgress-dashed': {
                        backgroundImage: 'none',
                      }
                    }}
                  />
                )}
              </Box>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', px: 1 }}>
              <Typography variant="caption" color="text.secondary">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'warning.main', mr: 0.5, verticalAlign: 'middle' }}></Box>
                Locked ({Math.round((activeBidsTotal / teamBudget) * 100)}%)
              </Typography>
              <Typography variant="caption" color="text.secondary">
                <Box component="span" sx={{ display: 'inline-block', width: 12, height: 12, borderRadius: '50%', bgcolor: 'success.main', mr: 0.5, verticalAlign: 'middle' }}></Box>
                Available ({Math.round(((teamBudget - activeBidsTotal) / teamBudget) * 100)}%)
              </Typography>
            </Box>
          </Box>
        </Box>
        
        {/* Tabs and actions - stacked on mobile, side by side on desktop */}
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'stretch', md: 'center' },
          mb: { xs: 1, md: 2 },
          gap: 1
        }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              minHeight: { xs: 36, md: 40 },
              '& .MuiTab-root': {
                minHeight: { xs: 36, md: 40 },
                py: { xs: 0.25, md: 0.5 },
                px: { xs: 1, md: 1.5 }
              }
            }}
          >
            <Tab label="All Auctions" />
            <Tab label="Live Now" />
            <Tab label="Upcoming" />
            <Tab label="My Bids" />
            <Tab label="Completed" />
          </Tabs>

          {/* Search and action buttons */}
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: 'center',
            gap: { xs: 0.5, sm: 1 },
            width: { xs: '100%', md: 'auto' }
          }}>
            {/* Search field - full width on mobile */}
            <TextField
              placeholder="Search auctions"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{
                order: { xs: -1, sm: 0 },
                width: { xs: '100%', sm: 200, md: 250 }
              }}
            />

            {/* Action buttons */}
            <Box sx={{
              display: 'flex',
              gap: 1,
              width: { xs: '100%', sm: 'auto' },
              justifyContent: { xs: 'space-between', sm: 'flex-start' }
            }}>
              <Button
                variant="outlined"
                size="small"
                component={Link}
                to="/auctions/live"
                color="secondary"
                startIcon={<GavelIcon />}
                sx={{ flex: { xs: 1, sm: 'none' } }}
              >
                Live
              </Button>
            </Box>
          </Box>
        </Box>

        {/* Loading state */}
        {loading ? (
          <Box sx={{ width: '100%', mt: 2, mb: 2 }}>
            <LinearProgress />
          </Box>
        ) : auctions.length === 0 ? (
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <Typography variant="body1" color="text.secondary">
              No auctions found in this category.
            </Typography>
          </Box>
        ) : (
          <>
            {/* Auction cards grid - using FIFA-style player cards */}
            <Grid 
              container 
              spacing={{ xs: 3, sm: 3, md: 4, lg: 4 }}
              sx={{ mb: 3 }} // Increased margin bottom
            >
              {auctions.map((auction) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={auction._id} sx={{ pb: 2 }}>
                  <AuctionPlayerCard
                    auction={auction}
                    onBid={handleOpenBidDialog}
                    currentUser={user}
                    onEdit={handleOpenEditDialog}
                    onDelete={handleOpenDeleteDialog}
                    isAdmin={user && user.role === 'admin'}
                  />
                </Grid>
              ))}
            </Grid>

            {/* Pagination */}
            {totalPages > 1 && (
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                mt: 3, // Reduced margin top
                mb: 1, // Reduced margin bottom
                pt: 1, // Reduced padding top
                position: 'relative',
                zIndex: 1
              }}>
                <Pagination
                  count={totalPages}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                  size="medium"
                />
              </Box>
            )}
          </>
        )}
      </Paper>

      {/* Bid Dialog */}
      <Dialog
        open={bidDialogOpen}
        onClose={() => setBidDialogOpen(false)}
        fullWidth
        maxWidth="xs"
        PaperProps={{
          sx: {
            borderRadius: 2,
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: 1.5
        }}>
          Place Bid
        </DialogTitle>
        <DialogContent dividers sx={{ p: { xs: 2, md: 3 } }}>
          {selectedAuction && (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box
                  component="img"
                  src={selectedAuction?.player?.image || '/default-player.png'}
                  alt={selectedAuction?.player?.name || 'Player'}
                  sx={{
                    width: 60,
                    height: 60,
                    borderRadius: 1,
                    objectFit: 'cover',
                    mr: 2,
                    border: '1px solid',
                    borderColor: 'divider'
                  }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = '/default-player.png';
                  }}
                />
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                    {selectedAuction?.player?.name || 'Player'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedAuction?.player?.type || 'Unknown'} • {selectedAuction?.player?.nationality || 'Unknown'}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 2,
                p: 1.5,
                bgcolor: 'background.paper',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}>
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Current Bid
                  </Typography>
                  <Typography variant="subtitle1" color="primary" fontWeight="bold">
                    {(selectedAuction?.currentBid || 0).toLocaleString()} $
                  </Typography>
                </Box>
              </Box>

              {/* Enhanced Budget Display in Bid Dialog */}
              <Box 
                sx={{ 
                  mb: 2, 
                  p: 1.5, 
                  borderRadius: 1, 
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography variant="subtitle2" gutterBottom>
                  Your Budget Status
                </Typography>
                
                <Grid container spacing={1}>
                  {/* Total Budget */}
                  <Grid item xs={4}>
                    <Tooltip title="Your total team budget for the season" arrow placement="top">
                      <Box>
                        <Typography variant="caption" color="text.secondary" display="block">
                          Total
                        </Typography>
                        <Typography variant="body2" color="primary" fontWeight="bold">
                          {teamBudget.toLocaleString()} $
                        </Typography>
                      </Box>
                    </Tooltip>
                  </Grid>
                  
                  {/* Locked in Bids */}
                  <Grid item xs={4}>
                    <Tooltip title="Amount currently committed in active bids. This will be returned if you are outbid." arrow placement="top">
                      <Box>
                        <Typography variant="caption" color="text.secondary" display="block">
                          Locked
                        </Typography>
                        {refreshingBudget ? (
                          <CircularProgress size={16} thickness={5} color="warning" />
                        ) : (
                          <Typography variant="body2" color="warning.main" fontWeight="bold">
                            {activeBidsTotal.toLocaleString()} $
                          </Typography>
                        )}
                      </Box>
                    </Tooltip>
                  </Grid>
                  
                  {/* Available Budget */}
                  <Grid item xs={4}>
                    <Tooltip title="Budget available for new bids" arrow placement="top">
                      <Box>
                        <Typography variant="caption" color="text.secondary" display="block">
                          Available
                        </Typography>
                        {refreshingBudget ? (
                          <CircularProgress size={16} thickness={5} color="success" />
                        ) : (
                          <Typography variant="body2" color="success.main" fontWeight="bold">
                            {(teamBudget - activeBidsTotal).toLocaleString()} $
                          </Typography>
                        )}
                      </Box>
                    </Tooltip>
                  </Grid>
                </Grid>
                
                {/* Budget Progress Bar */}
                <Box sx={{ mt: 1.5 }}>
                  {refreshingBudget ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>
                      <CircularProgress size={20} thickness={5} />
                    </Box>
                  ) : (
                    <LinearProgress
                      variant="buffer"
                      value={(activeBidsTotal / teamBudget) * 100}
                      valueBuffer={100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'warning.main',
                        },
                        '& .MuiLinearProgress-dashed': {
                          backgroundImage: 'none',
                        }
                      }}
                    />
                  )}
                </Box>
              </Box>

              <TextField
                fullWidth
                label="Your Bid Amount"
                type="number"
                value={bidAmount}
                onChange={(e) => setBidAmount(parseInt(e.target.value))}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
                helperText={`Minimum bid: ${((selectedAuction?.currentBid || 0) + (selectedAuction?.minimumBidIncrement || 100)).toLocaleString()} $`}
                sx={{ mt: 1 }}
              />
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setBidDialogOpen(false)}
            variant="outlined"
            size="small"
          >
            Cancel
          </Button>
          <Button
            onClick={handlePlaceBid}
            variant="contained"
            color="primary"
            size="small"
            startIcon={<GavelIcon />}
            disabled={
              bidLoading ||
              !selectedAuction ||
              bidAmount <= (selectedAuction?.currentBid || 0) ||
              bidAmount > (teamBudget - activeBidsTotal + (selectedAuction?.currentBidder && selectedAuction?.currentBidder._id === user?.id ? selectedAuction?.currentBid || 0 : 0))
            }
            title={
              selectedAuction && bidAmount > (teamBudget - activeBidsTotal + (selectedAuction?.currentBidder && selectedAuction?.currentBidder._id === user?.id ? selectedAuction?.currentBid || 0 : 0))
                ? `You don't have enough available budget. Your total budget: ${teamBudget.toLocaleString()} $, Active bids: ${activeBidsTotal.toLocaleString()} $`
                : ""
            }
          >
            {bidLoading ? 'Placing Bid...' : 'Place Bid'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Auction Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Auction</DialogTitle>
        <DialogContent>
          {editAuction && (
            <Box sx={{ pt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box
                  component="img"
                  src={editAuction?.player?.image || '/default-player.png'}
                  alt={editAuction?.player?.name || 'Player'}
                  sx={{ width: 60, height: 60, borderRadius: 1, mr: 2, objectFit: 'cover' }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = '/default-player.png';
                  }}
                />
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ mb: 0 }}>
                    {editAuction?.player?.name || 'Player'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {editAuction?.player?.type || 'Unknown'} • {editAuction?.player?.nationality || 'Unknown'}
                  </Typography>
                </Box>
              </Box>

              <TextField
                fullWidth
                label="Starting Price"
                name="startingPrice"
                type="number"
                value={editFormData.startingPrice}
                onChange={handleEditFormChange}
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />

              <TextField
                fullWidth
                label="Minimum Bid Increment"
                name="minimumBidIncrement"
                type="number"
                value={editFormData.minimumBidIncrement}
                onChange={handleEditFormChange}
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />

              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <Typography variant="body1" sx={{ mr: 2 }}>Active:</Typography>
                <input
                  type="checkbox"
                  name="isActive"
                  checked={editFormData.isActive}
                  onChange={handleEditFormChange}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button 
            onClick={handleEditSubmit}
            variant="contained"
            color="primary"
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Update Auction'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          {deleteAuction && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Are you sure you want to delete this auction?
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <Box
                  component="img"
                  src={deleteAuction?.player?.image || '/default-player.png'}
                  alt={deleteAuction?.player?.name || 'Player'}
                  sx={{ width: 60, height: 60, borderRadius: 1, mr: 2, objectFit: 'cover' }}
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = '/default-player.png';
                  }}
                />
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {deleteAuction?.player?.name || 'Player'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Bid: {(deleteAuction?.currentBid || 0).toLocaleString()} $
                  </Typography>
                </Box>
              </Box>
              {deleteAuction.status === 'live' && deleteAuction.currentBidder && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  Warning: This auction is live and has active bids. Deleting it will refund the current highest bidder.
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button 
            onClick={handleDeleteAuction}
            variant="contained"
            color="error"
            disabled={loading}
          >
            {loading ? 'Deleting...' : 'Delete Auction'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AuctionListings;
