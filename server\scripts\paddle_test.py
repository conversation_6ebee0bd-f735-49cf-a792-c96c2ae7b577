#!/usr/bin/env python
"""
Test PaddleOCR initialization
"""

import sys

try:
    from paddleocr import PaddleOCR
    print("PaddleOCR imported successfully")
    
    # Try to initialize PaddleOCR
    ocr = PaddleOCR(use_angle_cls=True, lang='en')
    print("PaddleOCR initialized successfully")
    
    # Print version info
    print(f"PaddleOCR version: {getattr(PaddleOCR, '__version__', 'unknown')}")
    
except ImportError as e:
    print(f"Error importing PaddleOCR: {e}")
    sys.exit(1)
except Exception as e:
    print(f"Error initializing PaddleOCR: {e}")
    sys.exit(1)

print("PaddleOCR test completed successfully")
sys.exit(0)