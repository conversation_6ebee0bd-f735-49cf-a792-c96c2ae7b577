const Auction = require('../models/Auction');
const Player = require('../models/Player');

/**
 * Process completed auctions and assign players to winners
 * This function is meant to be called by a scheduler
 */
const processCompletedAuctions = async () => {
  try {
    console.log('[Auction Scheduler] Processing completed auctions...');
    
    const now = new Date();
    
    // Find all auctions that have ended but still have status 'live'
    const completedAuctions = await Auction.find({
      status: 'live',
      endTime: { $lte: now },
      isActive: true,
      currentBidder: { $ne: null } // Only process auctions with a bidder
    }).populate('player').populate('currentBidder');
    
    console.log(`[Auction Scheduler] Found ${completedAuctions.length} completed auctions to process`);
    
    const results = {
      processed: 0,
      errors: 0,
      details: []
    };
    
    // Process each auction
    for (const auction of completedAuctions) {
      try {
        console.log(`[Auction Scheduler] Processing auction for player: ${auction.player.name}`);
        
        // Update auction status
        auction.status = 'completed';
        await auction.save();
        
        // Get the player
        const player = auction.player;
        
        // Update player owner and auction win information
        player.owner = auction.currentBidder._id;
        player.isAvailableOnMarket = false;
        player.auctionWin = {
          auctionId: auction._id,
          amount: auction.currentBid,
          date: now
        };
        
        await player.save();
        
        console.log(`[Auction Scheduler] Player ${player.name} assigned to ${auction.currentBidder.username} for ${auction.currentBid} credits`);
        
        results.processed++;
        results.details.push({
          auctionId: auction._id,
          playerName: player.name,
          winningBid: auction.currentBid,
          winner: auction.currentBidder.username
        });
      } catch (err) {
        console.error(`[Auction Scheduler] Error processing auction ${auction._id}:`, err);
        results.errors++;
        results.details.push({
          auctionId: auction._id,
          error: err.message
        });
      }
    }
    
    console.log(`[Auction Scheduler] Processed ${results.processed} auctions with ${results.errors} errors`);
    return results;
  } catch (err) {
    console.error('[Auction Scheduler] Error processing completed auctions:', err);
    throw err;
  }
};

module.exports = {
  processCompletedAuctions
};
