const OCRService = require('./services/ocrService');
const fs = require('fs');
const path = require('path');

async function testResultExclusion() {
  console.log('🧪 Testing Result Text Exclusion Logic...\n');

  try {
    // Load the most recent successful OCR.Space data
    const rawDataPath = path.join(__dirname, 'ocr-output/extracted/2025-05-26T14-21-20-762Z_ps5-t20-scorecard-example_ocrspace_raw.json');
    
    if (!fs.existsSync(rawDataPath)) {
      console.log('❌ No previous OCR.Space data found');
      return;
    }

    const rawData = JSON.parse(fs.readFileSync(rawDataPath, 'utf8'));
    console.log('✅ Loaded previous OCR.Space data');

    // Initialize OCR service
    const ocrService = new OCRService();

    // Test the improved extraction with result text exclusion
    console.log('\n📊 Testing improved extraction...');
    
    // Extract the response data
    const ocrSpaceData = ocrService.parseOCRSpaceResponse(rawData.apiResponse);
    
    // Test coordinate-based extraction
    if (ocrSpaceData.hasOverlay && ocrSpaceData.textOverlay) {
      console.log('🎯 Using coordinate-based extraction with result text exclusion...');
      const cricketData = ocrService.extractCricketDataFromCoordinates(ocrSpaceData.fullText, ocrSpaceData.textOverlay);
      
      console.log('\n🎯 IMPROVED EXTRACTION RESULTS:');
      console.log('================================');
      console.log(`Team 1: "${cricketData.team1}"`);
      console.log(`Team 2: "${cricketData.team2}"`);
      console.log(`Venue: "${cricketData.venue}"`);
      console.log(`Team 1 Score: ${cricketData.team1Score.runs}-${cricketData.team1Score.wickets} (${cricketData.team1Score.overs} overs)`);
      console.log(`Team 2 Score: ${cricketData.team2Score.runs}-${cricketData.team2Score.wickets} (${cricketData.team2Score.overs} overs)`);
      console.log(`Player of Match: "${cricketData.playerOfMatch}"`);
      console.log(`Confidence: ${cricketData.confidence}`);
      console.log(`Coordinate Elements: ${cricketData.coordinateData?.totalElements || 0}`);
      
      console.log('\n👥 PLAYER STATISTICS:');
      console.log('=====================');
      console.log(`Team 1 Batsmen: ${cricketData.team1Batsmen.length} players`);
      console.log(`Team 1 Bowlers: ${cricketData.team1Bowlers.length} players`);
      console.log(`Team 2 Batsmen: ${cricketData.team2Batsmen.length} players`);
      console.log(`Team 2 Bowlers: ${cricketData.team2Bowlers.length} players`);

      // Test specific result text exclusion
      console.log('\n🔍 RESULT TEXT EXCLUSION TEST:');
      console.log('==============================');
      
      const fullText = ocrSpaceData.fullText;
      const testNumbers = ['113', '96', '209'];
      
      for (const num of testNumbers) {
        const isFromResult = ocrService.isFromResultText(num, fullText);
        console.log(`Number "${num}": ${isFromResult ? '❌ EXCLUDED (from result text)' : '✅ INCLUDED (valid score)'}`);
      }

      // Save improved results
      const outputPath = path.join(__dirname, 'ocr-output/processed', `improved_${Date.now()}_cricket.json`);
      const outputData = {
        timestamp: new Date().toISOString(),
        testType: 'result_text_exclusion',
        cricketData: cricketData,
        improvements: {
          resultTextExclusion: true,
          coordinateBasedExtraction: true,
          improvedTeamNameDetection: true
        }
      };

      fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));
      console.log(`\n💾 Improved results saved to: ${outputPath}`);

    } else {
      console.log('❌ No coordinate overlay data available');
    }

  } catch (error) {
    console.error('❌ Error testing result exclusion:', error);
  }
}

// Run the test
testResultExclusion();
