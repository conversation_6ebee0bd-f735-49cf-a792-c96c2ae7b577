import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const HomePage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 8, textAlign: 'center' }}>
        <Typography variant="h2" component="h1" gutterBottom>
          Big Ant Cricket 24 Tournament Management
        </Typography>
        
        <Typography variant="h5" color="text.secondary" paragraph>
          Create and manage your own cricket tournaments, trade player cards, and track performance stats
        </Typography>
        
        <Box sx={{ mt: 4 }}>
          {user ? (
            <Button 
              variant="contained" 
              color="primary" 
              size="large"
              onClick={() => navigate('/dashboard')}
            >
              Go to Dashboard
            </Button>
          ) : (
            <Box>
              <Button 
                variant="contained" 
                color="primary" 
                size="large"
                onClick={() => navigate('/login')}
                sx={{ mx: 1 }}
              >
                Login
              </Button>
              <Button 
                variant="outlined" 
                color="primary" 
                size="large"
                onClick={() => navigate('/register')}
                sx={{ mx: 1 }}
              >
                Register
              </Button>
            </Box>
          )}
        </Box>
        
        <Box sx={{ mt: 8 }}>
          <Typography variant="h4" gutterBottom>
            Features
          </Typography>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4, flexWrap: 'wrap' }}>
            <Box sx={{ width: { xs: '100%', sm: '30%' }, mb: 4 }}>
              <Typography variant="h6">Player Card System</Typography>
              <Typography>
                Create and trade cricket player cards with dynamic ratings based on in-game performance
              </Typography>
            </Box>
            
            <Box sx={{ width: { xs: '100%', sm: '30%' }, mb: 4 }}>
              <Typography variant="h6">Tournament Management</Typography>
              <Typography>
                Create tournaments with different formats and track results and standings
              </Typography>
            </Box>
            
            <Box sx={{ width: { xs: '100%', sm: '30%' }, mb: 4 }}>
              <Typography variant="h6">Performance Tracking</Typography>
              <Typography>
                Upload scorecards and automatically track player performance stats
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};

export default HomePage;