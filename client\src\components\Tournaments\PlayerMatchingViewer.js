import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Autocomplete,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Sports as SportsIcon
} from '@mui/icons-material';
import { searchPlayers, confirmMatch } from '../../services/playerMatchingService';

/**
 * Player Matching Viewer Component
 * 
 * Displays player matching results from OCR with confidence indicators,
 * manual verification options, and player selection dialogs.
 */
const PlayerMatchingViewer = ({ 
  playerMatchingData, 
  onPlayerUpdate, 
  onMatchingComplete,
  showSummary = true 
}) => {
  const [editingPlayer, setEditingPlayer] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  if (!playerMatchingData || !playerMatchingData.processed) {
    const isAuthError = playerMatchingData?.isAuthError;
    const errorMessage = playerMatchingData?.error;
    
    return (
      <Alert severity={isAuthError ? "warning" : "info"}>
        {isAuthError 
          ? "Please ensure you're logged in to enable automatic player matching. Players will need to be verified manually."
          : errorMessage || "Player matching not available. Players will need to be verified manually."
        }
      </Alert>
    );
  }

  const { results, totalPlayers } = playerMatchingData;
  const summary = {
    automatic: results.automaticMatches?.length || 0,
    manual: results.manualVerificationRequired?.length || 0,
    noMatch: results.noMatches?.length || 0,
    total: totalPlayers
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'automatic': return 'success';
      case 'manual_verification': return 'warning';
      case 'no_match': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'automatic': return <CheckIcon />;
      case 'manual_verification': return <WarningIcon />;
      case 'no_match': return <ErrorIcon />;
      default: return <PersonIcon />;
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'success';
    if (confidence >= 0.6) return 'warning';
    return 'error';
  };

  const handleEditPlayer = async (playerData) => {
    setEditingPlayer(playerData);
    setSearchResults([]);
    
    // Pre-populate search with candidates if available
    if (playerData.candidates && playerData.candidates.length > 0) {
      setSearchResults(playerData.candidates);
    }
  };

  const handleSearchPlayers = async (query) => {
    if (!query || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      const results = await searchPlayers(query, { limit: 10 });
      setSearchResults(results.data || []);
    } catch (error) {
      console.error('Error searching players:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleConfirmMatch = async (selectedPlayer) => {
    if (!editingPlayer || !selectedPlayer) return;

    setConfirmLoading(true);
    try {
      await confirmMatch(editingPlayer.ocrName, selectedPlayer._id, 'manual');
      
      // Update the player data
      if (onPlayerUpdate) {
        onPlayerUpdate(editingPlayer.ocrName, {
          status: 'automatic',
          confidence: 0.9, // Manual confirmation gets high confidence
          matchedPlayer: selectedPlayer,
          candidates: []
        });
      }
      
      setEditingPlayer(null);
      setSearchResults([]);
      
      if (onMatchingComplete) {
        onMatchingComplete();
      }
    } catch (error) {
      console.error('Error confirming match:', error);
    } finally {
      setConfirmLoading(false);
    }
  };

  const renderPlayerRow = (playerData, index) => {
    const { ocrName, matchedPlayer, confidence, status, candidates } = playerData;
    
    return (
      <TableRow key={`${ocrName}-${index}`}>
        <TableCell>
          <Typography variant="body2" fontWeight="medium">
            {ocrName}
          </Typography>
        </TableCell>
        
        <TableCell>
          <Chip
            icon={getStatusIcon(status)}
            label={status.replace('_', ' ').toUpperCase()}
            color={getStatusColor(status)}
            size="small"
          />
        </TableCell>
        
        <TableCell>
          {confidence > 0 && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LinearProgress
                variant="determinate"
                value={confidence * 100}
                color={getConfidenceColor(confidence)}
                sx={{ width: 60, height: 6 }}
              />
              <Typography variant="caption" color={getConfidenceColor(confidence)}>
                {Math.round(confidence * 100)}%
              </Typography>
            </Box>
          )}
        </TableCell>
        
        <TableCell>
          {matchedPlayer ? (
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {matchedPlayer.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {matchedPlayer.team} • {matchedPlayer.type}
              </Typography>
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No match found
            </Typography>
          )}
        </TableCell>
        
        <TableCell>
          {(status === 'manual_verification' || status === 'no_match') && (
            <Tooltip title="Select player manually">
              <IconButton
                size="small"
                onClick={() => handleEditPlayer(playerData)}
                color="primary"
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </TableCell>
      </TableRow>
    );
  };

  const renderSummaryCards = () => (
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="success.main">
              {summary.automatic}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Auto Matched
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="warning.main">
              {summary.manual}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Need Review
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="error.main">
              {summary.noMatch}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              No Match
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} sm={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="h4" color="primary.main">
              {Math.round((summary.automatic / summary.total) * 100)}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Success Rate
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      {showSummary && renderSummaryCards()}
      
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SportsIcon />
            <Typography variant="h6">
              Player Matching Results ({summary.total} players)
            </Typography>
          </Box>
        </AccordionSummary>
        
        <AccordionDetails>
          {playerMatchingData.error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              Player matching failed: {playerMatchingData.error}
            </Alert>
          )}
          
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>OCR Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Confidence</TableCell>
                  <TableCell>Matched Player</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {results.automaticMatches?.map((player, index) => 
                  renderPlayerRow({ ...player, status: 'automatic' }, index)
                )}
                {results.manualVerificationRequired?.map((player, index) => 
                  renderPlayerRow({ 
                    ...player, 
                    status: 'manual_verification',
                    confidence: player.topCandidate?.confidence || 0,
                    candidates: player.candidates
                  }, index)
                )}
                {results.noMatches?.map((player, index) => 
                  renderPlayerRow({ ...player, status: 'no_match', confidence: 0 }, index)
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>

      {/* Player Selection Dialog */}
      <Dialog 
        open={!!editingPlayer} 
        onClose={() => setEditingPlayer(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Select Player for "{editingPlayer?.ocrName}"
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Autocomplete
              options={searchResults}
              getOptionLabel={(option) => `${option.name} (${option.team})`}
              loading={searchLoading}
              onInputChange={(event, value) => handleSearchPlayers(value)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Search Players"
                  placeholder="Type player name..."
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {option.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {option.team} • {option.type} • {option.nationality}
                    </Typography>
                  </Box>
                </Box>
              )}
              onChange={(event, value) => {
                if (value) {
                  handleConfirmMatch(value);
                }
              }}
            />
          </Box>
          
          {editingPlayer?.candidates && editingPlayer.candidates.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Suggested Matches:
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Player</TableCell>
                      <TableCell>Team</TableCell>
                      <TableCell>Confidence</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {editingPlayer.candidates.map((candidate, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2">
                            {candidate.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {candidate.team}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${Math.round(candidate.confidence * 100)}%`}
                            color={getConfidenceColor(candidate.confidence)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            onClick={() => handleConfirmMatch(candidate)}
                            disabled={confirmLoading}
                          >
                            Select
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setEditingPlayer(null)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PlayerMatchingViewer;