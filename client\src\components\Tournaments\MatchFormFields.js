import React from 'react';
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Paper,
  Chip,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch,
  Radio,
  RadioGroup
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Sports as SportsIcon
} from '@mui/icons-material';

/**
 * Match Form Fields Component
 * 
 * Renders form fields with validation highlighting and OCR data integration
 */
const MatchFormFields = ({ formData, tournament, validationIssues, onChange, uploadedImage }) => {
  
  const getFieldValidation = (fieldName) => {
    return validationIssues.find(issue => issue.field === fieldName);
  };

  const getFieldProps = (fieldName) => {
    const validation = getFieldValidation(fieldName);
    if (!validation) return {};

    return {
      error: validation.severity === 'error',
      helperText: validation.message,
      InputProps: {
        endAdornment: validation.severity === 'error' ? <ErrorIcon color="error" /> : <WarningIcon color="warning" />
      }
    };
  };

  const renderTeamSelect = (fieldName, label, value) => {
    const validation = getFieldValidation(fieldName);
    const teams = tournament.registeredTeams || [];

    return (
      <FormControl 
        fullWidth 
        error={validation?.severity === 'error'}
        sx={{ 
          bgcolor: validation ? (validation.severity === 'error' ? 'error.50' : 'warning.50') : 'inherit',
          borderRadius: 1
        }}
      >
        <InputLabel>{label}</InputLabel>
        <Select
          value={value || ''}
          label={label}
          onChange={(e) => onChange(fieldName, e.target.value)}
        >
          <MenuItem value="">
            <em>Select Team</em>
          </MenuItem>
          {teams.map((team) => (
            <MenuItem key={team._id} value={team._id}>
              {team.teamName}
            </MenuItem>
          ))}
        </Select>
        {validation && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1, px: 1 }}>
            {validation.severity === 'error' ? <ErrorIcon color="error" fontSize="small" /> : <WarningIcon color="warning" fontSize="small" />}
            <Typography variant="caption" color={validation.severity === 'error' ? 'error' : 'warning.main'}>
              {validation.message}
            </Typography>
          </Box>
        )}
      </FormControl>
    );
  };

  const renderScoreFields = (teamName, scoreData, fieldPrefix) => {
    const validation = getFieldValidation('scores');
    
    return (
      <Paper 
        sx={{ 
          p: 2, 
          bgcolor: validation ? 'warning.50' : 'inherit',
          border: validation ? '1px solid' : 'none',
          borderColor: 'warning.main'
        }}
      >
        <Typography variant="subtitle2" gutterBottom>
          {teamName} Score
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={4}>
            <TextField
              label="Runs"
              type="number"
              value={scoreData?.runs || 0}
              onChange={(e) => onChange(`${fieldPrefix}Score`, {
                ...scoreData,
                runs: parseInt(e.target.value) || 0
              })}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={4}>
            <TextField
              label="Wickets"
              type="number"
              value={scoreData?.wickets || 0}
              onChange={(e) => onChange(`${fieldPrefix}Score`, {
                ...scoreData,
                wickets: parseInt(e.target.value) || 0
              })}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={4}>
            <TextField
              label="Overs"
              type="number"
              step="0.1"
              value={scoreData?.overs || 0}
              onChange={(e) => onChange(`${fieldPrefix}Score`, {
                ...scoreData,
                overs: parseFloat(e.target.value) || 0
              })}
              fullWidth
              size="small"
            />
          </Grid>
        </Grid>
        {validation && (
          <Alert severity="warning" sx={{ mt: 1 }}>
            {validation.message}
          </Alert>
        )}
      </Paper>
    );
  };

  const renderPlayerStats = (title, players, fieldName) => {
    if (!players || players.length === 0) {
      return (
        <Paper sx={{ p: 2 }}>
          <Typography variant="subtitle2" gutterBottom>{title}</Typography>
          <Typography variant="body2" color="text.secondary">
            No player statistics detected
          </Typography>
        </Paper>
      );
    }

    return (
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SportsIcon />
            <Typography variant="subtitle2">{title}</Typography>
            <Chip label={`${players.length} players`} size="small" />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Player</TableCell>
                  {fieldName.includes('Batsmen') ? (
                    <>
                      <TableCell align="right">Runs</TableCell>
                      <TableCell align="right">Balls</TableCell>
                      <TableCell align="right">Status</TableCell>
                    </>
                  ) : (
                    <>
                      <TableCell align="right">Wickets</TableCell>
                      <TableCell align="right">Runs</TableCell>
                    </>
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {players.map((player, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <TextField
                        value={player.name || ''}
                        onChange={(e) => {
                          const newPlayers = [...players];
                          newPlayers[index] = { ...player, name: e.target.value };
                          onChange(fieldName, newPlayers);
                        }}
                        variant="standard"
                        fullWidth
                        size="small"
                      />
                    </TableCell>
                    {fieldName.includes('Batsmen') ? (
                      <>
                        <TableCell align="right">
                          <TextField
                            type="text"
                            value={typeof player.runs === 'string' && player.runs.endsWith('*') ? player.runs : (player.runs || 0)}
                            onChange={(e) => {
                              const newPlayers = [...players];
                              const value = e.target.value;
                              const isNotOut = value.endsWith('*');
                              const numericValue = parseInt(value.replace('*', ''));
                              newPlayers[index] = { 
                                ...player, 
                                runs: !isNaN(numericValue) ? (isNotOut ? `${numericValue}*` : numericValue) : (isNotOut ? '0*' : 0)
                              };
                              onChange(fieldName, newPlayers);
                            }}
                            variant="standard"
                            size="small"
                            inputProps={{ 
                              style: { textAlign: 'right' },
                              pattern: '[0-9]*[*]?'
                            }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            type="number"
                            value={player.balls || 0}
                            onChange={(e) => {
                              const newPlayers = [...players];
                              newPlayers[index] = { ...player, balls: parseInt(e.target.value) || 0 };
                              onChange(fieldName, newPlayers);
                            }}
                            variant="standard"
                            size="small"
                            inputProps={{ style: { textAlign: 'right' } }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          {player.verified && (
                            <Chip 
                              label="Verified" 
                              color="success" 
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </TableCell>
                      </>
                    ) : (
                      <>
                        <TableCell align="right">
                          <TextField
                            type="number"
                            value={player.wickets || 0}
                            onChange={(e) => {
                              const newPlayers = [...players];
                              newPlayers[index] = { ...player, wickets: parseInt(e.target.value) || 0 };
                              onChange(fieldName, newPlayers);
                            }}
                            variant="standard"
                            size="small"
                            inputProps={{ style: { textAlign: 'right' } }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            type="text"
                            value={typeof player.runs === 'string' && player.runs.endsWith('*') ? player.runs : (player.runs || 0)}
                            onChange={(e) => {
                              const newPlayers = [...players];
                              const value = e.target.value;
                              const isNotOut = value.endsWith('*');
                              const numericValue = parseInt(value.replace('*', ''));
                              newPlayers[index] = { 
                                ...player, 
                                runs: !isNaN(numericValue) ? (isNotOut ? `${numericValue}*` : numericValue) : (isNotOut ? '0*' : 0)
                              };
                              onChange(fieldName, newPlayers);
                            }}
                            variant="standard"
                            size="small"
                            inputProps={{ 
                              style: { textAlign: 'right' },
                              pattern: '[0-9]*[*]?'
                            }}
                          />
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>
    );
  };

  // Check if any players have been verified
  const hasVerifiedPlayers = 
    (formData.team1Batsmen && formData.team1Batsmen.some(p => p.verified)) || 
    (formData.team2Batsmen && formData.team2Batsmen.some(p => p.verified));

  return (
    <Box>
      {/* Verification Summary */}
      {hasVerifiedPlayers && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle2">
            Player scores verified with Google Vision
          </Typography>
          <Typography variant="body2">
            Some player scores have been automatically verified to correct potential 6/9 confusion.
            Verified players are marked with a "Verified" badge.
          </Typography>
        </Alert>
      )}
      
      {/* Basic Match Information */}
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SportsIcon />
        Match Information
      </Typography>

      <Grid container spacing={3}>
        {/* Teams */}
        <Grid item xs={12} md={6}>
          {renderTeamSelect('team1', 'Team 1', formData.team1)}
          {formData.team1Name && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              OCR detected: "{formData.team1Name}"
            </Typography>
          )}
        </Grid>
        
        <Grid item xs={12} md={6}>
          {renderTeamSelect('team2', 'Team 2', formData.team2)}
          {formData.team2Name && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              OCR detected: "{formData.team2Name}"
            </Typography>
          )}
        </Grid>

        {/* Home Team Selection */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isTeam1Home === true}
                  onChange={(e) => onChange('isTeam1Home', e.target.checked)}
                  color="primary"
                />
              }
              label={`${formData.team1Name || 'Team 1'} is the home team`}
            />
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              {formData.isTeam1Home ? 
                `${formData.team1Name || 'Team 1'} is home, ${formData.team2Name || 'Team 2'} is away` : 
                `${formData.team2Name || 'Team 2'} is home, ${formData.team1Name || 'Team 1'} is away`}
            </Typography>
          </Paper>
        </Grid>

        {/* Batting Order Selection */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Batting Order
            </Typography>
            <RadioGroup
              name="battingOrder"
              value={formData.homeTeamBattedFirst === undefined ? true : formData.homeTeamBattedFirst}
              onChange={(e) => onChange('homeTeamBattedFirst', e.target.value === 'true')}
            >
              <FormControlLabel
                value={true}
                control={<Radio color="primary" />}
                label={`${formData.isTeam1Home ? (formData.team1Name || 'Home team') : (formData.team2Name || 'Home team')} batted first`}
              />
              <FormControlLabel
                value={false}
                control={<Radio color="primary" />}
                label={`${formData.isTeam1Home ? (formData.team2Name || 'Away team') : (formData.team1Name || 'Away team')} batted first`}
              />
            </RadioGroup>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
              {formData.homeTeamBattedFirst ? 
                `${formData.isTeam1Home ? (formData.team1Name || 'Home team') : (formData.team2Name || 'Home team')} batted first, ${formData.isTeam1Home ? (formData.team2Name || 'Away team') : (formData.team1Name || 'Away team')} chased` : 
                `${formData.isTeam1Home ? (formData.team2Name || 'Away team') : (formData.team1Name || 'Home team')} batted first, ${formData.isTeam1Home ? (formData.team1Name || 'Home team') : (formData.team2Name || 'Away team')} chased`}
            </Typography>
          </Paper>
        </Grid>

        {/* Venue */}
        <Grid item xs={12} md={6}>
          <TextField
            label="Venue"
            value={formData.venue || ''}
            onChange={(e) => onChange('venue', e.target.value)}
            fullWidth
            {...getFieldProps('venue')}
          />
        </Grid>

        {/* Date */}
        <Grid item xs={12} md={6}>
          <TextField
            label="Match Date"
            type="date"
            value={formData.date ? new Date(formData.date).toISOString().split('T')[0] : ''}
            onChange={(e) => onChange('date', new Date(e.target.value))}
            fullWidth
            InputLabelProps={{ shrink: true }}
          />
        </Grid>

        {/* Scores */}
        <Grid item xs={12} md={6}>
          {renderScoreFields(formData.team1Name || 'Team 1', formData.team1Score, 'team1')}
        </Grid>

        <Grid item xs={12} md={6}>
          {renderScoreFields(formData.team2Name || 'Team 2', formData.team2Score, 'team2')}
        </Grid>

        {/* Winner */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Winner</InputLabel>
            <Select
              value={formData.winnerId || ''}
              label="Winner"
              onChange={(e) => onChange('winnerId', e.target.value)}
            >
              <MenuItem value="">
                <em>Select Winner</em>
              </MenuItem>
              {tournament.registeredTeams?.map((team) => (
                <MenuItem key={team._id} value={team._id}>
                  {team.teamName}
                </MenuItem>
              ))}
              <MenuItem value="tie">Match Tied</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Player of the Match */}
        <Grid item xs={12} md={6}>
          <TextField
            label="Player of the Match"
            value={formData.playerOfMatch || ''}
            onChange={(e) => onChange('playerOfMatch', e.target.value)}
            fullWidth
          />
        </Grid>

        {/* Match Notes */}
        <Grid item xs={12}>
          <TextField
            label="Match Notes"
            value={formData.matchNotes || ''}
            onChange={(e) => onChange('matchNotes', e.target.value)}
            fullWidth
            multiline
            rows={3}
          />
        </Grid>
      </Grid>

      {/* Player Statistics */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Player Statistics (From OCR)
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            {renderPlayerStats(`${formData.team1Name || 'Team 1'} Batsmen`, formData.team1Batsmen, 'team1Batsmen')}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderPlayerStats(`${formData.team1Name || 'Team 1'} Bowlers`, formData.team1Bowlers, 'team1Bowlers')}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderPlayerStats(`${formData.team2Name || 'Team 2'} Batsmen`, formData.team2Batsmen, 'team2Batsmen')}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderPlayerStats(`${formData.team2Name || 'Team 2'} Bowlers`, formData.team2Bowlers, 'team2Bowlers')}
          </Grid>
        </Grid>
      </Box>

      {/* Uploaded Image Preview */}
      {uploadedImage && (
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Uploaded Scorecard
          </Typography>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Box
              component="img"
              src={uploadedImage}
              alt="Uploaded scorecard"
              sx={{
                maxWidth: '100%',
                maxHeight: 300,
                objectFit: 'contain'
              }}
            />
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default MatchFormFields;
