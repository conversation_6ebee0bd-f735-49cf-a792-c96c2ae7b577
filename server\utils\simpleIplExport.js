/**
 * Simple utility to export IPL player names to CSV
 * This script focuses only on player names for better matching
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// List of IPL team URLs
const IPL_TEAM_URLS = [
  'https://www.iplt20.com/teams/chennai-super-kings/squad/2025',
  'https://www.iplt20.com/teams/delhi-capitals/squad/2025',
  'https://www.iplt20.com/teams/gujarat-titans/squad/2025',
  'https://www.iplt20.com/teams/kolkata-knight-riders/squad/2025',
  'https://www.iplt20.com/teams/lucknow-super-giants/squad/2025',
  'https://www.iplt20.com/teams/mumbai-indians/squad/2025',
  'https://www.iplt20.com/teams/punjab-kings/squad/2025',
  'https://www.iplt20.com/teams/rajasthan-royals/squad/2025',
  'https://www.iplt20.com/teams/royal-challengers-bengaluru/squad/2025',
  'https://www.iplt20.com/teams/sunrisers-hyderabad/squad/2025'
];

/**
 * Extract player names from an IPL team page
 * @param {string} url - Team URL
 * @returns {Promise<string[]>} - Array of player names
 */
async function extractPlayerNames(url) {
  console.log(`Extracting player names from ${url}`);
  let browser = null;
  
  try {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    });
    
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });
    
    // Extract player names using the specific selector for IPL website
    const playerNames = await page.evaluate(() => {
      // This selector targets the player name elements on IPL website
      const nameElements = document.querySelectorAll('.ih-p-name h2');
      
      // Convert to array of names
      return Array.from(nameElements)
        .map(el => el.textContent.trim())
        .filter(name => name); // Filter out empty names
    });
    
    console.log(`Found ${playerNames.length} players`);
    
    // Log the first few names for verification
    if (playerNames.length > 0) {
      console.log('Sample player names:');
      playerNames.slice(0, 5).forEach((name, i) => {
        console.log(`  ${i+1}. ${name}`);
      });
    } else {
      console.log('No player names found');
    }
    
    return playerNames;
  } catch (error) {
    console.error('Error extracting player names:', error);
    return [];
  } finally {
    if (browser) await browser.close();
  }
}

/**
 * Export player names to CSV
 * @param {string[]} playerNames - Array of player names
 * @param {string} outputPath - Path to save the CSV
 */
function exportToCsv(playerNames, outputPath) {
  // Create CSV content with just player names
  const csvContent = 'Player Name\n' + playerNames.map(name => `"${name}"`).join('\n');
  
  // Write to file
  fs.writeFileSync(outputPath, csvContent);
  console.log(`Exported ${playerNames.length} player names to ${outputPath}`);
}

/**
 * Main function to extract and export all IPL player names
 */
async function exportAllPlayerNames() {
  console.log('Starting simple IPL player name export');
  
  // Create array to hold all player names
  let allPlayerNames = [];
  
  // Process each team URL
  for (const url of IPL_TEAM_URLS) {
    const teamNames = await extractPlayerNames(url);
    allPlayerNames = [...allPlayerNames, ...teamNames];
  }
  
  // Remove duplicates
  const uniqueNames = [...new Set(allPlayerNames)];
  console.log(`Found ${uniqueNames.length} unique player names across all teams`);
  
  // Create output directory if it doesn't exist
  const outputDir = path.join(__dirname, '../exports');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Export to CSV
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputPath = path.join(outputDir, `ipl-player-names-${timestamp}.csv`);
  exportToCsv(uniqueNames, outputPath);
  
  return outputPath;
}

// Export for use in other modules
module.exports = {
  exportAllPlayerNames
};

// If script is run directly, execute the export
if (require.main === module) {
  exportAllPlayerNames()
    .then(outputPath => {
      console.log(`Export completed. File saved to: ${outputPath}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error during export:', error);
      process.exit(1);
    });
}
