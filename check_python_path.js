/**
 * <PERSON><PERSON>t to check Python path and PaddleOCR installation
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the Python path from the environment variable or use the default
const pythonPath = process.env.PYTHON_PATH || 'python';

console.log(`Checking Python path: ${pythonPath}`);

// Check Python version
exec(`${pythonPath} --version`, (error, stdout, stderr) => {
  const version = stdout || stderr; // Python version might be in stdout or stderr depending on Python version
  console.log(`Python version: ${version.trim()}`);
  
  // Check Python executable path
  exec(`${pythonPath} -c "import sys; print(sys.executable)"`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error getting Python executable: ${error.message}`);
      return;
    }
    
    const pythonExecutable = stdout.trim();
    console.log(`Python executable: ${pythonExecutable}`);
    
    // Check PaddleOCR installation
    exec(`${pythonPath} -c "try: from paddleocr import PaddleOCR; print('PaddleOCR is installed'); except ImportError as e: print(f'PaddleOCR is not installed: {e}')"`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error checking PaddleOCR: ${error.message}`);
        return;
      }
      
      console.log(`PaddleOCR check result: ${stdout.trim()}`);
      
      // Check if the directOcrService.js file is using the same Python path
      const servicePath = path.join(__dirname, 'server', 'services', 'ocr', 'directOcrService.js');
      
      if (fs.existsSync(servicePath)) {
        const content = fs.readFileSync(servicePath, 'utf8');
        const pythonPathMatch = content.match(/this\.pythonPath\s*=\s*process\.env\.PYTHON_PATH\s*\|\|\s*['"]([^'"]+)['"]/);
        
        if (pythonPathMatch) {
          const servicePythonPath = pythonPathMatch[1];
          console.log(`Python path in directOcrService.js: ${servicePythonPath}`);
          
          if (servicePythonPath !== pythonPath) {
            console.log(`⚠️ Warning: Python path mismatch. Service is using '${servicePythonPath}' but current path is '${pythonPath}'`);
          } else {
            console.log(`✅ Python paths match`);
          }
        } else {
          console.log(`Could not determine Python path from directOcrService.js`);
        }
      } else {
        console.log(`Could not find directOcrService.js at ${servicePath}`);
      }
    });
  });
});
