const express = require('express');
const router = express.Router();
const Player = require('../models/Player');
const { matchPlayersWithIplData, downloadPlayerImage } = require('../utils/playerPhotoMatcher');

/**
 * @route POST /api/player-photos/match
 * @desc Match players with IPL data and update their photos
 * @access Private
 */
router.post('/match', async (req, res) => {
  try {
    const { playerIds, iplTeamUrl } = req.body;

    if (!iplTeamUrl || !iplTeamUrl.includes('iplt20.com/teams')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid IPL team URL'
      });
    }

    if (!playerIds || !Array.isArray(playerIds) || playerIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No player IDs provided'
      });
    }

    // Get players from database
    const players = await Player.find({ _id: { $in: playerIds } });

    if (players.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No players found with the provided IDs'
      });
    }

    // Match players with IPL data
    const matchedPlayers = await matchPlayersWithIplData(players, iplTeamUrl);

    // Update player photos in database
    const updateResults = [];

    for (const player of matchedPlayers) {
      if (player.matched && player.imageUrl) {
        try {
          // Make sure we have a valid player ID
          // Access the ID from the _doc property if it's a Mongoose document
          let playerId = null;

          if (player._doc && player._doc._id) {
            // It's a Mongoose document
            playerId = player._doc._id.toString();
          } else if (player._id) {
            // It's a plain object with _id
            playerId = player._id.toString();
          }

          if (!playerId) {
            console.error('Player ID is undefined or invalid:', JSON.stringify(player, null, 2));
            updateResults.push({
              playerId: 'unknown',
              name: player.name || 'Unknown',
              success: false,
              message: 'Invalid player ID'
            });
            continue; // Skip to the next player
          }

          console.log(`Processing player: ${player.name} with ID: ${playerId}`);

          // Download image and save to uploads directory
          const localImagePath = await downloadPlayerImage(player.imageUrl, playerId);

          if (localImagePath) {
            // Update player in database
            await Player.findByIdAndUpdate(playerId, { image: localImagePath });

            updateResults.push({
              playerId: playerId,
              name: player.name,
              success: true,
              message: 'Photo updated successfully',
              imagePath: localImagePath
            });
          } else {
            updateResults.push({
              playerId: playerId,
              name: player.name,
              success: false,
              message: 'Failed to download image'
            });
          }
        } catch (error) {
          console.error(`Error updating photo for player ${playerId}:`, error);
          updateResults.push({
            playerId: playerId,
            name: player.name,
            success: false,
            message: 'Error updating photo'
          });
        }
      } else {
        // For players with no match, still ensure we have a valid ID
        let playerId = 'unknown';

        if (player._doc && player._doc._id) {
          // It's a Mongoose document
          playerId = player._doc._id.toString();
        } else if (player._id) {
          // It's a plain object with _id
          playerId = player._id.toString();
        }

        updateResults.push({
          playerId: playerId,
          name: player.name || 'Unknown',
          success: false,
          message: 'No match found on IPL website'
        });
      }
    }

    // Return results
    const successCount = updateResults.filter(result => result.success).length;

    return res.json({
      success: true,
      message: `Updated photos for ${successCount} out of ${players.length} players`,
      results: updateResults
    });
  } catch (error) {
    console.error('Error matching player photos:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while matching player photos'
    });
  }
});

module.exports = router;
