import axios from 'axios';
import { API_URL } from '../config';

/**
 * OCR Service
 *
 * This service provides functions for processing images with OCR
 * to extract cricket scorecard data using PaddleOCR.
 */

// Debug mode - can be overridden by options parameter
let DEBUG_MODE = false;

// Create API instance
const API = axios.create({
  baseURL: `${API_URL}/ocr`
});

// Add auth token to requests
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * Process an image with OCR to extract cricket scorecard data using PaddleOCR
 *
 * @param {Blob|File} imageFile - The image file to process
 * @param {Object} options - Options for processing
 * @returns {Promise<Object>} - Promise with extracted data
 */
export const processScorecardImage = (imageFile, options = {}) => {
  // Set debug mode from options
  DEBUG_MODE = options.debug || false;

  if (DEBUG_MODE) console.log('OCR processing with debug mode enabled', options);

  // Create a debug info object to track the processing steps
  const debugInfo = {
    startTime: new Date().toISOString(),
    options,
    steps: [],
    errors: []
  };

  const addDebugStep = (step, data = {}) => {
    if (DEBUG_MODE) {
      debugInfo.steps.push({
        step,
        timestamp: new Date().toISOString(),
        ...data
      });
      console.log(`[DEBUG] ${step}`, data);
    }
  };

  const addDebugError = (step, error) => {
    if (DEBUG_MODE) {
      debugInfo.errors.push({
        step,
        timestamp: new Date().toISOString(),
        message: error.message || String(error),
        stack: error.stack
      });
      console.error(`[DEBUG ERROR] ${step}:`, error);
    }
  };

  addDebugStep('Starting PaddleOCR processing');

  // Create form data for the image upload
  const formData = new FormData();
  formData.append('image', imageFile);
  formData.append('debug', DEBUG_MODE ? 'true' : 'false');

  // Process the image with PaddleOCR through the server
  return API.post('/process', formData)
    .then(response => {
      addDebugStep('Server processing complete', {
        extractionMethod: response.data.extractionMethod
      });

      // Add debug info if enabled
      if (DEBUG_MODE) {
        debugInfo.endTime = new Date().toISOString();
        response.data.debugInfo = debugInfo;
      }

      return response.data;
    })
    .catch(error => {
      addDebugError('Error processing image', error);
      console.error('OCR processing error:', error);
      throw new Error(error.response?.data?.message || 'OCR processing failed. Please try again.');
    });
};

export default { processScorecardImage };
