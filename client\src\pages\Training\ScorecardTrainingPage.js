import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Grid, // Note: Using legacy Grid component - consider upgrading to Grid v2 in the future
  CircularProgress,
  <PERSON>ert,
  <PERSON>nack<PERSON>,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import ScorecardLabeler from '../../components/Training/ScorecardLabeler';
import ModelTester from '../../components/Training/ModelTester';
import { useAuth } from '../../hooks/useAuth';
import mlTrainingService from '../../services/mlTrainingService';

/**
 * Scorecard Training Page
 *
 * This page provides access to the scorecard labeling and training interface.
 * It's only accessible to admin users.
 */
const ScorecardTrainingPage = () => {
  const { user } = useAuth();

  // Check if user is admin
  const isAdmin = user && user.role === 'admin';

  // Server state
  const [serverStatus, setServerStatus] = useState({ running: false, loading: true });
  const [serverAction, setServerAction] = useState({ loading: false, error: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [modelStatus, setModelStatus] = useState({ trained: false, loading: true });

  // Fetch server status on component mount
  useEffect(() => {
    if (isAdmin) {
      fetchServerStatus();
      fetchModelStatus();
    }
  }, [isAdmin]);

  // Fetch server status
  const fetchServerStatus = async () => {
    try {
      setServerStatus({ ...serverStatus, loading: true });

      // Call the API to check server status
      const status = await mlTrainingService.getTrainingServerStatus();

      // Update state with actual server status
      setServerStatus({
        running: status.running,
        loading: false
      });
    } catch (error) {
      console.error('Error fetching server status:', error);
      setServerStatus({ running: false, loading: false });
    }
  };

  // Fetch model status
  const fetchModelStatus = async () => {
    try {
      setModelStatus({ ...modelStatus, loading: true });

      // Call the API to check model status
      const status = await mlTrainingService.getModelStatus();

      // Update state with actual model status
      setModelStatus({
        trained: status.trained,
        lastTrained: status.lastTrained,
        labeled_images: status.labeled_images || 0,
        loading: false
      });
    } catch (error) {
      console.error('Error fetching model status:', error);
      setModelStatus({ trained: false, loading: false });
    }
  };

  // Start training server
  const handleStartServer = async () => {
    try {
      setServerAction({ loading: true, error: null });

      // Call the API to start the server
      const result = await mlTrainingService.startTrainingServer();

      // Fetch the updated server status
      await fetchServerStatus();

      showNotification(result.msg || 'Training server started successfully', 'success');
    } catch (error) {
      console.error('Error starting server:', error);

      // Extract detailed error information if available
      let errorMessage = 'Unknown error';
      if (error.response?.data) {
        const { msg, stdout, stderr } = error.response.data;
        errorMessage = msg || error.message || 'Error starting server';

        // Log detailed output for debugging
        if (stdout) console.log('Server stdout:', stdout);
        if (stderr) console.error('Server stderr:', stderr);

        // If there's stderr, show a more detailed error message
        if (stderr) {
          errorMessage += '\n\nServer Error: ' + stderr.substring(0, 200) + (stderr.length > 200 ? '...' : '');
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setServerAction({ loading: false, error: errorMessage });
      showNotification('Error starting training server: ' + errorMessage, 'error');
    } finally {
      setServerAction({ loading: false, error: null });
    }
  };

  // Stop training server
  const handleStopServer = async () => {
    try {
      setServerAction({ loading: true, error: null });

      // Call the API to stop the server
      const result = await mlTrainingService.stopTrainingServer();

      // Fetch the updated server status
      await fetchServerStatus();

      showNotification(result.msg || 'Training server stopped successfully', 'success');
    } catch (error) {
      console.error('Error stopping server:', error);
      setServerAction({ loading: false, error: error.message || 'Error stopping server' });
      showNotification('Error stopping training server: ' + (error.message || 'Unknown error'), 'error');
    } finally {
      setServerAction({ loading: false, error: null });
    }
  };

  // Train model
  const handleTrainModel = async () => {
    try {
      showNotification('Training model... This may take a while', 'info');

      // Call the API to train the model
      const result = await mlTrainingService.trainModel();

      // Fetch the updated model status
      await fetchModelStatus();

      showNotification(result.msg || 'Model trained successfully', 'success');
    } catch (error) {
      console.error('Error training model:', error);
      showNotification('Error training model: ' + (error.message || 'Unknown error'), 'error');
    }
  };

  // Show notification
  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
        {isAdmin ? (
          <>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" gutterBottom>
                Scorecard Training System
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Train the machine learning model to extract data from cricket scorecards.
              </Typography>
            </Box>

            {/* Server Management */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Training Server Status
                    </Typography>

                    {serverStatus.loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <Typography variant="body1" color={serverStatus.running ? 'success.main' : 'error.main'}>
                        {serverStatus.running ? 'Running' : 'Stopped'}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions>
                    {serverStatus.running ? (
                      <Button
                        variant="contained"
                        color="error"
                        onClick={handleStopServer}
                        disabled={serverAction.loading}
                        startIcon={serverAction.loading ? <CircularProgress size={20} /> : null}
                      >
                        Stop Server
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleStartServer}
                        disabled={serverAction.loading}
                        startIcon={serverAction.loading ? <CircularProgress size={20} /> : null}
                      >
                        Start Server
                      </Button>
                    )}

                    <Button
                      variant="outlined"
                      onClick={fetchServerStatus}
                      disabled={serverStatus.loading}
                    >
                      Refresh Status
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Model Status
                    </Typography>

                    {modelStatus.loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <>
                        <Typography variant="body1" color={modelStatus.trained ? 'success.main' : 'warning.main'}>
                          {modelStatus.trained ? 'Trained' : 'Not Trained'}
                        </Typography>

                        {modelStatus.trained && modelStatus.lastTrained && (
                          <Typography variant="body2" color="text.secondary">
                            Last trained: {new Date(modelStatus.lastTrained).toLocaleString()}
                          </Typography>
                        )}
                      </>
                    )}
                  </CardContent>
                  <CardActions>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleTrainModel}
                      disabled={!serverStatus.running}
                    >
                      Train Model
                    </Button>

                    <Button
                      variant="outlined"
                      onClick={fetchModelStatus}
                      disabled={modelStatus.loading}
                    >
                      Refresh Status
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            </Grid>

            {serverStatus.running ? (
              <>
                <ScorecardLabeler />

                {/* Model Testing Section */}
                <Box sx={{ mt: 4, mb: 4 }}>
                  <Typography variant="h5" gutterBottom>
                    Test Trained Model
                  </Typography>
                  <ModelTester />
                </Box>
              </>
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" color="warning.main" gutterBottom>
                  Training Server Not Running
                </Typography>
                <Typography variant="body1" paragraph>
                  Please start the training server to use the labeling interface.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleStartServer}
                  disabled={serverAction.loading}
                >
                  Start Server
                </Button>
              </Paper>
            )}
          </>
        ) : (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h5" color="error" gutterBottom>
              Access Denied
            </Typography>
            <Typography variant="body1" paragraph>
              You don't have permission to access this page. Only administrators can access the training system.
            </Typography>
            <Button
              component={RouterLink}
              to="/dashboard"
              variant="contained"
              color="primary"
            >
              Return to Dashboard
            </Button>
          </Paper>
        )}
      </Container>

      {/* Notification snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ScorecardTrainingPage;
