/**
 * IPL Player Data Scraper (Mock Implementation for Testing)
 *
 * This is a mock implementation that returns sample data
 * instead of actually scraping the IPL website.
 *
 * In a production environment, this would be replaced with
 * a server-side implementation that can bypass CORS restrictions.
 */

/**
 * Mock function that returns sample IPL player data
 * @param {string} teamUrl - URL of the team page (used to determine which team's data to return)
 * @returns {Promise<Array>} - Array of player objects
 */
export const scrapeIplPlayers = async (teamUrl) => {
  try {
    console.log(`Mock fetching player data from: ${teamUrl}`);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Determine which team's data to return based on the URL
    let teamName = 'Delhi Capitals';
    if (teamUrl.includes('mumbai-indians')) {
      teamName = 'Mumbai Indians';
    } else if (teamUrl.includes('chennai-super-kings')) {
      teamName = 'Chennai Super Kings';
    } else if (teamUrl.includes('royal-challengers')) {
      teamName = 'Royal Challengers Bangalore';
    }

    // Generate mock player data
    const players = generateMockPlayers(teamName);

    console.log(`Found ${players.length} players for ${teamName}`);
    return players;
  } catch (error) {
    console.error(`Mock scraping error: ${error}`);
    return [];
  }
};

/**
 * Maps IPL player role to our application's player type
 * @param {string} role - Player role from IPL website
 * @returns {string} - Mapped player type
 */
const mapPlayerRole = (role) => {
  if (!role) return 'Batsman';

  const lowerRole = role.toLowerCase();
  if (lowerRole.includes('all-rounder')) {
    return 'Allrounder';
  } else if (lowerRole.includes('bowler')) {
    return 'Bowler';
  } else if (lowerRole.includes('wicket')) {
    return 'Wicket Keeper';
  } else {
    return 'Batsman';
  }
};

/**
 * Maps player role to bowling hand
 * @param {string} role - Player role
 * @returns {string} - Bowling hand
 */
const mapBowlingHand = (role) => {
  if (!role) return 'None';

  const lowerRole = role.toLowerCase();
  if (lowerRole.includes('bowler') || lowerRole.includes('all-rounder')) {
    // 70% chance of right arm, 30% chance of left arm
    return Math.random() < 0.7 ? 'Right Arm Medium' : 'Left Arm Medium';
  } else {
    return 'None';
  }
};

/**
 * Generates a random player rating
 * @returns {number} - Player rating between 70-95
 */
const generateRating = () => {
  return Math.floor(Math.random() * 25) + 70; // 70-95
};

/**
 * Generates random player stats based on role
 * @param {string} role - Player role
 * @returns {Object} - Player stats
 */
const generateRandomStats = (role) => {
  const lowerRole = role ? role.toLowerCase() : '';

  if (lowerRole.includes('bowler')) {
    return {
      battingAverage: (Math.random() * 15 + 5).toFixed(2),
      strikeRate: (Math.random() * 80 + 70).toFixed(2),
      wickets: Math.floor(Math.random() * 150 + 50),
      economy: (Math.random() * 3 + 5).toFixed(2),
      highScore: Math.floor(Math.random() * 40 + 10)
    };
  } else if (lowerRole.includes('all-rounder')) {
    return {
      battingAverage: (Math.random() * 25 + 20).toFixed(2),
      strikeRate: (Math.random() * 40 + 120).toFixed(2),
      wickets: Math.floor(Math.random() * 100 + 20),
      economy: (Math.random() * 2 + 6).toFixed(2),
      highScore: Math.floor(Math.random() * 60 + 40)
    };
  } else {
    return {
      battingAverage: (Math.random() * 30 + 30).toFixed(2),
      strikeRate: (Math.random() * 50 + 130).toFixed(2),
      wickets: Math.floor(Math.random() * 10),
      economy: (Math.random() * 3 + 7).toFixed(2),
      highScore: Math.floor(Math.random() * 80 + 70)
    };
  }
};

/**
 * Generates a random height for a player
 * @returns {string} - Height in cm
 */
const generateRandomHeight = () => {
  return `${Math.floor(Math.random() * 20) + 170}cm`;
};
