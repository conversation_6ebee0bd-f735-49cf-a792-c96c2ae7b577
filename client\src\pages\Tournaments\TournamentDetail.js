import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  Box,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Avatar,
  Divider,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  EmojiEvents as TrophyIcon,
  CalendarToday as CalendarIcon,
  Group as TeamIcon,
  SportsVolleyball as SportIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  SportsCricket as CricketIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Add as AddIcon
} from '@mui/icons-material';

import { getTournamentById, registerTeam, unregisterTeam, deleteTournament } from '../../services/tournamentService';
import { useAuth } from '../../hooks/useAuth';
import MatchResultWizard from '../../components/Tournaments/MatchResultWizard';
import MatchDetail from '../../components/Tournaments/MatchDetail';

const TournamentDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tournament, setTournament] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addMatchDialogOpen, setAddMatchDialogOpen] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState(null);
  const [matchDetailOpen, setMatchDetailOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionSuccess, setActionSuccess] = useState(null);
  const [actionError, setActionError] = useState(null);

  useEffect(() => {
    fetchTournament();
  }, [id]);

  const fetchTournament = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await getTournamentById(id);
      setTournament(data);
    } catch (err) {
      console.error('Error fetching tournament:', err);
      setError('Failed to load tournament details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleRegisterTeam = async () => {
    try {
      setActionLoading(true);
      setActionError(null);
      setActionSuccess(null);

      await registerTeam(id);

      setActionSuccess('Your team has been registered successfully!');
      setConfirmDialogOpen(false);

      // Refresh tournament data
      fetchTournament();
    } catch (err) {
      console.error('Error registering team:', err);
      setActionError(err.message || 'Failed to register team. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUnregisterTeam = async () => {
    try {
      setActionLoading(true);
      setActionError(null);
      setActionSuccess(null);

      await unregisterTeam(id);

      setActionSuccess('Your team has been unregistered successfully!');
      setConfirmDialogOpen(false);

      // Refresh tournament data
      fetchTournament();
    } catch (err) {
      console.error('Error unregistering team:', err);
      setActionError(err.message || 'Failed to unregister team. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteTournament = async () => {
    try {
      setActionLoading(true);
      setActionError(null);

      await deleteTournament(id);

      setDeleteDialogOpen(false);
      navigate('/tournaments', { state: { message: 'Tournament deleted successfully' } });
    } catch (err) {
      console.error('Error deleting tournament:', err);
      setActionError(err.message || 'Failed to delete tournament. Please try again.');
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'registration':
        return 'primary';
      case 'in_progress':
        return 'success';
      case 'completed':
        return 'secondary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'registration':
        return 'Registration Open';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const isUserTeamRegistered = () => {
    if (!user || !user.team || !tournament || !tournament.registeredTeams) return false;
    return tournament.registeredTeams.some(team => team._id === user.team);
  };

  const canRegister = () => {
    if (!user || user.role !== 'team_owner' || !tournament) return false;
    return (
      tournament.status === 'registration' &&
      tournament.registeredTeams && tournament.registeredTeams.length < tournament.maxTeams &&
      !isUserTeamRegistered()
    );
  };

  const canUnregister = () => {
    if (!user || user.role !== 'team_owner' || !tournament) return false;
    return (
      tournament.status === 'registration' &&
      isUserTeamRegistered()
    );
  };

  const canAddMatch = () => {
    if (!user || user.role !== 'team_owner' || !tournament) return false;
    return (
      tournament.status === 'in_progress' &&
      isUserTeamRegistered()
    );
  };

  const handleAddMatchSuccess = () => {
    // Refresh tournament data after adding a match
    fetchTournament();
    setActionSuccess('Match added successfully!');
  };

  const handleViewMatch = (match) => {
    setSelectedMatch(match);
    setMatchDetailOpen(true);
  };

  const handleMatchActionComplete = () => {
    // Refresh tournament data after match action (verify, dispute, etc.)
    fetchTournament();
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          component={Link}
          to="/tournaments"
        >
          Back to Tournaments
        </Button>
      </Container>
    );
  }

  if (!tournament) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          Tournament not found.
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          component={Link}
          to="/tournaments"
        >
          Back to Tournaments
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Action messages */}
      {actionSuccess && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setActionSuccess(null)}>
          {actionSuccess}
        </Alert>
      )}

      {actionError && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setActionError(null)}>
          {actionError}
        </Alert>
      )}

      {/* Back button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          component={Link}
          to="/tournaments"
        >
          Back to Tournaments
        </Button>
      </Box>

      {/* Tournament header */}
      <Card sx={{ mb: 3, position: 'relative', overflow: 'visible' }}>
        <CardMedia
          component="img"
          height="200"
          image={tournament.banner || '/uploads/tournaments/default-banner.png'}
          alt={tournament.name}
        />

        <Avatar
          src={tournament.logo || '/uploads/tournaments/default.png'}
          sx={{
            width: 80,
            height: 80,
            position: 'absolute',
            top: 160,
            left: 24,
            border: '4px solid white',
            boxShadow: 2
          }}
        />

        <CardContent sx={{ pt: 5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Typography variant="h4" component="h1">
              {tournament.name}
            </Typography>

            <Chip
              label={getStatusLabel(tournament.status)}
              color={getStatusColor(tournament.status)}
            />
          </Box>

          <Typography variant="body1" color="text.secondary" paragraph>
            {tournament.description || 'No description provided.'}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Format: <strong>{tournament.format}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Dates: <strong>{formatDate(tournament.startDate)} - {formatDate(tournament.endDate)}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TeamIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Teams: <strong>{tournament.registeredTeams ? tournament.registeredTeams.length : 0} / {tournament.maxTeams}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Registration Deadline: <strong>{formatDate(tournament.registrationDeadline)}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" gutterBottom>
                Round Configuration
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Group Stage Rounds: <strong>{tournament.roundConfiguration?.groupStageRounds || 1}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Knockout Format: <strong>
                    {tournament.roundConfiguration?.knockoutFormat === 'single-elimination' ? 'Single Elimination' :
                     tournament.roundConfiguration?.knockoutFormat === 'double-elimination' ? 'Double Elimination' :
                     tournament.roundConfiguration?.knockoutFormat === 'playoffs' ? 'Playoffs' :
                     tournament.roundConfiguration?.knockoutFormat === 'ipl-style' ? 'IPL Style' : 'Single Elimination'}
                  </strong>
                </Typography>
              </Box>
            </Grid>

            {tournament.roundConfiguration?.knockoutFormat === 'ipl-style' && (
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    Playoff Qualifiers: <strong>
                      {tournament.roundConfiguration?.playoffQualifiers === 4 ? 'Top 4 Teams' : 'Top 8 Teams'}
                    </strong>
                  </Typography>
                </Box>
              </Grid>
            )}

            {tournament.roundConfiguration?.knockoutFormat === 'ipl-style' && (
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    Group Format: <strong>
                      {tournament.roundConfiguration?.useGroups ? 'Group Stage' : 'All Play All (No Groups)'}
                    </strong>
                  </Typography>
                </Box>
              </Grid>
            )}

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Third Place Match: <strong>{tournament.roundConfiguration?.thirdPlaceMatch ? 'Yes' : 'No'}</strong>
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary">
                  Final Format: <strong>
                    {tournament.roundConfiguration?.finalFormat === 'single-match' ? 'Single Match' :
                     tournament.roundConfiguration?.finalFormat === 'best-of-three' ? 'Best of Three' :
                     tournament.roundConfiguration?.finalFormat === 'best-of-five' ? 'Best of Five' : 'Single Match'}
                  </strong>
                </Typography>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            {user && user.role === 'admin' && (
              <>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  component={Link}
                  to={`/tournaments/${tournament._id}/edit`}
                >
                  Edit Tournament
                </Button>

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => setDeleteDialogOpen(true)}
                >
                  Delete Tournament
                </Button>
              </>
            )}

            {canRegister() && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<CheckCircleIcon />}
                onClick={() => setConfirmDialogOpen(true)}
              >
                Register Team
              </Button>
            )}

            {canUnregister() && (
              <Button
                variant="outlined"
                color="error"
                startIcon={<CancelIcon />}
                onClick={() => setConfirmDialogOpen(true)}
              >
                Unregister Team
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Tournament content tabs */}
      <Box sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} centered>
          <Tab label="Teams" icon={<TeamIcon />} iconPosition="start" />
          <Tab label="Matches" icon={<CricketIcon />} iconPosition="start" />
          <Tab label="Standings" icon={<TrophyIcon />} iconPosition="start" />
        </Tabs>
      </Box>

      {/* Teams tab */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {!tournament.registeredTeams || tournament.registeredTeams.length === 0 ? (
            <Grid item xs={12}>
              <Alert severity="info">
                No teams have registered for this tournament yet.
              </Alert>
            </Grid>
          ) : (
            tournament.registeredTeams.map((team) => (
              <Grid item xs={12} sm={6} md={4} key={team._id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar src={team.logo} alt={team.teamName} sx={{ mr: 2 }} />
                      <Typography variant="h6">{team.teamName}</Typography>
                    </Box>
                    <Button
                      variant="outlined"
                      size="small"
                      component={Link}
                      to={`/teams/${team._id}`}
                      fullWidth
                    >
                      View Team
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      )}

      {/* Matches tab */}
      {tabValue === 1 && (
        <Box>
          {/* Add Match button for team owners */}
          {canAddMatch() && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => setAddMatchDialogOpen(true)}
              >
                Add Match Result
              </Button>
            </Box>
          )}

          {tournament.phases && tournament.phases.length > 0 ? (
            tournament.phases.map((phase, index) => (
              <Box key={index} sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  {phase.name}
                </Typography>

                {phase.matches && phase.matches.length > 0 ? (
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Date</TableCell>
                          <TableCell>Teams</TableCell>
                          <TableCell>Venue</TableCell>
                          <TableCell>Format</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Result</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {phase.matches.map((match, matchIndex) => (
                          <TableRow
                            key={matchIndex}
                            hover
                            onClick={() => handleViewMatch(match)}
                            sx={{ cursor: 'pointer' }}
                          >
                            <TableCell>{formatDate(match.date)}</TableCell>
                            <TableCell>
                              {match.homeTeam.teamName} vs {match.awayTeam.teamName}
                            </TableCell>
                            <TableCell>{match.venue}</TableCell>
                            <TableCell>{match.format}</TableCell>
                            <TableCell>
                              <Chip
                                label={match.status}
                                color={
                                  match.status === 'completed' ? 'success' :
                                  match.status === 'in_progress' ? 'warning' :
                                  match.status === 'cancelled' ? 'error' : 'default'
                                }
                                size="small"
                              />
                              {match.result?.verificationStatus && match.result.verificationStatus !== 'pending' && (
                                <Chip
                                  label={match.result.verificationStatus}
                                  color={
                                    match.result.verificationStatus === 'verified' ? 'success' :
                                    match.result.verificationStatus === 'disputed' ? 'error' :
                                    match.result.verificationStatus === 'resolved' ? 'info' : 'default'
                                  }
                                  size="small"
                                  sx={{ ml: 0.5 }}
                                />
                              )}
                            </TableCell>
                            <TableCell>
                              {(() => {
                                console.log('registeredTeams:', tournament.registeredTeams);
                                console.log('match.result.winner:', match.result?.winner);
                                if (match.status !== 'completed') return '-';
                                if (match.result?.winner) {
                                  const winnerId =
                                    typeof match.result.winner === 'string'
                                      ? match.result.winner
                                      : match.result.winner?._id || match.result.winner?.toString();
                                  const winnerTeam =
                                    tournament.registeredTeams.find(
                                      t => t._id.toString() === winnerId
                                    ) || match.result.winner;
                                  const winnerName = winnerTeam?.teamName || 'Unknown Team';
                                  return (
                                    <Chip 
                                      label={`${winnerName} won`} 
                                      color="success" 
                                      size="small" 
                                    />
                                  );
                                } else if (match.result?.isTie) {
                                  return (
                                    <Chip 
                                      label="Match tied" 
                                      color="warning" 
                                      size="small" 
                                    />
                                  );
                                } else {
                                  return (
                                    <Typography variant="body2" color="text.secondary">
                                      No result
                                    </Typography>
                                  );
                                }
                              })()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Alert severity="info">
                    No matches scheduled for this phase yet.
                    {canAddMatch() && (
                      <Box sx={{ mt: 1 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<AddIcon />}
                          onClick={() => setAddMatchDialogOpen(true)}
                        >
                          Add Your First Match
                        </Button>
                      </Box>
                    )}
                  </Alert>
                )}
              </Box>
            ))
          ) : (
            <Alert severity="info">
              No matches have been scheduled for this tournament yet.
              {canAddMatch() && (
                <Box sx={{ mt: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={() => setAddMatchDialogOpen(true)}
                  >
                    Add Your First Match
                  </Button>
                </Box>
              )}
            </Alert>
          )}
        </Box>
      )}

      {/* Standings tab */}
      {tabValue === 2 && (
        <Box>
          {tournament.phases && tournament.phases.length > 0 ? (
            tournament.phases.map((phase, index) => (
              <Box key={index} sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  {phase.name}
                </Typography>

                {phase.standings && phase.standings.length > 0 ? (
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Team</TableCell>
                          <TableCell align="center">P</TableCell>
                          <TableCell align="center">W</TableCell>
                          <TableCell align="center">L</TableCell>
                          <TableCell align="center">T/NR</TableCell>
                          <TableCell align="center">Points</TableCell>
                          <TableCell align="center">NRR</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {phase.standings
                          .sort((a, b) => b.points - a.points || b.netRunRate - a.netRunRate)
                          .map((standing, standingIndex) => (
                            <TableRow key={standingIndex}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar
                                    src={standing.team.logo}
                                    alt={standing.team.teamName}
                                    sx={{ width: 24, height: 24, mr: 1 }}
                                  />
                                  {standing.team.teamName}
                                </Box>
                              </TableCell>
                              <TableCell align="center">{standing.played}</TableCell>
                              <TableCell align="center">{standing.won}</TableCell>
                              <TableCell align="center">{standing.lost}</TableCell>
                              <TableCell align="center">{standing.tied + standing.noResult}</TableCell>
                              <TableCell align="center"><strong>{standing.points}</strong></TableCell>
                              <TableCell align="center">{standing.netRunRate.toFixed(3)}</TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Alert severity="info">
                    No standings available for this phase yet.
                  </Alert>
                )}
              </Box>
            ))
          ) : (
            <Alert severity="info">
              No standings available for this tournament yet.
            </Alert>
          )}
        </Box>
      )}

      {/* Registration confirmation dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
      >
        <DialogTitle>
          {canRegister() ? 'Register for Tournament' : 'Unregister from Tournament'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {canRegister()
              ? `Are you sure you want to register your team for the "${tournament.name}" tournament?`
              : `Are you sure you want to unregister your team from the "${tournament.name}" tournament?`
            }
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)} disabled={actionLoading}>
            Cancel
          </Button>
          <Button
            onClick={canRegister() ? handleRegisterTeam : handleUnregisterTeam}
            color={canRegister() ? 'primary' : 'error'}
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : (canRegister() ? 'Register' : 'Unregister')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Tournament</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the "{tournament.name}" tournament? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={actionLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteTournament}
            color="error"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Match Result Wizard Dialog */}
      <MatchResultWizard
        open={addMatchDialogOpen}
        onClose={() => setAddMatchDialogOpen(false)}
        tournament={tournament}
        onMatchAdded={handleAddMatchSuccess}
      />

      {/* Match Detail Dialog */}
      <MatchDetail
        open={matchDetailOpen}
        onClose={() => setMatchDetailOpen(false)}
        match={selectedMatch}
        tournament={tournament}
        onActionComplete={handleMatchActionComplete}
      />
    </Container>
  );
};

export default TournamentDetail;
