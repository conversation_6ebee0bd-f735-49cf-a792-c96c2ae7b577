import axios from 'axios';
import { API_URL } from '../config';

// Create a default axios instance for API calls that don't use the training server
const defaultAxios = axios.create({
  baseURL: API_URL
});

// Add auth token to requests
defaultAxios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Log the API URL for debugging
console.log('API URL:', API_URL);
console.log('Default axios baseURL:', defaultAxios.defaults.baseURL);

// API base URL for the training server - try to get from localStorage or use default
const getTrainingApiBaseUrl = () => {
  const savedUrl = localStorage.getItem('training_api_url');
  // Try to use the same host as the main app but with a different port
  const currentHost = window.location.hostname;
  return savedUrl || `http://${currentHost}:5001/api`;
};

// API base URL for the training server
const API_BASE_URL = getTrainingApiBaseUrl();

// Create API instance for the main server
const API = axios.create({
  baseURL: API_URL
});

// Create API instance for the training server
const TRAINING_API = axios.create({
  baseURL: API_BASE_URL
});

// Function to update the training API URL
export const updateTrainingApiUrl = (newUrl) => {
  localStorage.setItem('training_api_url', newUrl);
  TRAINING_API.defaults.baseURL = newUrl;
  console.log('Updated training API URL to:', newUrl);
  // Reset availability check
  isTrainingServerAvailable = null;
  lastCheckTime = 0;
};

// Add auth token to requests
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Flag to track if training server is available
let isTrainingServerAvailable = null;
let lastCheckTime = 0;
const CHECK_INTERVAL = 30000; // 30 seconds

/**
 * Try to find the training server on different ports
 *
 * @returns {Promise<string|null>} - Promise that resolves to the working URL or null if not found
 */
export const findTrainingServer = async () => {
  console.log('Attempting to find training server on different ports...');

  // Try common ports and more
  const ports = [5000, 5001, 3001, 8000, 8080, 3000, 4000, 4001, 7000, 9000, 9001, 5002, 5003, 5004, 5005];

  // Try different base paths
  const basePaths = ['/api', '', '/v1', '/v1/api', '/api/v1'];

  // Get the current hostname
  const currentHost = window.location.hostname;

  // Generate all combinations
  const baseUrls = [];
  for (const port of ports) {
    for (const path of basePaths) {
      // Try both localhost and the current hostname
      baseUrls.push(`http://localhost:${port}${path}`);
      if (currentHost !== 'localhost') {
        baseUrls.push(`http://${currentHost}:${port}${path}`);
      }
    }
  }

  console.log(`Testing ${baseUrls.length} possible server URLs...`);

  // First try ping endpoint
  for (const url of baseUrls) {
    try {
      console.log(`Trying ${url}/ping...`);
      const response = await fetch(`${url}/ping`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(500) // Shorter timeout to check more URLs faster
      });

      if (response.ok) {
        console.log(`Found training server at ${url}!`);
        updateTrainingApiUrl(url);
        return url;
      }
    } catch (error) {
      // Silently continue to next URL
    }
  }

  // If ping doesn't work, try other common endpoints
  const endpoints = ['/categories', '/model-status', '/images', '/health', '/status'];

  for (const url of baseUrls) {
    for (const endpoint of endpoints) {
      try {
        console.log(`Trying ${url}${endpoint}...`);
        const response = await fetch(`${url}${endpoint}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(500)
        });

        if (response.ok) {
          console.log(`Found training server at ${url} (endpoint: ${endpoint})!`);
          updateTrainingApiUrl(url);
          return url;
        }
      } catch (error) {
        // Silently continue to next URL
      }
    }
  }

  console.warn('Could not find training server on any port or path');
  return null;
};

/**
 * Test a specific URL to see if it's a valid training server
 *
 * @param {string} url - The URL to test
 * @returns {Promise<boolean>} - Promise that resolves to true if the URL is a valid training server
 */
export const testServerUrl = async (url) => {
  console.log(`Testing specific URL: ${url}`);

  // Try endpoints specific to the training server
  const endpoints = ['/ping', '/categories', '/model-status', '/images'];

  for (const endpoint of endpoints) {
    try {
      console.log(`Trying ${url}${endpoint}...`);
      const response = await fetch(`${url}${endpoint}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(1000)
      });

      if (response.ok) {
        console.log(`Endpoint ${endpoint} is available at ${url}!`);
        return true;
      }
    } catch (error) {
      console.log(`Endpoint ${endpoint} not available at ${url}: ${error.message}`);
    }
  }

  console.warn(`No valid endpoints found at ${url}`);
  return false;
};

/**
 * Check if the training server is available
 *
 * @param {boolean} forceCheck - Force a new check even if cached result exists
 * @returns {Promise<boolean>} - Promise that resolves to true if server is available
 */
export const checkTrainingServerAvailability = async (forceCheck = false) => {
  const now = Date.now();

  console.log('Checking training server availability, forceCheck:', forceCheck);
  console.log('Current status:', { isTrainingServerAvailable, lastCheckTime, timeSinceLastCheck: now - lastCheckTime });

  // If we already checked recently and have a result, return the cached result
  if (!forceCheck && isTrainingServerAvailable !== null && (now - lastCheckTime) < CHECK_INTERVAL) {
    console.log('Using cached result:', isTrainingServerAvailable);
    return isTrainingServerAvailable;
  }

  // Log the base URL we're using
  console.log('Training API base URL:', TRAINING_API.defaults.baseURL);

  try {
    // Test the current URL first
    const isCurrentUrlValid = await testServerUrl(TRAINING_API.defaults.baseURL);

    if (isCurrentUrlValid) {
      console.log('Current URL is valid!');
      isTrainingServerAvailable = true;
      lastCheckTime = now;
      return true;
    }

    // If current URL is not valid, try to find the server on different ports
    console.log('Current URL is not valid, trying to find server on different ports...');
    const foundUrl = await findTrainingServer();

    if (foundUrl) {
      console.log('Found training server on different port:', foundUrl);
      isTrainingServerAvailable = true;
      lastCheckTime = now;
      return true;
    }

    // If we couldn't find the server, it's not available
    throw new Error('Could not find training server');
  } catch (error) {
    isTrainingServerAvailable = false;
    lastCheckTime = now;
    console.warn('Training server is not available:', error.message);
    return false;
  }
};

/**
 * Upload a scorecard image for labeling
 *
 * @param {File} file - The image file to upload
 * @returns {Promise<Object>} - Promise with upload result
 */
export const uploadScorecard = async (file) => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await TRAINING_API.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error uploading scorecard:', error);
    throw error.response?.data || error;
  }
};

/**
 * Get all uploaded scorecard images
 *
 * @returns {Promise<Array>} - Promise with list of image filenames
 */
export const getScorecardsImages = async () => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.get('/images');
    return response.data.images;
  } catch (error) {
    console.error('Error getting scorecard images:', error);
    throw error.response?.data || error;
  }
};

/**
 * Get a specific scorecard image
 *
 * @param {string} filename - The filename of the image
 * @returns {string} - Image URL
 */
export const getScorecardImage = (filename) => {
  return `${API_BASE_URL}/image/${filename}`;
};

/**
 * Get annotation for a specific scorecard image
 *
 * @param {string} filename - The filename of the image
 * @returns {Promise<Object>} - Promise with annotation data
 */
export const getScorecardAnnotation = async (filename) => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.get(`/annotations/${filename}`);
    return response.data.annotation;
  } catch (error) {
    console.error('Error getting scorecard annotation:', error);
    throw error.response?.data || error;
  }
};

/**
 * Save annotation for a scorecard image
 *
 * @param {string} filename - The filename of the image
 * @param {Array} annotation - The annotation data
 * @returns {Promise<Object>} - Promise with save result
 */
export const saveScorecardAnnotation = async (filename, annotation) => {
  // Force a fresh check for server availability
  const serverAvailable = await checkTrainingServerAvailability(true);

  if (!serverAvailable) {
    console.warn('Training server is not available for saving annotations');
    throw new Error('Training server is not available');
  }

  try {
    // Add a timeout to prevent hanging requests
    const response = await TRAINING_API.post('/save-annotation', {
      filename,
      annotation
    }, { timeout: 5000 });

    console.log('Successfully saved annotation to server:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error saving scorecard annotation:', error);

    // Provide more detailed error information
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out while saving annotation');
    } else if (!error.response) {
      throw new Error('Network error while saving annotation');
    } else {
      throw error.response?.data?.message ?
        new Error(error.response.data.message) :
        error.response?.data || error;
    }
  }
};

/**
 * Get available categories for labeling
 *
 * @returns {Promise<Array>} - Promise with list of categories
 */
export const getCategories = async () => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.get('/categories');
    return response.data.categories;
  } catch (error) {
    console.error('Error getting categories:', error);
    throw error.response?.data || error;
  }
};

/**
 * Add a custom category
 *
 * @param {string} category - The category name to add
 * @returns {Promise<Object>} - Promise with add result
 */
export const addCustomCategory = async (category) => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.post('/categories', { category });
    return response.data;
  } catch (error) {
    console.error('Error adding custom category:', error);

    // Provide more detailed error information
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    } else {
      throw error.response?.data || error;
    }
  }
};

/**
 * Delete a custom category
 *
 * @param {string} category - The category name to delete
 * @returns {Promise<Object>} - Promise with delete result
 */
export const deleteCustomCategory = async (category) => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.delete('/categories', {
      data: { category }
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting custom category:', error);

    // Provide more detailed error information
    if (error.response?.data?.error) {
      throw new Error(error.response.data.error);
    } else {
      throw error.response?.data || error;
    }
  }
};

/**
 * Train the ML model
 *
 * @param {boolean} simulateIfNeeded - Whether to simulate training if no endpoint is found
 * @returns {Promise<Object>} - Promise with training result
 */
export const trainModel = async (simulateIfNeeded = false) => {
  // Force a fresh check for server availability
  const serverAvailable = await checkTrainingServerAvailability(true);

  if (!serverAvailable) {
    console.warn('Training server is not available for model training');

    if (simulateIfNeeded) {
      console.log('Simulating model training due to server unavailability');
      // Simulate a delay for training
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        message: 'Model training simulated (server unavailable)',
        simulated: true
      };
    }

    throw new Error('Training server is not available');
  }

  try {
    console.log('Attempting to train model...');

    // Directly use the /train endpoint
    console.log('Using /train endpoint...');
    try {
      const response = await TRAINING_API.post('/train', {}, {
        timeout: 60000 // 1 minute timeout for training
      });

      console.log('Successfully trained model:', response.data);
      return response.data || { success: true, message: 'Model trained successfully' };
    } catch (trainError) {
      console.warn('Error using /train endpoint:', trainError.message);

      // Try with the full path
      try {
        console.log('Trying with full path /api/train...');
        const response = await fetch(`${TRAINING_API.defaults.baseURL}/train`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(60000)
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Successfully trained model using fetch:', data);
          return data || { success: true, message: 'Model trained successfully' };
        } else {
          throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
      } catch (fetchError) {
        console.warn('Error using fetch with /train:', fetchError.message);

        if (simulateIfNeeded) {
          console.log('Errors with training endpoints. Simulating model training.');
          // Simulate a delay for training
          await new Promise(resolve => setTimeout(resolve, 2000));
          return {
            success: true,
            message: 'Model training simulated (endpoint errors)',
            simulated: true
          };
        } else {
          throw new Error('Could not train model: ' + fetchError.message);
        }
      }
    }

    // Return a default success object
    return { success: true, message: 'Model trained successfully' };
  } catch (error) {
    console.error('Error training model:', error);

    if (simulateIfNeeded) {
      console.log('Error during training. Falling back to simulation.');
      // Simulate a delay for training
      await new Promise(resolve => setTimeout(resolve, 2000));
      return {
        success: true,
        message: 'Model training simulated (due to error)',
        simulated: true,
        originalError: error.message
      };
    }

    // Provide more detailed error information
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out while training model. The training might still be in progress on the server.');
    } else if (!error.response) {
      throw new Error('Network error while training model: ' + (error.message || 'Unknown error'));
    } else if (error.response?.status === 404) {
      throw new Error('Training endpoint not found. The server might not support model training.');
    } else if (error.response?.status === 405) {
      throw new Error('Method not allowed. The server does not support POST requests to the training endpoint.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error while training model. Check the server logs for details.');
    } else {
      const errorMessage = error.response?.data?.message || error.response?.data || error.message || 'Unknown error';
      throw new Error('Error training model: ' + errorMessage);
    }
  }
};

/**
 * Get the status of the trained model
 *
 * @returns {Promise<Object>} - Promise with model status
 */
export const getModelStatus = async () => {
  // Check if training server is available
  const serverAvailable = await checkTrainingServerAvailability();

  if (!serverAvailable) {
    throw new Error('Training server is not available');
  }

  try {
    const response = await TRAINING_API.get('/model-status');
    return response.data;
  } catch (error) {
    console.error('Error getting model status:', error);
    throw error.response?.data || error;
  }
};

/**
 * Get all labeled images
 *
 * @returns {Promise<Object>} - Promise with labeled images data
 */
export const getLabeledImages = async () => {
  try {
    // First try to use the training server directly
    const serverAvailable = await checkTrainingServerAvailability();

    if (serverAvailable) {
      try {
        // Try to use the training server directly
        const response = await TRAINING_API.get('/labeled-images');
        console.log('Successfully fetched labeled images directly:', response.data);
        return response.data;
      } catch (directError) {
        console.warn('Error fetching labeled images directly:', directError);
        // Fall back to using the main server as a proxy
      }
    }

    // Use the main server as a proxy
    console.log('Using main server as proxy to fetch labeled images...');
    const response = await API.get('/training/labeled-images');
    console.log('Successfully fetched labeled images via proxy:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching labeled images:', error);

    // Provide more detailed error information
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out while fetching labeled images');
    } else if (!error.response) {
      throw new Error('Network error while fetching labeled images');
    } else {
      throw error.response?.data?.message ?
        new Error(error.response.data.message) :
        error.response?.data || error;
    }
  }
};

/**
 * Clear annotations
 *
 * @param {Array} [filenames] - Optional array of filenames to clear. If not provided, all annotations will be cleared.
 * @returns {Promise<Object>} - Promise with clear result
 */
export const clearAnnotations = async (filenames) => {
  try {
    // First try to use the training server directly
    const serverAvailable = await checkTrainingServerAvailability();
    const payload = filenames && filenames.length > 0 ? { filenames } : {};

    if (serverAvailable) {
      try {
        // Try to use the training server directly
        const response = await TRAINING_API.post('/clear-annotations', payload);
        console.log('Successfully cleared annotations directly:', response.data);
        return response.data;
      } catch (directError) {
        console.warn('Error clearing annotations directly:', directError);
        // Fall back to using the main server as a proxy
      }
    }

    // Use the main server as a proxy
    console.log('Using main server as proxy to clear annotations...');
    const response = await API.post('/training/clear-annotations', payload);
    console.log('Successfully cleared annotations via proxy:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error clearing annotations:', error);

    // Provide more detailed error information
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timed out while clearing annotations');
    } else if (!error.response) {
      throw new Error('Network error while clearing annotations');
    } else {
      throw error.response?.data?.message ?
        new Error(error.response.data.message) :
        error.response?.data || error;
    }
  }
};

/**
 * Process a scorecard image with OCR
 *
 * @param {File} file - The image file to process
 * @param {Object} options - Processing options
 * @param {string} options.ocrMethod - OCR method to use ('paddle', 'google', 'tesseract', or 'auto')
 * @returns {Promise<Object>} - Promise with OCR result
 */
export const processScorecardWithOCR = async (file, options = {}) => {
  const formData = new FormData();
  formData.append('image', file);

  // Add OCR method if specified
  if (options.ocrMethod) {
    formData.append('ocrMethod', options.ocrMethod);
    console.log(`Using specified OCR method: ${options.ocrMethod}`);
  }

  // Add debug flag if specified
  if (options.debug) {
    formData.append('debug', 'true');
  }

  try {
    // Use the OCR Comparison endpoint which doesn't require authentication
    console.log('Calling OCR Comparison endpoint...');

    // Map the ocrMethod to the methods parameter for the OCR Comparison endpoint
    let methods = [];
    if (options.ocrMethod === 'paddle') {
      methods.push('direct');
      console.log(`Using OCR Comparison with method: direct (PaddleOCR)`);
    } else if (options.ocrMethod === 'google') {
      methods.push('google');
      console.log(`Using OCR Comparison with method: google`);
    } else if (options.ocrMethod === 'tesseract') {
      methods.push('tesseract');
      console.log(`Using OCR Comparison with method: tesseract`);
    } else {
      // Default to all methods
      methods = ['direct', 'google', 'tesseract'];
      console.log(`Using OCR Comparison with all methods`);
    }

    // Update the formData to use the methods parameter
    formData.delete('ocrMethod');
    formData.append('methods', methods.join(','));

    // Add a flag to indicate this is coming from the Scorecard Training System
    // This will help the server know to apply the latest OCR settings
    formData.append('source', 'scorecard-training');

    // Add a flag to use the latest OCR settings
    formData.append('useLatestSettings', 'true');

    const response = await axios.post('/api/ocr-comparison/compare', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // 60 seconds timeout for OCR processing
    });

    console.log('OCR comparison response:', response.data);

    // Ensure the response has the expected structure
    if (!response.data || !response.data.results) {
      console.warn('OCR response data is undefined or missing results, using empty object');
      return {
        data: {
          team1: 'Unknown Team 1',
          team2: 'Unknown Team 2',
          venue: 'Unknown Venue',
          team1Score: { runs: 0, wickets: 0, overs: 0 },
          team2Score: { runs: 0, wickets: 0, overs: 0 },
          team1Batsmen: [],
          team2Batsmen: [],
          team1Bowlers: [],
          team2Bowlers: [],
          extractionMethod: `${options.ocrMethod || 'ocr'}-error`,
          error: 'Empty response from OCR service'
        },
        rawText: '',
        error: 'Empty response from OCR service'
      };
    }

    // Extract the result for the selected method
    let result = null;
    let extractionMethod = '';
    let rawText = '';

    if (options.ocrMethod === 'paddle' && response.data.results.direct) {
      result = response.data.results.direct;
      extractionMethod = 'paddle-ocr-direct';
      rawText = result.full_text || '';
    } else if (options.ocrMethod === 'google' && response.data.results.google) {
      result = response.data.results.google;
      extractionMethod = 'google-vision';
      // For Google Vision, make sure we capture the full text
      // This is critical for the Scorecard Training System
      rawText = result.full_text || result.rawText || '';

      // Log the Google Vision result structure for debugging
      console.log('Google Vision result structure:', {
        hasFullText: !!result.full_text,
        hasRawText: !!result.rawText,
        hasTextAnnotations: !!(result.textAnnotations && result.textAnnotations.length),
        textAnnotationsCount: result.textAnnotations ? result.textAnnotations.length : 0,
        textLength: (result.full_text || result.rawText || '').length,
        firstFewChars: (result.full_text || result.rawText || '').substring(0, 50)
      });

      // If we don't have full_text in the result but we have it in the response
      if (!rawText && response.data.results.google.text) {
        rawText = response.data.results.google.text;
        console.log('Using text field from Google Vision result');
      }

      // Make sure textAnnotations is preserved
      if (!result.textAnnotations && response.data.results.google.textAnnotations) {
        result.textAnnotations = response.data.results.google.textAnnotations;
        console.log('Preserved textAnnotations from Google Vision result');
      }
    } else if (options.ocrMethod === 'tesseract' && response.data.results.tesseract) {
      result = response.data.results.tesseract;
      extractionMethod = 'tesseract';
      rawText = result.full_text || result.rawText || '';
    } else {
      // Use the first available result
      const firstMethod = Object.keys(response.data.results)[0];
      if (firstMethod) {
        result = response.data.results[firstMethod];
        extractionMethod = firstMethod === 'direct' ? 'paddle-ocr-direct' :
                          firstMethod === 'google' ? 'google-vision' : 'tesseract';
        rawText = result.full_text || result.rawText || '';

        // For Google Vision, also check the text field
        if (firstMethod === 'google' && !rawText && result.text) {
          rawText = result.text;
        }

        // For Google Vision, make sure textAnnotations is preserved
        if (firstMethod === 'google' && !result.textAnnotations && response.data.results.google.textAnnotations) {
          result.textAnnotations = response.data.results.google.textAnnotations;
          console.log('Preserved textAnnotations from Google Vision result in auto mode');
        }
      }
    }

    // If no result was found or there was an error, return a structured error response
    if (!result || result.error) {
      const errorMessage = result?.error || 'No valid OCR result found';
      console.warn(`Error in OCR result: ${errorMessage}`);

      // Even in error cases, preserve any raw text we might have
      const finalRawText = rawText || errorMessage;

      return {
        data: {
          team1: 'Unknown Team 1',
          team2: 'Unknown Team 2',
          venue: 'Unknown Venue',
          team1Score: { runs: 0, wickets: 0, overs: 0 },
          team2Score: { runs: 0, wickets: 0, overs: 0 },
          team1Batsmen: [],
          team2Batsmen: [],
          team1Bowlers: [],
          team2Bowlers: [],
          extractionMethod: `${extractionMethod}-error`,
          error: errorMessage,
          full_text: finalRawText // Include the raw text even in error cases
        },
        rawText: finalRawText,
        error: errorMessage
      };
    }

    // Return the response data in the expected format
    // Make sure to include the raw text in both the data object and at the top level
    return {
      data: {
        ...result,
        extractionMethod: extractionMethod,
        full_text: rawText // Add full_text to the data object for consistency
      },
      rawText: rawText,
      success: true
    };
  } catch (error) {
    console.error('Error processing scorecard with OCR:', error);

    // If the error is a timeout or network error, provide a more helpful message
    if (error.code === 'ECONNABORTED' || !error.response) {
      console.warn('OCR processing timed out or network error');
      const errorMessage = 'OCR processing timed out. The server might be busy or the image is too complex.';
      return {
        data: {
          team1: 'Unknown Team 1',
          team2: 'Unknown Team 2',
          venue: 'Unknown Venue',
          team1Score: { runs: 0, wickets: 0, overs: 0 },
          team2Score: { runs: 0, wickets: 0, overs: 0 },
          team1Batsmen: [],
          team2Batsmen: [],
          team1Bowlers: [],
          team2Bowlers: [],
          extractionMethod: `${options.ocrMethod || 'ocr'}-timeout-error`,
          error: errorMessage,
          full_text: errorMessage // Include full_text even in error cases
        },
        rawText: errorMessage, // Use the error message as raw text for consistency
        error: errorMessage
      };
    }

    // Return a structured error response instead of throwing
    const errorMessage = error.response?.data?.message || error.message || 'Unknown OCR error';
    return {
      data: {
        team1: 'Unknown Team 1',
        team2: 'Unknown Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        team1Batsmen: [],
        team2Batsmen: [],
        team1Bowlers: [],
        team2Bowlers: [],
        extractionMethod: `${options.ocrMethod || 'ocr'}-error`,
        error: errorMessage,
        full_text: errorMessage // Include full_text even in error cases
      },
      rawText: errorMessage, // Use the error message as raw text for consistency
      error: errorMessage
    };
  }
};

/**
 * Upload a scorecard image for training and process with OCR
 *
 * @param {File} file - The image file to upload
 * @param {Object} options - Processing options
 * @param {string} options.ocrMethod - OCR method to use ('paddle', 'google', 'tesseract', or 'auto')
 * @returns {Promise<Object>} - Promise with upload and OCR results
 */
export const uploadAndProcessScorecard = async (file, options = {}) => {
  try {
    let uploadResult = null;
    let ocrResult = null;

    // Check if training server is available
    const serverAvailable = await checkTrainingServerAvailability();

    if (serverAvailable) {
      try {
        // Try to upload the file to the training server
        uploadResult = await uploadScorecard(file);
        console.log('Successfully uploaded to training server:', uploadResult);
      } catch (uploadError) {
        console.warn('Failed to upload to training server:', uploadError.message);
        // Continue with OCR processing even if upload fails
      }
    } else {
      console.log('Training server not available, skipping upload');
    }

    // Process with OCR regardless of training server availability
    console.log('Processing with OCR using the OCR Comparison endpoint...');
    ocrResult = await processScorecardWithOCR(file, options);

    // Check if OCR processing returned an error
    if (ocrResult.error) {
      console.warn('OCR processing returned an error:', ocrResult.error);
      // The processScorecardWithOCR function now returns a structured error response
      // with the data property already set, so we don't need to create it here
    } else {
      console.log('Successfully processed with OCR:', ocrResult);
    }

    // Ensure ocrResult has the expected structure
    if (!ocrResult) {
      console.warn('OCR result is undefined, using empty data');
      ocrResult = {
        data: {
          full_text: 'No OCR result available',
          extractionMethod: 'error-no-result'
        },
        rawText: 'No OCR result available'
      };
    }

    if (!ocrResult.data) {
      console.warn('OCR result data is undefined, using empty object');
      ocrResult.data = {
        full_text: ocrResult.rawText || 'No OCR data available',
        extractionMethod: 'error-no-data'
      };
    }

    // Ensure we have full_text in the data object
    if (!ocrResult.data.full_text && ocrResult.rawText) {
      console.log('Adding rawText to data.full_text for consistency');
      ocrResult.data.full_text = ocrResult.rawText;
    }

    // Log the extraction method from the OCR result
    console.log('OCR extraction method:', ocrResult.data?.extractionMethod);

    // Combine the results
    return {
      ...(uploadResult || { filename: `scorecard_${Date.now()}.jpg` }),
      ocrData: ocrResult.data,
      extractionMethod: ocrResult.data?.extractionMethod, // Add extraction method to the result
      textElements: convertOcrResultToTextElements(ocrResult)
    };
  } catch (error) {
    console.error('Error uploading and processing scorecard:', error);
    throw error;
  }
};

/**
 * Convert OCR result to text elements format
 *
 * @param {Object} ocrResult - The OCR result from the server
 * @returns {Array} - Array of text elements
 */
export const convertOcrResultToTextElements = (ocrResult) => {
  if (!ocrResult || !ocrResult.data) {
    console.warn('No OCR result data available for conversion to text elements');
    return [];
  }

  console.log('Converting OCR result to text elements:', {
    hasData: !!ocrResult.data,
    hasRawText: !!ocrResult.rawText,
    extractionMethod: ocrResult.data?.extractionMethod,
    fullText: ocrResult.data?.full_text ? 'present' : 'missing'
  });

  const textElements = [];
  let id = 0;

  // Array to store bowling figures that need to be split into wickets and runs
  const bowlingFiguresToSplit = [];

  // Check if we have full_text from Google Vision or other OCR methods
  // This is the key difference between OCR Comparison and Scorecard Training System
  const fullText = ocrResult.rawText || ocrResult.data.full_text || '';

  // Extract team names
  if (ocrResult.data.teams) {
    ocrResult.data.teams.forEach((team, index) => {
      if (team.name) {
        textElements.push({
          id: id++,
          text: team.name,
          confidence: 0.95,
          category: index === 0 ? 'team1_name' : 'team2_name',
          x: 100 + (index * 300),
          y: 50,
          width: 150,
          height: 30,
          box: index === 0
            ? [[50, 35], [200, 35], [200, 65], [50, 65]]
            : [[350, 35], [500, 35], [500, 65], [350, 65]]
        });
      }

      // Extract scores
      if (team.score) {
        const scoreText = `${team.score.runs}/${team.score.wickets}`;
        textElements.push({
          id: id++,
          text: scoreText,
          confidence: 0.98,
          category: index === 0 ? 'team1_score' : 'team2_score',
          x: 100 + (index * 300),
          y: 100,
          width: 80,
          height: 25,
          box: index === 0
            ? [[60, 87], [140, 87], [140, 112], [60, 112]]
            : [[360, 87], [440, 87], [440, 112], [360, 112]]
        });
      }

      // Extract overs
      if (team.overs) {
        textElements.push({
          id: id++,
          text: `${team.overs} overs`,
          confidence: 0.96,
          category: index === 0 ? 'team1_overs' : 'team2_overs',
          x: 100 + (index * 300),
          y: 130,
          width: 100,
          height: 20,
          box: index === 0
            ? [[50, 120], [150, 120], [150, 140], [50, 140]]
            : [[350, 120], [450, 120], [450, 140], [350, 140]]
        });
      }
    });
  }

  // Extract match result
  if (ocrResult.data.result) {
    textElements.push({
      id: id++,
      text: ocrResult.data.result,
      confidence: 0.94,
      category: 'match_result',
      x: 250,
      y: 250,
      width: 300,
      height: 30,
      box: [[100, 235], [400, 235], [400, 265], [100, 265]]
    });
  }

  // Extract venue
  if (ocrResult.data.venue) {
    textElements.push({
      id: id++,
      text: ocrResult.data.venue,
      confidence: 0.89,
      category: 'venue',
      x: 250,
      y: 200,
      width: 200,
      height: 25,
      box: [[150, 187], [350, 187], [350, 212], [150, 212]]
    });
  }

  // Extract player of match
  if (ocrResult.data.playerOfMatch) {
    textElements.push({
      id: id++,
      text: ocrResult.data.playerOfMatch.name,
      confidence: 0.91,
      category: 'player_of_match',
      x: 100,
      y: 150,
      width: 120,
      height: 20,
      box: [[40, 140], [160, 140], [160, 160], [40, 160]]
    });

    if (ocrResult.data.playerOfMatch.stats) {
      textElements.push({
        id: id++,
        text: ocrResult.data.playerOfMatch.stats,
        confidence: 0.99,
        category: 'player_stats',
        x: 200,
        y: 150,
        width: 80,
        height: 20,
        box: [[180, 140], [260, 140], [260, 160], [180, 160]]
      });
    }
  }

  // Check if this is a Google Vision result
  const isGoogleVision = ocrResult.data?.extractionMethod?.includes('google');
  console.log(`OCR method: ${ocrResult.data?.extractionMethod}, isGoogleVision: ${isGoogleVision}`);

  // For Google Vision, we need special handling for text annotations
  if (isGoogleVision && ocrResult.data.textAnnotations) {
    // Check if we have text annotations
    const textAnnotations = ocrResult.data.textAnnotations || [];

    if (textAnnotations.length > 0) {
      console.log(`Processing Google Vision text annotations (${textAnnotations.length} items)`);

      // Process each text annotation
      textAnnotations.forEach((item, index) => {
        // Skip the first item which is the full text
        if (index === 0) return;

        // Log some sample annotations for debugging
        if (index < 5) {
          console.log(`Annotation ${index}: "${item.description}" at ${JSON.stringify(item.boundingPoly?.vertices)}`);
        }

        // Skip empty text
        if (!item.description || item.description.trim().length < 1) return;

        // Check if this item is already included in our text elements
        // Consider both text content AND position to avoid filtering out legitimate duplicates
        const alreadyIncluded = textElements.some(el => {
          // Get the bounding box from Google Vision format
          const box = item.boundingPoly?.vertices ? [
            [item.boundingPoly.vertices[0].x, item.boundingPoly.vertices[0].y],
            [item.boundingPoly.vertices[1].x, item.boundingPoly.vertices[1].y],
            [item.boundingPoly.vertices[2].x, item.boundingPoly.vertices[2].y],
            [item.boundingPoly.vertices[3].x, item.boundingPoly.vertices[3].y]
          ] : null;

          // If we don't have position data, fall back to text comparison only
          if (!box) {
            return el.text === item.description;
          }

          // Calculate center of the current item
          const x_min = Math.min(...box.map(p => p[0]));
          const x_max = Math.max(...box.map(p => p[0]));
          const y_min = Math.min(...box.map(p => p[1]));
          const y_max = Math.max(...box.map(p => p[1]));
          const x_center = (x_min + x_max) / 2;
          const y_center = (y_min + y_max) / 2;

          // Check if text matches AND positions are close
          return el.text === item.description &&
                 el.x && el.y && // Make sure element has position data
                 Math.abs(el.x - x_center) < 50 && // Within 50 pixels horizontally
                 Math.abs(el.y - y_center) < 20;   // Within 20 pixels vertically
        });

        if (!alreadyIncluded) {
          // Try to categorize the text
          let category = null;
          const text = item.description.trim();

          // Check for batsman runs
          if (/^\d+$/.test(text) && text.length < 4) {
            category = 'batsman_runs';
          }
          // Check for not-out batsman
          else if (/^\d+\*$/.test(text)) {
            category = 'batsman_runs';
          }
          // Check for just an asterisk (might be part of not-out indicator)
          else if (text === '*') {
            category = 'not_out';
          }
          // Check for balls faced
          else if (/^\(\d+\)$/.test(text)) {
            category = 'batsman_balls';
          }
          // Check for just digits in parentheses
          else if (/^\d+$/.test(text) && text.length < 3) {
            // Look at nearby elements to see if this might be balls faced
            const nearbyParenthesis = textElements.some(el =>
              el.text === '(' || el.text === ')' ||
              (el.category === 'batsman_balls' && el.text.includes('('))
            );
            if (nearbyParenthesis) {
              category = 'batsman_balls_number';
            } else {
              category = 'batsman_runs';
            }
          }
          // Check for bowling figures
          else if (/^\d+-\d+$/.test(text)) {
            category = 'bowler_figures';

            // Extract wickets and runs from the bowling figures
            const match = text.match(/^(\d+)-(\d+)$/);
            if (match) {
              const wickets = match[1];
              const runs = match[2];

              // Create separate elements for wickets and runs
              // These will be added later in the function
              bowlingFiguresToSplit.push({
                original: text,
                wickets: wickets,
                runs: runs,
                x: item.boundingPoly?.vertices ?
                  (item.boundingPoly.vertices[0].x + item.boundingPoly.vertices[1].x) / 2 : null,
                y: item.boundingPoly?.vertices ?
                  (item.boundingPoly.vertices[0].y + item.boundingPoly.vertices[3].y) / 2 : null,
                box: box
              });
            }
          }
          // Check for player names (all caps or proper case)
          else if ((/^[A-Z][A-Z\s]+$/.test(text) || /^[A-Z][a-z]+\s[A-Z][a-z]+$/.test(text)) && text.length > 3) {
            category = 'player_name';
          }
          // Check for overs
          else if (text.includes('OVERS') || text.includes('Overs')) {
            category = 'overs';
          }

          // Get the bounding box from Google Vision format
          const box = item.boundingPoly?.vertices ? [
            [item.boundingPoly.vertices[0].x, item.boundingPoly.vertices[0].y],
            [item.boundingPoly.vertices[1].x, item.boundingPoly.vertices[1].y],
            [item.boundingPoly.vertices[2].x, item.boundingPoly.vertices[2].y],
            [item.boundingPoly.vertices[3].x, item.boundingPoly.vertices[3].y]
          ] : [[150, 490 + (index * 25)], [250, 490 + (index * 25)], [250, 510 + (index * 25)], [150, 510 + (index * 25)]];

          // Calculate center and dimensions
          const x_min = Math.min(...box.map(p => p[0]));
          const x_max = Math.max(...box.map(p => p[0]));
          const y_min = Math.min(...box.map(p => p[1]));
          const y_max = Math.max(...box.map(p => p[1]));
          const x_center = (x_min + x_max) / 2;
          const y_center = (y_min + y_max) / 2;
          const width = x_max - x_min;
          const height = y_max - y_min;

          // Add the item as a text element
          textElements.push({
            id: id++,
            text: text,
            confidence: 0.9, // Google Vision doesn't provide confidence scores per element
            category: category,
            x: x_center,
            y: y_center,
            width: width,
            height: height,
            box: box
          });
        }
      });
    }
  }

  // Extract batsmen
  if (ocrResult.data.batsmen) {
    ocrResult.data.batsmen.forEach((batsman, index) => {
      if (batsman.name) {
        textElements.push({
          id: id++,
          text: batsman.name,
          confidence: 0.90,
          category: 'batsman_name',
          x: 100,
          y: 300 + (index * 30),
          width: 120,
          height: 20,
          box: [[40, 290 + (index * 30)], [160, 290 + (index * 30)], [160, 310 + (index * 30)], [40, 310 + (index * 30)]]
        });
      }

      if (batsman.runs !== undefined) {
        textElements.push({
          id: id++,
          text: `${batsman.runs}${batsman.notOut ? '*' : ''}`,
          confidence: 0.95,
          category: 'batsman_runs',
          x: 230,
          y: 300 + (index * 30),
          width: 40,
          height: 20,
          box: [[210, 290 + (index * 30)], [250, 290 + (index * 30)], [250, 310 + (index * 30)], [210, 310 + (index * 30)]]
        });
      }

      if (batsman.balls !== undefined) {
        textElements.push({
          id: id++,
          text: `(${batsman.balls})`,
          confidence: 0.93,
          category: 'batsman_balls',
          x: 280,
          y: 300 + (index * 30),
          width: 40,
          height: 20,
          box: [[260, 290 + (index * 30)], [300, 290 + (index * 30)], [300, 310 + (index * 30)], [260, 310 + (index * 30)]]
        });
      }
    });
  }

  // Extract bowlers
  if (ocrResult.data.bowlers) {
    ocrResult.data.bowlers.forEach((bowler, index) => {
      if (bowler.name) {
        textElements.push({
          id: id++,
          text: bowler.name,
          confidence: 0.89,
          category: 'bowler_name',
          x: 100,
          y: 450 + (index * 30),
          width: 120,
          height: 20,
          box: [[40, 440 + (index * 30)], [160, 440 + (index * 30)], [160, 460 + (index * 30)], [40, 460 + (index * 30)]]
        });
      }

      if (bowler.overs !== undefined) {
        textElements.push({
          id: id++,
          text: `${bowler.overs}`,
          confidence: 0.94,
          category: 'bowler_overs',
          x: 230,
          y: 450 + (index * 30),
          width: 40,
          height: 20,
          box: [[210, 440 + (index * 30)], [250, 440 + (index * 30)], [250, 460 + (index * 30)], [210, 460 + (index * 30)]]
        });
      }

      if (bowler.wickets !== undefined) {
        textElements.push({
          id: id++,
          text: `${bowler.wickets}`,
          confidence: 0.96,
          category: 'bowler_wickets',
          x: 280,
          y: 450 + (index * 30),
          width: 40,
          height: 20,
          box: [[260, 440 + (index * 30)], [300, 440 + (index * 30)], [300, 460 + (index * 30)], [260, 460 + (index * 30)]]
        });
      }
    });
  }

  // Process raw_items directly if available (especially for PaddleOCR)
  if (ocrResult.data.raw_items && ocrResult.data.raw_items.length > 0) {
    console.log(`Processing ${ocrResult.data.raw_items.length} raw items directly`);

    // Process each raw item
    ocrResult.data.raw_items.forEach((item, index) => {
      // Skip empty text
      if (!item.text || item.text.trim().length < 2) return;

      // Check if this item is already included in our text elements
      // Consider both text content AND position to avoid filtering out legitimate duplicates
      const alreadyIncluded = textElements.some(el => {
        // If we don't have position data for either element, fall back to text comparison only
        if (!el.x || !el.y || !item.x_center || !item.y_center) {
          return el.text === item.text;
        }

        // Check if text matches AND positions are close
        return el.text === item.text &&
               Math.abs(el.x - item.x_center) < 50 && // Within 50 pixels horizontally
               Math.abs(el.y - item.y_center) < 20;   // Within 20 pixels vertically
      });

      if (!alreadyIncluded) {
        // Try to categorize the text
        let category = null;

        // Check for batsman runs
        if (/^\d+$/.test(item.text) && item.text.length < 4) {
          category = 'batsman_runs';
        }
        // Check for not-out batsman
        else if (/^\d+\*$/.test(item.text)) {
          category = 'batsman_runs';
        }
        // Check for balls faced
        else if (/^\(\d+\)$/.test(item.text)) {
          category = 'batsman_balls';
        }
        // Check for bowling figures
        else if (/^\d+-\d+$/.test(item.text)) {
          category = 'bowler_figures';

          // Extract wickets and runs from the bowling figures
          const match = item.text.match(/^(\d+)-(\d+)$/);
          if (match) {
            const wickets = match[1];
            const runs = match[2];

            // Create separate elements for wickets and runs
            // These will be added later in the function
            bowlingFiguresToSplit.push({
              original: item.text,
              wickets: wickets,
              runs: runs,
              x: item.x_center,
              y: item.y_center,
              box: item.box
            });
          }
        }
        // Check for player names (all caps)
        else if (/^[A-Z][A-Z\s]+$/.test(item.text) && item.text.length > 3) {
          category = 'player_name';
        }
        // Check for overs
        else if (item.text.includes('OVERS')) {
          category = 'overs';
        }

        // Add the item as a text element
        textElements.push({
          id: id++,
          text: item.text,
          confidence: item.confidence || 0.8,
          category: category,
          x: item.x_center || 200,
          y: item.y_center || (500 + (index * 25)),
          width: item.width || 100,
          height: item.height || 20,
          box: item.box || [
            [item.x_min || 150, item.y_min || (490 + (index * 25))],
            [item.x_max || 250, item.y_min || (490 + (index * 25))],
            [item.x_max || 250, item.y_max || (510 + (index * 25))],
            [item.x_min || 150, item.y_max || (510 + (index * 25))]
          ]
        });
      }
    });
  }

  // Post-processing for Google Vision: combine runs and asterisks
  if (isGoogleVision) {
    // Add special handling for batsman runs in Google Vision
    // Google Vision often detects numbers that are batsman runs
    // Let's find all standalone numbers and categorize them as batsman_runs
    const standaloneNumbers = textElements.filter(el =>
      !el.category && // Not already categorized
      /^\d+$/.test(el.text) && // Just a number
      el.text.length < 4 // Not too long (likely not a year or other number)
    );

    console.log(`Found ${standaloneNumbers.length} potential batsman runs (standalone numbers)`);

    // Categorize these as batsman_runs
    standaloneNumbers.forEach(el => {
      el.category = 'batsman_runs';
      console.log(`Categorized "${el.text}" as batsman_runs`);
    });
  }

  // Process the full text from OCR (either from rawText or full_text)
  if (fullText) {
    const lines = fullText.split('\n');

    // Log the number of lines found
    console.log(`Found ${lines.length} lines in the full text`);

    // Log the first 20 lines for debugging
    console.log('First 20 lines of full text:');
    lines.slice(0, 20).forEach((line, i) => {
      console.log(`Line ${i+1}: "${line}"`);
    });

    // Log raw_items if available for debugging
    if (ocrResult.data.raw_items && ocrResult.data.raw_items.length > 0) {
      console.log(`Found ${ocrResult.data.raw_items.length} raw items`);
      console.log('First 10 raw items:');
      ocrResult.data.raw_items.slice(0, 10).forEach((item, i) => {
        console.log(`Item ${i+1}: "${item.text}" (confidence: ${item.confidence})`);
      });
    }

    // Process each line
    lines.forEach((line, index) => {
      // Skip empty lines or lines that are too short
      if (line.trim().length < 3) return;

      // Check if this line is already included in our text elements
      // For full text lines, we'll be more conservative about deduplication
      // Only consider exact matches as duplicates, not partial matches
      const alreadyIncluded = textElements.some(el => {
        // Make sure both el.text and line are defined and are strings
        if (!el || typeof el.text !== 'string' || typeof line !== 'string') {
          return false;
        }

        try {
          // Only consider exact matches as duplicates
          // This allows the same number to appear in different contexts
          return el.text.trim() === line.trim();
        } catch (error) {
          console.warn('Error comparing text elements:', error);
          return false;
        }
      });

      // Special handling for cricket scorecard data
      // Look for patterns like runs and balls faced (e.g., "38 (30)")
      const runsAndBallsPattern = /(\d+)\s*\((\d+)\)/;
      const bowlingFiguresPattern = /(\d+)-(\d+)/;
      const runsPattern = /^(\d+)$/;  // Just a number by itself could be runs
      const runsWithAsteriskPattern = /^(\d+)\*$/;  // Number with asterisk (not out batsman)

      let category = null;

      // Try to categorize the text based on patterns
      if (runsAndBallsPattern.test(line)) {
        // This might be a batsman's runs and balls faced
        const match = line.match(runsAndBallsPattern);
        if (match) {
          // Extract runs and balls as separate elements
          const runs = match[1];
          const balls = match[2];

          // Add runs as a separate element
          textElements.push({
            id: id++,
            text: runs,
            confidence: 0.95,
            category: 'batsman_runs',
            x: 230,
            y: 600 + (index * 25),
            width: 40,
            height: 20,
            box: [[210, 590 + (index * 25)], [250, 590 + (index * 25)], [250, 610 + (index * 25)], [210, 610 + (index * 25)]]
          });

          // Add balls as a separate element
          textElements.push({
            id: id++,
            text: `(${balls})`,
            confidence: 0.93,
            category: 'batsman_balls',
            x: 280,
            y: 600 + (index * 25),
            width: 40,
            height: 20,
            box: [[260, 590 + (index * 25)], [300, 590 + (index * 25)], [300, 610 + (index * 25)], [260, 610 + (index * 25)]]
          });

          // Set category for the original combined element
          category = 'batsman_stats';
        }
      } else if (bowlingFiguresPattern.test(line)) {
        // This might be bowling figures
        const match = line.match(bowlingFiguresPattern);
        if (match) {
          // Extract wickets and runs as separate elements
          const wickets = match[1];
          const runs = match[2];

          // Add to the list of bowling figures to split
          bowlingFiguresToSplit.push({
            original: line,
            wickets: wickets,
            runs: runs,
            x: 280,
            y: 600 + (index * 25),
            box: [[260, 590 + (index * 25)], [300, 590 + (index * 25)], [300, 610 + (index * 25)], [260, 610 + (index * 25)]]
          });

          // Set category for the original combined element
          category = 'bowler_figures';
        }
      } else if (line.includes('OVERS')) {
        category = 'overs';
      } else if (/\d+-\d+/.test(line)) {
        category = 'score';
      } else if (runsPattern.test(line) && line.trim().length < 4) {
        // This might be just runs (a number by itself)
        category = 'batsman_runs';
      } else if (runsWithAsteriskPattern.test(line)) {
        // This might be runs for a not out batsman
        category = 'batsman_runs';
      } else if (/^[A-Z][A-Z\s]+$/.test(line) && line.length > 3) {
        // This might be a player name (all caps)
        // Check if it's likely a batsman or bowler name

        // Check if any of the next few lines contain runs pattern or bowling figures
        let isBatsman = false;
        let isBowler = false;

        // Look ahead up to 3 lines to see if there are batting or bowling stats
        for (let i = 1; i <= 3 && index + i < lines.length; i++) {
          const nextLine = lines[index + i];
          if (runsAndBallsPattern.test(nextLine) || runsPattern.test(nextLine) || runsWithAsteriskPattern.test(nextLine)) {
            isBatsman = true;
            break;
          }
          if (bowlingFiguresPattern.test(nextLine)) {
            isBowler = true;
            break;
          }
        }

        if (isBatsman) {
          category = 'batsman_name';
        } else if (isBowler) {
          category = 'bowler_name';
        } else {
          // If we can't determine, just use a generic player name category
          category = 'player_name';
        }
      }

      if (!alreadyIncluded) {
        textElements.push({
          id: id++,
          text: line,
          confidence: 0.80,
          category: category,
          x: 200,
          y: 600 + (index * 25),
          width: 300,
          height: 20,
          box: [[50, 590 + (index * 25)], [350, 590 + (index * 25)], [350, 610 + (index * 25)], [50, 610 + (index * 25)]]
        });
      }
    });
  }

  // Process bowling figures to split into wickets and runs
  if (bowlingFiguresToSplit.length > 0) {
    console.log(`Processing ${bowlingFiguresToSplit.length} bowling figures to split into wickets and runs`);

    bowlingFiguresToSplit.forEach(figure => {
      // Add wickets as a separate element
      textElements.push({
        id: id++,
        text: figure.wickets,
        confidence: 0.95,
        category: 'bowler_wickets',
        x: figure.x ? figure.x - 20 : 280,
        y: figure.y || 450,
        width: 20,
        height: 20,
        box: figure.box || [[260, 440], [280, 440], [280, 460], [260, 460]]
      });

      // Add runs as a separate element
      textElements.push({
        id: id++,
        text: figure.runs,
        confidence: 0.95,
        category: 'bowler_runs',
        x: figure.x ? figure.x + 20 : 320,
        y: figure.y || 450,
        width: 20,
        height: 20,
        box: figure.box || [[300, 440], [320, 440], [320, 460], [300, 460]]
      });
    });
  }

  // Automatic team assignment
  // First, identify elements that need team assignment
  const teamElements = {
    batsman_name: [],
    batsman_runs: [],
    batsman_balls: [],
    bowler_name: [],
    bowler_figures: [],
    bowler_wickets: [],
    bowler_runs: []
  };

  // Collect elements by category
  textElements.forEach(element => {
    if (element.category && element.category in teamElements) {
      teamElements[element.category].push(element);
    }
  });

  // Try to determine batting order based on vertical position
  // Sort batsmen by y-coordinate (top to bottom)
  const sortedBatsmen = [...teamElements.batsman_name].sort((a, b) => a.y - b.y);

  // First half are likely team1, second half are likely team2
  const halfIndex = Math.ceil(sortedBatsmen.length / 2);
  const team1Batsmen = sortedBatsmen.slice(0, halfIndex);
  const team2Batsmen = sortedBatsmen.slice(halfIndex);

  // Update categories for batsmen
  team1Batsmen.forEach(element => {
    element.category = 'team1_batsman_name';
  });

  team2Batsmen.forEach(element => {
    element.category = 'team2_batsman_name';
  });

  // Do the same for bowlers
  const sortedBowlers = [...teamElements.bowler_name].sort((a, b) => a.y - b.y);
  const bowlerHalfIndex = Math.ceil(sortedBowlers.length / 2);

  // For bowlers, it's likely the opposite - team2 bowlers are for team1 batting and vice versa
  const team2Bowlers = sortedBowlers.slice(0, bowlerHalfIndex);
  const team1Bowlers = sortedBowlers.slice(bowlerHalfIndex);

  // Update categories for bowlers
  team1Bowlers.forEach(element => {
    element.category = 'team1_bowler_name';
  });

  team2Bowlers.forEach(element => {
    element.category = 'team2_bowler_name';
  });

  // Update related statistics based on proximity
  // For each batsman, find nearby runs and balls
  [...team1Batsmen, ...team2Batsmen].forEach(batsman => {
    const isTeam1 = batsman.category === 'team1_batsman_name';
    const teamPrefix = isTeam1 ? 'team1' : 'team2';

    // Find runs near this batsman
    teamElements.batsman_runs.forEach(runs => {
      if (Math.abs(runs.y - batsman.y) < 20 && Math.abs(runs.x - batsman.x) < 200) {
        runs.category = `${teamPrefix}_batsman_runs`;
      }
    });

    // Find balls near this batsman
    teamElements.batsman_balls.forEach(balls => {
      if (Math.abs(balls.y - batsman.y) < 20 && Math.abs(balls.x - batsman.x) < 200) {
        balls.category = `${teamPrefix}_batsman_balls`;
      }
    });
  });

  // For each bowler, find nearby figures, wickets and runs
  [...team1Bowlers, ...team2Bowlers].forEach(bowler => {
    const isTeam1 = bowler.category === 'team1_bowler_name';
    const teamPrefix = isTeam1 ? 'team1' : 'team2';

    // Find figures near this bowler
    teamElements.bowler_figures.forEach(figures => {
      if (Math.abs(figures.y - bowler.y) < 20 && Math.abs(figures.x - bowler.x) < 200) {
        figures.category = `${teamPrefix}_bowler_figures`;
      }
    });

    // Find wickets near this bowler
    teamElements.bowler_wickets.forEach(wickets => {
      if (Math.abs(wickets.y - bowler.y) < 20 && Math.abs(wickets.x - bowler.x) < 200) {
        wickets.category = `${teamPrefix}_bowler_wickets`;
      }
    });

    // Find runs near this bowler
    teamElements.bowler_runs.forEach(runs => {
      if (Math.abs(runs.y - bowler.y) < 20 && Math.abs(runs.x - bowler.x) < 200) {
        runs.category = `${teamPrefix}_bowler_runs`;
      }
    });
  });

  return textElements;
};

const trainingService = {
  uploadScorecard,
  getScorecardsImages,
  getScorecardImage,
  getScorecardAnnotation,
  saveScorecardAnnotation,
  getCategories,
  addCustomCategory,
  deleteCustomCategory,
  trainModel,
  getModelStatus,
  clearAnnotations,
  getLabeledImages,
  processScorecardWithOCR,
  uploadAndProcessScorecard,
  convertOcrResultToTextElements,
  checkTrainingServerAvailability
};

export default trainingService;
