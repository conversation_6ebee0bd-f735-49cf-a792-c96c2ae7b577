const fs = require('fs');
const path = require('path');
const https = require('https');

// Create directories if they don't exist
const backgroundsDir = path.join(__dirname, '../client/public/card-backgrounds');
if (!fs.existsSync(backgroundsDir)) {
  fs.mkdirSync(backgroundsDir, { recursive: true });
}

// Function to download image
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        const file = fs.createWriteStream(filepath);
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`Downloaded: ${filepath}`);
          resolve(filepath);
        });
      } else {
        reject(`Failed to download ${url}: ${response.statusCode}`);
      }
    }).on('error', (err) => {
      reject(`Error downloading ${url}: ${err.message}`);
    });
  });
};

// URL of a FIFA-style card background
const backgroundUrl = 'https://selimdoyranli.com/cdn/fut-player-card/img/card_bg.png';
const outputPath = path.join(backgroundsDir, 'cricket-card-bg.png');

// Download the background image
downloadImage(backgroundUrl, outputPath)
  .then(() => {
    console.log('Background image downloaded successfully!');
    
    // Update the CSS file to use the correct path
    const cssFilePath = path.join(__dirname, '../client/src/components/CricketPlayerCard.css');
    
    if (fs.existsSync(cssFilePath)) {
      let cssContent = fs.readFileSync(cssFilePath, 'utf8');
      
      // Replace the background image URL
      cssContent = cssContent.replace(
        /background-image: url\([^)]+\);/,
        `background-image: url('${process.env.PUBLIC_URL}/card-backgrounds/cricket-card-bg.png');`
      );
      
      fs.writeFileSync(cssFilePath, cssContent);
      console.log('CSS file updated successfully!');
    } else {
      console.error('CSS file not found:', cssFilePath);
    }
  })
  .catch(error => {
    console.error('Error:', error);
  });
