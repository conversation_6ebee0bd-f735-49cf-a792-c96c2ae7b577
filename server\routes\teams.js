const express = require('express');
const router = express.Router();
const teamController = require('../controllers/teamController');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');
const { upload } = require('../utils/fileUpload');

// @route   GET api/teams
// @desc    Get all teams
// @access  Private (Admin only)
router.get('/', [authenticateToken, roleAuth(['admin'])], teamController.getAllTeams);

// @route   GET api/teams/settings
// @desc    Get team settings
// @access  Private (Team Owner only)
router.get('/settings', [authenticateToken, roleAuth(['team_owner'])], teamController.getTeamSettings);

// @route   PUT api/teams/settings
// @desc    Update team settings
// @access  Private (Team Owner only)
router.put('/settings', [authenticateToken, roleAuth(['team_owner'])], teamController.updateTeamSettings);

// @route   POST api/teams/logo
// @desc    Upload team logo
// @access  Private (Team Owner only)
router.post(
  '/logo',
  [authenticateToken, roleAuth(['team_owner'])],
  upload.single('teamLogo'),
  teamController.uploadTeamLogo
);

// @route   GET api/teams/roster
// @desc    Get team roster
// @access  Private (Team Owner only)
router.get('/roster', [authenticateToken, roleAuth(['team_owner'])], teamController.getTeamRoster);

// @route   POST api/teams/roster/add
// @desc    Add player to team
// @access  Private (Team Owner only)
router.post('/roster/add', [authenticateToken, roleAuth(['team_owner'])], teamController.addPlayerToTeam);

// @route   POST api/teams/roster/remove
// @desc    Remove player from team
// @access  Private (Team Owner only)
router.post('/roster/remove', [authenticateToken, roleAuth(['team_owner'])], teamController.removePlayerFromTeam);

// @route   GET api/teams/statistics
// @desc    Get team statistics
// @access  Private (Team Owner only)
router.get('/statistics', [authenticateToken, roleAuth(['team_owner'])], teamController.getTeamStatistics);

// @route   GET api/teams/budget
// @desc    Get team budget
// @access  Private (Team Owner only)
router.get('/budget', [authenticateToken, roleAuth(['team_owner'])], teamController.getTeamBudget);

// @route   PUT api/teams/budget
// @desc    Update team budget
// @access  Private (Team Owner only)
router.put('/budget', [authenticateToken, roleAuth(['team_owner'])], teamController.updateTeamBudget);

// @route   POST api/teams/create
// @desc    Create a team for the user
// @access  Private (Team Owner only)
router.post('/create', authenticateToken, teamController.createTeam);

module.exports = router;
