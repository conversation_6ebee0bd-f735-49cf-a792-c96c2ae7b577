#!/usr/bin/env python
"""
Cricket Scorecard ML Training System

This script provides functionality to:
1. Process labeled scorecard data
2. Train ML models to classify scorecard elements
3. Export trained models for use in production
"""

import argparse
import json
import os
import sys
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import cv2
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
import joblib
import pickle

# Try to import PaddleOCR, with a helpful error message if it's not installed
try:
    from paddleocr import PaddleOCR
except ImportError:
    print("Error: PaddleOCR is not installed. Please install it with:")
    print("pip install paddleocr")
    sys.exit(1)

class ScorecardTrainer:
    """Cricket Scorecard ML Training System"""

    def __init__(self, data_dir: str = 'data', model_dir: str = 'models'):
        """
        Initialize the trainer

        Args:
            data_dir: Directory containing labeled data
            model_dir: Directory to save trained models
        """
        # Get the absolute path to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # If paths are relative, make them relative to the script directory
        if not os.path.isabs(data_dir):
            data_dir = os.path.join(script_dir, data_dir)
        if not os.path.isabs(model_dir):
            model_dir = os.path.join(script_dir, model_dir)

        self.data_dir = data_dir
        self.model_dir = model_dir
        self.ocr = PaddleOCR(use_angle_cls=True, lang='en')
        self.categories = [
            'team1_name', 'team2_name',
            'team1_score', 'team2_score',
            'team1_overs', 'team2_overs',
            'team1_batsman_name', 'team2_batsman_name',
            'team1_batsman_runs', 'team2_batsman_runs',
            'team1_batsman_balls', 'team2_batsman_balls',
            'team1_bowler_name', 'team2_bowler_name',
            'team1_bowler_figures', 'team2_bowler_figures',
            'team1_bowler_wickets', 'team2_bowler_wickets',
            'team1_bowler_runs', 'team2_bowler_runs',
            'batsman_name', 'batsman_runs', 'batsman_balls',
            'bowler_name', 'bowler_figures', 'bowler_wickets', 'bowler_runs',
            'venue', 'player_of_match', 'match_time'
        ]

        # Try to load custom categories if they exist
        categories_path = os.path.join(self.model_dir, 'categories.json')
        if os.path.exists(categories_path):
            try:
                with open(categories_path, 'r') as f:
                    self.categories = json.load(f)
                print(f"Loaded {len(self.categories)} categories from {categories_path}")
            except Exception as e:
                print(f"Error loading categories: {e}")
                # Keep using the default categories

        # Create directories if they don't exist
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(model_dir, exist_ok=True)
        os.makedirs(os.path.join(data_dir, 'images'), exist_ok=True)
        os.makedirs(os.path.join(data_dir, 'annotations'), exist_ok=True)

    def preprocess_image(self, image_path: str) -> np.ndarray:
        """
        Preprocess an image for OCR

        Args:
            image_path: Path to the image file

        Returns:
            Preprocessed image
        """
        # Read the image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not read image: {image_path}")

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        return thresh

    def extract_features(self, text_item: Dict) -> List[float]:
        """
        Extract features from a text item

        Args:
            text_item: Text item with position information

        Returns:
            List of features
        """
        text = text_item['text']
        box = text_item['box']

        # Calculate normalized position (0-1 range)
        x_min, y_min = min(p[0] for p in box), min(p[1] for p in box)
        x_max, y_max = max(p[0] for p in box), max(p[1] for p in box)
        width = x_max - x_min
        height = y_max - y_min

        # Text features
        has_numbers = any(c.isdigit() for c in text)
        has_letters = any(c.isalpha() for c in text)
        is_uppercase = text.isupper()
        text_length = len(text)

        # Special pattern features
        has_asterisk = '*' in text
        has_parentheses = '(' in text and ')' in text
        has_hyphen = '-' in text

        # Return all features as a list
        return [
            x_min, y_min, x_max, y_max, width, height,
            has_numbers, has_letters, is_uppercase, text_length,
            has_asterisk, has_parentheses, has_hyphen
        ]

    def process_annotation(self, annotation_path: str, image_path: str) -> pd.DataFrame:
        """
        Process an annotation file and extract features

        Args:
            annotation_path: Path to the annotation JSON file
            image_path: Path to the corresponding image file

        Returns:
            DataFrame with features and labels
        """
        # Load the annotation
        with open(annotation_path, 'r') as f:
            annotation = json.load(f)

        # Process the image with OCR
        preprocessed = self.preprocess_image(image_path)
        ocr_result = self.ocr.ocr(preprocessed, cls=True)

        # Extract features and labels
        features = []
        labels = []

        for item in annotation:
            if 'category' in item and item['category']:
                # Find the corresponding OCR result
                ocr_item = self._find_matching_ocr_item(item, ocr_result)
                if ocr_item:
                    # Extract features
                    item_features = self.extract_features(ocr_item)
                    features.append(item_features)
                    labels.append(item['category'])

        # Create DataFrame
        feature_columns = [
            'x_min', 'y_min', 'x_max', 'y_max', 'width', 'height',
            'has_numbers', 'has_letters', 'is_uppercase', 'text_length',
            'has_asterisk', 'has_parentheses', 'has_hyphen'
        ]
        df = pd.DataFrame(features, columns=feature_columns)
        df['label'] = labels

        return df

    def _find_matching_ocr_item(self, annotation_item: Dict, ocr_result: List) -> Optional[Dict]:
        """
        Find the OCR item that best matches an annotation item

        Args:
            annotation_item: Item from the annotation
            ocr_result: Result from OCR

        Returns:
            Matching OCR item or None
        """
        # Extract text and position from annotation
        text = annotation_item['text']
        x, y = annotation_item['x'], annotation_item['y']

        # Find the best match
        best_match = None
        best_score = float('inf')

        for line in ocr_result:
            for item in line:
                ocr_text = item[1][0]
                box = item[0]

                # Calculate center of the box
                center_x = sum(p[0] for p in box) / 4
                center_y = sum(p[1] for p in box) / 4

                # Calculate distance
                distance = ((center_x - x) ** 2 + (center_y - y) ** 2) ** 0.5

                # Calculate text similarity
                similarity = self._text_similarity(text, ocr_text)

                # Combined score (lower is better)
                score = distance * (1 + similarity)

                if score < best_score:
                    best_score = score
                    best_match = {
                        'text': ocr_text,
                        'box': box,
                        'confidence': item[1][1]
                    }

        return best_match

    def _text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts (0-1, lower is more similar)

        Args:
            text1: First text
            text2: Second text

        Returns:
            Similarity score
        """
        # Simple Levenshtein distance implementation
        if not text1 and not text2:
            return 0
        if not text1 or not text2:
            return 1

        # Normalize texts
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()

        # Calculate Levenshtein distance
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                cost = 0 if text1[i-1] == text2[j-1] else 1
                dp[i][j] = min(
                    dp[i-1][j] + 1,      # deletion
                    dp[i][j-1] + 1,      # insertion
                    dp[i-1][j-1] + cost  # substitution
                )

        # Normalize by the length of the longer string
        return dp[m][n] / max(m, n)

    def train_model(self) -> None:
        """Train the ML model on all labeled data"""
        # Collect all annotation files
        annotation_files = [f for f in os.listdir(os.path.join(self.data_dir, 'annotations'))
                           if f.endswith('.json')]

        if not annotation_files:
            print("No annotation files found. Please label some scorecards first.")
            return

        # Process all annotations
        all_data = []
        for annotation_file in annotation_files:
            image_file = annotation_file.replace('.json', '')
            annotation_path = os.path.join(self.data_dir, 'annotations', annotation_file)
            image_path = os.path.join(self.data_dir, 'images', image_file)

            if os.path.exists(image_path):
                try:
                    df = self.process_annotation(annotation_path, image_path)
                    all_data.append(df)
                except Exception as e:
                    print(f"Error processing {annotation_file}: {e}")

        if not all_data:
            print("No valid data processed. Check your annotation files and images.")
            return

        # Combine all data
        combined_data = pd.concat(all_data, ignore_index=True)

        # Split into features and labels
        X = combined_data.drop('label', axis=1)
        y = combined_data['label']

        # Split into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Train a Random Forest classifier
        clf = RandomForestClassifier(n_estimators=100, random_state=42)
        clf.fit(X_train, y_train)

        # Evaluate the model
        y_pred = clf.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        report = classification_report(y_test, y_pred)

        print(f"Model accuracy: {accuracy:.4f}")
        print("Classification report:")
        print(report)

        # Save the model
        model_path = os.path.join(self.model_dir, 'scorecard_classifier.pkl')
        joblib.dump(clf, model_path)
        print(f"Model saved to {model_path}")

        # Save feature columns
        feature_columns = X.columns.tolist()
        with open(os.path.join(self.model_dir, 'feature_columns.json'), 'w') as f:
            json.dump(feature_columns, f)

        # Save categories
        with open(os.path.join(self.model_dir, 'categories.json'), 'w') as f:
            json.dump(self.categories, f)

    def predict_elements(self, image):
        """
        Predict elements in a scorecard image

        Args:
            image: PIL Image or path to image

        Returns:
            List of predicted elements with their categories
        """
        # Load the model
        model_path = os.path.join(self.model_dir, 'scorecard_classifier.pkl')
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model not found at {model_path}. Please train the model first.")

        # Load the model
        clf = joblib.load(model_path)

        # Load feature columns
        feature_columns_path = os.path.join(self.model_dir, 'feature_columns.json')
        if os.path.exists(feature_columns_path):
            with open(feature_columns_path, 'r') as f:
                feature_columns = json.load(f)
        else:
            feature_columns = [
                'x_min', 'y_min', 'x_max', 'y_max', 'width', 'height',
                'has_numbers', 'has_letters', 'is_uppercase', 'text_length',
                'has_asterisk', 'has_parentheses', 'has_hyphen'
            ]

        # Convert PIL Image to numpy array if needed
        if hasattr(image, 'convert'):  # It's a PIL Image
            image_np = np.array(image.convert('RGB'))
            image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
        elif isinstance(image, str):  # It's a path
            image_np = cv2.imread(image)
        else:
            image_np = image

        # Process the image with OCR
        ocr_result = self.ocr.ocr(image_np, cls=True)

        # Extract features for each text element
        features = []
        text_items = []

        for line in ocr_result:
            for item in line:
                ocr_text = item[1][0]
                box = item[0]
                confidence = item[1][1]

                text_item = {
                    'text': ocr_text,
                    'box': box,
                    'confidence': confidence
                }

                # Extract features
                item_features = self.extract_features(text_item)
                features.append(item_features)
                text_items.append(text_item)

        if not features:
            return []

        # Create DataFrame with the same columns as training data
        df = pd.DataFrame(features, columns=feature_columns)

        # Make predictions
        predictions = clf.predict(df)
        probabilities = clf.predict_proba(df)

        # Combine predictions with text items
        results = []
        for i, (pred, text_item) in enumerate(zip(predictions, text_items)):
            # Get the probability for the predicted class
            pred_idx = list(clf.classes_).index(pred)
            prob = probabilities[i][pred_idx]

            results.append({
                'text': text_item['text'],
                'box': text_item['box'],
                'category': pred,
                'confidence': float(prob)
            })

        return results

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Cricket Scorecard ML Training System')
    parser.add_argument('--data-dir', default='data', help='Directory containing labeled data')
    parser.add_argument('--model-dir', default='models', help='Directory to save trained models')
    parser.add_argument('--train', action='store_true', help='Train the model')

    args = parser.parse_args()

    trainer = ScorecardTrainer(args.data_dir, args.model_dir)

    if args.train:
        trainer.train_model()
    else:
        parser.print_help()

if __name__ == '__main__':
    main()
