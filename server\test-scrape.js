const axios = require('axios');
const fs = require('fs');

// Function to fetch HTML from a URL
async function fetchHtml(url) {
  try {
    console.log(`Fetching HTML from: ${url}`);
    
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://www.iplt20.com/'
      }
    });
    
    // Save the HTML to a file for examination
    fs.writeFileSync('ipl-page.html', response.data);
    
    console.log('HTML saved to ipl-page.html');
    
    // Look for player cards in the HTML
    const html = response.data;
    
    // Try different selectors that might contain player data
    console.log('Searching for player cards...');
    
    // Check for ih-td-tab class (original selector)
    const ihTdTabCount = (html.match(/ih-td-tab/g) || []).length;
    console.log(`Found ${ihTdTabCount} occurrences of 'ih-td-tab' class`);
    
    // Check for player-card class
    const playerCardCount = (html.match(/player-card/g) || []).length;
    console.log(`Found ${playerCardCount} occurrences of 'player-card' class`);
    
    // Check for squad-player class
    const squadPlayerCount = (html.match(/squad-player/g) || []).length;
    console.log(`Found ${squadPlayerCount} occurrences of 'squad-player' class`);
    
    // Check for player name class
    const playerNameCount = (html.match(/player-name/g) || []).length;
    console.log(`Found ${playerNameCount} occurrences of 'player-name' class`);
    
    // Check for player image tags
    const playerImgCount = (html.match(/player-img/g) || []).length;
    console.log(`Found ${playerImgCount} occurrences of 'player-img' class`);
    
    return response.data;
  } catch (error) {
    console.error('Error fetching HTML:', error.message);
    throw error;
  }
}

// Main function
async function main() {
  try {
    const url = 'https://www.iplt20.com/teams/delhi-capitals/squad/2025';
    await fetchHtml(url);
  } catch (error) {
    console.error('Main error:', error);
  }
}

// Run the script
main();
