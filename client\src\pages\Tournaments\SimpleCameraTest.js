import React, { useState, useRef, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Alert,
  Grid,
  CircularProgress
} from '@mui/material';

/**
 * A simplified camera test component
 * This component focuses solely on getting the camera to work
 */
const SimpleCameraTest = () => {
  const videoRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [error, setError] = useState(null);
  const [cameraPermission, setCameraPermission] = useState(null);
  const [loading, setLoading] = useState(false);
  const [videoInfo, setVideoInfo] = useState(null);
  const [logs, setLogs] = useState([]);

  // Add a log entry
  const addLog = (message) => {
    console.log(message);
    setLogs(prevLogs => [...prevLogs, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Initialize camera
  const initCamera = async (constraints = { video: true }) => {
    setLoading(true);
    setError(null);
    
    try {
      addLog(`Requesting camera with constraints: ${JSON.stringify(constraints)}`);
      
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      addLog('Camera access granted, stream obtained');
      
      // Log information about the tracks
      const videoTracks = mediaStream.getVideoTracks();
      addLog(`Video tracks obtained: ${videoTracks.length}`);
      
      if (videoRef.current) {
        addLog('Setting video source');
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
        setCameraPermission(true);
        
        // Add event listeners
        videoRef.current.onloadedmetadata = () => {
          addLog(`Video metadata loaded: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
          setVideoInfo({
            width: videoRef.current.videoWidth,
            height: videoRef.current.videoHeight
          });
        };
        
        videoRef.current.onplay = () => {
          addLog('Video playback started');
        };
        
        videoRef.current.onerror = (e) => {
          addLog(`Video error: ${e.target.error?.message || 'Unknown error'}`);
          setError(`Video error: ${e.target.error?.message || 'Unknown error'}`);
        };
      } else {
        addLog('Video ref not available');
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (err) {
      addLog(`Error accessing camera: ${err.message}`);
      setError(`Error accessing camera: ${err.message}`);
      setCameraPermission(false);
    } finally {
      setLoading(false);
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (stream) {
      addLog('Stopping camera stream');
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    }
  };

  // Check browser compatibility
  useEffect(() => {
    // Log browser information
    const userAgent = navigator.userAgent;
    const isHttps = window.location.protocol === 'https:';
    const hasMediaDevices = !!navigator.mediaDevices;
    const hasGetUserMedia = hasMediaDevices && !!navigator.mediaDevices.getUserMedia;
    const isSecureContext = window.isSecureContext;
    
    addLog(`Browser: ${userAgent}`);
    addLog(`Protocol: ${window.location.protocol}`);
    addLog(`Secure context: ${isSecureContext}`);
    addLog(`Media devices API: ${hasMediaDevices}`);
    addLog(`getUserMedia API: ${hasGetUserMedia}`);
    
    // Check if the browser supports the required APIs
    if (!hasMediaDevices || !hasGetUserMedia) {
      setError('Your browser does not support the camera API');
    }
    
    // Check if we're on HTTPS
    if (!isHttps) {
      setError('Camera access requires HTTPS. Please use an HTTPS connection.');
    }
    
    return () => {
      stopCamera();
    };
  }, []);

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Simple Camera Test
      </Typography>
      
      <Typography variant="body1" paragraph>
        This is a simplified test to check if the camera works on your device.
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Camera Feed
            </Typography>
            
            <Box sx={{ 
              position: 'relative',
              width: '100%',
              height: 300,
              bgcolor: 'black',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              borderRadius: 1
            }}>
              {loading ? (
                <CircularProgress />
              ) : !stream ? (
                <Typography variant="body2" color="white">
                  No camera feed
                </Typography>
              ) : null}
              
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            </Box>
            
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button 
                variant="contained" 
                onClick={() => initCamera({ video: true })}
                disabled={loading}
              >
                Start Basic Camera
              </Button>
              
              <Button 
                variant="contained" 
                color="secondary"
                onClick={() => initCamera({ video: { facingMode: 'environment' } })}
                disabled={loading}
              >
                Start Back Camera
              </Button>
              
              <Button 
                variant="outlined" 
                color="error"
                onClick={stopCamera}
                disabled={!stream || loading}
              >
                Stop Camera
              </Button>
            </Box>
            
            {videoInfo && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Video dimensions: {videoInfo.width}x{videoInfo.height}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Debug Logs
            </Typography>
            
            <Box sx={{ 
              height: 300,
              overflow: 'auto',
              bgcolor: 'background.paper',
              p: 1,
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider',
              fontFamily: 'monospace',
              fontSize: '0.8rem'
            }}>
              {logs.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No logs yet
                </Typography>
              ) : (
                logs.map((log, index) => (
                  <Box key={index} sx={{ mb: 0.5 }}>
                    {log}
                  </Box>
                ))
              )}
            </Box>
            
            <Box sx={{ mt: 2 }}>
              <Button 
                variant="outlined" 
                size="small"
                onClick={() => setLogs([])}
              >
                Clear Logs
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default SimpleCameraTest;
