import { alpha } from '@mui/material/styles';

// COMMON COMPONENTS STYLING
const commonComponents = {
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: 12,
        boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '0 8px 25px 0 rgba(0,0,0,0.1)'
        }
      }
    }
  },
  MuiPaper: {
    styleOverrides: {
      root: {
        borderRadius: 12
      }
    }
  },
  MuiButton: {
    styleOverrides: {
      root: {
        borderRadius: 8,
        textTransform: 'none',
        fontWeight: 600,
        boxShadow: 'none',
        '&:hover': {
          boxShadow: '0 4px 12px 0 rgba(0,0,0,0.1)'
        }
      },
      containedPrimary: {
        '&:hover': {
          boxShadow: '0 6px 15px 0 rgba(25, 118, 210, 0.3)'
        }
      },
      containedSecondary: {
        '&:hover': {
          boxShadow: '0 6px 15px 0 rgba(220, 0, 78, 0.3)'
        }
      }
    }
  },
  MuiAppBar: {
    styleOverrides: {
      root: {
        boxShadow: '0 2px 10px 0 rgba(0,0,0,0.05)'
      }
    }
  },
  MuiDrawer: {
    styleOverrides: {
      paper: {
        borderRight: 'none'
      }
    }
  },
  MuiListItemButton: {
    styleOverrides: {
      root: {
        borderRadius: 8,
        margin: '4px 0',
        '&.Mui-selected': {
          fontWeight: 600
        }
      }
    }
  },
  MuiAvatar: {
    styleOverrides: {
      root: {
        boxShadow: '0 2px 10px 0 rgba(0,0,0,0.1)'
      }
    }
  },
  MuiChip: {
    styleOverrides: {
      root: {
        borderRadius: 6,
        fontWeight: 500
      }
    }
  }
};

// LIGHT THEME
export const lightTheme = {
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#fff'
    },
    secondary: {
      main: '#dc004e',
      light: '#ff4081',
      dark: '#c51162',
      contrastText: '#fff'
    },
    background: {
      default: '#f5f7fa',
      paper: '#ffffff'
    },
    text: {
      primary: '#2a3548',
      secondary: '#707a8a'
    },
    divider: 'rgba(0, 0, 0, 0.08)'
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700
    },
    h2: {
      fontWeight: 700
    },
    h3: {
      fontWeight: 700
    },
    h4: {
      fontWeight: 700
    },
    h5: {
      fontWeight: 600
    },
    h6: {
      fontWeight: 600
    },
    button: {
      fontWeight: 600
    }
  },
  shape: {
    borderRadius: 12
  },
  components: {
    ...commonComponents,
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: '#f5f7fa',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px'
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1'
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '4px'
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#a8a8a8'
          }
        }
      }
    }
  }
};

// DARK THEME
export const darkTheme = {
  palette: {
    mode: 'dark',
    primary: {
      main: '#90caf9',
      light: '#e3f2fd',
      dark: '#42a5f5',
      contrastText: '#000'
    },
    secondary: {
      main: '#f48fb1',
      light: '#fce4ec',
      dark: '#f06292',
      contrastText: '#000'
    },
    background: {
      default: '#111827',
      paper: '#1f2937'
    },
    text: {
      primary: '#f3f4f6',
      secondary: '#d1d5db'
    },
    divider: 'rgba(255, 255, 255, 0.08)'
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700
    },
    h2: {
      fontWeight: 700
    },
    h3: {
      fontWeight: 700
    },
    h4: {
      fontWeight: 700
    },
    h5: {
      fontWeight: 600
    },
    h6: {
      fontWeight: 600
    },
    button: {
      fontWeight: 600
    }
  },
  shape: {
    borderRadius: 12
  },
  components: {
    ...commonComponents,
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: '#111827',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px'
          },
          '&::-webkit-scrollbar-track': {
            background: '#2d3748'
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#4b5563',
            borderRadius: '4px'
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#6b7280'
          }
        }
      }
    }
  }
};

// CRICKET THEME
export const cricketTheme = {
  palette: {
    mode: 'light',
    primary: {
      main: '#1e4620', // Cricket field green
      light: '#4caf50',
      dark: '#1b5e20',
      contrastText: '#fff'
    },
    secondary: {
      main: '#b71c1c', // Cricket ball red
      light: '#e57373',
      dark: '#7f0000',
      contrastText: '#fff'
    },
    background: {
      default: '#f1f8e9', // Light green tint
      paper: '#ffffff'
    },
    text: {
      primary: '#2e3b2e',
      secondary: '#546e54'
    },
    divider: 'rgba(0, 0, 0, 0.08)'
  },
  typography: {
    fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700
    },
    h2: {
      fontWeight: 700
    },
    h3: {
      fontWeight: 700
    },
    h4: {
      fontWeight: 700
    },
    h5: {
      fontWeight: 600
    },
    h6: {
      fontWeight: 600
    },
    button: {
      fontWeight: 600
    }
  },
  shape: {
    borderRadius: 12
  },
  components: {
    ...commonComponents,
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: '#f1f8e9',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px'
          },
          '&::-webkit-scrollbar-track': {
            background: '#e8f5e9'
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#81c784',
            borderRadius: '4px'
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#66bb6a'
          }
        }
      }
    }
  }
};
