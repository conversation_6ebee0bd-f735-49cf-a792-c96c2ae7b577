/**
 * IPL Player Scraper Utility
 * Uses Puppeteer to scrape player data from IPL team pages
 */

// This will be used when <PERSON><PERSON>pet<PERSON> is installed
// const puppeteer = require('puppeteer');

/**
 * Scrape player data from IPL team page using Puppeteer
 * @param {string} url - The URL of the IPL team page
 * @returns {Promise<Array>} - Array of player objects
 */
async function scrapeIplPlayers(url) {
  try {
    console.log(`Starting headless browser to scrape: ${url}`);

    // Check if puppeteer is installed
    let puppeteer;
    try {
      puppeteer = require('puppeteer');
    } catch (error) {
      console.error('Puppeteer is not installed. Please run: npm install puppeteer');
      console.log('Falling back to mock data...');
      return useMockData(url);
    }

    // Launch a headless browser
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      // Open a new page
      const page = await browser.newPage();

      // Set a realistic user agent
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Navigate to the IPL team page
      console.log(`Navigating to ${url}`);
      await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: 60000
      });

      // Wait for player cards to load
      console.log('Waiting for player cards to load...');
      await page.waitForSelector('.ih-pcard1', { timeout: 10000 });

      // Extract player data
      console.log('Extracting player data...');
      const players = await page.evaluate(() => {
        const playerCards = document.querySelectorAll('.ih-pcard1');

        return Array.from(playerCards).map(card => {
          // Extract player name
          const name = card.querySelector('.ih-p-name h2')?.textContent.trim() || '';

          // Extract player role
          const role = card.querySelector('.ih-p-img span.d-block')?.textContent.trim() || '';

          // Extract player image URL
          const imageUrl = card.querySelector('.ih-p-img img')?.dataset.src || '';

          // Determine player type based on role
          let playerType = 'Batsman'; // Default
          if (role) {
            const lowerRole = role.toLowerCase();
            if (lowerRole.includes('bowler')) {
              playerType = 'Bowler';
            } else if (lowerRole.includes('all-rounder')) {
              playerType = 'Allrounder';
            } else if (lowerRole.includes('wk') || lowerRole.includes('wicket') || lowerRole.includes('keeper')) {
              playerType = 'Wicket Keeper';
            }
          }

          // Check if player is foreign by looking at the flag image
          // This is a more reliable way to determine nationality
          let nationality = 'India'; // Default
          const flagImg = card.querySelector('.ih-p-country img');
          if (flagImg) {
            const flagSrc = flagImg.src || '';
            const flagAlt = flagImg.alt || '';

            // Try to determine nationality from flag image
            if (flagSrc.includes('afghanistan') || flagAlt.includes('afghanistan')) {
              nationality = 'Afghanistan';
            } else if (flagSrc.includes('australia') || flagAlt.includes('australia')) {
              nationality = 'Australia';
            } else if (flagSrc.includes('bangladesh') || flagAlt.includes('bangladesh')) {
              nationality = 'Bangladesh';
            } else if (flagSrc.includes('england') || flagAlt.includes('england')) {
              nationality = 'England';
            } else if (flagSrc.includes('new-zealand') || flagAlt.includes('new zealand')) {
              nationality = 'New Zealand';
            } else if (flagSrc.includes('south-africa') || flagAlt.includes('south africa')) {
              nationality = 'South Africa';
            } else if (flagSrc.includes('sri-lanka') || flagAlt.includes('sri lanka')) {
              nationality = 'Sri Lanka';
            } else if (flagSrc.includes('west-indies') || flagAlt.includes('west indies')) {
              nationality = 'West Indies';
            } else if (flagSrc.includes('zimbabwe') || flagAlt.includes('zimbabwe')) {
              nationality = 'Zimbabwe';
            } else if (flagSrc.includes('pakistan') || flagAlt.includes('pakistan')) {
              nationality = 'Pakistan';
            } else if (!flagSrc.includes('india') && !flagAlt.includes('india')) {
              // If we can't determine the specific country but it's not India
              nationality = 'Foreign';
            }
          }

          // Get player profile URL
          const profileUrl = card.querySelector('a')?.href || '';

          return {
            name,
            type: playerType,
            role,
            imageUrl,
            nationality,
            profileUrl
          };
        }).filter(player => player.name && player.imageUrl); // Filter out incomplete entries
      });

      console.log(`Found ${players.length} players`);
      return players;
    } finally {
      // Always close the browser to free resources
      await browser.close();
      console.log('Browser closed');
    }
  } catch (error) {
    console.error('Error scraping players:', error);
    console.log('Falling back to mock data...');
    return useMockData(url);
  }
}

/**
 * Fallback function to use mock data when scraping fails
 * @param {string} url - The URL of the IPL team page
 * @returns {Array} - Array of player objects from mock data
 */
function useMockData(url) {
  const mockIplTeams = require('../data/mockIplPlayers');

  // Extract team slug from URL
  let teamSlug = '';
  const urlParts = url.split('/');
  for (let i = 0; i < urlParts.length; i++) {
    if (urlParts[i] === 'teams' && i + 1 < urlParts.length) {
      teamSlug = urlParts[i + 1].split('#')[0].split('?')[0];
      break;
    }
  }

  // Default to delhi-capitals if team not found
  if (!mockIplTeams[teamSlug]) {
    teamSlug = 'delhi-capitals';
  }

  console.log(`Using mock data for team: ${teamSlug}`);
  return mockIplTeams[teamSlug].players;
}

module.exports = { scrapeIplPlayers };
