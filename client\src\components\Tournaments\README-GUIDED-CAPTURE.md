# Guided Scorecard Capture Feature

This feature provides a mobile-optimized camera interface that guides users to properly align their phone camera with a cricket match scorecard displayed on their TV/monitor. It uses real-time boundary detection to show when the scorecard is properly aligned and automatically captures the image when ready.

## Features

- **Mobile-optimized camera interface** with boundary detection
- **Real-time visual feedback** with green boundaries when properly aligned
- **Automatic capture** when scorecard is detected and properly aligned
- **Manual capture option** as a fallback
- **Image adjustment controls** for brightness and contrast
- **Data extraction** from captured scorecard images
- **User-friendly verification interface** for reviewing and correcting extracted data

## How It Works

1. The user navigates to the match result submission page
2. They select "Add Match Result" and choose "Guided Scorecard Capture"
3. The system opens their device camera with an overlay guide
4. The user points their camera at the scorecard on their TV/monitor
5. The system provides real-time feedback with boundary indicators
6. When the scorecard is properly aligned, the boundaries turn green
7. The system automatically captures the image (or the user can manually capture)
8. The system processes the image and extracts match data
9. The user reviews and corrects any errors in the extracted data
10. The user confirms and submits the match result

## Implementation Details

### Components

- **GuidedScorecardCapture.js**: Main component for the guided camera interface
- **TestGuidedCapture.js**: Test page for demonstrating the feature

### Technologies Used

- **HTML5 Camera API**: For accessing the device camera
- **Canvas API**: For real-time image processing and boundary detection
- **JavaScript**: For image analysis and edge detection
- **React**: For building the user interface
- **Material-UI**: For UI components and styling

### Key Functions

- `initCamera()`: Initializes the device camera with optimal settings
- `detectScorecardBoundaries()`: Analyzes the camera feed to detect scorecard edges
- `drawGuideOverlay()`: Renders the boundary guide overlay on the camera feed
- `captureImage()`: Captures the current frame when boundaries are detected
- `processImage()`: Processes the captured image for OCR (to be implemented)
- `extractData()`: Extracts match data from the processed image (to be implemented)

## Future Enhancements

- **Improved boundary detection** using machine learning for more accurate recognition
- **OCR integration** for automatic data extraction from scorecard images
- **Template matching** to support different scorecard formats from various cricket games
- **Error correction** algorithms to improve data extraction accuracy
- **Offline processing** to work without an internet connection

## Usage Example

```jsx
import GuidedScorecardCapture from '../../components/Tournaments/GuidedScorecardCapture';

// In your component
const [guidedCaptureOpen, setGuidedCaptureOpen] = useState(false);

// Handle successful upload
const handleUploadSuccess = (result) => {
  console.log('Scorecard uploaded:', result);
  // Update UI or state with the result
};

// Render the component
return (
  <>
    <Button
      variant="contained"
      onClick={() => setGuidedCaptureOpen(true)}
      startIcon={<CameraIcon />}
    >
      Capture Scorecard
    </Button>
    
    <GuidedScorecardCapture
      open={guidedCaptureOpen}
      onClose={() => setGuidedCaptureOpen(false)}
      tournamentId="tournament-id"
      matchId="match-id"
      onUploadSuccess={handleUploadSuccess}
    />
  </>
);
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| `open` | boolean | Controls whether the dialog is open |
| `onClose` | function | Callback when the dialog is closed |
| `tournamentId` | string | ID of the tournament |
| `matchId` | string | ID of the match |
| `onUploadSuccess` | function | Callback when upload is successful |

## Troubleshooting

- **Camera access denied**: Ensure the user has granted camera permissions to the browser
- **Poor boundary detection**: Adjust lighting conditions and ensure the scorecard is clearly visible
- **Auto-capture not working**: Use the manual capture button as a fallback
- **Extracted data errors**: Use the data editor to correct any mistakes before submission

## Browser Compatibility

This feature works best on modern mobile browsers:
- Chrome for Android (latest)
- Safari for iOS (latest)
- Firefox for Android (latest)

Some features may have limited functionality on older browsers or desktop browsers.
