import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Snackbar,
  Paper,
  Link
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import { exportIplPlayers, getDownloadUrl } from '../services/exportService';

/**
 * Component for exporting IPL player data to CSV
 */
const ExportIplPlayers = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [exportResult, setExportResult] = useState(null);

  const handleExport = async () => {
    setLoading(true);
    setError('');
    setSuccess('');
    setExportResult(null);

    try {
      const result = await exportIplPlayers();

      if (result.success) {
        setSuccess(result.message);
        setExportResult(result.file);
      } else {
        setError(result.message || 'Export failed');
      }
    } catch (err) {
      console.error('Error exporting IPL players:', err);
      setError('Error exporting IPL players: ' + (typeof err === 'string' ? err : err.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError('');
    setSuccess('');
  };

  const handleDownload = async () => {
    if (exportResult && exportResult.name) {
      try {
        // Get the auth token
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          return;
        }

        // Create the download URL
        const downloadUrl = getDownloadUrl(exportResult.name);

        // Use fetch with authentication to get the file
        const response = await fetch(downloadUrl, {
          headers: {
            'x-auth-token': token
          }
        });

        if (!response.ok) {
          throw new Error(`Download failed: ${response.statusText}`);
        }

        // Get the blob from the response
        const blob = await response.blob();

        // Create a download link and trigger it
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = exportResult.name;
        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        setSuccess('File downloaded successfully!');
      } catch (err) {
        console.error('Error downloading file:', err);
        setError('Error downloading file: ' + (err.message || 'Unknown error'));
      }
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" gutterBottom>
        Export IPL Player Data
      </Typography>

      <Typography variant="body2" color="text.secondary" paragraph>
        This tool will export all IPL player names, roles, teams, and image URLs to a CSV file.
        You can use this data to update your player spreadsheet for better name matching.
      </Typography>

      <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleExport}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{ alignSelf: 'flex-start' }}
        >
          {loading ? 'Exporting...' : 'Export IPL Player Data'}
        </Button>

        {exportResult && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
            <Typography variant="subtitle1" gutterBottom>
              Export Completed Successfully!
            </Typography>
            <Typography variant="body2" paragraph>
              File: {exportResult.name}
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
            >
              Download CSV File
            </Button>
          </Box>
        )}
      </Box>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default ExportIplPlayers;
