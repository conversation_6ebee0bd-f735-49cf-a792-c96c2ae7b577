const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { spawn } = require('child_process');
const { authenticateToken } = require('../middlewares/auth');
const adminAuth = require('../middlewares/adminAuth');
const trainingServerManager = require('../utils/trainingServerManager');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../scripts/ml_training/data/images');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate a unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: function (req, file, cb) {
    // Accept only image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'), false);
    }
  }
});

/**
 * @route   POST /api/training/start-server
 * @desc    Start the training server
 * @access  Private (Admin only)
 */
router.post('/start-server', authenticateToken, adminAuth, async (req, res) => {
  try {
    const result = await trainingServerManager.startServer();

    if (result.success) {
      return res.json({ msg: result.message });
    } else {
      return res.status(500).json({
        msg: result.message,
        stdout: result.stdout,
        stderr: result.stderr,
        error: result.error
      });
    }
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/training/stop-server
 * @desc    Stop the training server
 * @access  Private (Admin only)
 */
router.post('/stop-server', authenticateToken, adminAuth, async (req, res) => {
  try {
    const result = await trainingServerManager.stopServer();

    if (result.success) {
      return res.json({ msg: result.message });
    } else {
      return res.status(500).json({ msg: result.message });
    }
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/training/server-status
 * @desc    Check if the training server is running
 * @access  Private (Admin only)
 */
router.get('/server-status', authenticateToken, adminAuth, async (req, res) => {
  try {
    const status = await trainingServerManager.getServerStatus();
    return res.json(status);
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/training/upload-scorecard
 * @desc    Upload a scorecard image for training
 * @access  Private (Admin only)
 */
router.post('/upload-scorecard', authenticateToken, adminAuth, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ msg: 'No file uploaded' });
    }

    return res.json({
      msg: 'File uploaded successfully',
      filename: req.file.filename,
      path: req.file.path
    });
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/training/train-model
 * @desc    Train the ML model
 * @access  Private (Admin only)
 */
router.post('/train-model', authenticateToken, adminAuth, (req, res) => {
  try {
    // Run the training script
    const trainerPath = path.join(__dirname, '../scripts/ml_training/scorecard_trainer.py');

    const trainer = spawn('python', [trainerPath, '--train']);

    // Collect output
    let output = '';

    trainer.stdout.on('data', (data) => {
      output += data.toString();
      console.log(`Trainer stdout: ${data}`);
    });

    trainer.stderr.on('data', (data) => {
      console.error(`Trainer stderr: ${data}`);
    });

    trainer.on('close', (code) => {
      console.log(`Trainer exited with code ${code}`);

      if (code === 0) {
        return res.json({
          msg: 'Model trained successfully',
          output: output
        });
      } else {
        return res.status(500).json({
          msg: 'Error training model',
          output: output
        });
      }
    });
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/training/model-status
 * @desc    Get the status of the trained model
 * @access  Private (Admin only)
 */
router.get('/model-status', authenticateToken, adminAuth, (req, res) => {
  try {
    const modelPath = path.join(__dirname, '../scripts/ml_training/models/scorecard_classifier.pkl');

    if (fs.existsSync(modelPath)) {
      const stats = fs.statSync(modelPath);

      return res.json({
        trained: true,
        lastTrained: stats.mtime
      });
    } else {
      return res.json({
        trained: false
      });
    }
  } catch (err) {
    console.error(err.message);
    return res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/training/clear-annotations
 * @desc    Clear annotations (all or specific ones)
 * @access  Private (Admin only)
 */
router.post('/clear-annotations', authenticateToken, adminAuth, async (req, res) => {
  try {
    // Check if training server is running
    const serverStatus = await trainingServerManager.getServerStatus();

    if (!serverStatus.running) {
      return res.status(400).json({
        success: false,
        msg: 'Training server is not running. Please start the server first.'
      });
    }

    // Make a request to the training server to clear annotations
    const axios = require('axios');
    const trainingServerUrl = trainingServerManager.getServerUrl();

    // Get filenames from request body if provided
    const filenames = req.body.filenames || [];

    const response = await axios.post(`${trainingServerUrl}/clear-annotations`, {
      filenames: filenames
    });

    if (response.data.success) {
      return res.json({
        success: true,
        msg: response.data.message,
        clearedCount: response.data.cleared_count,
        selective: response.data.selective
      });
    } else {
      return res.status(500).json({
        success: false,
        msg: 'Error clearing annotations',
        error: response.data.error
      });
    }
  } catch (err) {
    console.error('Error clearing annotations:', err.message);
    return res.status(500).json({
      success: false,
      msg: 'Server Error',
      error: err.message
    });
  }
});

/**
 * @route   GET /api/training/labeled-images
 * @desc    Get all labeled images
 * @access  Private (Admin only)
 */
router.get('/labeled-images', authenticateToken, adminAuth, async (req, res) => {
  try {
    // Check if training server is running
    const serverStatus = await trainingServerManager.getServerStatus();

    if (!serverStatus.running) {
      return res.status(400).json({
        success: false,
        msg: 'Training server is not running. Please start the server first.'
      });
    }

    // Make a request to the training server to get labeled images
    const axios = require('axios');
    const trainingServerUrl = trainingServerManager.getServerUrl();

    const response = await axios.get(`${trainingServerUrl}/labeled-images`);

    if (response.data.success) {
      return res.json(response.data);
    } else {
      return res.status(500).json({
        success: false,
        msg: 'Error getting labeled images',
        error: response.data.error
      });
    }
  } catch (err) {
    console.error('Error getting labeled images:', err.message);
    return res.status(500).json({
      success: false,
      msg: 'Server Error',
      error: err.message
    });
  }
});

/**
 * @route   GET /api/training/image/:filename
 * @desc    Get a specific image from the training server
 * @access  Private (Admin only)
 */
router.get('/image/:filename', authenticateToken, adminAuth, async (req, res) => {
  try {
    // Check if training server is running
    const serverStatus = await trainingServerManager.getServerStatus();

    if (!serverStatus.running) {
      return res.status(400).json({
        success: false,
        msg: 'Training server is not running. Please start the server first.'
      });
    }

    const filename = req.params.filename;
    const axios = require('axios');
    const trainingServerUrl = trainingServerManager.getServerUrl();

    // Make a request to the training server to get the image
    try {
      const response = await axios.get(`${trainingServerUrl}/image/${filename}`, {
        responseType: 'arraybuffer'
      });

      // Set the content type based on the filename extension
      const contentType = filename.endsWith('.png') ? 'image/png' :
                          filename.endsWith('.jpg') || filename.endsWith('.jpeg') ? 'image/jpeg' :
                          'application/octet-stream';

      res.set('Content-Type', contentType);
      return res.send(response.data);
    } catch (error) {
      console.error(`Error fetching image ${filename}:`, error.message);
      return res.status(404).json({
        success: false,
        msg: 'Image not found',
        error: error.message
      });
    }
  } catch (err) {
    console.error('Error proxying image:', err.message);
    return res.status(500).json({
      success: false,
      msg: 'Server Error',
      error: err.message
    });
  }
});

module.exports = router;
