/**
 * Admin authentication middleware
 * Verifies that the authenticated user has admin role
 */
const adminAuth = (req, res, next) => {
  try {
    // Check if user exists in request (set by auth middleware)
    if (!req.user) {
      return res.status(401).json({ msg: 'Authentication required' });
    }

    // Check if user has admin role
    if (req.user.role !== 'admin') {
      console.log('Access denied: User is not an admin', req.user);
      return res.status(403).json({ msg: 'Access denied: Admin privileges required' });
    }

    // User is admin, proceed
    console.log('Admin access granted for user:', req.user.id);
    next();
  } catch (err) {
    console.error('Admin auth middleware error:', err);
    res.status(500).json({
      msg: 'Server error in admin authentication',
      error: err.message
    });
  }
};

module.exports = { requireAdmin: adminAuth };
