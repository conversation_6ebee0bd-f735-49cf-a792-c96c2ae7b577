/**
 * Training Server Manager
 *
 * This module manages the lifecycle of the Python training server.
 * It provides functions to start, stop, and check the status of the server.
 */

const { spawn } = require('child_process');
const path = require('path');
const axios = require('axios');
const fs = require('fs');

// Global reference to the training server process
let trainingServer = null;

// Configuration
const DEFAULT_PORT = 5001;
const DEFAULT_HOST = '0.0.0.0';
const SERVER_URL = `http://localhost:${DEFAULT_PORT}/api`;

/**
 * Start the training server
 *
 * @returns {Promise<Object>} - Promise that resolves to { success, message, error }
 */
const startServer = async () => {
  try {
    // Check if server is already running
    if (trainingServer) {
      return { success: true, message: 'Training server is already running' };
    }

    // Get the path to the training server script
    const serverPath = path.join(__dirname, '../scripts/ml_training/labeling_server.py');

    console.log(`Starting training server from path: ${serverPath}`);

    // Check if the script exists
    if (!fs.existsSync(serverPath)) {
      console.error(`Training server script not found at: ${serverPath}`);
      return { success: false, message: 'Training server script not found', error: `File not found: ${serverPath}` };
    }

    // Collect all output for debugging
    let stdoutData = '';
    let stderrData = '';

    // Start the server with detailed command
    const command = `python "${serverPath}" --host ${DEFAULT_HOST} --port ${DEFAULT_PORT}`;
    console.log(`Executing command: ${command}`);

    // Use the full path to python.exe to ensure it works
    const pythonPath = process.env.PYTHON_PATH || 'python';
    console.log(`Using Python executable: ${pythonPath}`);

    // Set the working directory to the script's directory to ensure imports work
    const options = {
      cwd: path.dirname(serverPath),
      env: { ...process.env, PYTHONUNBUFFERED: '1' } // Ensure Python output is not buffered
    };

    console.log(`Working directory: ${options.cwd}`);

    // Spawn the process
    trainingServer = spawn(pythonPath, [serverPath, '--host', DEFAULT_HOST, '--port', DEFAULT_PORT], options);

    // Handle server output
    trainingServer.stdout.on('data', (data) => {
      const output = data.toString();
      stdoutData += output;
      console.log(`Training server stdout: ${output}`);
    });

    trainingServer.stderr.on('data', (data) => {
      const output = data.toString();
      stderrData += output;
      console.error(`Training server stderr: ${output}`);
    });

    trainingServer.on('close', (code) => {
      console.log(`Training server exited with code ${code}`);
      trainingServer = null;
    });

    // Wait a bit for the server to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check if the server is actually running with retries
    const isRunning = await checkServerRunning(5, 1000);

    if (isRunning) {
      return { success: true, message: 'Training server started successfully' };
    } else {
      // Server didn't respond properly
      console.error('Training server started but not responding correctly');
      if (trainingServer) {
        trainingServer.kill();
        trainingServer = null;
      }

      return {
        success: false,
        message: 'Training server failed to start properly',
        error: 'Server not responding',
        stdout: stdoutData,
        stderr: stderrData
      };
    }
  } catch (error) {
    console.error('Error starting training server:', error.message);

    // Clean up if there was an error
    if (trainingServer) {
      console.log('Killing unresponsive training server process');
      trainingServer.kill();
      trainingServer = null;
    }

    return {
      success: false,
      message: 'Error starting training server: ' + error.message,
      error: error.message
    };
  }
};

/**
 * Stop the training server
 *
 * @returns {Promise<Object>} - Promise that resolves to { success, message }
 */
const stopServer = async () => {
  try {
    // Check if server is running
    if (!trainingServer) {
      return { success: true, message: 'Training server is not running' };
    }

    // Stop the server
    trainingServer.kill();
    trainingServer = null;

    return { success: true, message: 'Training server stopped successfully' };
  } catch (error) {
    console.error('Error stopping training server:', error.message);
    return { success: false, message: 'Error stopping training server: ' + error.message };
  }
};

/**
 * Check if the training server is running
 *
 * @param {number} retries - Number of retries
 * @param {number} delay - Delay between retries in milliseconds
 * @returns {Promise<boolean>} - Promise that resolves to true if server is running
 */
const checkServerRunning = async (retries = 1, delay = 0) => {
  console.log(`Checking if training server is running (${retries} retries left)...`);

  try {
    const response = await axios.get(`${SERVER_URL}/ping`, {
      timeout: 3000
    });

    return response.status === 200;
  } catch (error) {
    if (retries <= 0) {
      return false;
    }

    console.log(`Server not responding yet, retrying in ${delay}ms...`);
    await new Promise(resolve => setTimeout(resolve, delay));
    return checkServerRunning(retries - 1, delay);
  }
};

/**
 * Get the status of the training server
 *
 * @returns {Promise<Object>} - Promise that resolves to { running, processExists }
 */
const getServerStatus = async () => {
  try {
    // First check if we have a process reference
    const processRunning = trainingServer !== null;

    // Then check if the server is actually responding
    let serverResponding = false;

    if (processRunning) {
      try {
        const response = await axios.get(`${SERVER_URL}/ping`, {
          timeout: 2000
        });

        serverResponding = response.status === 200;
      } catch (error) {
        console.error('Error connecting to Python server:', error.message);
        serverResponding = false;
      }
    }

    // If process is running but server is not responding, kill the process
    if (processRunning && !serverResponding) {
      console.log('Process exists but server is not responding. Cleaning up...');
      try {
        trainingServer.kill();
        trainingServer = null;
      } catch (killError) {
        console.error('Error killing unresponsive server process:', killError);
      }
    }

    return {
      running: serverResponding,
      processExists: processRunning
    };
  } catch (error) {
    console.error('Error getting server status:', error.message);
    return { running: false, processExists: false };
  }
};

/**
 * Get the training server URL
 *
 * @returns {string} - The training server URL
 */
const getServerUrl = () => {
  return SERVER_URL;
};

module.exports = {
  startServer,
  stopServer,
  getServerStatus,
  checkServerRunning,
  getServerUrl
};
