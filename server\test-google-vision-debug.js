/**
 * Debug script to see what Google Vision is detecting in a scorecard
 * 
 * Usage: node test-google-vision-debug.js <scorecard-filename>
 * Example: node test-google-vision-debug.js scorecard11.jpg
 */

const path = require('path');
const GoogleVisionClient = require('./utils/googleVisionClient');

async function debugGoogleVision() {
  // Get the scorecard filename from command line arguments
  const scorecardName = process.argv[2];
  
  if (!scorecardName) {
    console.error('❌ Error: Please provide a scorecard filename');
    console.log('Usage: node test-google-vision-debug.js <scorecard-filename>');
    console.log('Example: node test-google-vision-debug.js scorecard11.jpg');
    return;
  }
  
  console.log('🔍 GOOGLE VISION DEBUG');
  console.log('=====================');
  console.log(`Testing scorecard: ${scorecardName}\n`);
  
  try {
    // Initialize Google Vision client
    const visionClient = new GoogleVisionClient();
    
    // Set the image path
    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);
    
    console.log(`📸 Processing: ${imagePath}`);
    
    // Process the image with Google Vision
    console.log('\n⏳ Processing image with Google Vision...');
    const visionResults = await visionClient.detectText(imagePath);
    
    // Display all detected text elements
    console.log(`\n📝 Google Vision detected ${visionResults.textElements.length} text elements:`);
    
    // Group elements by rows
    const rows = groupElementsByRows(visionResults.textElements);
    
    console.log(`\n📊 Grouped into ${rows.length} rows:`);
    rows.forEach((row, i) => {
      // Sort by x-coordinate
      row.sort((a, b) => a.x - b.x);
      
      // Extract text from row
      const rowText = row.map(el => el.text).join(' ');
      
      console.log(`Row ${i+1}: ${rowText}`);
      console.log(`  Elements: ${row.map(el => `"${el.text}" at (${el.x}, ${el.y})`).join(', ')}`);
      
      // Check if this row might contain player stats
      const hasName = row.some(el => 
        el.x < 500 && // Names are typically on the left
        /^[A-Za-z]+$/.test(el.text) && // Only alphabetic characters
        el.text.length > 2 // At least 3 characters
      );
      
      const hasNumbers = row.some(el => /\d/.test(el.text));
      
      if (hasName && hasNumbers) {
        console.log(`  ✅ This row likely contains player stats`);
        
        // Try to extract player name
        const nameElements = row.filter(el => 
          el.x < 500 && // Names are typically on the left
          /^[A-Za-z]+$/.test(el.text) && // Only alphabetic characters
          el.text.length > 2 // At least 3 characters
        );
        
        if (nameElements.length > 0) {
          const playerName = nameElements.map(el => el.text).join(' ');
          console.log(`  👤 Player: ${playerName}`);
        }
        
        // Try to extract runs
        const runsElement = row.find(el => 
          /^\d+\*?$/.test(el.text) && // Number, possibly with * for not out
          el.x > (nameElements.length > 0 ? nameElements[nameElements.length - 1].x : 0)
        );
        
        if (runsElement) {
          console.log(`  🏏 Runs: ${runsElement.text}`);
        }
        
        // Try to extract balls
        const ballsElement = row.find(el => /^\(\d+\)$/.test(el.text));
        
        if (ballsElement) {
          console.log(`  🏏 Balls: ${ballsElement.text}`);
        } else {
          // If balls are split into separate elements: (, digit, )
          const openParen = row.find(el => el.text === '(');
          const closeParen = row.find(el => el.text === ')');
          
          if (openParen && closeParen) {
            const ballsDigit = row.find(el => 
              /^\d+$/.test(el.text) && 
              el.x > openParen.x && 
              el.x < closeParen.x
            );
            
            if (ballsDigit) {
              console.log(`  🏏 Balls: (${ballsDigit.text})`);
            }
          }
        }
      }
      
      console.log('');
    });
    
    // Look for specific players
    const playersToFind = [
      'JOS BUTTLER',
      'LIAM LIVINGSTONE',
      'MITCHELL SANTNER',
      'RACHIN RAVINDRA'
    ];
    
    console.log('\n🔍 SEARCHING FOR SPECIFIC PLAYERS:');
    playersToFind.forEach(playerName => {
      console.log(`\nLooking for: ${playerName}`);
      
      // Split the player name into parts
      const nameParts = playerName.split(' ');
      
      // Find elements matching any part of the player name
      const nameElements = visionResults.textElements.filter(el => 
        nameParts.some(part => 
          el.text.toUpperCase().includes(part.toUpperCase())
        )
      );
      
      if (nameElements.length > 0) {
        console.log(`✅ Found ${nameElements.length} elements matching parts of ${playerName}:`);
        nameElements.forEach(el => {
          console.log(`  "${el.text}" at (${el.x}, ${el.y})`);
        });
      } else {
        console.log(`❌ No elements found matching ${playerName}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

/**
 * Group OCR elements by rows based on y-coordinates
 * @param {Array} elements - OCR text elements
 * @returns {Array} - Array of rows, each containing elements in that row
 */
function groupElementsByRows(elements) {
  const rows = [];
  const sortedElements = [...elements].sort((a, b) => a.y - b.y);
  
  let currentRow = [];
  let currentY = 0;
  
  sortedElements.forEach(element => {
    if (currentRow.length === 0) {
      currentRow.push(element);
      currentY = element.y;
    } else if (Math.abs(element.y - currentY) < 20) {
      currentRow.push(element);
    } else {
      rows.push([...currentRow]);
      currentRow = [element];
      currentY = element.y;
    }
  });
  
  if (currentRow.length > 0) {
    rows.push([...currentRow]);
  }
  
  return rows;
}

// Run the debug function
debugGoogleVision();