import React from 'react';
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Avatar,
  useTheme,
  useMediaQuery,
  alpha
} from '@mui/material';
import { ArrowForward as ArrowIcon } from '@mui/icons-material';

/**
 * ResponsiveDashboardCard - A responsive card component for dashboard features
 *
 * This component provides a consistent card layout for dashboard features
 * with proper spacing, padding, and responsive behavior.
 *
 * @param {Object} props
 * @param {string} props.title - The title of the feature
 * @param {string} props.subtitle - The subtitle of the feature
 * @param {React.ReactNode} props.icon - The icon to display
 * @param {string} props.iconColor - The color of the icon (primary, secondary, error, warning, info, success)
 * @param {string} props.actionText - The text for the action button
 * @param {Function} props.onClick - The function to call when the action button is clicked
 * @param {Object} props.sx - Additional styles to apply to the card
 * @param {boolean} props.disabled - Whether the card is disabled
 * @param {string} props.variant - The variant of the card (outlined, elevation)
 */
const ResponsiveDashboardCard = ({
  title,
  subtitle,
  icon,
  iconColor = 'primary',
  actionText = 'View',
  onClick,
  sx = {},
  disabled = false,
  variant = 'elevation',
  elevation = 1
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Get the actual color from theme
  const color = theme.palette[iconColor]?.main || theme.palette.primary.main;

  return (
    <Card
      variant={variant}
      elevation={variant === 'outlined' ? 0 : elevation}
      sx={{
        height: '100%',
        minHeight: { xs: 220, sm: 240, md: 260 },
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 3,
        overflow: 'hidden',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        '&:hover': {
          transform: disabled ? 'none' : 'translateY(-5px)',
          boxShadow: disabled ? theme.shadows[elevation] : theme.shadows[elevation + 2]
        },
        opacity: disabled ? 0.7 : 1,
        ...sx
      }}
    >
      <CardContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          p: { xs: 2, sm: 3 },
          flexGrow: 1
        }}
      >
        <Avatar
          sx={{
            bgcolor: alpha(color, 0.1),
            color: color,
            width: { xs: 56, sm: 64, md: 72 },
            height: { xs: 56, sm: 64, md: 72 },
            mb: 2
          }}
        >
          {icon}
        </Avatar>

        <Typography
          variant="h6"
          component="h3"
          gutterBottom
          sx={{
            fontWeight: 600,
            fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.25rem' },
            mb: 1
          }}
        >
          {title}
        </Typography>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 2,
            fontSize: { xs: '0.85rem', sm: '0.875rem' },
            maxWidth: '90%',
            mx: 'auto'
          }}
        >
          {subtitle}
        </Typography>
      </CardContent>

      <CardActions
        sx={{
          p: { xs: 2, sm: 2 },
          pt: 0,
          justifyContent: 'center'
        }}
      >
        <Button
          variant="outlined"
          color={iconColor}
          size={isMobile ? "small" : "medium"}
          endIcon={<ArrowIcon />}
          onClick={onClick}
          disabled={disabled}
          sx={{
            borderRadius: 2,
            width: '100%',
            maxWidth: '200px'
          }}
        >
          {actionText}
        </Button>
      </CardActions>
    </Card>
  );
};

export default ResponsiveDashboardCard;
