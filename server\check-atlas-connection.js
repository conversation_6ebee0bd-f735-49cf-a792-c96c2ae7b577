const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function checkAtlasConnection() {
  try {
    console.log('🔌 Connecting to MongoDB Atlas...');
    console.log('📍 Database URI:', process.env.MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));
    
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB Atlas successfully!');
    
    // Get database name
    const dbName = mongoose.connection.name;
    console.log(`📍 Connected to database: ${dbName}`);
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('\n📦 Collections in database:');
    collections.forEach(col => {
      console.log(`  - ${col.name}`);
    });
    
    // Check document counts for each collection
    console.log('\n📊 Document counts:');
    const collectionNames = ['users', 'teams', 'players', 'tournaments', 'auctions', 'templates', 'ocrsettings'];
    
    let totalDocs = 0;
    for (const colName of collectionNames) {
      try {
        const count = await mongoose.connection.db.collection(colName).countDocuments();
        console.log(`  ${colName.padEnd(12)}: ${count} documents`);
        totalDocs += count;
        
        // Show a sample document if collection has data
        if (count > 0) {
          const sample = await mongoose.connection.db.collection(colName).findOne();
          console.log(`    Sample: ${sample._id || 'No _id'} - ${sample.name || sample.username || sample.title || 'No name field'}`);
        }
      } catch (err) {
        console.log(`  ${colName.padEnd(12)}: Collection not found`);
      }
    }
    
    console.log(`\n📈 Total documents: ${totalDocs}`);
    
    if (totalDocs > 0) {
      console.log('\n✅ Migration verification successful!');
      console.log('🎯 Your local data has been successfully migrated to Atlas.');
    } else {
      console.log('\n⚠️  No documents found. Migration may not have completed successfully.');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error connecting to Atlas:', error.message);
    process.exit(1);
  }
}

checkAtlasConnection();