/**
 * Bowling Figure Parser Utility
 * Parses bowling figures like "2-49", "4-67", "0-15" into wickets and runs
 */

/**
 * Parse bowling figure string into wickets and runs
 * @param {string} figureText - Bowling figure text (e.g., "2-49", "4-67")
 * @returns {Object} - { wickets: number, runs: number, isValid: boolean, confidence: string }
 */
function parseBowlingFigure(figureText) {
  if (!figureText || typeof figureText !== 'string') {
    return { wickets: null, runs: null, isValid: false, confidence: 'low' };
  }

  // Clean the text
  const cleanText = figureText.trim().replace(/\s+/g, '');
  
  // Pattern for bowling figures: wickets-runs (e.g., "2-49", "10-125")
  const bowlingPattern = /^(\d{1,2})-(\d{1,3})$/;
  const match = cleanText.match(bowlingPattern);
  
  if (!match) {
    return { wickets: null, runs: null, isValid: false, confidence: 'low' };
  }
  
  const wickets = parseInt(match[1], 10);
  const runs = parseInt(match[2], 10);
  
  // Validate cricket logic
  if (wickets < 0 || wickets > 10 || runs < 0 || runs > 300) {
    return { wickets: null, runs: null, isValid: false, confidence: 'low' };
  }
  
  // Determine confidence based on cricket logic
  let confidence = 'medium';
  
  // High confidence: typical cricket figures
  if (wickets <= 10 && runs <= 200 && (wickets === 0 || runs >= wickets)) {
    confidence = 'high';
  }
  
  // Low confidence: unusual figures
  if (wickets > 6 || runs > 150 || (wickets > 0 && runs < wickets)) {
    confidence = 'low';
  }
  
  return {
    wickets,
    runs,
    isValid: true,
    confidence,
    originalText: figureText
  };
}

/**
 * Parse multiple bowling figures from an array of text
 * @param {Array} textArray - Array of text strings
 * @returns {Array} - Array of parsed bowling figures
 */
function parseBowlingFigures(textArray) {
  if (!Array.isArray(textArray)) {
    return [];
  }
  
  return textArray
    .map(text => parseBowlingFigure(text))
    .filter(result => result.isValid);
}

/**
 * Extract bowling figures from OCR data based on field mappings
 * @param {Object} ocrData - OCR extracted data with field mappings
 * @returns {Object} - Enhanced data with parsed bowling figures
 */
function enhanceWithBowlingFigures(ocrData) {
  const enhanced = { ...ocrData };
  
  // Process Team 1 bowling figures
  if (ocrData.team1_bowler_figure) {
    const figures = Array.isArray(ocrData.team1_bowler_figure) 
      ? ocrData.team1_bowler_figure 
      : [ocrData.team1_bowler_figure];
    
    enhanced.team1_bowler_figures_parsed = figures.map(figure => parseBowlingFigure(figure));
    
    // Auto-populate individual wickets and runs if not already present
    if (!enhanced.team1_bowler_wickets_taken) {
      enhanced.team1_bowler_wickets_taken = enhanced.team1_bowler_figures_parsed
        .filter(f => f.isValid)
        .map(f => f.wickets);
    }
    
    if (!enhanced.team1_bowler_runs_conceded) {
      enhanced.team1_bowler_runs_conceded = enhanced.team1_bowler_figures_parsed
        .filter(f => f.isValid)
        .map(f => f.runs);
    }
  }
  
  // Process Team 2 bowling figures
  if (ocrData.team2_bowler_figure) {
    const figures = Array.isArray(ocrData.team2_bowler_figure) 
      ? ocrData.team2_bowler_figure 
      : [ocrData.team2_bowler_figure];
    
    enhanced.team2_bowler_figures_parsed = figures.map(figure => parseBowlingFigure(figure));
    
    // Auto-populate individual wickets and runs if not already present
    if (!enhanced.team2_bowler_wickets_taken) {
      enhanced.team2_bowler_wickets_taken = enhanced.team2_bowler_figures_parsed
        .filter(f => f.isValid)
        .map(f => f.wickets);
    }
    
    if (!enhanced.team2_bowler_runs_conceded) {
      enhanced.team2_bowler_runs_conceded = enhanced.team2_bowler_figures_parsed
        .filter(f => f.isValid)
        .map(f => f.runs);
    }
  }
  
  return enhanced;
}

/**
 * Validate bowling figure text
 * @param {string} text - Text to validate
 * @returns {boolean} - True if text looks like a bowling figure
 */
function isBowlingFigure(text) {
  const result = parseBowlingFigure(text);
  return result.isValid;
}

/**
 * Format bowling figure for display
 * @param {number} wickets - Number of wickets
 * @param {number} runs - Number of runs
 * @returns {string} - Formatted bowling figure (e.g., "2-49")
 */
function formatBowlingFigure(wickets, runs) {
  if (wickets == null || runs == null) {
    return '';
  }
  return `${wickets}-${runs}`;
}

module.exports = {
  parseBowlingFigure,
  parseBowlingFigures,
  enhanceWithBowlingFigures,
  isBowlingFigure,
  formatBowlingFigure
};
