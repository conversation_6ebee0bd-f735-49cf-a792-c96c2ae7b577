import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardHeader,
  CardContent,
  TextField,
  Button,
  Divider,
  Slider,
  FormControlLabel,
  Switch,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';

// TabPanel component for tabs
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ocr-settings-tabpanel-${index}`}
      aria-labelledby={`ocr-settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const OcrSettings = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  
  // PaddleOCR settings
  const [paddleSettings, setPaddleSettings] = useState({
    det_db_thresh: 0.25,
    det_db_box_thresh: 0.4,
    det_db_unclip_ratio: 2.0,
    drop_score: 0.3
  });
  
  // Google Vision settings
  const [googleSettings, setGoogleSettings] = useState({
    useDocumentTextDetection: true,
    enhancePreprocessing: true
  });
  
  // Tesseract settings
  const [tesseractSettings, setTesseractSettings] = useState({
    tessedit_pageseg_mode: 6,
    tessjs_create_hocr: true,
    tessjs_create_tsv: true,
    enhancePreprocessing: true
  });
  
  // Handle tab changes
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Load settings from server
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/ocr-settings');
        
        // Update state with settings from server
        if (response.data.paddle) {
          setPaddleSettings(response.data.paddle);
        }
        
        if (response.data.google) {
          setGoogleSettings(response.data.google);
        }
        
        if (response.data.tesseract) {
          setTesseractSettings(response.data.tesseract);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching OCR settings:', err);
        setError('Failed to fetch OCR settings');
        setLoading(false);
      }
    };
    
    fetchSettings();
  }, []);
  
  // Handle PaddleOCR settings change
  const handlePaddleSettingChange = (setting, value) => {
    setPaddleSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };
  
  // Handle Google Vision settings change
  const handleGoogleSettingChange = (setting, value) => {
    setGoogleSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };
  
  // Handle Tesseract settings change
  const handleTesseractSettingChange = (setting, value) => {
    setTesseractSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };
  
  // Save settings to server
  const saveSettings = async () => {
    try {
      setLoading(true);
      setError('');
      
      await axios.post('/api/ocr-settings', {
        paddle: paddleSettings,
        google: googleSettings,
        tesseract: tesseractSettings
      });
      
      setSuccess(true);
      setLoading(false);
    } catch (err) {
      console.error('Error saving OCR settings:', err);
      setError('Failed to save OCR settings');
      setLoading(false);
    }
  };
  
  // Reset settings to defaults
  const resetSettings = () => {
    setPaddleSettings({
      det_db_thresh: 0.25,
      det_db_box_thresh: 0.4,
      det_db_unclip_ratio: 2.0,
      drop_score: 0.3
    });
    
    setGoogleSettings({
      useDocumentTextDetection: true,
      enhancePreprocessing: true
    });
    
    setTesseractSettings({
      tessedit_pageseg_mode: 6,
      tessjs_create_hocr: true,
      tessjs_create_tsv: true,
      enhancePreprocessing: true
    });
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        OCR Settings
      </Typography>
      
      <Paper elevation={2} sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} centered>
            <Tab label="PaddleOCR" />
            <Tab label="Google Vision API" />
            <Tab label="Tesseract.js" />
          </Tabs>
        </Box>
        
        {/* PaddleOCR Settings */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            PaddleOCR Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Adjust these settings to improve text detection and recognition accuracy for cricket scorecards.
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Detection Threshold ({paddleSettings.det_db_thresh})
              </Typography>
              <Slider
                value={paddleSettings.det_db_thresh}
                onChange={(e, value) => handlePaddleSettingChange('det_db_thresh', value)}
                min={0.1}
                max={0.9}
                step={0.05}
                valueLabelDisplay="auto"
                disabled={loading}
              />
              <Typography variant="body2" color="text.secondary">
                Lower values detect more text but may include noise (0.1-0.9)
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Box Threshold ({paddleSettings.det_db_box_thresh})
              </Typography>
              <Slider
                value={paddleSettings.det_db_box_thresh}
                onChange={(e, value) => handlePaddleSettingChange('det_db_box_thresh', value)}
                min={0.1}
                max={0.9}
                step={0.05}
                valueLabelDisplay="auto"
                disabled={loading}
              />
              <Typography variant="body2" color="text.secondary">
                Controls text box detection sensitivity (0.1-0.9)
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Unclip Ratio ({paddleSettings.det_db_unclip_ratio})
              </Typography>
              <Slider
                value={paddleSettings.det_db_unclip_ratio}
                onChange={(e, value) => handlePaddleSettingChange('det_db_unclip_ratio', value)}
                min={1.0}
                max={3.0}
                step={0.1}
                valueLabelDisplay="auto"
                disabled={loading}
              />
              <Typography variant="body2" color="text.secondary">
                Higher values expand text boxes (1.0-3.0)
              </Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Confidence Threshold ({paddleSettings.drop_score})
              </Typography>
              <Slider
                value={paddleSettings.drop_score}
                onChange={(e, value) => handlePaddleSettingChange('drop_score', value)}
                min={0.1}
                max={0.9}
                step={0.05}
                valueLabelDisplay="auto"
                disabled={loading}
              />
              <Typography variant="body2" color="text.secondary">
                Minimum confidence score for text recognition (0.1-0.9)
              </Typography>
            </Grid>
          </Grid>
        </TabPanel>
        
        {/* Google Vision API Settings */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Google Vision API Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure Google Vision API settings for cricket scorecard extraction.
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={googleSettings.useDocumentTextDetection}
                    onChange={(e) => handleGoogleSettingChange('useDocumentTextDetection', e.target.checked)}
                    disabled={loading}
                  />
                }
                label="Use Document Text Detection (better for structured text)"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={googleSettings.enhancePreprocessing}
                    onChange={(e) => handleGoogleSettingChange('enhancePreprocessing', e.target.checked)}
                    disabled={loading}
                  />
                }
                label="Enable Enhanced Image Preprocessing"
              />
            </Grid>
          </Grid>
        </TabPanel>
        
        {/* Tesseract.js Settings */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Tesseract.js Settings
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure Tesseract.js settings for cricket scorecard extraction.
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Page Segmentation Mode ({tesseractSettings.tessedit_pageseg_mode})
              </Typography>
              <Slider
                value={tesseractSettings.tessedit_pageseg_mode}
                onChange={(e, value) => handleTesseractSettingChange('tessedit_pageseg_mode', value)}
                min={1}
                max={13}
                step={1}
                marks
                valueLabelDisplay="auto"
                disabled={loading}
              />
              <Typography variant="body2" color="text.secondary">
                Controls how the page is analyzed (6 = assume a single uniform block of text)
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tesseractSettings.tessjs_create_hocr}
                    onChange={(e) => handleTesseractSettingChange('tessjs_create_hocr', e.target.checked)}
                    disabled={loading}
                  />
                }
                label="Create HOCR Output (includes position information)"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tesseractSettings.tessjs_create_tsv}
                    onChange={(e) => handleTesseractSettingChange('tessjs_create_tsv', e.target.checked)}
                    disabled={loading}
                  />
                }
                label="Create TSV Output (includes detailed information)"
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={tesseractSettings.enhancePreprocessing}
                    onChange={(e) => handleTesseractSettingChange('enhancePreprocessing', e.target.checked)}
                    disabled={loading}
                  />
                }
                label="Enable Enhanced Image Preprocessing"
              />
            </Grid>
          </Grid>
        </TabPanel>
        
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={resetSettings}
            disabled={loading}
          >
            Reset to Defaults
          </Button>
          
          <Button
            variant="contained"
            color="primary"
            onClick={saveSettings}
            disabled={loading}
            startIcon={loading && <CircularProgress size={20} color="inherit" />}
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>
      </Paper>
      
      {/* Success message */}
      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
      >
        <Alert onClose={() => setSuccess(false)} severity="success">
          OCR settings saved successfully!
        </Alert>
      </Snackbar>
      
      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Container>
  );
};

export default OcrSettings;
