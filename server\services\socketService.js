const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const redisService = require('../config/redis');
const auctionService = require('./auctionService');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map();
    this.isRedisAvailable = false;
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.SOCKET_IO_CORS_ORIGIN?.split(',') || ["http://localhost:3000", "http://localhost:4000"],
        methods: ["GET", "POST"]
      }
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication error'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.user.id).select('-password');
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });

    this.setupEventHandlers();
    
    // Check Redis availability
    this.isRedisAvailable = redisService.isRedisConnected && redisService.isRedisConnected();
    
    if (this.isRedisAvailable) {
      this.setupRedisSubscriptions();
      console.log('✅ Socket.io service initialized with Redis support');
    } else {
      console.log('⚠️ Socket.io service initialized without Redis - using direct broadcasting');
    }
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`🔌 User connected: ${socket.user.username} (${socket.userId})`);
      
      this.connectedUsers.set(socket.userId, {
        socketId: socket.id,
        user: socket.user,
        connectedAt: new Date()
      });

      // Send connection confirmation
      socket.emit('connected', {
        message: 'Connected to auction server',
        userId: socket.userId,
        username: socket.user.username
      });

      // Join auction rooms
      socket.on('join_auction', async (auctionId) => {
        socket.join(`auction:${auctionId}`);
        console.log(`👥 User ${socket.user.username} joined auction ${auctionId}`);
        
        // Send current auction state
        const auctionState = await auctionService.initializeAuction(auctionId);
        if (auctionState) {
          socket.emit('auction_state', auctionState);
        }
      });

      socket.on('leave_auction', (auctionId) => {
        socket.leave(`auction:${auctionId}`);
        console.log(`👋 User ${socket.user.username} left auction ${auctionId}`);
      });

      // Handle real-time bidding
      socket.on('place_bid', async (data) => {
        try {
          const { auctionId, amount } = data;
          console.log(`💰 Bid attempt: ${socket.user.username} - $${amount} on auction ${auctionId}`);
          
          const result = await auctionService.placeBid(
            auctionId, 
            socket.userId, 
            amount, 
            socket.user.username
          );
          
          socket.emit('bid_success', result);
        } catch (error) {
          console.error(`❌ Bid error for ${socket.user.username}:`, error.message);
          socket.emit('bid_error', { message: error.message });
        }
      });

      // Handle auction subscription
      socket.on('subscribe_auctions', () => {
        socket.join('auction_updates');
        console.log(`📺 User ${socket.user.username} subscribed to auction updates`);
      });

      socket.on('unsubscribe_auctions', () => {
        socket.leave('auction_updates');
        console.log(`📺 User ${socket.user.username} unsubscribed from auction updates`);
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date() });
      });

      socket.on('disconnect', (reason) => {
        console.log(`🔌 User disconnected: ${socket.user.username} - Reason: ${reason}`);
        this.connectedUsers.delete(socket.userId);
      });

      socket.on('error', (error) => {
        console.error(`🔌 Socket error for ${socket.user.username}:`, error);
      });
    });
  }

  setupRedisSubscriptions() {
    if (!this.isRedisAvailable) {
      console.log('Skipping Redis subscriptions - Redis not available');
      return;
    }
    
    try {
      const subscriber = redisService.getSubscriber();
      
      if (subscriber) {
        // Subscribe to auction updates
        subscriber.subscribe('auction:updates');
        
        // Subscribe to specific auction channels
        subscriber.psubscribe('auction:*');
        
        subscriber.on('message', (channel, message) => {
          this.handleRedisMessage(channel, message);
        });
        
        subscriber.on('pmessage', (pattern, channel, message) => {
          this.handleRedisMessage(channel, message);
        });
        
        console.log('Redis subscriptions setup complete');
      }
    } catch (error) {
      console.error('Error setting up Redis subscriptions:', error);
      this.isRedisAvailable = false;
    }
  }

  getConnectedUsers() {
    return Array.from(this.connectedUsers.values());
  }

  getUserCount() {
    return this.connectedUsers.size;
  }

  emitToUser(userId, event, data) {
    const userConnection = this.connectedUsers.get(userId);
    if (userConnection) {
      this.io.to(userConnection.socketId).emit(event, data);
      return true;
    }
    return false;
  }

  emitToAuction(auctionId, event, data) {
    this.io.to(`auction:${auctionId}`).emit(event, data);
  }

  emitToAll(event, data) {
    this.io.emit(event, data);
  }

  // Broadcast system messages
  broadcastSystemMessage(message, type = 'info') {
    this.io.emit('system_message', {
      message,
      type,
      timestamp: new Date()
    });
  }

  handleRedisMessage(channel, message) {
    try {
      const data = JSON.parse(message);
      
      console.log(`📡 Broadcasting auction update: ${data.type} for auction ${data.auctionId}`);
      
      // Broadcast to all users in the specific auction room
      this.io.to(`auction:${data.auctionId}`).emit('auction_update', data);
      
      // Also broadcast to general auction subscribers
      this.io.to('auction_updates').emit('global_auction_update', data);
    } catch (error) {
      console.error('Error processing Redis message:', error);
    }
  }

  broadcastAuctionUpdate(auctionId, updateData) {
    try {
      // Broadcast to specific auction room
      this.io.to(`auction:${auctionId}`).emit('auction_update', updateData);
      
      // Broadcast to general auction updates room
      this.io.to('auction_updates').emit('auction_update', updateData);
      
      console.log(`Broadcasted auction update for ${auctionId}:`, updateData.type);
    } catch (error) {
      console.error('Error broadcasting auction update:', error);
    }
  }

  // Get statistics
  getStats() {
    return {
      connectedUsers: this.getUserCount(),
      activeRooms: this.io.sockets.adapter.rooms.size,
      totalSockets: this.io.sockets.sockets.size
    };
  }
}

module.exports = new SocketService();