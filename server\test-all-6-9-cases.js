/**
 * Test script to verify that our 6/9 confusion detection works for all cases
 */

const OCRService = require('./services/ocrService');

// Create mock players with various 6/9 patterns
const mockPlayers = [
  { name: 'Player1', runs: '6', balls: '6' },       // 6(6)
  { name: 'Player2', runs: '9', balls: '9' },       // 9(9)
  { name: 'Player3', runs: '6', balls: '9' },       // 6(9)
  { name: 'Player4', runs: '9', balls: '6' },       // 9(6)
  { name: 'Player5', runs: '6*', balls: '6' },      // 6*(6)
  { name: 'Player6', runs: '9*', balls: '9' },      // 9*(9)
  { name: 'Player7', runs: '16', balls: '6' },      // 16(6)
  { name: 'Player8', runs: '19', balls: '9' },      // 19(9)
  { name: 'Player9', runs: '6', balls: '12' },      // 6(12)
  { name: '<PERSON>10', runs: '9', balls: '12' },     // 9(12)
  { name: 'Player11', runs: '12', balls: '12' },    // 12(12) - should not be verified
  { name: 'Player12', runs: '7', balls: '7' },      // 7(7) - should not be verified
  { name: 'Player13', runs: '7*', balls: '6' },     // 7*(6)
  { name: 'Player14', runs: '7', balls: '9' },      // 7(9)
  { name: 'Player15', runs: '6*', balls: '12' },    // 6*(12)
  { name: 'Player16', runs: '9*', balls: '12' },    // 9*(12)
];

// Test the verification logic
async function testVerificationLogic() {
  console.log('🧪 Testing 6/9 Confusion Detection Logic\n');
  
  // Initialize OCR service
  const ocrService = new OCRService();
  
  // Override the verifyPlayerScore method to avoid actual API calls
  ocrService.googleVisionService.verifyPlayerScore = async (imagePath, playerName) => {
    return {
      name: playerName,
      found: true,
      runs: '9', // Always return 9 for testing
      balls: 9   // Always return 9 for testing
    };
  };
  
  try {
    // Call the verifyPlayerScores method with our mock players
    const playersToVerify = mockPlayers.filter(player => {
      // This is the same logic as in the OCR service
      const runsStr = player.runs ? player.runs.toString() : '';
      const ballsStr = player.balls ? player.balls.toString() : '';
      
      // Extract numeric part from runs (removing * if present)
      const runsNumeric = runsStr.replace('*', '');
      const runs = parseInt(runsNumeric, 10);
      const balls = parseInt(ballsStr, 10);
      
      // Check if runs or balls contain 6 or 9
      const hasRunsWith6or9 = runsNumeric === '6' || runsNumeric === '9';
      const hasBallsWith6or9 = ballsStr === '6' || ballsStr === '9';
      
      // Also check for other common patterns
      return (
        // When runs is 6 or 9 (with or without *)
        hasRunsWith6or9 || 
        // Or when balls is 6 or 9
        hasBallsWith6or9
      );
    });
    
    console.log('Players that should be verified:');
    playersToVerify.forEach(player => {
      console.log(`- ${player.name}: ${player.runs}(${player.balls})`);
    });
    
    console.log(`\nTotal players to verify: ${playersToVerify.length} out of ${mockPlayers.length}`);
    
    // Check which players were NOT selected for verification
    const playersNotVerified = mockPlayers.filter(player => 
      !playersToVerify.some(p => p.name === player.name)
    );
    
    console.log('\nPlayers that should NOT be verified:');
    playersNotVerified.forEach(player => {
      console.log(`- ${player.name}: ${player.runs}(${player.balls})`);
    });
    
    // Verify that our logic is correct
    const expectedToVerify = [
      'Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6',
      'Player7', 'Player8', 'Player9', 'Player10', 'Player13', 'Player14',
      'Player15', 'Player16'
    ];
    
    const expectedNotToVerify = [
      'Player11', 'Player12'
    ];
    
    const allVerifiedCorrectly = expectedToVerify.every(name => 
      playersToVerify.some(p => p.name === name)
    );
    
    const allNotVerifiedCorrectly = expectedNotToVerify.every(name => 
      playersNotVerified.some(p => p.name === name)
    );
    
    if (allVerifiedCorrectly && allNotVerifiedCorrectly) {
      console.log('\n✅ TEST PASSED: All players were correctly identified for verification');
    } else {
      console.log('\n❌ TEST FAILED: Some players were incorrectly identified for verification');
      
      // Show which players were incorrectly identified
      const incorrectlyVerified = expectedNotToVerify.filter(name => 
        playersToVerify.some(p => p.name === name)
      );
      
      const incorrectlyNotVerified = expectedToVerify.filter(name => 
        playersNotVerified.some(p => p.name === name)
      );
      
      if (incorrectlyVerified.length > 0) {
        console.log('\nIncorrectly selected for verification:');
        incorrectlyVerified.forEach(name => {
          const player = mockPlayers.find(p => p.name === name);
          console.log(`- ${player.name}: ${player.runs}(${player.balls})`);
        });
      }
      
      if (incorrectlyNotVerified.length > 0) {
        console.log('\nIncorrectly NOT selected for verification:');
        incorrectlyNotVerified.forEach(name => {
          const player = mockPlayers.find(p => p.name === name);
          console.log(`- ${player.name}: ${player.runs}(${player.balls})`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testVerificationLogic();