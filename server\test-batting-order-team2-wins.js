/**
 * Test script to verify the batting order fix in matchOutcomeService.js
 * with Team 2 winning scenarios
 */

const MatchOutcomeService = require('./services/matchOutcomeService');
const connectDB = require('./config/db');

async function testBattingOrderFixTeam2Wins() {
  console.log('🏏 TESTING BATTING ORDER FIX - TEAM 2 WINS SCENARIOS');
  console.log('==============================================\n');

  // Initialize the match outcome service
  const matchService = new MatchOutcomeService();
  await matchService.initialize();

  // Create mock data for the test where Team 2 wins
  const mockData = {
    success: true,
    team1: 'Chennai Super Kings',
    team2: 'Mumbai Indians',
    venue: 'Chennai Cricket Ground',
    team1Score: { runs: 162, wickets: 5, overs: 20.0 },
    team2Score: { runs: 164, wickets: 2, overs: 18.2 },
    team1Batsmen: [
      { name: 'Player CSK1', runs: 70, balls: 55 },
      { name: 'Player CSK2', runs: 50, balls: 40 }
    ],
    team2Batsmen: [
      { name: 'Player MI1', runs: 80, balls: 52 },
      { name: 'Player MI2', runs: 60, balls: 40 }
    ]
  };

  // Test Case 1: Team 2 is home team and home team batted first
  console.log('\n📋 TEST CASE 1: Team 2 is home team and home team batted first');
  const mockMatch1 = {
    team1IsHomeTeam: false,
    homeTeamBattedFirst: true
  };
  await runTest(matchService, mockData, mockMatch1);

  // Test Case 2: Team 2 is home team and home team batted second
  console.log('\n📋 TEST CASE 2: Team 2 is home team and home team batted second');
  const mockMatch2 = {
    team1IsHomeTeam: false,
    homeTeamBattedFirst: false
  };
  await runTest(matchService, mockData, mockMatch2);

  console.log('\n✅ All test cases completed!');
}

async function runTest(matchService, mockData, mockMatch) {
  console.log(`   Match configuration: ${JSON.stringify(mockMatch)}`);
  console.log(`   Team 1 (${mockData.team1}): ${mockData.team1Score.runs}-${mockData.team1Score.wickets}`);
  console.log(`   Team 2 (${mockData.team2}): ${mockData.team2Score.runs}-${mockData.team2Score.wickets}`);

  try {
    const outcome = await matchService.calculateMatchOutcome(mockData, 'test-tournament', mockMatch);
    
    console.log('\n   📊 RESULT:');
    console.log(`   Winner: ${outcome.winner}`);
    console.log(`   Description: ${outcome.resultDescription}`);
    
    // Validate the outcome
    const team1BattedFirst = mockMatch.team1IsHomeTeam ? mockMatch.homeTeamBattedFirst : !mockMatch.homeTeamBattedFirst;
    console.log(`   Expected team1BattedFirst: ${team1BattedFirst}`);
    
    // Check if the description matches the expected outcome
    const expectedMarginType = team1BattedFirst ? 'wickets' : 'runs';
    const hasExpectedMarginType = outcome.resultDescription.toLowerCase().includes(expectedMarginType);
    
    if (hasExpectedMarginType) {
      console.log(`   ✅ PASSED: Description correctly includes '${expectedMarginType}'`);
    } else {
      console.log(`   ❌ FAILED: Expected description to include '${expectedMarginType}', got: ${outcome.resultDescription}`);
    }
  } catch (error) {
    console.error(`   ❌ ERROR: ${error.message}`);
  }
}

// Connect to database and run the test
async function main() {
  console.log('🔌 Connecting to database...');
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('⚠️  Database connection failed, continuing with limited functionality...');
  } else {
    console.log('✅ Database connected successfully\n');
  }
  
  await testBattingOrderFixTeam2Wins();
  
  // Exit the process
  process.exit(0);
}

// Run the test
main().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});