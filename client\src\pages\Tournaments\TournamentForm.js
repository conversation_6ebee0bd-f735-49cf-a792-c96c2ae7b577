import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  IconButton
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  EmojiEvents as TrophyIcon,
  Image as ImageIcon
} from '@mui/icons-material';

import { createTournament, getTournamentById, updateTournament } from '../../services/tournamentService';

const TournamentForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    format: 'T20',
    startDate: new Date(new Date().setDate(new Date().getDate() + 7)),
    endDate: new Date(new Date().setDate(new Date().getDate() + 14)),
    registrationDeadline: new Date(new Date().setDate(new Date().getDate() + 5)),
    maxTeams: 8,
    pointsForWin: 2,
    pointsForTie: 1,
    pointsForNoResult: 1,
    roundConfiguration: {
      groupStageRounds: 2,
      knockoutFormat: 'single-elimination',
      playoffQualifiers: 4,
      useGroups: false,
      thirdPlaceMatch: false,
      finalFormat: 'single-match'
    },
    logo: '',
    banner: '',
    primaryColor: '#1e88e5',
    secondaryColor: '#bbdefb'
  });

  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    if (isEditMode) {
      fetchTournament();
    }
  }, [id]);

  const fetchTournament = async () => {
    try {
      setLoading(true);
      setError(null);

      const tournament = await getTournamentById(id);

      setFormData({
        name: tournament.name,
        description: tournament.description,
        format: tournament.format,
        startDate: new Date(tournament.startDate),
        endDate: new Date(tournament.endDate),
        registrationDeadline: new Date(tournament.registrationDeadline),
        maxTeams: tournament.maxTeams,
        pointsForWin: tournament.pointsForWin,
        pointsForTie: tournament.pointsForTie,
        pointsForNoResult: tournament.pointsForNoResult,
        roundConfiguration: tournament.roundConfiguration || {
          groupStageRounds: 1,
          knockoutFormat: 'single-elimination',
          thirdPlaceMatch: false,
          finalFormat: 'single-match'
        },
        logo: tournament.logo,
        banner: tournament.banner,
        primaryColor: tournament.primaryColor,
        secondaryColor: tournament.secondaryColor
      });
    } catch (err) {
      console.error('Error fetching tournament:', err);
      setError('Failed to load tournament data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested roundConfiguration fields
    if (name.startsWith('roundConfiguration.')) {
      const configField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        roundConfiguration: {
          ...prev.roundConfiguration,
          [configField]: value === 'true' ? true :
                         value === 'false' ? false :
                         value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const handleDateChange = (name, date) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Tournament name is required';
    }

    if (!formData.format) {
      errors.format = 'Format is required';
    }

    if (!formData.startDate) {
      errors.startDate = 'Start date is required';
    }

    if (!formData.endDate) {
      errors.endDate = 'End date is required';
    } else if (formData.endDate <= formData.startDate) {
      errors.endDate = 'End date must be after start date';
    }

    if (!formData.registrationDeadline) {
      errors.registrationDeadline = 'Registration deadline is required';
    } else if (formData.registrationDeadline >= formData.startDate) {
      errors.registrationDeadline = 'Registration deadline must be before start date';
    }

    if (!formData.maxTeams || formData.maxTeams < 2) {
      errors.maxTeams = 'Maximum teams must be at least 2';
    } else if (formData.maxTeams > 32) {
      errors.maxTeams = 'Maximum teams cannot exceed 32';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      if (isEditMode) {
        await updateTournament(id, formData);
        setSuccess('Tournament updated successfully!');
      } else {
        const newTournament = await createTournament(formData);
        setSuccess('Tournament created successfully!');

        // Redirect to the new tournament after a short delay
        setTimeout(() => {
          navigate(`/tournaments/${newTournament._id}`);
        }, 1500);
      }
    } catch (err) {
      console.error('Error saving tournament:', err);
      setError(err.message || 'Failed to save tournament. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          <TrophyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          {isEditMode ? 'Edit Tournament' : 'Create Tournament'}
        </Typography>

        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          component={Link}
          to={isEditMode ? `/tournaments/${id}` : '/tournaments'}
        >
          {isEditMode ? 'Back to Tournament' : 'Back to Tournaments'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tournament Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!formErrors.name}
                helperText={formErrors.name}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!formErrors.format}>
                <InputLabel>Format</InputLabel>
                <Select
                  name="format"
                  value={formData.format}
                  onChange={handleChange}
                  label="Format"
                >
                  <MenuItem value="T10">T10</MenuItem>
                  <MenuItem value="T20">T20</MenuItem>
                  <MenuItem value="ODI">ODI</MenuItem>
                  <MenuItem value="Test">Test</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                multiline
                rows={3}
              />
            </Grid>

            {/* Dates */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Tournament Dates
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Registration Deadline"
                  value={formData.registrationDeadline}
                  onChange={(date) => handleDateChange('registrationDeadline', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!formErrors.registrationDeadline,
                      helperText: formErrors.registrationDeadline
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Start Date"
                  value={formData.startDate}
                  onChange={(date) => handleDateChange('startDate', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!formErrors.startDate,
                      helperText: formErrors.startDate
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={4}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="End Date"
                  value={formData.endDate}
                  onChange={(date) => handleDateChange('endDate', date)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!formErrors.endDate,
                      helperText: formErrors.endDate
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            {/* Tournament Settings */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Tournament Settings
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Maximum Teams"
                name="maxTeams"
                type="number"
                value={formData.maxTeams}
                onChange={handleChange}
                inputProps={{ min: 2, max: 32 }}
                error={!!formErrors.maxTeams}
                helperText={formErrors.maxTeams}
                required
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Points for Win"
                name="pointsForWin"
                type="number"
                value={formData.pointsForWin}
                onChange={handleChange}
                inputProps={{ min: 1, max: 10 }}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Points for Tie"
                name="pointsForTie"
                type="number"
                value={formData.pointsForTie}
                onChange={handleChange}
                inputProps={{ min: 0, max: 5 }}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Points for No Result"
                name="pointsForNoResult"
                type="number"
                value={formData.pointsForNoResult}
                onChange={handleChange}
                inputProps={{ min: 0, max: 5 }}
              />
            </Grid>

            {/* Round Configuration */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Round Configuration
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Group Stage Rounds"
                name="roundConfiguration.groupStageRounds"
                type="number"
                value={formData.roundConfiguration.groupStageRounds}
                onChange={handleChange}
                inputProps={{ min: 1, max: 4 }}
                helperText="Number of times each team plays against others"
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <InputLabel>Knockout Format</InputLabel>
                <Select
                  name="roundConfiguration.knockoutFormat"
                  value={formData.roundConfiguration.knockoutFormat}
                  onChange={handleChange}
                  label="Knockout Format"
                >
                  <MenuItem value="single-elimination">Single Elimination</MenuItem>
                  <MenuItem value="double-elimination">Double Elimination</MenuItem>
                  <MenuItem value="playoffs">Playoffs</MenuItem>
                  <MenuItem value="ipl-style">IPL Style</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Conditional IPL-style options */}
            {formData.roundConfiguration.knockoutFormat === 'ipl-style' && (
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Playoff Qualifiers</InputLabel>
                  <Select
                    name="roundConfiguration.playoffQualifiers"
                    value={formData.roundConfiguration.playoffQualifiers}
                    onChange={handleChange}
                    label="Playoff Qualifiers"
                  >
                    <MenuItem value={4}>Top 4 Teams</MenuItem>
                    <MenuItem value={8}>Top 8 Teams</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}

            {formData.roundConfiguration.knockoutFormat === 'ipl-style' && (
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Use Groups</InputLabel>
                  <Select
                    name="roundConfiguration.useGroups"
                    value={formData.roundConfiguration.useGroups.toString()}
                    onChange={handleChange}
                    label="Use Groups"
                  >
                    <MenuItem value="false">No Groups (All Play All)</MenuItem>
                    <MenuItem value="true">Use Group Stage</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <InputLabel>Third Place Match</InputLabel>
                <Select
                  name="roundConfiguration.thirdPlaceMatch"
                  value={formData.roundConfiguration.thirdPlaceMatch.toString()}
                  onChange={handleChange}
                  label="Third Place Match"
                >
                  <MenuItem value="true">Yes</MenuItem>
                  <MenuItem value="false">No</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <InputLabel>Final Format</InputLabel>
                <Select
                  name="roundConfiguration.finalFormat"
                  value={formData.roundConfiguration.finalFormat}
                  onChange={handleChange}
                  label="Final Format"
                >
                  <MenuItem value="single-match">Single Match</MenuItem>
                  <MenuItem value="best-of-three">Best of Three</MenuItem>
                  <MenuItem value="best-of-five">Best of Five</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Appearance */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Appearance
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Primary Color"
                name="primaryColor"
                value={formData.primaryColor}
                onChange={handleChange}
                type="color"
                InputProps={{
                  startAdornment: (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: 1,
                        backgroundColor: formData.primaryColor,
                        mr: 1
                      }}
                    />
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Secondary Color"
                name="secondaryColor"
                value={formData.secondaryColor}
                onChange={handleChange}
                type="color"
                InputProps={{
                  startAdornment: (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        borderRadius: 1,
                        backgroundColor: formData.secondaryColor,
                        mr: 1
                      }}
                    />
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Logo URL"
                name="logo"
                value={formData.logo}
                onChange={handleChange}
                placeholder="/uploads/tournaments/default.png"
                InputProps={{
                  startAdornment: <ImageIcon sx={{ color: 'action.active', mr: 1 }} />
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Banner URL"
                name="banner"
                value={formData.banner}
                onChange={handleChange}
                placeholder="/uploads/tournaments/default-banner.png"
                InputProps={{
                  startAdornment: <ImageIcon sx={{ color: 'action.active', mr: 1 }} />
                }}
              />
            </Grid>

            {/* Form Actions */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<CancelIcon />}
                  component={Link}
                  to={isEditMode ? `/tournaments/${id}` : '/tournaments'}
                  disabled={submitting}
                >
                  Cancel
                </Button>

                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={submitting ? <CircularProgress size={24} /> : <SaveIcon />}
                  disabled={submitting}
                >
                  {isEditMode ? 'Update Tournament' : 'Create Tournament'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default TournamentForm;
