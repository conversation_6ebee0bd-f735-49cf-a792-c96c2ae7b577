import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  Paper,
  Typography,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  <PERSON>eteF<PERSON>ver as DeleteIcon,
  Refresh as RefreshIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Image as ImageIcon,
  Label as LabelIcon
} from '@mui/icons-material';
import { getLabeledImages } from '../../services/trainingService';

const ImageSelectionModal = ({ open, onClose, onClearSelected }) => {
  // State for images
  const [images, setImages] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load images when modal opens
  useEffect(() => {
    if (open) {
      fetchImages();
    }
  }, [open]);

  // Fetch images from server
  const fetchImages = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getLabeledImages();

      if (result.success) {
        // Filter to only show labeled images
        const labeledImages = result.all_images.filter(img => img.labeled);
        setImages(labeledImages);
      } else {
        setError('Failed to fetch labeled images');
      }
    } catch (err) {
      console.error('Error fetching labeled images:', err);
      setError(err.message || 'Failed to fetch labeled images');
    } finally {
      setLoading(false);
    }
  };

  // Handle image selection
  const handleImageSelect = (filename) => {
    if (selectedImages.includes(filename)) {
      setSelectedImages(selectedImages.filter(img => img !== filename));
    } else {
      setSelectedImages([...selectedImages, filename]);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedImages.length === images.length) {
      // If all are selected, deselect all
      setSelectedImages([]);
    } else {
      // Otherwise, select all
      setSelectedImages(images.map(img => img.filename));
    }
  };

  // Handle clear selected
  const handleClearSelected = () => {
    if (selectedImages.length === 0) return;

    onClearSelected(selectedImages);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: '#1e2a3a',
          color: '#fff',
          border: '1px solid #2d4055',
          borderRadius: '8px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid #2d4055'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <LabelIcon sx={{ mr: 1, color: '#f44336' }} />
          <Typography variant="h6">Select Images to Clear</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: '#90caf9' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <DialogContentText sx={{ color: '#fff', mb: 2 }}>
          Select the labeled images you want to clear. This will remove the annotations for these images.
        </DialogContentText>

        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleSelectAll}
            startIcon={selectedImages.length === images.length ? <CheckBoxIcon /> : <CheckBoxOutlineBlankIcon />}
            disabled={loading || images.length === 0}
            sx={{
              borderColor: 'rgba(33, 150, 243, 0.5)',
              color: '#2196f3',
              '&:hover': {
                borderColor: '#2196f3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
              },
            }}
          >
            {selectedImages.length === images.length ? 'Deselect All' : 'Select All'}
          </Button>

          <Button
            variant="outlined"
            color="primary"
            onClick={fetchImages}
            startIcon={loading ? <CircularProgress size={16} sx={{ color: '#2196f3' }} /> : <RefreshIcon />}
            disabled={loading}
            sx={{
              borderColor: 'rgba(33, 150, 243, 0.5)',
              color: '#2196f3',
              '&:hover': {
                borderColor: '#2196f3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
              },
            }}
          >
            Refresh
          </Button>
        </Box>

        <Divider sx={{ mb: 2, borderColor: '#2d4055' }} />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress sx={{ color: '#2196f3' }} />
          </Box>
        ) : images.length === 0 ? (
          <Box sx={{ textAlign: 'center', p: 4 }}>
            <Typography variant="body1" sx={{ color: '#90caf9' }}>
              No labeled images found.
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {images.map((image) => (
              <Grid item xs={12} sm={6} md={4} key={image.filename}>
                <Paper
                  sx={{
                    p: 1,
                    backgroundColor: selectedImages.includes(image.filename) ? 'rgba(33, 150, 243, 0.1)' : '#263445',
                    border: selectedImages.includes(image.filename) ? '1px solid #2196f3' : '1px solid #2d4055',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: selectedImages.includes(image.filename) ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)',
                    },
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                  onClick={() => handleImageSelect(image.filename)}
                >
                  <Box sx={{ position: 'relative', mb: 1 }}>
                    <img
                      src={`${process.env.REACT_APP_API_URL || ''}/training/image/${image.filename}`}
                      alt={image.filename}
                      style={{
                        width: '100%',
                        height: '120px',
                        objectFit: 'contain',
                        borderRadius: '4px',
                        backgroundColor: '#1a2635'
                      }}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = 'https://via.placeholder.com/150?text=Image+Not+Found';
                      }}
                    />
                    <Checkbox
                      checked={selectedImages.includes(image.filename)}
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        color: '#90caf9',
                        '&.Mui-checked': {
                          color: '#2196f3',
                        },
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleImageSelect(image.filename);
                      }}
                    />
                  </Box>
                  <Typography variant="body2" sx={{
                    color: '#fff',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    fontSize: '0.75rem'
                  }}>
                    {image.filename}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid #2d4055' }}>
        <Button
          onClick={onClose}
          sx={{ color: '#90caf9' }}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleClearSelected}
          color="error"
          variant="contained"
          disabled={loading || selectedImages.length === 0}
          startIcon={<DeleteIcon />}
          sx={{
            backgroundColor: '#f44336',
            '&:hover': {
              backgroundColor: '#d32f2f'
            }
          }}
        >
          Clear Selected ({selectedImages.length})
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ImageSelectionModal;
