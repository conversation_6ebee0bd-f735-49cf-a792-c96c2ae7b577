<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 1024 1024" width="1024" height="1024" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(619,188)" d="m0 0 16 1 13 3 11 7 10 8 3 6 1 9-3 12-12 25-2 7v12l4 5 11 8 10 8 12 11 7 8 14 15 15 15 11 16 11 20 9 23 4 8 16 7h15l35-6 8-1h18l5 2 1 2v9l-8 27-2 18-1 16-3 9-8 12-4 5-2 5v14l2 6v14l-3 8-9 12-6 9-2 22-3 7-12 14-6 9-5 20-9 11-18 14-9 10-5 8-8 8-7 5-13 4-26 4-6 4-11 16-6 13-5 10-9 10-5 5-8 7-15 10-14 10-21 14-16 13-8 7-17 12-12 10-12 8-12 9-7 3h-9l-6-3-11-11-10-7-16-12-26-22-15-10-19-12-19-13-14-9-9-8-5-7-15-10-9-7-6-8-8-19-3-3-18-4-10-3-8-5-8-8-7-13-9-7-11-7-10-9-5-8-4-15-6-9-8-7-6-9-3-9-1-22-4-10-11-21-2-7v-24l-5-10-4-7-2-12 1-13 1-7v-12l-6-23v-14l2-5 5-3h17l9 1h21l15-1 5-2 7-10 10-17 12-20 9-12 8-11 5-9 8-13 7-12 18-22 12-13 12-12 10-6 25-10 10-5 25-7 22-3 30-1 29 2 18 3 13 3 33 4h16l7-6 9-15 8-16 7-11 5-5z" fill="#181A1E"/>
<path transform="translate(437,366)" d="m0 0 7 6 9 6 20 8 4 6v3h-11l16 4h28l20-4 5-3 4-8-4 1-2 3-4-1-3-3-1-7 3-6 4-4h8l26 14 17 10 3 6-1 8-4 6-5 2-1 5-5 5h-10l-6-4-1-2 9-5h2v-6l4-4v-4l-7-4 1 7-4 6-14 7-41 14-18 4-13 1 14 4 10 6 7 7 4 9 1 10-2 11-4 10-8 10-10 7-12 5-19 3 13 18 14 18 13 18 1 3h-59l-7-8-14-19-13-18-8-11-17-1-2 11-10 42-2 4h-50l4-18 9-37 12-50h51l-2 12-3 13 59-1 8-4 4-6 1-3v-12l-4-5-4-2-110-1-3-9-4-15v-3h99l-8-24v-17l4-8 9-10z" fill="#FCFCFC"/>
<path transform="translate(669,694)" d="m0 0v3zm-2 3 1 3h2v4h-4v-2l-3 3 1 5-2 4h2v-2h2v-2h4v4h-2v2h-2l-1 6-4 8-2 7h-2l-7 14-7 6h-3v2l-4 3h-2v2l-3 2h-3v2h-4v2h-2v2h-2v2l-4 2h-6v2h-2v2h-4v3l-4 1v2h-4v2h-2v2h-2v2l-5 4h-3v2l-3 2h-3v2l-3 2h-3v2h-2v2h-4v2h-2v2l-6 4h-2v2h-4v2h-2v2l-3 2h-3v2l-6 4h-2v2h-4v2h-2v2l-3 2h-3v2h-2v2l-6 2v2h-4v2h-5l-3-2v-2h-2v-2h-4v-2l-5-2-9-6v-2h-4v-2h-2v-2h-2v-2h-4v-2l-5-2-3-2v-2l-4-1-2-1v-2h-2v-2h-4v-2l-5-2-3-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-4v-2h-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-2v-2h-6l-4-2v-2h-4v-2h-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-2v-2h-2v-2h-2v-2h-2l-4-12h-2l1-6 4-5 6 2 16 8 15 6 25 8 23 5 22 3 14 1h23l25-2 22-4 24-6 24-9 21-11 14-10 7-6h2v-2h2v-2h2v-2h2l2-4z" fill="#231820"/>
<path transform="translate(523,430)" d="m0 0 126 1 16 3 14 7 8 7 5 10 1 4v13l-3 12-5 10-9 10-9 6-13 5-12 2-70 1-10 42-3 9h-50l2-11 13-54 9-39 1-1h51l-3 15-3 11 50-1 7-4 5-9 1-11-2-5-3-3-3-1-104-1-7-26z" fill="#FCFCFC"/>
<path transform="translate(697,634)" d="m0 0 1 4-7 20-8 16-7 12-10 13-7 7-1 2h-2v2h-2v2h-2v2l-17 13-13 8-21 10-24 8-26 6-22 3-16 1h-23l-23-2-24-4-25-7-20-7-22-10-12-7 5-6 11-8 10 2 23 5 23 3 12 1h51l34-4 28-6 21-6 21-8 18-8 22-13 15-11 12-9 10-9 8-7h2v-2z" fill="#FBD8DB"/>
<path transform="translate(439,578)" d="m0 0h62l-1 5-7 17-8 14-8 10-6 7-20 14-34 25-18 13-19 14-14 10-7 6-4 11-2 24-2 3h-6l-6-5-7-16-3-11-1-6v-18l3-13 7-8 8-7 29-29h2l1-3 8-7 19-19 22-9 4 2 4 3 3-26z" fill="#FCFCFC"/>
<path transform="translate(713,430)" d="m0 0 57 1-2 12-18 74-6 26h85l-3 9-7 12-7 8h-127l1-8 25-103 3-13v-11l-2-6z" fill="#FCFBFB"/>
<path transform="translate(618,193)" d="m0 0 9 2 17 7 14 9 8 7-2 10-7 14-12 22-13 24-12 22-28 52-10 18-6-2-12-7-1-3 11-24 21-49 28-66 8-19-1-6-3-2-2 9-12 28-13 31-11 26-26 60-5 11-10-5 1-5 11-25 34-82 15-37 8-19z" fill="#FCFBFB"/>
<path transform="translate(588,526)" d="m0 0h12l11 6 8 7 2 4 1 113 5 14v5l-13 8-7 3h-7l-3-3-9-25-26-70-1-3 7-8 5-7 3-14 5-28z" fill="#FCFCFC"/>
<path transform="translate(326,512)" d="m0 0 66 1h58l5 5 14 19 14 18 11 15v2h-59l-7-8-14-19-13-18-8-11-17-1-2 11-10 42-2 4h-50l4-18 9-37z" fill="#FBD8DB"/>
<path transform="translate(391,756)" d="m0 0 11 1 2 1v2l5 1 3 1v2h4v2l8 2v2l4 1 18 9v2h2v2h5l15 8v2h4v2l4 1 18 9v2h4v2h4v2l7 1 4-3h3v-2h4v-2l6-2v-2l13-7 5-1v-2l16-9 4-1v-2l12-7 5-1v-2h3v-2h4v-2l4-2h4v-2h2v-2h4v-2h4v-2h9l2 3-1 9h-2v2l4 1-2 1v2h-2v2h-4v2h-2v2h-4v3l-4 1v2h-4v2h-2v2h-2v2l-5 4h-3v2l-3 2h-3v2l-3 2h-3v2h-2v2h-4v2h-2v2l-6 4h-2v2h-4v2h-2v2l-3 2h-3v2l-6 4h-2v2h-4v2h-2v2l-3 2h-3v2h-2v2l-6 2v2h-4v2h-5l-3-2v-2h-2v-2h-4v-2l-5-2-9-6v-2h-4v-2h-2v-2h-2v-2h-4v-2l-5-2-3-2v-2l-4-1-2-1v-2h-2v-2h-4v-2l-5-2-3-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-4v-2h-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-2h-2v-2h-4v-2h-2v-6l-4-2z" fill="#8A1F28"/>
<path transform="translate(697,634)" d="m0 0 1 4-7 20-8 16-7 12-10 13-7 7-1 2h-2v2h-2v2h-2v2l-17 13-13 8-21 10-24 8-5-1 1-5 1-2-1-21 1-4 8-4 16-6 7-4h3l-1-5 3-1v-2l8-4v-2l12-6 17-11 11-8 12-9 10-9 8-7h2v-2z" fill="#F97179"/>
<path transform="translate(713,430)" d="m0 0 57 1-2 12-17 70-12 1h-39v2h-2l4-18 12-50v-11l-2-6z" fill="#FCFCFC"/>
<path transform="translate(170,421)" d="m0 0 10 3 23 6 19 3 10 1h40l31-3h6l-3 9-7 12-10 10-9 3-21 3-14 1h-38l-8-2-8-5-5-5-6-7-7-14-3-11z" fill="#FCFCFC"/>
<path transform="translate(461,239)" d="m0 0h34l10 1v1l-25 3-29 6-27 8-24 10-21 11-19 13-7 6 14-8 3-2 3 1-13 11-12 11-13 13-9 11-11 16-8 13-6 12h-1v-15l4-20 6-15 8-15 9-12 9-10 8-8 12-9 15-9 16-8 24-8 24-5z" fill="#FCFAFB"/>
<path transform="translate(642,310)" d="m0 0h4v2l5 2 3 2v2h2v2h2v2l5 2 7 6v2h2l4 4v2h2v2h2v2h2v2h2v2h2l2 3v3l4 1v3h2v2h2v2h2l4 6v2h2l10 15v3h2l6 11v3h2l3 8 1 1v6l-28-1-2-3v-3h-2v-4h-2l-4-7v-3h-2l-2-3v-3h-2l-2-6h-2l-4-5v-3h-2v-2h-2v-2h-2l-8-10-6-5v-3l-4-1-6-5v-2h-2v-2h-2v-2h-2v-2l-4-1-2-1v-2l-4-1 1-5 1-2h2l1-4 1-2h2v-4h2l1-4z" fill="#8A1F28"/>
<path transform="translate(523,511)" d="m0 0 1 2h57l19 1 2 5-30 2-10 42-3 9h-50l2-11z" fill="#FCFBFB"/>
<path transform="translate(291,483)" d="m0 0 4 1-5 13-6 9-5 6-10 6-20 4-11 1h-27l-12-6-10-10-8-14-2-9 8 1 24 4 9 1h28l29-4z" fill="#FCFBFC"/>
<path transform="translate(445,289)" d="m0 0 12 1 9 4 9 7 5 7 21 4-1 4-11 7-21 9-17 5-11 2-18 1-2 6-3-1-5-11-1-4v-14l4-9 7-8 5-4 8-4z" fill="#FCFBFB"/>
<path transform="translate(763,592)" d="m0 0 4 2-8 20 8-6 11-4 21-4 12-4-1 5-5 9-10 11-12 7-6 2h-17l-9-2-10 16-9 12 3-2 9-5 4-1 27-2 11-2-4 7-11 11-14 7-3 1h-18l-12-3-15 16-11 11-9 6-2-2 5-6 8-7 14-14 8-10 10-13 13-20 8-14 9-21z" fill="#FCFAFA"/>
<path transform="translate(247,592)" d="m0 0 5 1 8 18 11 20 8 12 11 15 13 15 9 10 8 7 1 5h-3l-10-9-11-11-7-8-11 3-13 1-12-3-12-7-9-9-4-6v-2l11 2 8 1 19 1 10 4 4 3-9-13-8-13-9 1h-17l-11-4-11-8-8-10-5-10v-2l9 3 17 4 14 3 8 4 5 4-7-15z" fill="#FCFAFA"/>
<path transform="translate(457,530)" d="m0 0 7 1 10 12 14 19 6 8v2h-59l-7-8-14-19-6-8 1-3 18-2 27-1z" fill="#FCFBFB"/>
<path transform="translate(380,712)" d="m0 0 10 2 23 5 23 3 21 2 31 1v1l-16 1v1l-16 1-8 3v17l-5 3-7 2-8-1-30-10-22-10-12-7 5-6z" fill="#FCFCFC"/>
<path transform="translate(851,421)" d="m0 0h2l-3 13-7 14-8 10-8 6-8 4-5 1h-19l-14-1 1-9 5-22 2-2 27-4 24-6z" fill="#FCFBFB"/>
<path transform="translate(836,484)" d="m0 0h5l-5 13-7 11-7 7-10 6-5 2h-18l-18-2 1-8 5-20 1-2 30-2 24-4z" fill="#FCFBFB"/>
<path transform="translate(321,537)" d="m0 0 4 2h29l4-1h11l1 5-7 28-1 1h-50l4-18 4-16z" fill="#FCFBFB"/>
<path transform="translate(505,266)" d="m0 0h23l2 1 21 3 1 1 10 2 10 3-2 8h-2v4h-2l-1 6-3 6-6-1-7-1-1-1-20-3v-2h-22l-1-2z" fill="#8A1F28"/>
<path transform="translate(739,513)" d="m0 0h11l-1 9-5 18h-28l-23-2 2-11 3-11h2v-2z" fill="#FBD8DB"/>
<path transform="translate(187,538)" d="m0 0 5 1 33 11 8 5 7 8 5 11v4l-7 1h-11l-9-2-8-4-10-8-9-14z" fill="#FCFBFB"/>
<path transform="translate(479,336)" d="m0 0 3 1 3 16v12l-5 5-6 2h-13l-9-3-6-5-12-17v-2h23l13-4z" fill="#FCFBFB"/>
<path transform="translate(295,585)" d="m0 0 3 1 5 10 4 16v14l-3 12-8 14-2 3h-2l-5-8-4-11-1-12 4-16z" fill="#FCFBFB"/>
<path transform="translate(326,515)" d="m0 0 2 1v2l21-1h7l2-1h10l6 1 2-1-4 18-1 5h-13l-4 1h-32l-1-6 4-17z" fill="#FBD8DB"/>
<path transform="translate(720,584)" d="m0 0 3 4 8 21 2 7v19l-5 12-5 8-5-5-6-12-2-7-1-13 2-14 5-13z" fill="#FCFBFB"/>
<path transform="translate(287,532)" d="m0 0h2v8l-3 12-6 11-10 10-8 3-11 2h-4l-1-9 3-11 9-10 26-14z" fill="#FCFBFB"/>
<path transform="translate(653,622)" d="m0 0h5l5 5v7l-10 15-9 10-5 6-6 5h-2l-3-9-2-12v-11l4-3 10-4z" fill="#FCFBFB"/>
<path transform="translate(523,511)" d="m0 0 1 2h57l19 1 2 5-30 2-2 9-1-2h-47l-2-4z" fill="#FCD9DC"/>
<path transform="translate(656,760)" d="m0 0h2l1 5-5 5-15 11-5 4h-2v2l-9 6-21 16-13 9 3 2-6 5-1-3-3 2-14 10-13 10-10 7-4 3h-2v2l-17 12-10 7-5-1-1-4h-3l-5-4v-4l4 2 7 5 10-4 5-5 24-18 3-1 5-5 17-12 11-9 10-7 9-7 5-2v-2l6-4 19-14 16-12 5-3z" fill="#30272E"/>
<path transform="translate(360,752)" d="m0 0 5 5 7 11 9 7 9 6v2l5 2 13 10 12 8v2l5 2 16 12 18 13 19 14 9 7 11 8-4 2-5-2-5-4-11-8 3-1v-2l-5 2-10-8-15-11-11-8v-5h-3l-1 2-16-12-14-10-15-11-6-4v-2l-4-2-9-6-7-13-1-5z" fill="#30272E"/>
<path transform="translate(649,288)" d="m0 0h3v2l3-1 11 8 5 5v3l4-1 13 12 2 1v2l4 2 10 10 9 11 7 8v2l5 5 11 15 9 15 11 23 1 6h-4l-12-25-12-20-7-10v-2l-2-1 1-2h-3l-8-10v-2l-3-1-7-8-1-3h-2l-4-4v-2l-3-1v-2h-2l-5-5-11-10-18-13-5-5z" fill="#30272E"/>
<path transform="translate(712,688)" d="m0 0h34l-2 4-10 8-11 5-10 2-11-1-11-4 4-5 10-7z" fill="#FCFAFA"/>
<path transform="translate(268,688)" d="m0 0 39 1 10 6 3 3-1 5-7 3-12 1-12-3-12-7-8-7z" fill="#FCFAFA"/>
<path transform="translate(362,325)" d="m0 0v3l-8 16-6 14-4 15-2 10-5 6-2 6-2 9h-1l-1-6v-14l4-17 7-16 7-11 9-11z" fill="#FCF7F8"/>
<path transform="translate(280,363)" d="m0 0h2l2 4-4 5-6 10-10 18-7 14h-2l1 4h-5l3-10 9-17 6-11 8-13z" fill="#30272E"/>
<path transform="translate(485,372)" d="m0 0 4 2 16 16 2 4-3 1h-16l-3-15z" fill="#FCFAFA"/>
<path transform="translate(528,244)" d="m0 0h8l32 6 10 3 2 1-1 4-19-4-16-3 3-2-4-1v2l-15-1z" fill="#31282F"/>
<path transform="translate(675,718)" d="m0 0 4 2-3 9-11 25-4 5-3-2 3-9 10-21z" fill="#30272E"/>
<path transform="translate(700,514)" d="m0 0h48l-2 4-10 1-7-1h-29l-2-2h2z" fill="#F97179"/>
<path transform="translate(326,512)" d="m0 0 1 2h48l-2 4-5-1h-10l-2 1h-28l-2-3z" fill="#F9737A"/>
<path transform="translate(450,297)" d="m0 0 6 2 2 3-1 6-5 3-6-2-1-1v-7l2-3z" fill="#1A1B20"/>
<path transform="translate(649,288)" d="m0 0h3v2l3-1 11 8 5 5 1 5-4-2-14-10-5-5z" fill="#30272E"/>
<path transform="translate(476,845)" d="m0 0 9 6 9 7 4 4-6 1-8-6-11-8 3-1z" fill="#31282F"/>
<path transform="translate(667,739)" d="m0 0h3l-1 6-7 14-4-2 3-9 4-8z" fill="#291F26"/>
<path transform="translate(560,249)" d="m0 0 8 1 10 3 2 1-1 4-19-4z" fill="#31282F"/>
<path transform="translate(506,260)" d="m0 0h2v3l12 1 2 2h-17l1 26h-2l-1-24-1-2h2v-2h2z" fill="#231820"/>
<path transform="translate(291,349)" d="m0 0h3l-2 6-7 10-2-1v-2l-2-1 9-11z" fill="#31282F"/>
<path transform="translate(398,328)" d="m0 0 3 1-16 8-14 8h-3l10-8 14-7z" fill="#F9EEEF"/>
<path transform="translate(548,247)" d="m0 0 10 1 2 1v2l2 1-7 1-11-2z" fill="#2F252D"/>
<path transform="translate(603,807)" d="m0 0h1v5l-10 7h-6l5-5z" fill="#262228"/>
<path transform="translate(604,780)" d="m0 0h4l-1 4h-3v2h-2v2h-4v2h-2v2h-4v-2h2v-2h2v-2h4v-2l4-1z" fill="#231820"/>
<path transform="translate(669,694)" d="m0 0v3zm-2 3 1 3h2v4h-4v-2l-8 6 2-4zm-11 11 2 1-2 1z" fill="#311A22"/>
<path transform="translate(654,710)" d="m0 0 5 1-1 3-2 2h-4v2h-2v2h-2l4-8h2z" fill="#1A1A1F"/>
<path transform="translate(515,243)" d="m0 0h10l-3 2v3h-7z" fill="#31282F"/>
<path transform="translate(711,344)" d="m0 0 5 1 4 6-7-1z" fill="#281E25"/>
<path transform="translate(661,705)" d="m0 0 3 1-1 5h-2l-1 2v-2l-3-1z" fill="#21181F"/>
<path transform="translate(458,814)" d="m0 0 6 2v2l5 2 1 2h-4v-2h-4v-2h-2v-2h-2z" fill="#231820"/>
<path transform="translate(598,811)" d="m0 0m-2 1 2 1-4 6h-6l5-5z" fill="#1D1B20"/>
<path transform="translate(676,715)" d="m0 0h5l-1 8-2-1 1-2-3-1z" fill="#271C24"/>
<path transform="translate(656,756)" d="m0 0 5 2v4l-2 2-2-2 1-2h-2z" fill="#241921"/>
<path transform="translate(656,666)" d="m0 0 3 1-9 7h-2v-2h2v-2h2v-2h4z" fill="#281A22"/>
</svg>
