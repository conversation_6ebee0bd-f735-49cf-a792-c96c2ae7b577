@echo off
echo Setting up HTTPS for React development server...

echo Creating .env file...
echo HTTPS=true > .env
echo SSL_CRT_FILE=cert.pem >> .env
echo SSL_KEY_FILE=key.pem >> .env

echo Generating self-signed certificates...
openssl req -x509 -newkey rsa:2048 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/CN=localhost"

echo Done! You can now start your React app with npm start
echo Your app will be available at https://localhost:3000
echo Note: You will need to accept the self-signed certificate warning in your browser
