# Dependencies
node_modules
client/node_modules
server/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
client/build
server/public

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
client/.env*
server/.env*

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Documentation
*.md
README*

# Test files
client/src/**/*.test.js
client/src/**/*.spec.js
server/test*
server/tests

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Temporary folders
tmp
temp

# Deployment files
.dokploy
dokploy*
deploylogs

# Python cache
__pycache__
*.pyc
*.pyo
*.pyd
.Python

# Misc
.pytest_cache
.qodo
.trae
*.html
*.css
*.docx
*.csv
*.xlsx
*.txt
*.json.new
output*
query
scripts
eng.traineddata
Procfile