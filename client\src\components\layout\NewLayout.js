import React, { useState } from 'react';
import {
  <PERSON>,
  App<PERSON><PERSON>,
  <PERSON><PERSON>bar,
  IconButton,
  Typo<PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Container,
  useMediaQuery,
  Tooltip,
  Avatar,
  Menu,
  MenuItem,
  Button,
  useTheme as useMuiTheme
} from '@mui/material';
import { useEnhancedTheme } from '../../theme/EnhancedThemeProvider';
import ThemeSelector from '../common/ThemeSelector';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useLayout } from '../../context/LayoutContext';

// Icons
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import SportsCricketIcon from '@mui/icons-material/SportsCricket';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import LoginIcon from '@mui/icons-material/Login';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import CloseIcon from '@mui/icons-material/Close';
import GroupsIcon from '@mui/icons-material/Groups';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import TuneIcon from '@mui/icons-material/Tune';
import GavelIcon from '@mui/icons-material/Gavel';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import SchoolIcon from '@mui/icons-material/School';
import CompareIcon from '@mui/icons-material/Compare';

// Drawer width
const drawerWidth = 240;

// Navigation items based on user role
const getNavItems = (role) => {
  const items = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard',
      roles: ['admin', 'team_owner', 'viewer', null]
    },
    // Team Management Section
    {
      text: 'Team Dashboard',
      icon: <GroupsIcon />,
      path: '/team',
      roles: ['team_owner']
    },
    {
      text: 'Team Settings',
      icon: <TuneIcon />,
      path: '/team/settings',
      roles: ['team_owner']
    },
    {
      text: 'Team Roster',
      icon: <PeopleIcon />,
      path: '/team/roster',
      roles: ['team_owner']
    },
    /* Coming soon
    {
      text: 'Team Budget',
      icon: <AccountBalanceWalletIcon />,
      path: '/team/budget',
      roles: ['team_owner']
    },
    */
    // Legacy My Team page
    {
      text: 'My Team (Legacy)',
      icon: <PeopleIcon />,
      path: '/my-team',
      roles: ['team_owner']
    },
    {
      text: 'Players',
      icon: <SportsCricketIcon />,
      path: '/players',
      roles: ['admin', 'team_owner', 'viewer']
    },
    {
      text: 'Tournaments',
      icon: <EmojiEventsIcon />,
      path: '/tournaments',
      roles: ['admin', 'team_owner', 'viewer']
    },
    {
      text: 'Transfer Market',
      icon: <AttachMoneyIcon />,
      path: '/market',
      roles: ['admin', 'team_owner']
    },
    {
      text: 'Player Auctions',
      icon: <GavelIcon />,
      path: '/auctions',
      roles: ['team_owner']
    },
    {
      text: 'Admin',
      icon: <SettingsIcon />,
      path: '/admin',
      roles: ['admin']
    },
    {
      text: 'Auction Management',
      icon: <GavelIcon />,
      path: '/admin/auctions',
      roles: ['admin']
    },
    // Scorecard Training
    {
      text: 'Scorecard Training',
      icon: <SchoolIcon />,
      path: '/admin/training/scorecard',
      roles: ['admin']
    },
    // OCR Comparison
    {
      text: 'OCR Comparison',
      icon: <CompareIcon />,
      path: '/admin/ocr-comparison',
      roles: ['admin']
    },
    // OCR Settings
    {
      text: 'OCR Settings',
      icon: <SettingsIcon />,
      path: '/admin/ocr-settings',
      roles: ['admin']
    },
    // Scorecard Capture
    {
      text: 'Scorecard Capture',
      icon: <CameraAltIcon />,
      path: '/scorecard-capture',
      roles: ['admin', 'team_owner', 'viewer']
    }
  ];

  // Filter items based on user role
  return items.filter(item => !item.roles || item.roles.includes(role));
};

// Footer component
const Footer = () => {
  const { currentTheme } = useEnhancedTheme();
  const isDark = currentTheme === 'dark';

  return (
    <Box
      component="footer"
      sx={{
        py: 3,
        px: 2,
        mt: 'auto',
        backgroundColor: (theme) => theme.palette.mode === 'light'
          ? theme.palette.grey[100]
          : theme.palette.grey[900],
        borderTop: '1px solid',
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="lg">
        <Typography variant="body2" color="text.secondary" align="center">
          © {new Date().getFullYear()} Cricket 24 Tournament Management
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block" sx={{ mt: 1 }}>
          All rights reserved
        </Typography>
      </Container>
    </Box>
  );
};

// Theme component is now imported from ThemeSelector

// Main Layout component
const NewLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const { currentTheme } = useEnhancedTheme();
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const muiTheme = useMuiTheme();
  const { headerVisible } = useLayout();

  // Use MUI's useMediaQuery hook for responsive design
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'));

  // Handle drawer toggle
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Handle user menu
  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handle logout
  const handleLogout = () => {
    handleClose();
    logout();
    navigate('/login');
  };

  // Navigation items based on user role
  const navItems = getNavItems(user?.role);

  // Drawer content
  const drawer = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Typography variant="h6" component={Link} to="/" sx={{
          textDecoration: 'none',
          color: 'primary.main',
          fontWeight: 700,
          display: 'flex',
          alignItems: 'center'
        }}>
          <SportsCricketIcon sx={{ mr: 1 }} />
          Cricket 24
        </Typography>
        {isMobile && (
          <IconButton onClick={handleDrawerToggle}>
            <CloseIcon />
          </IconButton>
        )}
      </Box>

      <Divider />

      <List sx={{ flexGrow: 1, px: 2 }}>
        {navItems.map((item) => (
          <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
            <ListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
              sx={{
                borderRadius: 1,
                py: 1,
              }}
              onClick={isMobile ? handleDrawerToggle : undefined}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider />

      <Box sx={{ p: 2 }}>
        <ThemeSelector />
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* App Bar - conditionally rendered based on headerVisible */}
      {headerVisible && (
        <AppBar
          position="fixed"
          sx={{
            zIndex: (theme) => theme.zIndex.drawer + 1,
            backgroundColor: currentTheme === 'dark' ? 'background.paper' : 'primary.main'
          }}
          elevation={1}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>

            <Typography
              variant="h6"
              component={Link}
              to="/"
              sx={{
                display: { xs: 'none', sm: 'flex' },
                alignItems: 'center',
                textDecoration: 'none',
                color: 'inherit',
                fontWeight: 700
              }}
            >
              <SportsCricketIcon sx={{ mr: 1 }} />
              Cricket 24
            </Typography>

            <Box sx={{ flexGrow: 1 }} />

            {user ? (
              <>
                <Tooltip title="Account settings">
                  <IconButton
                    onClick={handleMenu}
                    size="small"
                    sx={{ ml: 2 }}
                    aria-controls="menu-appbar"
                    aria-haspopup="true"
                  >
                    <Avatar
                      alt={user.username}
                      src={user.profileImage}
                      sx={{ width: 32, height: 32 }}
                    />
                  </IconButton>
                </Tooltip>
                <Menu
                  id="menu-appbar"
                  anchorEl={anchorEl}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  open={Boolean(anchorEl)}
                  onClose={handleClose}
                >
                  <MenuItem onClick={() => { handleClose(); navigate('/profile'); }}>
                    Profile
                  </MenuItem>
                  <MenuItem onClick={handleLogout}>
                    <LogoutIcon fontSize="small" sx={{ mr: 1 }} />
                    Logout
                  </MenuItem>
                </Menu>
              </>
            ) : (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  component={Link}
                  to="/login"
                  size={isMobile ? 'small' : 'medium'}
                  sx={{
                    color: currentTheme === 'dark' ? 'primary.main' : 'white',
                    borderColor: currentTheme === 'dark' ? 'primary.main' : 'white',
                  }}
                  startIcon={<LoginIcon />}
                >
                  Login
                </Button>
                <Button
                  variant="contained"
                  component={Link}
                  to="/register"
                  size={isMobile ? 'small' : 'medium'}
                  sx={{
                    bgcolor: currentTheme === 'dark' ? 'primary.main' : 'white',
                    color: currentTheme === 'dark' ? 'white' : 'primary.main',
                  }}
                  startIcon={<PersonAddIcon />}
                >
                  Register
                </Button>
              </Box>
            )}

            {!isMobile && <ThemeSelector />}
          </Toolbar>
        </AppBar>
      )}

      {/* Drawer - conditionally rendered based on headerVisible */}
      {headerVisible && (
        <Box
          component="nav"
          sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
        >
          {/* Mobile drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{ keepMounted: true }}
            sx={{
              display: { xs: 'block', md: 'none' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRadius: { xs: '0 16px 16px 0', md: 0 }
              },
            }}
          >
            {drawer}
          </Drawer>

          {/* Desktop drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', md: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRight: '1px solid',
                borderColor: 'divider'
              },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>
      )}

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: headerVisible ? 3 : 0,
          width: headerVisible ? { md: `calc(100% - ${drawerWidth}px)` } : '100%',
          ml: headerVisible ? { md: `${drawerWidth}px` } : 0,
          mt: headerVisible ? '64px' : 0, // AppBar height when visible
        }}
      >
        {children}
      </Box>

      {/* Footer - conditionally rendered based on headerVisible */}
      {headerVisible && <Footer />}
    </Box>
  );
};

export default NewLayout;
