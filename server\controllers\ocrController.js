/**
 * OCR Controller
 *
 * Clean, simple OCR controller for cricket scorecard processing
 */

const OCRService = require('../services/ocrService');
const { processImageFile } = require('../services/ocrService');
const MatchOutcomeService = require('../services/matchOutcomeService');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/scorecards';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 1 * 1024 * 1024 // 1MB max file size to match OCR.space limit
  }
});

// Initialize OCR service
let ocrService;
(async () => {
  try {
    ocrService = await OCRService.initialize();
    console.log('OCR service initialized successfully');
  } catch (error) {
    console.error('Failed to initialize OCR service:', error);
  }
})();

/**
 * Process an image file with OCR
 * @param {string} filePath - Path to the image file
 * @param {Object} options - Processing options
 * @returns {Promise<Object>} - OCR results
 */
exports.processImageFile = async (filePath, options = {}) => {
  // Ensure OCR service is initialized
  if (!ocrService) {
    console.log('OCR service not initialized, initializing now...');
    ocrService = await OCRService.initialize();
  }
  // Create debug file to track if this function is called
  const debugDir = require('path').join(__dirname, '..', 'ocr-output', 'extracted');
  const debugFile = require('path').join(debugDir, `debug_processImageFile_${Date.now()}.json`);

  try {
    console.log('=== OCR CONTROLLER processImageFile CALLED ===');
    console.log('Processing image file with OCR:', filePath);
    console.log('Options:', options);

    // Save debug info immediately
    require('fs').writeFileSync(debugFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      step: 'processImageFile_called',
      filePath: filePath,
      options: options,
      fileExists: filePath ? require('fs').existsSync(filePath) : false
    }, null, 2));
    console.log('Saved debug file:', debugFile);

    // Process with OCR service using configured method
    console.log('Processing image with configured OCR method...');
    let result = await ocrService.processImage(filePath, options);

    // Add engine information to the result
    if (result.success) {
      result.engine = result.ocrStatus === 'success_fallback' ? 'Google Vision' : 'Primary OCR';
    }

    return {
      success: result.success || result.ocrStatus === 'success_fallback',
      data: result,
      filePath: filePath ? `/${filePath.replace(/\\/g, '/')}` : null
    };

  } catch (error) {
    console.error('Error processing image file:', error);
    console.error('processImageFile error stack:', error.stack);

    // Return enhanced error structure
    return {
      success: false,
      error: error.message,
      errorStack: error.stack,
      errorLocation: 'ocrController.processImageFile',
      data: {
        success: false,
        error: error.message,
        errorStack: error.stack,
        errorLocation: 'ocrController.processImageFile',
        team1: 'Team 1',
        team2: 'Team 2',
        venue: 'Unknown Venue',
        team1Score: { runs: 0, wickets: 0, overs: 0 },
        team2Score: { runs: 0, wickets: 0, overs: 0 },
        playerOfMatch: '',
        resultText: '',
        team1Batsmen: [],
        team1Bowlers: [],
        team2Batsmen: [],
        team2Bowlers: [],
        rawText: '',
        ocrStatus: 'error',
        ocrMessage: `OCR error: ${error.message}. Please enter match details manually.`,
        debugInfo: {
          errorType: error.constructor.name,
          errorMessage: error.message,
          timestamp: new Date().toISOString(),
          inputFilePath: filePath
        }
      },
      filePath: filePath ? `/${filePath.replace(/\\/g, '/')}` : null
    };
  }
};

/**
 * Process uploaded image with OCR
 * @route POST /api/ocr/process
 * @access Private
 */
exports.processImage = async (req, res) => {
  const uploadMiddleware = upload.single('image');

  uploadMiddleware(req, res, async function (err) {
    if (err) {
      console.error('Upload error:', err);
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image file uploaded'
      });
    }

    try {
      // Get absolute file path
      const filePath = path.resolve(req.file.path);

      console.log('Processing uploaded image:', filePath);
      console.log('File exists:', require('fs').existsSync(filePath));

      // Process the image with OCR
      const result = await exports.processImageFile(filePath, {
        language: req.body.language || 'en',
        source: 'upload'
      });

      console.log('OCR processing result:', {
        success: result.success,
        hasData: !!result.data,
        ocrStatus: result.data?.ocrStatus,
        textCount: result.data?.rawText?.length || 0
      });

      // If OCR was successful and we have match data, calculate match outcome
      if (result.success && result.data && result.data.ocrStatus === 'success') {
        try {
          const matchOutcomeService = new MatchOutcomeService();
          await matchOutcomeService.initialize();
          
          const matchOutcome = await matchOutcomeService.calculateMatchOutcome(
            result.data,
            req.body.tournamentId,
            req.body.matchId
          );
          
          // Add match outcome to the result
          result.data.matchOutcome = matchOutcome;
          
          console.log('Match outcome calculated:', {
            winner: matchOutcome.winner,
            margin: matchOutcome.margin,
            confidence: matchOutcome.confidence
          });
          
        } catch (outcomeError) {
          console.error('Error calculating match outcome:', outcomeError);
          // Don't fail the entire request if match outcome calculation fails
          result.data.matchOutcomeError = {
            message: 'Failed to calculate match outcome',
            error: outcomeError.message
          };
        }
      }

      return res.status(200).json(result);

    } catch (error) {
      console.error('Error in OCR processing:', error);
      console.error('OCR Controller error stack:', error.stack);

      return res.status(500).json({
        success: false,
        error: error.message || 'Error processing image with OCR',
        errorStack: error.stack,
        errorLocation: 'ocrController.processImage',
        debugInfo: {
          errorType: error.constructor.name,
          errorMessage: error.message,
          timestamp: new Date().toISOString(),
          filePath: req.file?.path || 'unknown'
        }
      });
    }
  });
};

/**
 * Test OCR endpoint
 * @route GET /api/ocr/test
 * @access Public
 */
exports.testOCR = async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'OCR service is running',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

module.exports = exports;
