import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  alpha,
  Button,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import {
  SportsEsports as GameIcon,
  EmojiEvents as TrophyIcon,
  Person as PlayerIcon,
  AttachMoney as MoneyIcon,
  InsertChart as StatsIcon,
  Visibility as ViewIcon,
  ArrowForward as ArrowIcon,
  SportsCricket as CricketIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import ResponsiveCard from '../../components/ui/ResponsiveCard';

const Dashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // const isTablet = useMediaQuery(theme.breakpoints.down('md')); // Uncomment if needed

  // Placeholder data - would come from API in real implementation
  const upcomingMatches = [
    { id: 1, opponent: 'Chennai Super Kings', date: '2025-05-10', format: 'T20' },
    { id: 2, opponent: 'Mumbai Indians', date: '2025-05-15', format: 'T20' },
    { id: 3, opponent: 'Royal Challengers', date: '2025-05-20', format: 'ODI' }
  ];

  const recentActivity = [
    { id: 1, type: 'match', description: 'Won against Royal Challengers', date: '2025-05-01' },
    { id: 2, type: 'transfer', description: 'Bought player Virat Kohli', date: '2025-04-29' },
    { id: 3, type: 'tournament', description: 'Joined IPL T20 Tournament', date: '2025-04-25' },
    { id: 4, type: 'match', description: 'Lost against Mumbai Indians', date: '2025-04-22' }
  ];

  // Format functions
  const formatMatchFormat = (format) => {
    switch(format) {
      case 'T20': return 'Twenty20';
      case 'ODI': return 'One Day International';
      case 'Test': return 'Test Match';
      default: return format;
    }
  };

  // Get appropriate chip color for match format
  const getFormatColor = (format) => {
    switch(format) {
      case 'T20': return 'success';
      case 'ODI': return 'primary';
      case 'Test': return 'secondary';
      default: return 'default';
    }
  };

  // Get icon for activity type
  const getActivityIcon = (type) => {
    switch(type) {
      case 'match': return <GameIcon />;
      case 'transfer': return <MoneyIcon />;
      case 'tournament': return <TrophyIcon />;
      default: return <CricketIcon />;
    }
  };

  return (
    <Box sx={{ pb: { xs: 4, sm: 6 } }}>
      <Container maxWidth="xl">
        {/* Page Title */}
        <Typography
          variant="h4"
          sx={{
            mb: 2,
            fontWeight: 'bold',
            fontSize: { xs: '1.75rem', sm: '2.125rem' }
          }}
        >
          Dashboard
        </Typography>

        {/* Welcome Banner */}
        <Paper
          elevation={0}
          sx={{
            p: { xs: 3, md: 4 },
            mb: 4,
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
            color: 'white',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{
            position: 'absolute',
            right: -30,
            top: -30,
            opacity: 0.15,
            transform: 'rotate(-15deg)'
          }}>
            <CricketIcon sx={{ fontSize: '12rem' }} />
          </Box>

          <Typography
            variant="h4"
            gutterBottom
            sx={{
              fontSize: { xs: '1.5rem', sm: '2rem', md: '2.25rem' },
              fontWeight: 'bold',
              mb: 1
            }}
          >
            Welcome back, {user?.username}!
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              opacity: 0.9,
              fontSize: { xs: '0.9rem', sm: '1rem' },
              mb: 3
            }}
          >
            {user?.role === 'team_owner' && `Team: ${user.teamName}`}
            {user?.role === 'admin' && 'Admin Dashboard'}
            {(!user?.role || user?.role === 'viewer') && 'Cricket Tournament Management System'}
          </Typography>

          {user?.role === 'team_owner' && (
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'flex-start', sm: 'center' },
              gap: 2
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                py: 1,
                px: 2,
                bgcolor: alpha('#fff', 0.2),
                borderRadius: 3
              }}>
                <MoneyIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>
                  {user.virtualCurrency?.toLocaleString() || '0'} Credits
                </Typography>
              </Box>

              <Button
                variant="contained"
                startIcon={<MoneyIcon />}
                onClick={() => navigate('/market')}
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: alpha('#fff', 0.9)
                  },
                  fontWeight: 'medium',
                  py: 1,
                  borderRadius: 2
                }}
              >
                Go to Market
              </Button>
            </Box>
          )}
        </Paper>

        <Grid container spacing={3}>
          {/* Quick Actions Section */}
          <Grid item xs={12} md={8}>
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h5"
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                Quick Actions
              </Typography>

              <Grid container spacing={2}>
                {user?.role === 'team_owner' && (
                  <>
                    <Grid item xs={6} sm={4}>
                      <Card
                        elevation={0}
                        sx={{
                          borderRadius: 2,
                          transition: 'all 0.3s ease',
                          border: '1px solid',
                          borderColor: 'divider',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            boxShadow: 3
                          }
                        }}
                      >
                        <CardContent sx={{
                          p: 3,
                          pb: 2,
                          textAlign: 'center'
                        }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: 'primary.main',
                              width: 60,
                              height: 60,
                              margin: '0 auto',
                              mb: 1.5
                            }}
                          >
                            <PlayerIcon fontSize="large" />
                          </Avatar>
                          <Typography
                            variant="h6"
                            gutterBottom
                            sx={{ fontWeight: 'medium', fontSize: '1rem' }}
                          >
                            My Team
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 1.5 }}
                          >
                            Manage players and team
                          </Typography>
                        </CardContent>
                        <CardActions
                          sx={{
                            px: 2,
                            pb: 2,
                            display: 'flex',
                            justifyContent: 'center'
                          }}
                        >
                          <Button
                            size="medium"
                            color="primary"
                            fullWidth
                            variant="outlined"
                            onClick={() => navigate('/my-team')}
                            endIcon={<ArrowIcon />}
                            sx={{ borderRadius: 2 }}
                          >
                            View Team
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>

                    <Grid item xs={6} sm={4}>
                      <Card
                        elevation={0}
                        sx={{
                          borderRadius: 2,
                          transition: 'all 0.3s ease',
                          border: '1px solid',
                          borderColor: 'divider',
                          '&:hover': {
                            transform: 'translateY(-5px)',
                            boxShadow: 3
                          }
                        }}
                      >
                        <CardContent sx={{
                          p: 3,
                          pb: 2,
                          textAlign: 'center'
                        }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.success.main, 0.1),
                              color: 'success.main',
                              width: 60,
                              height: 60,
                              margin: '0 auto',
                              mb: 1.5
                            }}
                          >
                            <TrophyIcon fontSize="large" />
                          </Avatar>
                          <Typography
                            variant="h6"
                            gutterBottom
                            sx={{ fontWeight: 'medium', fontSize: '1rem' }}
                          >
                            Tournaments
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 1.5 }}
                          >
                            Join and view tournaments
                          </Typography>
                        </CardContent>
                        <CardActions
                          sx={{
                            px: 2,
                            pb: 2,
                            display: 'flex',
                            justifyContent: 'center'
                          }}
                        >
                          <Button
                            size="medium"
                            color="success"
                            fullWidth
                            variant="outlined"
                            onClick={() => navigate('/tournaments')}
                            endIcon={<ArrowIcon />}
                            sx={{ borderRadius: 2 }}
                          >
                            View Tournaments
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  </>
                )}

                <Grid item xs={6} sm={4}>
                  <Card
                    elevation={0}
                    sx={{
                      borderRadius: 2,
                      transition: 'all 0.3s ease',
                      border: '1px solid',
                      borderColor: 'divider',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 3
                      }
                    }}
                  >
                    <CardContent sx={{
                      p: 3,
                      pb: 2,
                      textAlign: 'center'
                    }}>
                      <Avatar
                        sx={{
                          bgcolor: alpha(theme.palette.secondary.main, 0.1),
                          color: 'secondary.main',
                          width: 60,
                          height: 60,
                          margin: '0 auto',
                          mb: 1.5
                        }}
                      >
                        <MoneyIcon fontSize="large" />
                      </Avatar>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ fontWeight: 'medium', fontSize: '1rem' }}
                      >
                        Transfer Market
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 1.5 }}
                      >
                        Buy and sell players
                      </Typography>
                    </CardContent>
                    <CardActions
                      sx={{
                        px: 2,
                        pb: 2,
                        display: 'flex',
                        justifyContent: 'center'
                      }}
                    >
                      <Button
                        size="medium"
                        color="secondary"
                        fullWidth
                        variant="outlined"
                        onClick={() => navigate('/market')}
                        endIcon={<ArrowIcon />}
                        sx={{ borderRadius: 2 }}
                      >
                        Go to Market
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>

                <Grid item xs={6} sm={4}>
                  <ResponsiveCard
                    title="Leaderboards"
                    subtitle="Stats and rankings"
                    icon={<StatsIcon fontSize="large" />}
                    iconColor="info"
                    actionText="View Stats"
                    onClick={() => navigate('/leaderboards')}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={6} sm={4}>
                  <ResponsiveCard
                    title="Play Game"
                    subtitle="Launch Big Ant Cricket 24"
                    icon={<GameIcon fontSize="large" />}
                    iconColor="warning"
                    actionText="Launch Game"
                    onClick={() => {}}
                    variant="outlined"
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Upcoming Matches Section */}
            {user?.role === 'team_owner' && (
              <Paper
                elevation={0}
                sx={{
                  p: { xs: 2, sm: 3 },
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    mb: 2,
                    fontWeight: 'medium',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <GameIcon color="primary" /> Upcoming Matches
                </Typography>

                <List sx={{ pt: 0 }}>
                  {upcomingMatches.map((match, index) => (
                    <React.Fragment key={match.id}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          py: 2,
                          px: { xs: 0, sm: 1 },
                          flexDirection: { xs: 'column', sm: 'row' }
                        }}
                      >
                        <ListItemAvatar sx={{
                          minWidth: { xs: '100%', sm: 56 },
                          mb: { xs: 1, sm: 0 },
                          display: 'flex',
                          justifyContent: { xs: 'center', sm: 'flex-start' }
                        }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: 'primary.main'
                            }}
                          >
                            <GameIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              flexDirection: { xs: 'column', sm: 'row' },
                              gap: { xs: 1, sm: 0 }
                            }}>
                              <Typography
                                component="span"
                                variant="body1"
                                fontWeight="medium"
                                sx={{ textAlign: { xs: 'center', sm: 'left' } }}
                              >
                                vs {match.opponent}
                              </Typography>
                              <Chip
                                label={formatMatchFormat(match.format)}
                                color={getFormatColor(match.format)}
                                size="small"
                                sx={{
                                  ml: { sm: 1 },
                                  fontWeight: 'medium'
                                }}
                              />
                            </Box>
                          }
                          secondary={
                            <Typography
                              component="span"
                              variant="body2"
                              color="text.secondary"
                              sx={{ textAlign: { xs: 'center', sm: 'left' } }}
                            >
                              {new Date(match.date).toLocaleDateString(undefined, {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </Typography>
                          }
                          sx={{ textAlign: { xs: 'center', sm: 'left' } }}
                        />
                        <Box sx={{
                          position: { xs: 'relative', sm: 'absolute' },
                          transform: { xs: 'none', sm: 'translateY(-50%)' },
                          top: { xs: 'auto', sm: '50%' },
                          right: { xs: 'auto', sm: 6 },
                          width: { xs: '100%', sm: 'auto' },
                          mt: { xs: 1, sm: 0 },
                          display: 'flex',
                          justifyContent: { xs: 'center', sm: 'flex-end' }
                        }}>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<ViewIcon />}
                            sx={{ borderRadius: 2 }}
                          >
                            Details
                          </Button>
                        </Box>
                      </ListItem>
                      {index < upcomingMatches.length - 1 && (
                        <Divider component="li" />
                      )}
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            )}
          </Grid>

          {/* Activity Feed */}
          <Grid item xs={12} md={4}>
            {isMobile ? (
              <Box className="mobile-card mobile-activity-card">
                <Typography className="mobile-activity-title">
                  Recent Activity
                </Typography>

                <Box className="mobile-activity-list">
                  {recentActivity.map((activity) => (
                    <Box key={activity.id} className="mobile-activity-item">
                      <Box className="mobile-activity-avatar" sx={{
                        bgcolor: alpha(
                          activity.type === 'match' ? theme.palette.primary.main :
                          activity.type === 'transfer' ? theme.palette.secondary.main :
                          theme.palette.success.main,
                          0.1
                        ),
                        color:
                          activity.type === 'match' ? 'primary.main' :
                          activity.type === 'transfer' ? 'secondary.main' :
                          'success.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        {getActivityIcon(activity.type)}
                      </Box>
                      <Box className="mobile-activity-content">
                        <Typography className="mobile-activity-text">
                          {activity.description}
                        </Typography>
                        <Typography className="mobile-activity-subtext">
                          {new Date(activity.date).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>

                <Button
                  className="mobile-card-button"
                  endIcon={<ArrowIcon />}
                  sx={{ mt: 2, width: '100%' }}
                >
                  View all activity
                </Button>
              </Box>
            ) : (
              <Paper
                elevation={0}
                sx={{
                  p: { xs: 2, sm: 3 },
                  height: '100%',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    mb: 2,
                    fontWeight: 'medium',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  Recent Activity
                </Typography>

                <List sx={{ pt: 0 }}>
                  {recentActivity.map((activity, index) => (
                    <React.Fragment key={activity.id}>
                      <ListItem alignItems="flex-start" sx={{ py: 1.5, px: { xs: 0, sm: 1 } }}>
                        <ListItemAvatar>
                          <Avatar
                            sx={{
                              bgcolor: alpha(
                                activity.type === 'match' ? theme.palette.primary.main :
                                activity.type === 'transfer' ? theme.palette.secondary.main :
                                theme.palette.success.main,
                                0.1
                              ),
                              color:
                                activity.type === 'match' ? 'primary.main' :
                                activity.type === 'transfer' ? 'secondary.main' :
                                'success.main'
                            }}
                          >
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Typography component="span" variant="body2" fontWeight="medium">
                              {activity.description}
                            </Typography>
                          }
                          secondary={
                            <Typography component="span" variant="caption" color="text.secondary">
                              {new Date(activity.date).toLocaleDateString(undefined, {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < recentActivity.length - 1 && (
                        <Divider component="li" variant="inset" />
                      )}
                    </React.Fragment>
                  ))}
                </List>

                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button
                    variant="text"
                    color="primary"
                    endIcon={<ArrowIcon />}
                    size="small"
                  >
                    View all activity
                  </Button>
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Dashboard;