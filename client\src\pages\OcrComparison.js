import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material';
import { PhotoCamera } from '@mui/icons-material';

/**
 * OCR Component for processing images with PaddleOCR.
 */
export default function OcrComparison() {
  const [uploadedImage, setUploadedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState('');
  const [storedScorecards, setStoredScorecards] = useState([]);
  const [selectedScorecard, setSelectedScorecard] = useState('');

  // Fetch available scorecards on component mount
  useEffect(() => {
    const fetchScorecards = async () => {
      try {
        const response = await axios.get('/api/ocr-comparison/scorecards');
        setStoredScorecards(response.data.scorecards);
      } catch (err) {
        console.error('Error fetching scorecards:', err);
        setError('Failed to fetch scorecards');
      }
    };

    fetchScorecards();
  }, []);

  // Handle scorecard selection
  const handleScorecardChange = (e) => {
    setSelectedScorecard(e.target.value);
    setUploadedImage(null);
    setImagePreview(e.target.value);
  };

  // Handle image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedImage(file);
      setSelectedScorecard('');
      setImagePreview(URL.createObjectURL(file));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!uploadedImage && !selectedScorecard) {
      setError('Please select a scorecard or upload an image');
      return;
    }

    setError('');
    setLoading(true);
    setResults(null);

    try {
      const formData = new FormData();

      if (uploadedImage) {
        formData.append('image', uploadedImage);
      } else {
        formData.append('scorecardPath', selectedScorecard);
      }

      formData.append('methods', JSON.stringify(['paddle']));

      const response = await axios.post('/api/ocr-comparison/compare', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setResults(response.data);
    } catch (err) {
      console.error('Error processing image:', err);
      setError(err.response?.data?.error || 'Failed to process image');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Cricket Scorecard OCR
      </Typography>

      <Card>
        <CardHeader title="Select Image" />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <select
                  value={selectedScorecard}
                  onChange={handleScorecardChange}
                  disabled={loading}
                  style={{
                    padding: '8px',
                    marginBottom: '16px',
                    width: '100%'
                  }}
                >
                  <option value="">-- Select a stored scorecard --</option>
                  {storedScorecards.map((card) => (
                    <option key={card.path} value={card.path}>
                      {card.name}
                    </option>
                  ))}
                </select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Or upload a new image:
              </Typography>
              <input
                type="file"
                id="image-upload"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: 'none' }}
                disabled={loading}
              />
              <label htmlFor="image-upload">
                <Button
                  variant="contained"
                  startIcon={<PhotoCamera />}
                  component="span"
                  disabled={loading}
                >
                  Upload Image
                </Button>
              </label>
              {uploadedImage && (
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  Selected: {uploadedImage.name}
                </Typography>
              )}
            </Grid>

            {imagePreview && (
              <Grid item xs={12}>
                <img
                  src={imagePreview}
                  alt="Preview"
                  style={{
                    maxWidth: '100%',
                    maxHeight: '300px',
                    objectFit: 'contain'
                  }}
                />
              </Grid>
            )}

            <Grid item xs={12}>
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={loading || (!uploadedImage && !selectedScorecard)}
                fullWidth
              >
                {loading ? <CircularProgress size={24} /> : 'Process Image'}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {results && (
        <Card sx={{ mt: 3 }}>
          <CardHeader title="OCR Results" />
          <CardContent>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Field</TableCell>
                    <TableCell>Value</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Object.entries(results || {}).map(([key, value]) => (
                    <TableRow key={key}>
                      <TableCell>{key}</TableCell>
                      <TableCell>
                        {typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}
