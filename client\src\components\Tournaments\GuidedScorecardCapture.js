import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  IconButton,
  Paper,
  Grid,
  Slider,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  CameraAlt as CameraIcon,
  Cameraswitch as CameraSwitchIcon,
  FlashOn as FlashOnIcon,
  FlashOff as FlashOffIcon,
  Check as CheckIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { uploadScorecard } from '../../services/scorecardService';
import { useAuth } from '../../hooks/useAuth';

/**
 * Guided Scorecard Capture Component
 *
 * This component provides a mobile-optimized camera interface that guides users
 * to properly align their camera with a cricket match scorecard on their TV/monitor.
 * It uses real-time boundary detection to show when the scorecard is properly aligned,
 * and automatically captures the image when ready.
 */
const GuidedScorecardCapture = ({ open, onClose, tournamentId, matchId, onUploadSuccess, onSwitchToTraditional }) => {
  // eslint-disable-next-line no-unused-vars
  const { user } = useAuth(); // Keep for future use with user preferences
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const captureCanvasRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [cameraPermission, setCameraPermission] = useState(null);
  const [facingMode, setFacingMode] = useState('environment'); // 'environment' for back camera
  const [flashMode, setFlashMode] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [boundaryDetected, setBoundaryDetected] = useState(false);
  const [autoCaptureEnabled, setAutoCaptureEnabled] = useState(true);
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [extractedData, setExtractedData] = useState(null);
  const [showDataEditor, setShowDataEditor] = useState(false);
  const [browserCompatible, setBrowserCompatible] = useState(true);

  // Animation frame ID for cleanup
  const animationFrameId = useRef(null);

  // Check browser compatibility on mount
  useEffect(() => {
    // Check if we're on HTTPS
    const isHttps = window.location.protocol === 'https:';
    console.log('Connection security:', isHttps ? 'HTTPS (Secure)' : 'HTTP (Not Secure)');

    // Log browser information for debugging
    console.log('Browser info:', {
      userAgent: navigator.userAgent,
      protocol: window.location.protocol,
      mediaDevices: !!navigator.mediaDevices,
      getUserMedia: navigator.mediaDevices ? !!navigator.mediaDevices.getUserMedia : false,
      mediaStream: !!window.MediaStream,
      isSecureContext: window.isSecureContext
    });

    // Check if we're on a mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    console.log('Device type:', isMobile ? 'Mobile' : 'Desktop');

    // If we're on mobile and not using HTTPS, this is likely the issue
    if (isMobile && !isHttps) {
      console.error('Mobile device detected with insecure connection (HTTP)');
      console.error('Camera access requires HTTPS on mobile devices');

      // Set a specific error for this case
      setError('Camera access requires a secure connection (HTTPS) on mobile devices. Please use HTTPS to access this feature.');

      // We'll still try to use the camera, but warn the user about the likely issue
      setBrowserCompatible(true);
      return;
    }

    // More permissive check - assume compatibility by default for modern browsers
    // This is especially important for mobile browsers that might have different API structures
    let isCompatible = true;

    // Only mark as incompatible if we're absolutely sure
    if (!navigator.mediaDevices) {
      console.warn('mediaDevices API not found - checking for older APIs');

      // Check for older getUserMedia API versions (for older browsers)
      const oldGetUserMedia = navigator.getUserMedia ||
                             navigator.webkitGetUserMedia ||
                             navigator.mozGetUserMedia ||
                             navigator.msGetUserMedia;

      if (!oldGetUserMedia) {
        console.error('No getUserMedia API found at all');
        isCompatible = false;
      } else {
        console.log('Found older getUserMedia API, will try to use it');
      }
    }

    console.log('Browser compatibility check result:', isCompatible ? 'Compatible' : 'Not compatible');
    setBrowserCompatible(isCompatible);

    if (!isCompatible) {
      setError('Your browser does not support the camera features required for this functionality. Please try using Chrome, Firefox, or Safari.');
    }
  }, []);

  // Initialize camera when component mounts
  useEffect(() => {
    if (open) {
      try {
        console.log('Initializing camera...');
        initCamera();
      } catch (err) {
        console.error('Error in camera initialization useEffect:', err);
        setError(`Camera initialization error: ${err.message}`);
        setCameraPermission(false);
      }
    }

    return () => {
      try {
        console.log('Stopping camera...');
        stopCamera();
      } catch (err) {
        console.error('Error stopping camera:', err);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, facingMode]);
  // Note: initCamera and stopCamera are intentionally omitted from the dependency array
  // to avoid infinite loops, as they reference state setters that would trigger re-renders

  // Initialize camera - using the exact approach that worked in SimplifiedGuidedCapture
  const initCamera = async () => {
    try {
      console.log('initCamera called');

      // Reset states
      setCapturedImage(null);
      setError(null);
      setSuccess(null);
      setBoundaryDetected(false);
      setExtractedData(null);

      console.log('Requesting camera access');

      // Use the simplest possible constraints - this worked in SimplifiedGuidedCapture
      const mediaStream = await navigator.mediaDevices.getUserMedia({ video: true });

      console.log('Camera access granted, stream obtained');

      if (videoRef.current) {
        console.log('Setting video source');
        videoRef.current.srcObject = mediaStream;
        setStream(mediaStream);
        setCameraPermission(true);

        // Add event listeners
        videoRef.current.onloadedmetadata = () => {
          console.log(`Video metadata loaded: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
          startBoundaryDetection();
        };
      } else {
        console.log('Video ref not available');
        mediaStream.getTracks().forEach(track => track.stop());
      }
    } catch (err) {
      console.error(`Error accessing camera: ${err.message}`);
      setCameraPermission(false);
      setError(`Error accessing camera: ${err.message}`);
    }
  };

  // Stop camera and release resources
  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }

    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }
  };

  // Switch between front and back camera
  const toggleCamera = () => {
    setFacingMode(prevMode => prevMode === 'environment' ? 'user' : 'environment');
  };

  // Toggle flash mode (if available)
  const toggleFlash = async () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        try {
          const capabilities = videoTrack.getCapabilities();
          if (capabilities.torch) {
            const newFlashMode = !flashMode;
            await videoTrack.applyConstraints({
              advanced: [{ torch: newFlashMode }]
            });
            setFlashMode(newFlashMode);
          } else {
            setError('Flash not available on this device');
          }
        } catch (err) {
          console.error('Error toggling flash:', err);
          setError('Failed to toggle flash');
        }
      }
    }
  };

  // Start boundary detection process
  const startBoundaryDetection = () => {
    console.log('startBoundaryDetection called');

    if (!canvasRef.current || !videoRef.current) {
      console.error('Canvas or video ref not available');
      return;
    }

    const detectBoundaries = () => {
      try {
        if (!canvasRef.current || !videoRef.current) {
          console.log('Canvas or video ref no longer available, stopping detection loop');
          return;
        }

        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        // Make sure video is playing and has dimensions
        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          // Check if video has valid dimensions
          if (video.videoWidth === 0 || video.videoHeight === 0) {
            console.log('Video dimensions not yet available');
            animationFrameId.current = requestAnimationFrame(detectBoundaries);
            return;
          }

          // Set canvas dimensions to match video
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          console.log('Processing frame, video dimensions:', video.videoWidth, 'x', video.videoHeight);

          try {
            // Draw video frame to canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Apply image adjustments
            if (brightness !== 100 || contrast !== 100) {
              try {
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                const brightnessValue = brightness / 100;
                const contrastValue = contrast / 100;

                for (let i = 0; i < data.length; i += 4) {
                  // Apply brightness
                  data[i] = data[i] * brightnessValue;
                  data[i + 1] = data[i + 1] * brightnessValue;
                  data[i + 2] = data[i + 2] * brightnessValue;

                  // Apply contrast
                  data[i] = ((data[i] - 128) * contrastValue) + 128;
                  data[i + 1] = ((data[i + 1] - 128) * contrastValue) + 128;
                  data[i + 2] = ((data[i + 2] - 128) * contrastValue) + 128;
                }

                ctx.putImageData(imageData, 0, 0);
              } catch (adjustError) {
                console.error('Error applying image adjustments:', adjustError);
                // Continue without adjustments
              }
            }

            // Detect scorecard boundaries
            let detected = false;
            try {
              detected = detectScorecardBoundaries(ctx, canvas.width, canvas.height);
            } catch (detectionError) {
              console.error('Error in boundary detection:', detectionError);
              // Continue with detected = false
            }

            // Draw guide overlay
            try {
              drawGuideOverlay(ctx, canvas.width, canvas.height, detected);
            } catch (overlayError) {
              console.error('Error drawing guide overlay:', overlayError);
              // Continue without overlay
            }

            // Update state if boundary detection status changed
            if (detected !== boundaryDetected) {
              console.log('Boundary detection status changed:', detected);
              setBoundaryDetected(detected);

              // Auto-capture if enabled and boundaries detected
              if (detected && autoCaptureEnabled) {
                console.log('Auto-capturing image');
                captureImage();
              }
            }
          } catch (frameError) {
            console.error('Error processing video frame:', frameError);
          }
        } else {
          console.log('Video not ready yet, readyState:', video.readyState);
        }

        // Continue detection loop
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      } catch (loopError) {
        console.error('Error in detection loop:', loopError);
        // Try to continue the loop despite errors
        animationFrameId.current = requestAnimationFrame(detectBoundaries);
      }
    };

    // Start detection loop
    console.log('Starting detection loop');
    detectBoundaries();
  };

  // Detect scorecard boundaries in the image
  const detectScorecardBoundaries = (ctx, width, height) => {
    // This is a simplified placeholder implementation
    // In a real implementation, this would use more sophisticated image processing

    // For the proof of concept, we'll use a simple edge detection approach
    try {
      // Get image data
      const imageData = ctx.getImageData(0, 0, width, height);
      const data = imageData.data;

      // Simple edge detection - look for high contrast areas that might be scorecard boundaries
      let edgeCount = 0;
      const sampleSize = 20; // Sample every 20th pixel for performance

      for (let y = 0; y < height; y += sampleSize) {
        for (let x = 0; x < width; x += sampleSize) {
          const i = (y * width + x) * 4;

          // Skip if we're at the edge of the image
          if (x + sampleSize >= width || y + sampleSize >= height) continue;

          // Get current pixel and pixel to the right
          const r1 = data[i];
          const g1 = data[i + 1];
          const b1 = data[i + 2];

          // Pixel to the right
          const ir = (y * width + (x + sampleSize)) * 4;
          const r2 = data[ir];
          const g2 = data[ir + 1];
          const b2 = data[ir + 2];

          // Pixel below
          const ib = ((y + sampleSize) * width + x) * 4;
          const r3 = data[ib];
          const g3 = data[ib + 1];
          const b3 = data[ib + 2];

          // Calculate color differences
          const diffH = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
          const diffV = Math.abs(r1 - r3) + Math.abs(g1 - g3) + Math.abs(b1 - b3);

          // If difference is high, it might be an edge
          if (diffH > 150 || diffV > 150) {
            edgeCount++;
          }
        }
      }

      // Determine if the image contains a scorecard based on edge count
      // This threshold would need to be tuned based on testing
      const edgeDensity = edgeCount / ((width * height) / (sampleSize * sampleSize));
      const hasScorecard = edgeDensity > 0.1 && edgeDensity < 0.3;

      return hasScorecard;
    } catch (err) {
      console.error('Error in boundary detection:', err);
      return false;
    }
  };

  // Draw guide overlay on canvas
  const drawGuideOverlay = (ctx, width, height, detected) => {
    // Draw rectangle guide
    const padding = 50;
    const rectWidth = width - (padding * 2);
    const rectHeight = height - (padding * 2);

    // Set style based on detection status
    ctx.strokeStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.lineWidth = 4;

    // Draw rectangle
    ctx.beginPath();
    ctx.rect(padding, padding, rectWidth, rectHeight);
    ctx.stroke();

    // Add corner markers
    const cornerSize = 30;

    // Top-left corner
    ctx.beginPath();
    ctx.moveTo(padding, padding + cornerSize);
    ctx.lineTo(padding, padding);
    ctx.lineTo(padding + cornerSize, padding);
    ctx.stroke();

    // Top-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, padding);
    ctx.lineTo(width - padding, padding);
    ctx.lineTo(width - padding, padding + cornerSize);
    ctx.stroke();

    // Bottom-left corner
    ctx.beginPath();
    ctx.moveTo(padding, height - padding - cornerSize);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(padding + cornerSize, height - padding);
    ctx.stroke();

    // Bottom-right corner
    ctx.beginPath();
    ctx.moveTo(width - padding - cornerSize, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.lineTo(width - padding, height - padding - cornerSize);
    ctx.stroke();

    // Add text guide
    ctx.fillStyle = detected ? '#00FF00' : '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';

    if (detected) {
      ctx.fillText('Scorecard Detected! Capturing...', width / 2, height - 20);
    } else {
      ctx.fillText('Align scorecard within boundaries', width / 2, height - 20);
    }
  };

  // Capture current frame as image
  const captureImage = useCallback(() => {
    if (!videoRef.current || capturedImage || processing) return;

    try {
      setProcessing(true);

      // Create a canvas for the captured image
      const video = videoRef.current;
      const canvas = captureCanvasRef.current;

      if (!canvas) return;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw current video frame to canvas
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert canvas to image data URL
      const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setCapturedImage(imageDataUrl);

      // Simulate OCR processing
      setTimeout(() => {
        // Mock extracted data for proof of concept
        // In a real implementation, this would come from OCR processing
        setExtractedData({
          homeTeam: {
            name: 'VEERS-XI',
            score: {
              runs: 93,
              wickets: 4,
              overs: 11.1
            },
            players: [
              { name: 'GARY GILMOUR', runs: 24, balls: 19 },
              { name: 'VEER SINGH', runs: 17, balls: 10 },
              { name: 'MITCHELL MCCLENAGHAN', runs: 15, balls: 7 },
              { name: 'BRENDAN TAYLOR', runs: 11, balls: 4 }
            ]
          },
          awayTeam: {
            name: 'AMIGOS',
            score: {
              runs: 94,
              wickets: 7,
              overs: 12.4
            },
            players: [
              { name: 'HARRY NIELSEN', runs: 27, balls: 20 },
              { name: 'WAVELL HINDS', runs: 20, balls: 17 },
              { name: 'MARCUS NORTH', runs: 12, balls: 11 },
              { name: 'DAN LAWRENCE', runs: 10, balls: 8 }
            ]
          },
          result: 'AMIGOS WON BY 3 WICKETS',
          playerOfMatch: 'Spencer Johnson'
        });

        setProcessing(false);
      }, 2000);
    } catch (err) {
      console.error('Error capturing image:', err);
      setError('Failed to capture image. Please try again.');
      setProcessing(false);
    }
  }, [capturedImage, processing]);

  // Handle upload of captured image
  const handleUpload = async () => {
    if (!capturedImage) {
      setError('No image captured');
      return;
    }

    try {
      setUploading(true);
      setError(null);

      // Convert data URL to Blob
      const response = await fetch(capturedImage);
      const blob = await response.blob();

      // Create File object from Blob
      const file = new File([blob], 'scorecard.jpg', { type: 'image/jpeg' });

      // Upload to server
      const result = await uploadScorecard(tournamentId, matchId, file);

      setSuccess('Scorecard uploaded successfully');

      // Notify parent component
      if (onUploadSuccess) {
        onUploadSuccess(result);
      }

      // Close dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      console.error('Error uploading scorecard:', err);
      setError(err.message || 'Failed to upload scorecard. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Reset capture and go back to camera view
  const resetCapture = () => {
    setCapturedImage(null);
    setExtractedData(null);
    setShowDataEditor(false);
    setProcessing(false);
    setError(null);
    setSuccess(null);
  };

  // Handle dialog close
  const handleClose = () => {
    if (!uploading && !processing) {
      stopCamera();
      onClose();
    }
  };

  // Toggle data editor view
  const toggleDataEditor = () => {
    setShowDataEditor(!showDataEditor);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            {capturedImage
              ? 'Review Captured Scorecard'
              : 'Guided Scorecard Capture'}
          </Typography>
          <IconButton onClick={handleClose} disabled={uploading || processing}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ m: 2 }}>
            {success}
          </Alert>
        )}

        {!cameraPermission && !capturedImage && (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom color="error">
              {!browserCompatible ? 'Browser Not Compatible' :
               error && error.includes('HTTPS') ? 'Secure Connection Required' :
               'Camera Access Required'}
            </Typography>

            {!browserCompatible ? (
              <>
                <Typography variant="body1" paragraph>
                  Your browser does not support the camera features required for this functionality.
                </Typography>

                <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.paper' }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    <strong>Recommended browsers:</strong>
                    <ul style={{ textAlign: 'left', marginTop: '8px', marginBottom: 0 }}>
                      <li>Chrome (latest version)</li>
                      <li>Firefox (latest version)</li>
                      <li>Safari (latest version)</li>
                    </ul>
                  </Typography>
                </Paper>

                <Divider sx={{ my: 3 }}>OR</Divider>

                <Typography variant="body2" paragraph>
                  Please use the traditional upload method instead:
                </Typography>
              </>
            ) : error && error.includes('HTTPS') ? (
              <>
                <Typography variant="body1" paragraph>
                  Camera access on mobile devices requires a secure connection (HTTPS).
                </Typography>

                <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
                  <Typography variant="body2" paragraph sx={{ mb: 0 }}>
                    <strong>Current connection:</strong> HTTP (Not Secure)
                  </Typography>
                </Paper>

                <Typography variant="body2" paragraph sx={{ mt: 3 }}>
                  <strong>How to fix this:</strong>
                  <ul style={{ textAlign: 'left', marginTop: '8px' }}>
                    <li>Access this application using HTTPS instead of HTTP</li>
                    <li>For local development, you need to set up HTTPS on your development server</li>
                    <li>You can use tools like ngrok or local-ssl-proxy to create a secure tunnel</li>
                  </ul>
                </Typography>

                <Divider sx={{ my: 3 }}>OR</Divider>

                <Typography variant="body2" paragraph>
                  Use the traditional upload method instead:
                </Typography>
              </>
            ) : (
              <>
                <Typography variant="body1" paragraph>
                  This feature requires access to your device camera to capture the scorecard.
                </Typography>

                <Typography variant="body2" color="text.secondary" paragraph>
                  <strong>Troubleshooting tips:</strong>
                  <ul style={{ textAlign: 'left', marginTop: '8px' }}>
                    <li>Make sure your browser has permission to access your camera</li>
                    <li>Try using Chrome, Firefox, or Safari for best compatibility</li>
                    <li>If using a mobile device, make sure the site is loaded via HTTPS</li>
                    <li>Some browsers require HTTPS for camera access</li>
                    <li>If on iOS, Safari is the only browser that fully supports camera access</li>
                  </ul>
                </Typography>

                <Box sx={{ mt: 3, mb: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={initCamera}
                    size="large"
                  >
                    Try Again
                  </Button>

                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => {
                      // Try with basic constraints
                      stopCamera();
                      setTimeout(() => {
                        initCamera({ video: true });
                      }, 500);
                    }}
                    size="large"
                  >
                    Try Basic Camera
                  </Button>

                  <Button
                    variant="contained"
                    color="error"
                    onClick={() => {
                      // Direct camera access without using our initCamera function
                      if (stream) {
                        stream.getTracks().forEach(track => track.stop());
                        setStream(null);
                        if (videoRef.current) {
                          videoRef.current.srcObject = null;
                        }
                      }

                      // Use the exact same code that worked in SimpleCameraTest
                      setTimeout(() => {
                        navigator.mediaDevices.getUserMedia({ video: true })
                          .then(mediaStream => {
                            console.log('Direct camera access granted');
                            if (videoRef.current) {
                              videoRef.current.srcObject = mediaStream;
                              setStream(mediaStream);
                              setCameraPermission(true);

                              videoRef.current.onloadedmetadata = () => {
                                console.log('Video metadata loaded:',
                                  videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
                                startBoundaryDetection();
                              };
                            }
                          })
                          .catch(err => {
                            console.error('Direct camera access failed:', err);
                            setError(`Direct camera error: ${err.message}`);
                          });
                      }, 500);
                    }}
                    size="large"
                  >
                    Direct Camera Access
                  </Button>
                </Box>

                <Divider sx={{ my: 3 }}>OR</Divider>

                <Typography variant="body2" paragraph>
                  If camera access doesn't work, you can use the traditional upload method instead:
                </Typography>
              </>
            )}

            <Button
              variant="outlined"
              color="primary"
              onClick={() => {
                if (onSwitchToTraditional) {
                  onSwitchToTraditional();
                } else {
                  onClose();
                }
              }}
            >
              Switch to Traditional Upload
            </Button>
          </Box>
        )}

        {cameraPermission && !capturedImage && (
          <Box sx={{ position: 'relative', flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            {/* Camera feed with overlay */}
            <Box sx={{
              position: 'relative',
              flexGrow: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
              bgcolor: 'black',
              borderRadius: 1
            }}>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  transform: facingMode === 'user' ? 'scaleX(-1)' : 'none'
                }}
              />
              <canvas
                ref={canvasRef}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </Box>

            {/* Camera controls */}
            <Paper sx={{ p: 2 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" gutterBottom>
                    Brightness
                  </Typography>
                  <Slider
                    value={brightness}
                    onChange={(e, newValue) => setBrightness(newValue)}
                    min={50}
                    max={150}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" gutterBottom>
                    Contrast
                  </Typography>
                  <Slider
                    value={contrast}
                    onChange={(e, newValue) => setContrast(newValue)}
                    min={50}
                    max={150}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={autoCaptureEnabled}
                          onChange={(e) => setAutoCaptureEnabled(e.target.checked)}
                        />
                      }
                      label="Auto-capture"
                    />
                    <Box>
                      <IconButton onClick={toggleCamera} title="Switch Camera">
                        <CameraSwitchIcon />
                      </IconButton>
                      <IconButton onClick={toggleFlash} title="Toggle Flash">
                        {flashMode ? <FlashOnIcon /> : <FlashOffIcon />}
                      </IconButton>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={captureImage}
                        startIcon={<CameraIcon />}
                        disabled={processing}
                      >
                        Capture
                      </Button>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, flexDirection: 'column', gap: 2 }}>
                    <Button
                      variant="contained"
                      color="secondary"
                      size="large"
                      fullWidth
                      onClick={() => {
                        // Try with basic constraints using our simplified approach
                        stopCamera();
                        setTimeout(() => {
                          initCamera();
                        }, 500);
                      }}
                    >
                      Try Basic Camera
                    </Button>

                    <Button
                      variant="contained"
                      color="error"
                      size="large"
                      fullWidth
                      onClick={() => {
                        // Direct camera access without using our initCamera function
                        if (stream) {
                          stream.getTracks().forEach(track => track.stop());
                          setStream(null);
                          if (videoRef.current) {
                            videoRef.current.srcObject = null;
                          }
                        }

                        // Use the exact same code that worked in SimpleCameraTest
                        setTimeout(() => {
                          navigator.mediaDevices.getUserMedia({ video: true })
                            .then(mediaStream => {
                              console.log('Direct camera access granted');
                              if (videoRef.current) {
                                videoRef.current.srcObject = mediaStream;
                                setStream(mediaStream);
                                setCameraPermission(true);

                                videoRef.current.onloadedmetadata = () => {
                                  console.log('Video metadata loaded:',
                                    videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
                                  startBoundaryDetection();
                                };
                              }
                            })
                            .catch(err => {
                              console.error('Direct camera access failed:', err);
                              setError(`Direct camera error: ${err.message}`);
                            });
                        }, 500);
                      }}
                    >
                      Direct Camera Access
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Box>
        )}

        {capturedImage && (
          <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
            {processing ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', flexGrow: 1 }}>
                <CircularProgress size={60} />
                <Typography variant="h6" sx={{ mt: 2 }}>
                  Processing Scorecard...
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Extracting match data from image
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {/* Captured image */}
                <Grid item xs={12} md={showDataEditor ? 6 : 12}>
                  <Paper sx={{ p: 2, height: '100%' }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Captured Scorecard
                    </Typography>
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <img
                        src={capturedImage}
                        alt="Captured scorecard"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '50vh',
                          objectFit: 'contain'
                        }}
                      />
                    </Box>
                  </Paper>
                </Grid>

                {/* Extracted data */}
                {extractedData && (
                  <Grid item xs={12} md={showDataEditor ? 6 : 12}>
                    <Paper sx={{ p: 2, height: '100%', overflow: 'auto' }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle1">
                          Extracted Match Data
                        </Typography>
                        <Button
                          startIcon={<EditIcon />}
                          onClick={toggleDataEditor}
                          size="small"
                        >
                          {showDataEditor ? 'Hide Editor' : 'Edit Data'}
                        </Button>
                      </Box>

                      {showDataEditor ? (
                        <Typography variant="body2" color="text.secondary">
                          Data editor would be implemented here in the full version
                        </Typography>
                      ) : (
                        <>
                          <Grid container spacing={2}>
                            {/* Home team */}
                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">
                                {extractedData.homeTeam.name}
                              </Typography>
                              <Typography variant="h4">
                                {extractedData.homeTeam.score.runs}/{extractedData.homeTeam.score.wickets}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Overs: {extractedData.homeTeam.score.overs}
                              </Typography>
                            </Grid>

                            {/* Away team */}
                            <Grid item xs={12} sm={6}>
                              <Typography variant="h6">
                                {extractedData.awayTeam.name}
                              </Typography>
                              <Typography variant="h4">
                                {extractedData.awayTeam.score.runs}/{extractedData.awayTeam.score.wickets}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Overs: {extractedData.awayTeam.score.overs}
                              </Typography>
                            </Grid>

                            {/* Result */}
                            <Grid item xs={12}>
                              <Typography variant="body1" sx={{ mt: 2, fontWeight: 'bold' }}>
                                {extractedData.result}
                              </Typography>
                              <Typography variant="body2">
                                Player of the Match: {extractedData.playerOfMatch}
                              </Typography>
                            </Grid>
                          </Grid>
                        </>
                      )}
                    </Paper>
                  </Grid>
                )}
              </Grid>
            )}

            {/* Hidden canvas for capturing */}
            <canvas
              ref={captureCanvasRef}
              style={{ display: 'none' }}
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        {capturedImage ? (
          <>
            <Button onClick={resetCapture} disabled={uploading || processing}>
              Retake
            </Button>
            <Button
              onClick={handleUpload}
              variant="contained"
              color="primary"
              disabled={uploading || processing || !extractedData}
              startIcon={uploading ? <CircularProgress size={20} /> : <CheckIcon />}
            >
              {uploading ? 'Uploading...' : 'Confirm & Upload'}
            </Button>
          </>
        ) : (
          <Button onClick={handleClose} disabled={uploading || processing}>
            Cancel
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default GuidedScorecardCapture;
