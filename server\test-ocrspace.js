/**
 * Test OCR.Space Integration
 *
 * Simple test script to verify OCR.Space API is working
 */

const OCRService = require('./services/ocrService');
const path = require('path');
const fs = require('fs');

async function testOCRSpace() {
  console.log('🚀 Testing OCR.Space Integration...');

  try {
    // Initialize OCR service
    const ocrService = new OCRService();

    // Look for a test scorecard image (use available images)
    const testImagePaths = [
      path.join(__dirname, 'uploads/scorecards/scorecard1.jpg'),
      path.join(__dirname, 'uploads/scorecards/scorecard2.jpg'),
      path.join(__dirname, 'uploads/scorecards/scorecard3.jpg'),
      path.join(__dirname, 'uploads/scorecards/scorecard4.jpg'),
      path.join(__dirname, 'uploads/scorecards/scorecard5.jpg')
    ];

    let testImagePath = null;
    for (const imagePath of testImagePaths) {
      if (fs.existsSync(imagePath)) {
        testImagePath = imagePath;
        console.log(`✅ Found test image: ${imagePath}`);
        break;
      }
    }

    if (!testImagePath) {
      console.log('❌ No test scorecard image found. Please upload a scorecard image to test.');
      console.log('Expected locations:');
      testImagePaths.forEach(path => console.log(`  - ${path}`));
      return;
    }

    console.log('📸 Testing our FIXED spatial coordinate matching...');

    // Test our main processImage method which includes our spatial coordinate fix
    const result = await ocrService.processImage(testImagePath);

    console.log('\n🎯 FIXED EXTRACTION RESULTS:');
    console.log('='.repeat(50));

    // Basic match info
    console.log('📍 MATCH INFO:');
    console.log('  Team 1:', result.team1);
    console.log('  Team 2:', result.team2);
    console.log('  Venue:', result.venue);
    console.log('  Team 1 Score:', result.team1Score);
    console.log('  Team 2 Score:', result.team2Score);
    console.log('  Player of Match:', result.playerOfMatch);
    console.log('  Extraction Method:', result.extractionMethod);
    console.log('  Confidence:', result.confidence);

    // Phoenix batting (should now be correct!)
    console.log('\n🏏 PHOENIX BATTING:');
    if (result.team1Batsmen && result.team1Batsmen.length > 0) {
      result.team1Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${player.strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }

    // Invincibles batting (should now be correct!)
    console.log('\n🏏 INVINCIBLES BATTING:');
    if (result.team2Batsmen && result.team2Batsmen.length > 0) {
      result.team2Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls}) - SR: ${player.strikeRate}`);
      });
    } else {
      console.log('  No batting data found');
    }

    // Phoenix bowling
    console.log('\n🎳 PHOENIX BOWLING:');
    if (result.team1Bowlers && result.team1Bowlers.length > 0) {
      result.team1Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }

    // Invincibles bowling
    console.log('\n🎳 INVINCIBLES BOWLING:');
    if (result.team2Bowlers && result.team2Bowlers.length > 0) {
      result.team2Bowlers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.figure}`);
      });
    } else {
      console.log('  No bowling data found');
    }

    console.log('\n' + '='.repeat(50));
    if (result.success && result.team1Batsmen && result.team1Batsmen.length > 0) {
      console.log('✅ Spatial coordinate fix is working!');
      console.log('🎯 Check if the batting stats now match the actual scorecard!');
    } else {
      console.log('❌ Extraction failed:', result.error || 'No batting data extracted');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
testOCRSpace();
