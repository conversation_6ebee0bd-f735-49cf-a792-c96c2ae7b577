import React, { useState, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Image as ImageIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material';
import { uploadScorecard } from '../../services/scorecardService';

/**
 * Scorecard Upload Step Component
 * 
 * Handles file upload and OCR processing with real-time feedback
 */
const ScorecardUploadStep = ({ tournament, onUploadSuccess, onError }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [ocrProcessing, setOcrProcessing] = useState(false);
  const [playerStats, setPlayerStats] = useState(null);
  const [editedStats, setEditedStats] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        onError('Please select a valid image file (JPG, PNG, etc.)');
        return;
      }

      // Validate file size (max 1MB)
      if (file.size > 1 * 1024 * 1024) {
        onError('File size must be less than 1MB');
        return;
      }

      setSelectedFile(file);
      
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      
      // Clear any previous errors
      onError(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      onError('Please select a file first');
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);
      onError(null);

      console.log('Starting upload for tournament:', tournament._id);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Upload and process with OCR
      const result = await uploadScorecard(tournament._id, null, selectedFile);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      console.log('Upload result:', result);

      // Start OCR processing indication
      setOcrProcessing(true);
      
      // Simulate OCR processing time
      setTimeout(() => {
        setOcrProcessing(false);
        setUploading(false);
        
        // Even if OCR failed, we still want to proceed to the form
        // so the user can manually enter the data
        if (result.ocrData) {
          if (result.ocrData.success) {
            // OCR was successful
            const stats = result.ocrData.playerStats || [];
            setPlayerStats(stats);
            setEditedStats(stats);
            onUploadSuccess({ ...result, playerStats: stats });
          } else {
            // OCR failed but we still have the image, so proceed to form with warning
            console.log('OCR failed but proceeding to form:', result.ocrData.ocrMessage);
            onError(result.ocrData.ocrMessage || 'OCR processing failed. Please enter match details manually.');
            onUploadSuccess(result);
          }
        } else {
          // No OCR data at all
          onError('Failed to process the scorecard. Please try again or enter match details manually.');
        }
      }, 2000);

    } catch (error) {
      console.error('Upload failed:', error);
      setUploading(false);
      setOcrProcessing(false);
      setUploadProgress(0);
      onError(`Upload failed: ${error.message}`);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      handleFileSelect({ target: { files: [file] } });
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%', maxWidth: 800, mx: 'auto' }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Upload Scorecard Image
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Upload a clear image of your cricket scorecard. Our OCR system will automatically extract match details.
        </Typography>
      </Box>

      <Grid container spacing={3} justifyContent="center">
        {/* Upload Area */}
        <Grid item xs={12} md={8}>
          <Paper
            elevation={2}
            sx={{
              border: '2px dashed',
              borderColor: 'primary.light',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: 'grey.50',
              width: '100%',
              maxWidth: 400,
              mx: 'auto',
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: 'grey.100'
              }
            }}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
            
            {selectedFile ? (
              <Box>
                <SuccessIcon color="success" sx={{ fontSize: 48, mb: 2 }} />
                <Typography variant="h6" color="success.main">
                  File Selected
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedFile.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </Typography>
              </Box>
            ) : (
              <Box>
                <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2, opacity: 0.8 }} />
                <Typography variant="h6" color="primary.main" sx={{ fontWeight: 500 }}>
                  Drop your scorecard image here
                </Typography>
                <Typography variant="body2" color="primary.main">
                  Supports JPG, PNG, WebP (max 1MB)
                </Typography>
                <Typography variant="body2" color="primary.main" sx={{ mt: 1 }}>
                  or click to browse files
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Upload Button */}
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button
              variant="contained"
              size="large"
              onClick={handleUpload}
              disabled={!selectedFile || uploading}
              startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
            >
              {uploading ? 'Processing...' : 'Upload & Process'}
            </Button>
          </Box>

          {/* Progress Indicators */}
          {uploading && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Upload Progress
              </Typography>
              <LinearProgress variant="determinate" value={uploadProgress} />
            </Box>
          )}

          {ocrProcessing && (
            <Box sx={{ mt: 2 }}>
              <Alert severity="info" icon={<CircularProgress size={20} />}>
                Processing scorecard with OCR... This may take a few seconds.
              </Alert>
            </Box>
          )}
        </Grid>

        {/* Preview Area */}
        <Grid item xs={12} md={6}>
          {previewUrl && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Preview
                </Typography>
                <Box
                  component="img"
                  src={previewUrl}
                  alt="Scorecard preview"
                  sx={{
                    width: '100%',
                    height: 'auto',
                    maxHeight: 300,
                    objectFit: 'contain',
                    border: 1,
                    borderColor: 'grey.300',
                    borderRadius: 1
                  }}
                />
              </CardContent>
            </Card>
          )}
        </Grid>

          {/* Player Statistics Table */}
          {playerStats && (
            <Grid item xs={12} sx={{ mt: 4 }}>
              <Typography variant="h6" gutterBottom>
                Player Statistics (From OCR)
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Review and edit the extracted data if needed
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Player Name</TableCell>
                      <TableCell align="right">Runs</TableCell>
                      <TableCell align="right">Balls</TableCell>
                      <TableCell align="right">Wickets</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {editedStats && editedStats.map((player, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={player.name || ''}
                            onChange={(e) => {
                              const updatedStats = [...editedStats];
                              updatedStats[index] = { ...updatedStats[index], name: e.target.value };
                              setEditedStats(updatedStats);
                              onUploadSuccess({ playerStats: updatedStats });
                            }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            type="number"
                            size="small"
                            value={player.runs || ''}
                            onChange={(e) => {
                              const updatedStats = [...editedStats];
                              updatedStats[index] = { ...updatedStats[index], runs: parseInt(e.target.value) || 0 };
                              setEditedStats(updatedStats);
                              onUploadSuccess({ playerStats: updatedStats });
                            }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            type="number"
                            size="small"
                            value={player.balls || ''}
                            onChange={(e) => {
                              const updatedStats = [...editedStats];
                              updatedStats[index] = { ...updatedStats[index], balls: parseInt(e.target.value) || 0 };
                              setEditedStats(updatedStats);
                              onUploadSuccess({ playerStats: updatedStats });
                            }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            type="number"
                            size="small"
                            value={player.wickets || ''}
                            onChange={(e) => {
                              const updatedStats = [...editedStats];
                              updatedStats[index] = { ...updatedStats[index], wickets: parseInt(e.target.value) || 0 };
                              setEditedStats(updatedStats);
                              onUploadSuccess({ playerStats: updatedStats });
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          )}
        </Grid>
      </Box>
    );
};

export default ScorecardUploadStep;
