# RPL Cricket Project Issues Creation Script
# Creates GitHub issues for all project phases and adds them to the project board

param(
    [string]$RepoOwner = "rhingonekar",
    [string]$RepoName = "rplwebapp",
    [switch]$DryRun = $false
)

Write-Host "📝 Creating RPL Cricket Project Issues" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

# Check GitHub CLI
try {
    gh auth status 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) { throw "Not authenticated" }
    Write-Host "✅ GitHub CLI ready" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub CLI not ready. Please run setup-project-board.ps1 first" -ForegroundColor Red
    exit 1
}

# Define all project tasks
$ProjectTasks = @(
    # Phase 1.0 - Core Infrastructure (COMPLETED)
    @{
        Title = "✅ 1.1 Authentication System"
        Body = @"
## Status: COMPLETE ✅

**JWT authentication, role-based access control (admin, team_owner, viewer)**

### Files Implemented
- ``server/controllers/authController.js``
- ``server/routes/auth.js``
- ``client/src/context/AuthContext.js``

### Features
- JWT token generation and validation
- Role-based access control
- Protected routes
- User session management

**Phase:** 1.1 Core Infrastructure & Authentication  
**Completion:** 100%
"@
        Labels = @("✅ Done", "⚙️ Backend", "phase-1")
        State = "closed"
        Phase = "Phase 1.0"
        Priority = "🟢 Low"
        Completion = 100
    },
    
    @{
        Title = "✅ 1.2 Database Configuration"
        Body = @"
## Status: COMPLETE ✅

**MongoDB Atlas connection, comprehensive data models**

### Files Implemented
- ``server/config/db.js``
- ``server/models/*.js``

### Features
- MongoDB Atlas integration
- Comprehensive data models
- Database connection management
- Schema validation

**Phase:** 1.2 Core Infrastructure & Authentication  
**Completion:** 100%
"@
        Labels = @("✅ Done", "🗄️ Database", "phase-1")
        State = "closed"
        Phase = "Phase 1.0"
        Priority = "🟢 Low"
        Completion = 100
    },
    
    @{
        Title = "✅ 1.3 Project Structure & Build System"
        Body = @"
## Status: COMPLETE ✅

**React frontend, Express backend, build configuration**

### Files Implemented
- ``package.json``
- ``client/package.json``
- ``server/package.json``
- ``Dockerfile``

### Features
- React frontend setup
- Express backend configuration
- Docker containerization
- Build and deployment scripts

**Phase:** 1.3 Core Infrastructure & Authentication  
**Completion:** 100%
"@
        Labels = @("✅ Done", "🚀 Deployment", "phase-1")
        State = "closed"
        Phase = "Phase 1.0"
        Priority = "🟢 Low"
        Completion = 100
    },
    
    # Phase 2.0 - Player & Team Management
    @{
        Title = "🔄 2.6 Transfer Market System"
        Body = @"
## Status: IN PROGRESS 🔄

**Player trading between teams, market value calculations, transfer history**

### What's Implemented ✅
- Basic transfer market UI
- Player listing functionality
- Market value calculations (partial)

### What's Missing ❌
- [ ] Complete trading logic
- [ ] Market value algorithms
- [ ] Transfer history tracking
- [ ] Transaction validation
- [ ] Notification system

### Files
- ``client/src/pages/TransferMarket/``
- ``server/controllers/`` (needs completion)

**Phase:** 2.6 Player & Team Management  
**Priority:** 🔴 Critical  
**Completion:** 80%
"@
        Labels = @("🔄 In Progress", "🏆 Trading", "phase-2", "🔴 Critical")
        State = "open"
        Phase = "Phase 2.0"
        Priority = "🔴 Critical"
        Completion = 80
    },
    
    # Phase 7.0 - Big Ant Cricket 24 Features (CRITICAL)
    @{
        Title = "🎮 7.1 Advanced Skill Points & Rating System"
        Body = @"
## Status: TODO 📋

**Implement automatic rating increases based on skill points (5000 points = +1 rating)**

### Core Big Ant Cricket 24 Feature ⭐

### Acceptance Criteria
- [ ] 1 run = 1 skill point
- [ ] 1 wicket = 10 skill points  
- [ ] 5000 skill points = +1 rating increase
- [ ] Admin configurable thresholds
- [ ] Automatic rating updates after each match
- [ ] Skill point history tracking
- [ ] Rating change notifications

### Implementation Notes
This is the core feature from the original Big Ant Cricket 24 vision where player ratings dynamically increase based on performance.

**Phase:** 7.1 Big Ant Cricket 24 Integration  
**Priority:** 🔴 Critical  
**Estimated Effort:** 1 week
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🔴 Critical")
        State = "open"
        Phase = "Phase 7.0"
        Priority = "🔴 Critical"
        Completion = 0
    },
    
    @{
        Title = "🎮 7.2 Performance Milestone Bonuses"
        Body = @"
## Status: TODO 📋

**Implement milestone bonus system for exceptional performances**

### Core Big Ant Cricket 24 Feature ⭐

### Acceptance Criteria
- [ ] Batting milestones: 30 (+60 points), 50 (+90 points), 100 (+150 points)
- [ ] Bowling milestones: 3W (+60 points), 5W (+90 points)
- [ ] Automatic detection from scorecard OCR
- [ ] Historical milestone tracking
- [ ] Milestone achievement notifications
- [ ] Leaderboard integration

### Implementation Notes
Essential for the original vision where milestones provide significant bonus skill points beyond base performance.

**Phase:** 7.2 Big Ant Cricket 24 Integration  
**Priority:** 🔴 Critical  
**Estimated Effort:** 1 week
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🔴 Critical")
        State = "open"
        Phase = "Phase 7.0"
        Priority = "🔴 Critical"
        Completion = 0
    },
    
    @{
        Title = "🎮 7.3 Comprehensive Leaderboards"
        Body = @"
## Status: TODO 📋

**Create comprehensive leaderboards for competitive gaming experience**

### Core Big Ant Cricket 24 Feature ⭐

### Acceptance Criteria
- [ ] Multiple leaderboard categories (Most Runs, 30s, 50s, 100s, Wickets, 3W/5W Hauls, MOM)
- [ ] Format-wise filtering (T10, T20, ODI, Test)
- [ ] Tournament-wise and overall statistics
- [ ] Real-time updates after each match
- [ ] Player ranking system
- [ ] Historical performance tracking

### Implementation Notes
Leaderboards are essential for the competitive gaming experience that makes the Big Ant Cricket 24 vision engaging.

**Phase:** 7.3 Big Ant Cricket 24 Integration  
**Priority:** 🟠 High  
**Estimated Effort:** 2 weeks
"@
        Labels = @("📋 Todo", "🎮 Big Ant Cricket 24", "phase-7", "🟠 High")
        State = "open"
        Phase = "Phase 7.0"
        Priority = "🟠 High"
        Completion = 0
    }
)

# Function to create issues
function New-ProjectIssues {
    param($Tasks)
    
    Write-Host "📝 Creating project issues..." -ForegroundColor Cyan
    $createdIssues = @()
    
    foreach ($task in $Tasks) {
        Write-Host "   Creating: $($task.Title)" -ForegroundColor White
        
        if ($DryRun) {
            Write-Host "      [DRY RUN] Would create issue" -ForegroundColor Yellow
            continue
        }
        
        try {
            # Create the issue
            $labelsParam = $task.Labels -join ","
            $issueUrl = gh issue create --title $task.Title --body $task.Body --label $labelsParam --repo "$RepoOwner/$RepoName"
            
            # Close the issue if it's marked as complete
            if ($task.State -eq "closed") {
                $issueNumber = ($issueUrl -split "/")[-1]
                gh issue close $issueNumber --repo "$RepoOwner/$RepoName" --comment "Marking as complete - this work was done in previous development cycles."
            }
            
            Write-Host "      ✅ Created: $issueUrl" -ForegroundColor Green
            $createdIssues += @{
                Url = $issueUrl
                Task = $task
            }
            
            # Small delay to avoid rate limiting
            Start-Sleep -Milliseconds 500
            
        } catch {
            Write-Host "      ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $createdIssues
}

# Function to display summary
function Show-Summary {
    param($Issues)
    
    Write-Host ""
    Write-Host "📊 Issue Creation Summary" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    
    $phaseGroups = $Issues | Group-Object { $_.Task.Phase }
    
    foreach ($group in $phaseGroups) {
        $completed = ($group.Group | Where-Object { $_.Task.State -eq "closed" }).Count
        $total = $group.Count
        $percentage = if ($total -gt 0) { [math]::Round(($completed / $total) * 100) } else { 0 }
        
        Write-Host ""
        Write-Host "$($group.Name): $completed/$total tasks ($percentage%)" -ForegroundColor White
        
        foreach ($issue in $group.Group) {
            $status = if ($issue.Task.State -eq "closed") { "✅" } else { "📋" }
            Write-Host "   $status $($issue.Task.Title)" -ForegroundColor Gray
        }
    }
}

# Main execution
try {
    Write-Host "Repository: $RepoOwner/$RepoName" -ForegroundColor Cyan
    if ($DryRun) {
        Write-Host "Mode: DRY RUN (no issues will be created)" -ForegroundColor Yellow
    }
    Write-Host ""
    
    # Create all issues
    $createdIssues = New-ProjectIssues -Tasks $ProjectTasks
    
    # Show summary
    Show-Summary -Issues $createdIssues
    
    Write-Host ""
    Write-Host "🎉 Issue Creation Complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Go to your GitHub repository" -ForegroundColor White
    Write-Host "2. Open the Projects tab" -ForegroundColor White
    Write-Host "3. Add these issues to your project board" -ForegroundColor White
    Write-Host "4. Organize them by phase and status" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 Repository: https://github.com/$RepoOwner/$RepoName" -ForegroundColor Blue
    Write-Host "🔗 Issues: https://github.com/$RepoOwner/$RepoName/issues" -ForegroundColor Blue
    
} catch {
    Write-Host ""
    Write-Host "❌ Issue creation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
