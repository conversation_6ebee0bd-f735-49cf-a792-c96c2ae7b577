#!/usr/bin/env python
"""
PaddleOCR Coordinate Extractor for Cricket Scorecards (Fixed Version)
Extracts text with coordinates in OCR.Space-compatible format
"""

import argparse
import json
import os
import sys
from typing import Dict, List, Any

# Try to import PaddleOCR
try:
    from paddleocr import PaddleOC<PERSON>
except ImportError:
    print("Error: PaddleOCR is not installed. Please install it with:")
    print("pip install paddleocr")
    sys.exit(1)

def extract_text_with_coordinates(image_path: str, settings: Dict = None) -> Dict:
    """Extract text with coordinates using PaddleOCR"""
    try:
        print(f"Starting PaddleOCR coordinate extraction for: {image_path}", file=sys.stderr)

        # Default settings (compatible with current PaddleOCR version)
        default_settings = {
            'use_textline_orientation': True,  # Updated parameter name
            'lang': 'en'
        }

        # Update with provided settings
        if settings:
            default_settings.update(settings)

        print(f"PaddleOCR settings: {default_settings}", file=sys.stderr)

        # Initialize PaddleOCR
        ocr = PaddleOCR(**default_settings)

        # Perform OCR
        print("Running PaddleOCR with coordinate extraction...", file=sys.stderr)
        result = ocr.ocr(image_path)  # Use standard ocr method

        print(f"PaddleOCR result type: {type(result)}", file=sys.stderr)
        print(f"PaddleOCR result length: {len(result) if result else 0}", file=sys.stderr)

        if not result:
            print("No text detected by PaddleOCR - empty result", file=sys.stderr)
            return {
                'success': False,
                'text_elements': [],
                'full_text': '',
                'has_coordinates': False,
                'total_elements': 0,
                'error': 'No text detected - empty result'
            }

        # Get the first result
        ocr_result = result[0]
        
        # Extract text elements
        text_elements = []
        full_text_parts = []
        
        # Try to access the texts directly from OCRResult object
        if hasattr(ocr_result, 'rec_texts'):
            print(f"Found rec_texts with {len(ocr_result.rec_texts)} items", file=sys.stderr)
            texts = ocr_result.rec_texts
            
            # Try to get coordinates from rec_polys
            if hasattr(ocr_result, 'rec_polys'):
                polys = ocr_result.rec_polys
                scores = ocr_result.rec_scores if hasattr(ocr_result, 'rec_scores') else [0.5] * len(texts)
                
                print(f"Found rec_polys with {len(polys)} items", file=sys.stderr)
                
                for i, text in enumerate(texts):
                    if i < len(polys):
                        poly = polys[i]
                        confidence = scores[i] if i < len(scores) else 0.5
                        
                        # Convert poly to x, y, width, height
                        try:
                            # Extract x and y coordinates from the polygon points
                            x_coords = []
                            y_coords = []
                            
                            # Handle different poly formats
                            if hasattr(poly, 'tolist'):
                                # Convert numpy array to list
                                poly_list = poly.tolist()
                            else:
                                poly_list = poly
                                
                            # Extract coordinates from points
                            for point in poly_list:
                                if len(point) >= 2:
                                    x_coords.append(point[0])
                                    y_coords.append(point[1])
                            
                            if x_coords and y_coords:
                                left = int(min(x_coords))
                                top = int(min(y_coords))
                                width = int(max(x_coords) - min(x_coords))
                                height = int(max(y_coords) - min(y_coords))
                                
                                text_elements.append({
                                    'text': text,
                                    'x': left,
                                    'y': top,
                                    'width': width,
                                    'height': height,
                                    'confidence': float(confidence)
                                })
                                
                                full_text_parts.append(text)
                        except Exception as e:
                            print(f"Error processing poly: {e}", file=sys.stderr)
            
            # If we couldn't get coordinates, just use the texts
            elif len(text_elements) == 0:
                print("No coordinates found, using texts only", file=sys.stderr)
                for i, text in enumerate(texts):
                    text_elements.append({
                        'text': text,
                        'x': 0,
                        'y': i * 20,  # Simple vertical layout
                        'width': 100,
                        'height': 20,
                        'confidence': 0.5
                    })
                    full_text_parts.append(text)
        
        # Try to access the texts from dictionary format
        elif isinstance(ocr_result, dict) and 'rec_texts' in ocr_result:
            print(f"Found rec_texts in dictionary with {len(ocr_result['rec_texts'])} items", file=sys.stderr)
            texts = ocr_result['rec_texts']
            
            # Try to get coordinates
            if 'rec_polys' in ocr_result:
                polys = ocr_result['rec_polys']
                scores = ocr_result.get('rec_scores', [0.5] * len(texts))
                
                print(f"Found rec_polys in dictionary with {len(polys)} items", file=sys.stderr)
                
                for i, text in enumerate(texts):
                    if i < len(polys):
                        poly = polys[i]
                        confidence = scores[i] if i < len(scores) else 0.5
                        
                        # Convert poly to x, y, width, height (similar to above)
                        try:
                            x_coords = []
                            y_coords = []
                            
                            for point in poly:
                                if len(point) >= 2:
                                    x_coords.append(point[0])
                                    y_coords.append(point[1])
                            
                            if x_coords and y_coords:
                                left = int(min(x_coords))
                                top = int(min(y_coords))
                                width = int(max(x_coords) - min(x_coords))
                                height = int(max(y_coords) - min(y_coords))
                                
                                text_elements.append({
                                    'text': text,
                                    'x': left,
                                    'y': top,
                                    'width': width,
                                    'height': height,
                                    'confidence': float(confidence)
                                })
                                
                                full_text_parts.append(text)
                        except Exception as e:
                            print(f"Error processing poly: {e}", file=sys.stderr)
            
            # If we couldn't get coordinates, just use the texts
            elif len(text_elements) == 0:
                print("No coordinates found in dictionary, using texts only", file=sys.stderr)
                for i, text in enumerate(texts):
                    text_elements.append({
                        'text': text,
                        'x': 0,
                        'y': i * 20,  # Simple vertical layout
                        'width': 100,
                        'height': 20,
                        'confidence': 0.5
                    })
                    full_text_parts.append(text)
        
        # If we still don't have any text elements, try the old format
        if len(text_elements) == 0 and isinstance(ocr_result, list):
            print("Trying old format", file=sys.stderr)
            for line in ocr_result:
                if len(line) >= 2:
                    bbox = line[0]  # Bounding box coordinates
                    text_info = line[1]
                    
                    if text_info and len(text_info) >= 2:
                        text = text_info[0].strip()
                        confidence = text_info[1]
                        
                        if text:
                            # Convert bbox to x, y, width, height format
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            
                            left = int(min(x_coords))
                            top = int(min(y_coords))
                            width = int(max(x_coords) - min(x_coords))
                            height = int(max(y_coords) - min(y_coords))
                            
                            text_elements.append({
                                'text': text,
                                'x': left,
                                'y': top,
                                'width': width,
                                'height': height,
                                'confidence': confidence
                            })
                            
                            full_text_parts.append(text)
        
        # Create full text from parts
        full_text = '\n'.join(full_text_parts)
        
        print(f"PaddleOCR extracted {len(text_elements)} text elements with coordinates", file=sys.stderr)
        
        return {
            'success': len(text_elements) > 0,
            'text_elements': text_elements,
            'full_text': full_text,
            'has_coordinates': len(text_elements) > 0,
            'total_elements': len(text_elements),
            'engine': 'PaddleOCR'
        }
    
    except Exception as e:
        print(f"PaddleOCR coordinate extraction failed: {str(e)}", file=sys.stderr)
        return {
            'success': False,
            'text_elements': [],
            'full_text': '',
            'has_coordinates': False,
            'total_elements': 0,
            'error': str(e),
            'engine': 'PaddleOCR'
        }

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Extract text with coordinates using PaddleOCR')
    parser.add_argument('image_path', help='Path to the image file')
    parser.add_argument('--output', help='Output JSON file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    
    args = parser.parse_args()
    
    # Check if image exists
    if not os.path.exists(args.image_path):
        print(json.dumps({
            'success': False,
            'error': f'Image file not found: {args.image_path}'
        }))
        sys.exit(1)
    
    # Extract text with coordinates
    result = extract_text_with_coordinates(args.image_path)
    
    # Output result
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"Results saved to: {args.output}", file=sys.stderr)
    else:
        try:
            # Try to print with ensure_ascii=False for better readability
            print(json.dumps(result, indent=2, ensure_ascii=False))
        except UnicodeEncodeError:
            # Fallback to ensure_ascii=True if there are encoding issues
            print(json.dumps(result, indent=2, ensure_ascii=True))

if __name__ == '__main__':
    main()