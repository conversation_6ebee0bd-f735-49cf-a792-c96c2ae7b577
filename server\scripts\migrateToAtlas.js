/**
 * Database Migration Script: Local MongoDB to MongoDB Atlas
 * 
 * This script helps migrate data from your local MongoDB Compass database
 * to your MongoDB Atlas cloud database.
 * 
 * BEFORE RUNNING:
 * 1. Make sure your local MongoDB is running
 * 2. Update your .env file with Atlas connection string
 * 3. Backup your local data first (recommended)
 * 
 * Usage: node scripts/migrateToAtlas.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import all models
const User = require('../models/User');
const Team = require('../models/Team');
const Player = require('../models/Player');
const Tournament = require('../models/Tournament');
const Auction = require('../models/Auction');
const Template = require('../models/Template');
const OCRSettings = require('../models/OCRSettings');

// Database configurations
const LOCAL_DB = 'mongodb://localhost:27017/cricket24'; // Update if your local DB name is different
const ATLAS_DB = 'mongodb+srv://rpl-admin:<EMAIL>/rpl-cricket?retryWrites=true&w=majority&appName=Cluster0';

console.log('🔧 Using provided MongoDB Atlas connection string');
console.log('📍 Atlas Database: rpl-cricket');
console.log('📍 Local Database: cricket24');

class DatabaseMigrator {
  constructor() {
    this.localConnection = null;
    this.atlasConnection = null;
    this.migrationStats = {
      users: 0,
      teams: 0,
      players: 0,
      tournaments: 0,
      auctions: 0,
      templates: 0,
      ocrSettings: 0
    };
  }

  async connectToLocal() {
    try {
      console.log('🔌 Connecting to local MongoDB...');
      this.localConnection = await mongoose.createConnection(LOCAL_DB);
      console.log('✅ Connected to local MongoDB');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to local MongoDB:', error.message);
      return false;
    }
  }

  async connectToAtlas() {
    try {
      console.log('🌐 Connecting to MongoDB Atlas...');
      this.atlasConnection = await mongoose.createConnection(ATLAS_DB);
      console.log('✅ Connected to MongoDB Atlas');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB Atlas:', error.message);
      console.log('Please check your MONGODB_URI and network connection');
      return false;
    }
  }

  async migrateCollection(modelName, Model) {
    try {
      console.log(`\n📦 Migrating ${modelName}...`);
      
      // Get model instances for both connections
      const LocalModel = this.localConnection.model(modelName, Model.schema);
      const AtlasModel = this.atlasConnection.model(modelName, Model.schema);
      
      // Fetch all documents from local database
      const localDocs = await LocalModel.find({}).lean();
      console.log(`   Found ${localDocs.length} ${modelName.toLowerCase()} in local database`);
      
      if (localDocs.length === 0) {
        console.log(`   ⚠️  No ${modelName.toLowerCase()} to migrate`);
        return 0;
      }
      
      // Check if collection already exists in Atlas
      const existingCount = await AtlasModel.countDocuments();
      if (existingCount > 0) {
        console.log(`   ⚠️  Atlas already has ${existingCount} ${modelName.toLowerCase()}`);
        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        const answer = await new Promise(resolve => {
          rl.question(`   Do you want to clear existing ${modelName.toLowerCase()} and replace with local data? (y/N): `, resolve);
        });
        rl.close();
        
        if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
          await AtlasModel.deleteMany({});
          console.log(`   🗑️  Cleared existing ${modelName.toLowerCase()} from Atlas`);
        } else {
          console.log(`   ⏭️  Skipping ${modelName} migration`);
          return 0;
        }
      }
      
      // Insert documents to Atlas
      const result = await AtlasModel.insertMany(localDocs, { ordered: false });
      console.log(`   ✅ Successfully migrated ${result.length} ${modelName.toLowerCase()}`);
      
      return result.length;
    } catch (error) {
      console.error(`   ❌ Error migrating ${modelName}:`, error.message);
      return 0;
    }
  }

  async runMigration() {
    console.log('🚀 Starting Database Migration from Local MongoDB to Atlas\n');
    console.log('Local Database:', LOCAL_DB);
    console.log('Atlas Database:', ATLAS_DB.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@'));
    console.log('\n' + '='.repeat(60));
    
    // Connect to both databases
    const localConnected = await this.connectToLocal();
    if (!localConnected) {
      console.log('\n💡 Make sure your local MongoDB is running and the database name is correct');
      return;
    }
    
    const atlasConnected = await this.connectToAtlas();
    if (!atlasConnected) {
      return;
    }
    
    // Migrate each collection
    const collections = [
      { name: 'User', model: User },
      { name: 'Team', model: Team },
      { name: 'Player', model: Player },
      { name: 'Tournament', model: Tournament },
      { name: 'Auction', model: Auction },
      { name: 'Template', model: Template },
      { name: 'OCRSettings', model: OCRSettings }
    ];
    
    for (const collection of collections) {
      const migrated = await this.migrateCollection(collection.name, collection.model);
      this.migrationStats[collection.name.toLowerCase() + 's'] = migrated;
    }
    
    // Print migration summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 MIGRATION SUMMARY');
    console.log('='.repeat(60));
    
    let totalMigrated = 0;
    Object.entries(this.migrationStats).forEach(([collection, count]) => {
      console.log(`${collection.padEnd(15)}: ${count} documents`);
      totalMigrated += count;
    });
    
    console.log('='.repeat(60));
    console.log(`Total migrated: ${totalMigrated} documents`);
    
    if (totalMigrated > 0) {
      console.log('\n✅ Migration completed successfully!');
      console.log('\n🎯 NEXT STEPS:');
      console.log('1. Test your application with Atlas database');
      console.log('2. Verify all data is correctly migrated');
      console.log('3. Update your production environment variables');
      console.log('4. Deploy your application');
    } else {
      console.log('\n⚠️  No data was migrated. Please check your local database.');
    }
  }

  async cleanup() {
    if (this.localConnection) {
      await this.localConnection.close();
      console.log('\n🔌 Disconnected from local MongoDB');
    }
    if (this.atlasConnection) {
      await this.atlasConnection.close();
      console.log('🌐 Disconnected from MongoDB Atlas');
    }
  }
}

// Run migration
async function main() {
  const migrator = new DatabaseMigrator();
  
  try {
    await migrator.runMigration();
  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
  } finally {
    await migrator.cleanup();
    process.exit(0);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⏹️  Migration interrupted by user');
  process.exit(0);
});

process.on('unhandledRejection', (error) => {
  console.error('\n❌ Unhandled rejection:', error.message);
  process.exit(1);
});

if (require.main === module) {
  main();
}

module.exports = DatabaseMigrator;