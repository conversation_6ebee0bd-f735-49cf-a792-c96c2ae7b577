import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Card,
  CardContent,
  Button,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  CircularProgress,
  LinearProgress,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  Gavel as GavelIcon,
  AccessTime as AccessTimeIcon,
  MonetizationOn as MoneyIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { getAuctions, placeBid, getMyBids, getAuctionById, getMyActiveBids } from '../../services/auctionService';
import { getTeamBudget } from '../../services/teamService';
import { format, formatDistanceToNow } from 'date-fns';
// import io from 'socket.io-client'; // Uncomment when implementing real-time updates
import { motion } from 'framer-motion';
import { useSnackbar } from 'notistack';
import PlayerAuctionStatus from '../../components/auction/PlayerAuctionStatus';
import BidActivityFeed from '../../components/auction/BidActivityFeed';

// Components moved to separate files:
// - BidActivityFeed.js
// - PlayerAuctionStatus.js

// Main component
const LiveAuctionDashboard = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  // const isSmallScreen = useMediaQuery(theme.breakpoints.down('md')); // Uncomment if needed for responsive design

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [liveAuctions, setLiveAuctions] = useState([]);
  const [selectedAuction, setSelectedAuction] = useState(null);
  const [timeLeft, setTimeLeft] = useState('');
  const [bidHistory, setBidHistory] = useState([]);
  const [myBids, setMyBids] = useState([]);
  const [teamBudget, setTeamBudget] = useState(10000);
  const [activeBidsTotal, setActiveBidsTotal] = useState(0);
  const [bidLoading, setBidLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [refreshingBudget, setRefreshingBudget] = useState(false);

  // Socket connection for real-time updates
  const socketRef = useRef(null);
  const timerRef = useRef(null);

  // Load auctions and team budget
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load live auctions
        const auctionsData = await getAuctions({ status: 'live' });

        // Filter out auctions that have ended
        const now = new Date();
        const filteredAuctions = auctionsData.auctions.filter(auction => {
          const endTime = new Date(auction.endTime);
          return now < endTime; // Only include auctions that haven't ended yet
        });

        setLiveAuctions(filteredAuctions);

        // Set selected auction to the first one if available
        if (filteredAuctions.length > 0 && !selectedAuction) {
          setSelectedAuction(filteredAuctions[0]);

          // Load bid history for the selected auction
          const auctionDetails = await getAuctionById(filteredAuctions[0]._id);
          if (auctionDetails && auctionDetails.bids) {
            // Process bids to add isWinning flag
            const processedBids = auctionDetails.bids.map((bid, index, array) => {
              const isHighestBid = index === array.length - 1;
              return {
                ...bid,
                isWinning: isHighestBid,
                isHighestBid
              };
            });
            setBidHistory(processedBids);
          }
        }

        // Load my bids
        const myBidsData = await getMyBids();
        setMyBids(myBidsData);

        // Load team budget
        const budgetData = await getTeamBudget();
        if (budgetData && budgetData.budget) {
          setTeamBudget(budgetData.budget.totalBudget);
        }

        // Calculate active bids total using the getMyActiveBids function
        if (user && user._id) {
          try {
            const activeBidsResult = await getMyActiveBids(user._id);
            setActiveBidsTotal(activeBidsResult.total);
            console.log('Initial active bids total:', activeBidsResult.total);
          } catch (activeBidsErr) {
            console.error('Error calculating initial active bids:', activeBidsErr);
          }
        }

      } catch (err) {
        console.error('Error loading auction data:', err);
        setError('Failed to load auction data: ' + (err.msg || err.message));
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Set up socket connection for real-time updates
    // This is a placeholder - you would implement actual socket connection
    // socketRef.current = io('http://localhost:5000');
    // socketRef.current.on('bidPlaced', handleNewBid);

    // Clean up
    return () => {
      // Store socket reference in a variable to avoid the exhaustive-deps warning
      const socket = socketRef.current;
      if (socket) {
        socket.disconnect();
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [user]);

  // Update time left for selected auction
  useEffect(() => {
    if (!selectedAuction) return;

    const updateTimeLeft = () => {
      const now = new Date();
      const endTime = new Date(selectedAuction.endTime);

      if (now >= endTime) {
        setTimeLeft('Auction ended');

        // Refresh live auctions to remove this auction from the list
        const refreshAuctions = async () => {
          try {
            const auctionsData = await getAuctions({ status: 'live' });

            // Filter out auctions that have ended
            const filteredAuctions = auctionsData.auctions.filter(auction => {
              const auctionEndTime = new Date(auction.endTime);
              return now < auctionEndTime;
            });

            setLiveAuctions(filteredAuctions);

            // If there are still live auctions, select the first one
            if (filteredAuctions.length > 0) {
              setSelectedAuction(filteredAuctions[0]);
            }
          } catch (err) {
            console.error('Error refreshing auctions:', err);
          }
        };

        refreshAuctions();
        return;
      }

      const secondsLeft = Math.floor((endTime - now) / 1000);
      const hours = Math.floor(secondsLeft / 3600);
      const minutes = Math.floor((secondsLeft % 3600) / 60);
      const seconds = secondsLeft % 60;

      setTimeLeft(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTimeLeft();
    timerRef.current = setInterval(updateTimeLeft, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
    // selectedAuction is intentionally the only dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAuction]);

  // Handle new bid (from socket or after placing a bid)
  const handleNewBid = (bid) => {
    // Update bid history
    setBidHistory(prevBids => {
      // Mark all previous bids as not winning
      const updatedBids = prevBids.map(prevBid => ({
        ...prevBid,
        isWinning: false,
        isHighestBid: false
      }));

      // Add new bid as winning
      return [...updatedBids, {
        ...bid,
        isWinning: true,
        isHighestBid: true
      }];
    });

    // Update selected auction
    setSelectedAuction(prevAuction => {
      if (prevAuction && prevAuction._id === bid.auctionId) {
        return {
          ...prevAuction,
          currentBid: bid.amount,
          currentBidder: bid.bidder
        };
      }
      return prevAuction;
    });

    // Show notification if user was outbid
    if (user && user.id !== bid.bidder._id && selectedAuction && selectedAuction.currentBidder && selectedAuction.currentBidder._id === user.id) {
      enqueueSnackbar('You have been outbid!', {
        variant: 'warning',
        autoHideDuration: 5000
      });
    }
  };

  // Handle auction selection
  const handleSelectAuction = async (auction) => {
    setSelectedAuction(auction);

    try {
      // Load bid history for the selected auction
      const auctionDetails = await getAuctionById(auction._id);
      if (auctionDetails && auctionDetails.bids) {
        // Process bids to add isWinning flag
        const processedBids = auctionDetails.bids.map((bid, index, array) => {
          const isHighestBid = index === array.length - 1;
          return {
            ...bid,
            isWinning: isHighestBid,
            isHighestBid
          };
        });
        setBidHistory(processedBids);
      }
    } catch (err) {
      console.error('Error loading auction details:', err);
    }
  };

  // Handle bid placement
  const handlePlaceBid = async (auction, amount) => {
    if (!auction) return;

    setBidLoading(true);
    try {
      // Validate bid amount
      if (amount <= (auction?.currentBid || 0)) {
        setError('Bid amount must be higher than the current bid');
        return;
      }

      if (amount < (auction?.currentBid || 0) + (auction?.minimumBidIncrement || 100)) {
        setError(`Minimum bid increment is ${auction?.minimumBidIncrement || 100} Credits`);
        return;
      }

      // Calculate available budget
      const availableBudget = teamBudget - activeBidsTotal +
        (auction?.currentBidder && auction?.currentBidder._id === user?.id ? auction?.currentBid || 0 : 0);

      if (amount > availableBudget) {
        setError(`You do not have enough available budget. Your available budget is ${availableBudget.toLocaleString()} Credits`);
        return;
      }

      // Place bid via API
      await placeBid(auction._id, amount);

      // Show success message
      setSuccess('Bid placed successfully');
      enqueueSnackbar('Bid placed successfully!', {
        variant: 'success',
        autoHideDuration: 3000
      });

      // Refresh auction data
      const auctionDetails = await getAuctionById(auction._id);
      setSelectedAuction(auctionDetails);

      // Update bid history
      if (auctionDetails && auctionDetails.bids) {
        // Process bids to add isWinning flag
        const processedBids = auctionDetails.bids.map((bid, index, array) => {
          const isHighestBid = index === array.length - 1;
          return {
            ...bid,
            isWinning: isHighestBid,
            isHighestBid
          };
        });
        setBidHistory(processedBids);
      }

      // Refresh live auctions
      const auctionsData = await getAuctions({ status: 'live' });

      // Filter out auctions that have ended
      const now = new Date();
      const filteredAuctions = auctionsData.auctions.filter(auction => {
        const endTime = new Date(auction.endTime);
        return now < endTime; // Only include auctions that haven't ended yet
      });

      setLiveAuctions(filteredAuctions);

      // Refresh my bids
      const myBidsData = await getMyBids();
      setMyBids(myBidsData);

      // Refresh active bids total
      await refreshActiveBidsTotal();

    } catch (err) {
      console.error('Error placing bid:', err);
      setError('Failed to place bid: ' + (err.message || 'Unknown error'));
      enqueueSnackbar('Failed to place bid: ' + (err.message || 'Unknown error'), {
        variant: 'error',
        autoHideDuration: 5000
      });
    } finally {
      setBidLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Function to refresh active bids total
  const refreshActiveBidsTotal = async () => {
    setRefreshingBudget(true);
    try {
      // Log the entire user object to see its structure
      console.log('User object:', user);
      
      // Check if user exists and has an _id property (MongoDB uses _id)
      const userId = user?._id;
      console.log('Refreshing active bids total for user ID:', userId);
      
      if (!userId) {
        console.log('No user ID available, cannot refresh bids');
        return;
      }
      
      // Use the getMyActiveBids function
      const result = await getMyActiveBids(userId);
      
      // Update the state with the total
      setActiveBidsTotal(result.total);
      
      console.log('Refreshed active bids total:', result.total);
      console.log('Active bids count:', result.activeBids.length);
      
      return result.activeBids;
    } catch (error) {
      console.error('Error refreshing active bids total:', error);
      return [];
    } finally {
      setRefreshingBudget(false);
    }
  };

  // Set up periodic refresh of active bids
  useEffect(() => {
    // Only refresh if we have a user
    if (user && user._id) {
      console.log('Setting up periodic refresh for user:', user._id);
      
      // Initial refresh
      refreshActiveBidsTotal();
      
      // Set up interval to refresh every 30 seconds
      const intervalId = setInterval(() => {
        refreshActiveBidsTotal();
      }, 30000); // 30 seconds
      
      // Clean up interval on unmount
      return () => clearInterval(intervalId);
    } else {
      console.log('No user available for periodic refresh');
    }
  }, [user]);

  // Calculate available budget
  const availableBudget = teamBudget - activeBidsTotal +
    (selectedAuction?.currentBidder && selectedAuction?.currentBidder._id === user?.id ? selectedAuction?.currentBid || 0 : 0);

  return (
    <Box sx={{
      py: { xs: 1, sm: 1.5, md: 2 },
      px: { xs: 0.5, sm: 1, md: 2 },
      height: { xs: 'calc(100vh - 56px)', sm: 'auto' },
      overflow: { xs: 'auto', sm: 'visible' }
    }}>
      {/* Header with enhanced budget info */}
      <Paper sx={{ p: { xs: 1, sm: 1.5, md: 2 }, mb: { xs: 1, sm: 1.5, md: 2 } }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: { xs: 0.5, sm: 1 }
        }}>
          <Typography variant={{ xs: 'h6', sm: 'h5', md: 'h5' }} sx={{ mb: { xs: 0.5, sm: 0 } }}>
            Live Auction Dashboard
          </Typography>
          
          {/* Budget Display */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box 
              sx={{ 
                p: 1, 
                borderRadius: 1, 
                bgcolor: 'background.paper', 
                boxShadow: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold">
                  Your Budget
                </Typography>
                <Tooltip title="Refresh budget information" arrow>
                  <IconButton 
                    size="small" 
                    onClick={() => refreshActiveBidsTotal()}
                    sx={{ p: 0.5 }}
                    disabled={refreshingBudget}
                    color="primary"
                  >
                    {refreshingBudget ? (
                      <CircularProgress size={16} thickness={5} />
                    ) : (
                      <RefreshIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Grid container spacing={1}>
                {/* Total Budget */}
                <Grid item xs={4}>
                  <Tooltip title="Your total team budget for the season" arrow placement="top">
                    <Box>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Total
                      </Typography>
                      <Typography variant="body2" color="primary" fontWeight="bold">
                        {teamBudget.toLocaleString()} $
                      </Typography>
                    </Box>
                  </Tooltip>
                </Grid>
                
                {/* Locked in Bids */}
                <Grid item xs={4}>
                  <Tooltip title="Amount currently committed in active bids. This will be returned if you are outbid." arrow placement="top">
                    <Box>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Locked
                      </Typography>
                      {refreshingBudget ? (
                        <CircularProgress size={16} thickness={5} color="warning" />
                      ) : (
                        <Typography variant="body2" color="warning.main" fontWeight="bold">
                          {activeBidsTotal.toLocaleString()} $
                        </Typography>
                      )}
                    </Box>
                  </Tooltip>
                </Grid>
                
                {/* Available Budget */}
                <Grid item xs={4}>
                  <Tooltip title="Budget available for new bids" arrow placement="top">
                    <Box>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Available
                      </Typography>
                      {refreshingBudget ? (
                        <CircularProgress size={16} thickness={5} color="success" />
                      ) : (
                        <Typography variant="body2" color="success.main" fontWeight="bold">
                          {availableBudget.toLocaleString()} $
                        </Typography>
                      )}
                    </Box>
                  </Tooltip>
                </Grid>
              </Grid>
              
              {/* Budget Progress Bar */}
              <Box sx={{ mt: 1 }}>
                {refreshingBudget ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 0.5 }}>
                    <CircularProgress size={16} thickness={5} />
                  </Box>
                ) : (
                  <LinearProgress
                    variant="buffer"
                    value={(activeBidsTotal / teamBudget) * 100}
                    valueBuffer={100}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: 'warning.main',
                      },
                      '& .MuiLinearProgress-dashed': {
                        backgroundImage: 'none',
                      }
                    }}
                  />
                )}
              </Box>
            </Box>
          </Box>
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ display: { xs: 'none', sm: 'block' } }}>
          Bid on players in real-time and track your auction activity
        </Typography>
      </Paper>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      {/* Main content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={{ xs: 0.5, sm: 1, md: 2, lg: 2 }}>
          {/* Mobile view - Tabs for switching between auction and activity */}
          <Grid item xs={12} display={{ xs: 'block', md: 'none' }}>
            <Paper sx={{ p: 0.5, mb: 0.5 }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  minHeight: '36px',
                  '& .MuiTab-root': {
                    minHeight: '36px',
                    py: 0.5,
                    fontSize: '0.75rem'
                  }
                }}
              >
                <Tab label="Current Auction" />
                <Tab label="Bid Activity" />
              </Tabs>
            </Paper>
          </Grid>

          {/* Left column - Live auctions */}
          <Grid item xs={12} md={8} lg={9} display={{ xs: tabValue === 0 ? 'block' : 'none', md: 'block' }}>
            {/* Selected auction details */}
            {selectedAuction ? (
              <PlayerAuctionStatus
                auction={selectedAuction}
                timeLeft={timeLeft}
                onBid={handlePlaceBid}
                currentUser={user}
                availableBudget={availableBudget}
              />
            ) : (
              <Paper sx={{ p: { xs: 2, md: 3 }, mb: { xs: 2, md: 3 }, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary">
                  No live auctions available
                </Typography>
              </Paper>
            )}

            {/* Other live auctions */}
            <Paper sx={{ p: { xs: 1, sm: 1.5, md: 2 } }}>
              <Typography variant={{ xs: 'subtitle1', sm: 'h6' }} gutterBottom sx={{ fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' } }}>
                Live Auctions
              </Typography>

              {liveAuctions.length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                  No live auctions available at the moment
                </Typography>
              ) : (
                <Grid container spacing={{ xs: 3, sm: 4, md: 4, lg: 5 }}>
                  {liveAuctions.map(auction => (
                    <Grid item xs={6} sm={4} md={3} lg={3} key={auction._id}>
                      <Card
                        sx={{
                          cursor: 'pointer',
                          transition: 'transform 0.2s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 4
                          },
                          border: selectedAuction && selectedAuction._id === auction._id ? `2px solid ${theme.palette.primary.main}` : 'none',
                          maxHeight: { xs: 180, sm: 'none' }
                        }}
                        onClick={() => handleSelectAuction(auction)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <Box
                            component="img"
                            src={auction.player.image || '/uploads/players/default.png'}
                            alt={auction.player.name}
                            sx={{
                              width: '100%',
                              height: { xs: 60, sm: 80, md: 90, lg: 100 },
                              objectFit: 'cover'
                            }}
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = '/uploads/players/default.png';
                            }}
                          />

                          <Chip
                            label="Live"
                            color="success"
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: { xs: 2, sm: 5 },
                              right: { xs: 2, sm: 5 },
                              fontWeight: 'bold',
                              height: { xs: 16, sm: 20 },
                              '& .MuiChip-label': {
                                px: { xs: 0.5, sm: 1 },
                                fontSize: { xs: '0.6rem', sm: '0.7rem' }
                              }
                            }}
                          />

                          {auction.currentBidder && auction.currentBidder._id === user?.id && (
                            <Chip
                              label="Leading"
                              color="primary"
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: { xs: 2, sm: 5 },
                                left: { xs: 2, sm: 5 },
                                fontWeight: 'bold',
                                height: { xs: 16, sm: 20 },
                                '& .MuiChip-label': {
                                  px: { xs: 0.5, sm: 1 },
                                  fontSize: { xs: '0.6rem', sm: '0.7rem' }
                                }
                              }}
                            />
                          )}
                        </Box>

                        <CardContent sx={{ p: { xs: 0.5, sm: 0.75, md: 1 } }}>
                          <Typography variant="subtitle2" noWrap sx={{
                            fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' },
                            lineHeight: { xs: 1.2, sm: 1.3 },
                            mb: { xs: 0.25, sm: 0.5 }
                          }}>
                            {auction.player.name}
                          </Typography>

                          <Typography variant="caption" color="text.secondary" noWrap sx={{
                            fontSize: { xs: '0.6rem', sm: '0.65rem', md: '0.7rem' },
                            lineHeight: { xs: 1.1, sm: 1.2 },
                            display: 'block',
                            mb: { xs: 0.25, sm: 0.5 }
                          }}>
                            {auction.player.type}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <MoneyIcon sx={{ mr: 0.25, color: 'primary.main', fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' } }} />
                            <Typography variant="body2" fontWeight="bold" color="primary" sx={{
                              fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' },
                              lineHeight: { xs: 1.2, sm: 1.3 }
                            }}>
                              {auction.currentBid.toLocaleString()} $
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Paper>
          </Grid>

          {/* Right column - Bid activity */}
          <Grid item xs={12} md={4} lg={3} display={{ xs: tabValue === 1 ? 'block' : 'none', md: 'block' }}>
            {/* Desktop view - Tabs for switching between activity types */}
            <Paper sx={{
              p: { xs: 1, sm: 1.5, md: 2 },
              mb: { xs: 1, sm: 1.5, md: 2 },
              height: { xs: 'calc(100vh - 200px)', md: '100%' },
              overflow: { xs: 'auto', md: 'visible' }
            }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  mb: { xs: 0.5, sm: 1, md: 1.5 },
                  minHeight: { xs: '36px', sm: '48px' },
                  '& .MuiTab-root': {
                    minHeight: { xs: '36px', sm: '48px' },
                    py: { xs: 0.5, sm: 0.75 },
                    fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' }
                  }
                }}
              >
                <Tab label="Auction Activity" />
                <Tab label="My Bids" />
              </Tabs>

              {tabValue === 0 && (
                <BidActivityFeed
                  bids={bidHistory}
                  currentUserId={user?.id}
                  title={`Bids for ${selectedAuction?.player?.name || 'Selected Auction'}`}
                  maxHeight={{ xs: 300, sm: 350, md: 400 }}
                />
              )}

              {tabValue === 1 && (
                <Box sx={{
                  height: { xs: 'calc(100vh - 250px)', sm: 'calc(100vh - 280px)', md: 400 },
                  overflowY: 'auto',
                  pb: { xs: 1, sm: 2 }
                }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' } }}>
                    My Bids
                  </Typography>

                  {myBids.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2, fontSize: { xs: '0.75rem', sm: '0.8rem' } }}>
                      You haven't placed any bids yet
                    </Typography>
                  ) : (
                    <List dense sx={{
                      '& .MuiListItem-root': {
                        px: { xs: 1, sm: 1.5 },
                        py: { xs: 0.5, sm: 0.75 }
                      }
                    }}>
                      {myBids.map(auction => {
                        const isWinning = auction.currentBidder && auction.currentBidder._id === user?.id;
                        return (
                          <ListItem
                            key={auction._id}
                            sx={{
                              mb: { xs: 0.5, sm: 0.75 },
                              bgcolor: 'background.paper',
                              borderRadius: 1,
                              border: `1px solid ${isWinning ? theme.palette.success.main : theme.palette.divider}`
                            }}
                          >
                            <ListItemAvatar sx={{ minWidth: { xs: 32, sm: 40, md: 48 } }}>
                              <Avatar
                                src={auction.player.image || '/uploads/players/default.png'}
                                sx={{
                                  width: { xs: 24, sm: 28, md: 32 },
                                  height: { xs: 24, sm: 28, md: 32 },
                                  border: isWinning ? `2px solid ${theme.palette.success.main}` : 'none'
                                }}
                              />
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Typography component="span" variant="body2" sx={{
                                    fontWeight: 'medium',
                                    fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' }
                                  }}>
                                    {auction.player.name}
                                  </Typography>
                                  {isWinning && (
                                    <Chip
                                      size="small"
                                      label="Winning"
                                      color="success"
                                      sx={{
                                        ml: 0.5,
                                        height: { xs: 14, sm: 16 },
                                        '& .MuiChip-label': {
                                          px: { xs: 0.25, sm: 0.5 },
                                          fontSize: { xs: '0.55rem', sm: '0.625rem' }
                                        }
                                      }}
                                    />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box component="span" sx={{ display: 'block' }}>
                                  <Typography component="span" variant="body2" color="primary" fontWeight="bold" sx={{
                                    display: 'block',
                                    fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' }
                                  }}>
                                    {auction.currentBid.toLocaleString()} $
                                  </Typography>
                                  <Typography component="span" variant="caption" color="text.secondary" sx={{
                                    display: 'block',
                                    fontSize: { xs: '0.6rem', sm: '0.65rem', md: '0.7rem' }
                                  }}>
                                    Ends {formatDistanceToNow(new Date(auction.endTime), { addSuffix: true })}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  )}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default LiveAuctionDashboard;
