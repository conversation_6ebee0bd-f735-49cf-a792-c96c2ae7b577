{"name": "rpl-cricket-app", "version": "1.0.0", "description": "RPL Cricket Application - Full Stack", "main": "server/index.js", "scripts": {"build": "cd client && npm install && npm run build", "start": "cd server && npm start", "dev": "concurrently \"cd server && npm run dev\" \"cd client && npm start\"", "install-deps": "cd server && npm install && cd ../client && npm install", "heroku-postbuild": "cd client && npm install && npm run build"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {"@google-cloud/vision": "^5.1.0", "@octokit/rest": "^22.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.6.1", "express": "^5.1.0", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "node-fetch": "^3.3.2", "puppeteer": "^24.8.0", "serve": "^14.2.4", "sharp": "^0.34.2", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"chalk": "^5.4.1", "cli-table3": "^0.6.5", "commander": "^13.1.0", "concurrently": "^7.6.0", "nodemon": "^3.1.10", "tesseract.js": "^6.0.1"}, "keywords": ["cricket", "rpl", "react", "node", "express", "mongodb"], "author": "Your Name", "license": "MIT"}