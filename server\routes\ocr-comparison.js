const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/temp');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Get list of available scorecards
router.get('/scorecards', (req, res) => {
  const scorecardsDir = path.join(__dirname, '../uploads/scorecards');
  
  try {
    if (!fs.existsSync(scorecardsDir)) {
      return res.json({ scorecards: [] });
    }

    const files = fs.readdirSync(scorecardsDir);
    const scorecards = files
      .filter(file => /\.(jpg|jpeg|png|bmp)$/i.test(file))
      .map(file => ({
        name: file,
        path: `/uploads/scorecards/${file}`
      }));

    res.json({ scorecards });
  } catch (error) {
    console.error('Error reading scorecards directory:', error);
    res.status(500).json({ error: 'Failed to read scorecards' });
  }
});

// Compare OCR methods
router.post('/compare', upload.single('image'), async (req, res) => {
  try {
    // Log incoming request data for debugging
    console.log('Request body:', req.body);
    console.log('Request file:', req.file);

    // Validate image input
    if (!req.file && !req.body.scorecard) {
      return res.status(400).json({
        error: 'No image provided',
        details: 'Please provide either an image file upload or select an existing scorecard'
      });
    }

    // Validate and parse methods array
    let selectedMethods;
    try {
      // Log the raw methods data for debugging
      console.log('Raw methods data:', req.body.methods);
      
      // Handle different input formats
      if (typeof req.body.methods === 'string') {
        selectedMethods = JSON.parse(req.body.methods);
      } else if (Array.isArray(req.body.methods)) {
        selectedMethods = req.body.methods;
      } else if (!req.body.methods) {
        selectedMethods = [];
      } else {
        throw new Error('Unsupported methods format');
      }

      console.log('Parsed methods:', selectedMethods);
    } catch (parseError) {
      console.error('Methods parsing error:', parseError);
      return res.status(400).json({
        error: 'Invalid methods format',
        details: 'The methods parameter must be either a JSON array string (e.g., "[\"paddle\"]") or an array',
        received: req.body.methods
      });
    }

    if (!Array.isArray(selectedMethods) || !selectedMethods.length) {
      return res.status(400).json({
        error: 'No OCR methods selected',
        details: 'Please select at least one OCR method for comparison'
      });
    }

    // Process the image with selected OCR methods
    const imagePath = req.file ? req.file.path : path.join(__dirname, '..', req.body.scorecardPath);
    
    // Process with cricket scorecard parser
    try {
      const OCRService = require('../services/ocrService');
      const ocrService = new OCRService();
      const result = await ocrService.processImage(imagePath);
      res.json(result);
    } catch (err) {
      console.error('OCR processing error:', err);
      res.status(500).json({
        success: false,
        error: 'Failed to process scorecard',
        details: err.message
      });
    }
  } catch (error) {
    console.error('Error processing OCR comparison:', error);
    res.status(500).json({ error: 'Failed to process OCR comparison' });
  }
});

module.exports = router;