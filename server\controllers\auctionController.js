const Auction = require('../models/Auction');
const Player = require('../models/Player');
const Team = require('../models/Team');
const User = require('../models/User');
const cacheService = require('../services/cacheService');
const auctionService = require('../services/auctionService');
const socketService = require('../services/socketService');

// Initialize auction service
auctionService.initialize().catch(console.error);

/**
 * Get all auctions with filtering options
 * @route GET /api/auctions
 * @access Public
 */
exports.getAllAuctions = async (req, res) => {
  try {
    const { status, search, playerId, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Try to get from cache first
    const cacheKey = `auctions:${JSON.stringify(req.query)}`;
    let cachedResult = await cacheService.get(cacheKey);
    
    if (cachedResult) {
      return res.json(cachedResult);
    }

    // Build query
    const query = {};

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Filter by player if provided
    if (playerId) {
      query.player = playerId;
    }

    // Search by player name (requires population)
    let auctions;
    if (search) {
      // First find players matching the search term
      const players = await Player.find({
        name: { $regex: search, $options: 'i' }
      }).select('_id');

      // Then find auctions for those players
      const playerIds = players.map(p => p._id);
      query.player = { $in: playerIds };
    }

    // Get total count for pagination
    const total = await Auction.countDocuments(query);

    // Get auctions with pagination
    auctions = await Auction.find(query)
      .populate('player', 'name type nationality ratings image battingHand bowlingHand height')
      .populate('currentBidder', 'username')
      .populate('createdBy', 'username')
      .sort({ startTime: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Update auction statuses and get real-time data
    const now = new Date();
    const updatedAuctions = await Promise.all(auctions.map(async (auction) => {
      const auctionObj = auction.toObject();

      // Get real-time state from Redis if auction is live
      if (now >= auction.startTime && now < auction.endTime && auction.isActive) {
        const liveState = await cacheService.getAuctionState(auction._id);
        if (liveState) {
          auctionObj.currentBid = liveState.currentBid;
          auctionObj.currentBidder = liveState.currentBidder;
          auctionObj.bidsCount = liveState.bidsCount;
        }
        auctionObj.status = 'live';
      } else if (!auction.isActive) {
        auctionObj.status = 'cancelled';
      } else if (now < auction.startTime) {
        auctionObj.status = 'scheduled';
      } else {
        auctionObj.status = 'completed';
      }

      return auctionObj;
    }));

    const result = {
      auctions: updatedAuctions,
      pagination: {
        total,
        page: parseInt(page),
        pages: Math.ceil(total / limit)
      }
    };

    // Cache the result for 30 seconds
    await cacheService.set(cacheKey, result, 30);

    res.json(result);
  } catch (err) {
    console.error('Error getting auctions:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get a single auction by ID
 * @route GET /api/auctions/:id
 * @access Public
 */
exports.getAuctionById = async (req, res) => {
  try {
    const cacheKey = `auction:${req.params.id}`;
    
    // Try to get from cache first
    let auction = await cacheService.get(cacheKey);
    
    if (!auction) {
      // Get from database
      auction = await Auction.findById(req.params.id)
        .populate('player', 'name type nationality ratings image battingHand bowlingHand height')
        .populate('currentBidder', 'username')
        .populate('bids.bidder', 'username');

      if (!auction) {
        return res.status(404).json({ msg: 'Auction not found' });
      }

      // Update auction status if needed
      const now = new Date();
      if (auction.status === 'live' && auction.endTime <= now) {
        auction.status = 'completed';
        await auction.save();
      }

      // Get real-time bid data from Redis if auction is live
      if (auction.status === 'live') {
        const redisBidData = await auctionService.getAuctionState(req.params.id);
        if (redisBidData) {
          auction.currentBid = redisBidData.currentBid || auction.currentBid;
          auction.currentBidder = redisBidData.currentBidder || auction.currentBidder;
          
          // Merge Redis bids with database bids
          if (redisBidData.bids && redisBidData.bids.length > 0) {
            const lastDbBidTime = auction.bids.length > 0 ? 
              new Date(auction.bids[auction.bids.length - 1].time) : new Date(0);
            
            const newRedisBids = redisBidData.bids.filter(bid => 
              new Date(bid.time) > lastDbBidTime
            );
            
            auction.bids = [...auction.bids, ...newRedisBids];
          }
        }
      }

      // Cache for 10 seconds (shorter for individual auctions)
      await cacheService.set(cacheKey, auction, 10);
    }

    res.json(auction);
  } catch (err) {
    console.error('Error getting auction:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Auction not found' });
    }
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Create a new auction
 * @route POST /api/auctions
 * @access Private (Admin only)
 */
exports.createAuction = async (req, res) => {
  try {
    const {
      playerId,
      startingPrice,
      minimumBidIncrement,
      startTime,
      endTime,
      isActive,
      reservePrice,
      description
    } = req.body;

    // Validate player exists
    const player = await Player.findById(playerId);
    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Create new auction
    const newAuction = new Auction({
      player: playerId,
      startingPrice,
      currentBid: startingPrice,
      minimumBidIncrement,
      startTime,
      endTime,
      isActive,
      reservePrice,
      description,
      createdBy: req.user.id
    });

    await newAuction.save();

    // Populate the auction with player details
    await newAuction.populate('player', 'name type nationality ratings image battingHand bowlingHand height');
    await newAuction.populate('createdBy', 'username');

    // Initialize auction in Redis for real-time features
     await auctionService.initializeAuction(newAuction._id.toString(), {
       playerId: newAuction.player._id.toString(),
       playerName: newAuction.player.name,
       startingPrice: newAuction.startingPrice,
       currentBid: newAuction.currentBid,
       endTime: newAuction.endTime,
       status: newAuction.status,
       bids: newAuction.bids || []
     });

    // Clear cache
    await cacheService.deletePattern('auctions:*');

    // Broadcast auction creation to all connected clients
    socketService.broadcastAuctionUpdate({
      type: 'auction_created',
      auction: newAuction
    });

    res.status(201).json(newAuction);
  } catch (err) {
    console.error('Error creating auction:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({ msg: err.message });
    }
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Update an auction
 * @route PUT /api/auctions/:id
 * @access Private (Admin only)
 */
exports.updateAuction = async (req, res) => {
  try {
    const {
      startingPrice,
      minimumBidIncrement,
      startTime,
      endTime,
      isActive,
      reservePrice,
      description
    } = req.body;

    // Find auction
    const auction = await Auction.findById(req.params.id);
    if (!auction) {
      return res.status(404).json({ msg: 'Auction not found' });
    }

    // Check if auction has bids - only restrict certain fields for non-admin users
    if (auction.bids.length > 0 && req.user.role !== 'admin') {
      return res.status(400).json({
        msg: 'Cannot update auction that already has bids'
      });
    }
    
    // For auctions with bids, admins can only update certain fields
    const hasBids = auction.bids.length > 0;
    const isAdmin = req.user.role === 'admin';

    // Update fields
    if (startingPrice) {
      auction.startingPrice = startingPrice;
      // If starting price changed and there are no bids, update current bid
      if (auction.currentBid === auction.startingPrice && !hasBids) {
        auction.currentBid = startingPrice;
      }
    }
    
    if (minimumBidIncrement) auction.minimumBidIncrement = minimumBidIncrement;
    
    // Time fields can only be updated if no bids or by admin
    if (!hasBids || isAdmin) {
      if (startTime) auction.startTime = startTime;
      if (endTime) auction.endTime = endTime;
    }
    
    if (isActive !== undefined) auction.isActive = isActive;
    if (reservePrice !== undefined) auction.reservePrice = reservePrice;
    if (description !== undefined) auction.description = description;
    
    // Admin-only updates for auctions with bids
    if (isAdmin && hasBids) {
      // Allow admin to update current bid amount
      if (req.body.currentBid !== undefined) {
        auction.currentBid = req.body.currentBid;
      }
      
      // Allow admin to update current bidder
      if (req.body.currentBidderId) {
        auction.currentBidder = req.body.currentBidderId;
      }
    }

    await auction.save();

    res.json(auction);
  } catch (err) {
    console.error('Error updating auction:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({ msg: err.message });
    }
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Delete an auction
 * @route DELETE /api/auctions/:id
 * @access Private (Admin only)
 */
exports.deleteAuction = async (req, res) => {
  try {
    const auction = await Auction.findById(req.params.id);
    if (!auction) {
      return res.status(404).json({ msg: 'Auction not found' });
    }

    // Check if player exists
    const player = await Player.findById(auction.player);
    
    // Allow deletion if player is missing, regardless of bids
    if (!player) {
      await Auction.deleteOne({ _id: auction._id });
      return res.json({ msg: 'Auction with missing player reference removed' });
    }

    // For auctions with valid player references, check if it has bids
    if (auction.bids.length > 0) {
      return res.status(400).json({
        msg: 'Cannot delete auction that already has bids'
      });
    }

    await Auction.deleteOne({ _id: auction._id });

    res.json({ msg: 'Auction removed' });
  } catch (err) {
    console.error('Error deleting auction:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Place a bid on an auction
 * @route POST /api/auctions/:id/bid
 * @access Private
 */
exports.placeBid = async (req, res) => {
  try {
    const { amount } = req.body;
    console.log('Placing bid:', { auctionId: req.params.id, amount, userId: req.user.id });

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({ msg: 'Bid amount must be greater than 0' });
    }

    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
      }
    }

    if (!team) {
      if (user.role === 'team_owner') {
        return res.status(404).json({
          msg: 'Team not found. Please use the "Verify/Create Team" button to create your team.',
          errorCode: 'TEAM_MISSING'
        });
      }
      return res.status(404).json({ msg: 'Team not found' });
    }

    // Use Redis-based auction service for placing bid
    const result = await auctionService.placeBid(req.params.id, {
      userId: req.user.id,
      username: user.username,
      teamId: team._id.toString(),
      teamBudget: team.budget.totalBudget,
      amount
    });

    if (!result.success) {
      return res.status(400).json({ msg: result.error });
    }

    // Clear relevant cache
    await cacheService.delete(`auction:${req.params.id}`);
    await cacheService.deletePattern('auctions:*');

    res.json({
      msg: `Bid of ${amount} credits placed successfully`,
      auction: result.auction,
      bidId: result.bidId
    });
  } catch (err) {
    console.error('Error placing bid:', err);
    if (err.name === 'ValidationError') {
      return res.status(400).json({ msg: err.message });
    }
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get auctions the current user has bid on
 * @route GET /api/auctions/my-bids
 * @access Private
 */
exports.getMyBids = async (req, res) => {
  try {
    const auctions = await Auction.find({
      'bids.bidder': req.user.id
    })
      .populate('player', 'name type nationality ratings image battingHand bowlingHand height')
      .populate('currentBidder', 'username')
      .sort({ endTime: -1 });

    res.json(auctions);
  } catch (err) {
    console.error('Error getting user bids:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Process completed auctions and assign players to winners
 * @route POST /api/auctions/process-completed
 * @access Private (Admin only)
 */
exports.processCompletedAuctions = async (req, res) => {
  try {
    console.log('Processing completed auctions...');

    const now = new Date();

    // Find all auctions that have ended but still have status 'live'
    const completedAuctions = await Auction.find({
      status: 'live',
      endTime: { $lte: now },
      isActive: true,
      currentBidder: { $ne: null } // Only process auctions with a bidder
    }).populate('player').populate('currentBidder');

    console.log(`Found ${completedAuctions.length} completed auctions to process`);

    const results = {
      processed: 0,
      errors: 0,
      details: []
    };

    // Process each auction
    for (const auction of completedAuctions) {
      try {
        console.log(`Processing auction for player: ${auction.player.name}`);

        // Update auction status
        auction.status = 'completed';
        await auction.save();

        // Get the player
        const player = auction.player;

        // Update player owner and auction win information
        player.owner = auction.currentBidder._id;
        player.isAvailableOnMarket = false;
        player.auctionWin = {
          auctionId: auction._id,
          amount: auction.currentBid,
          date: now
        };

        await player.save();

        console.log(`Player ${player.name} assigned to ${auction.currentBidder.username} for ${auction.currentBid} credits`);

        results.processed++;
        results.details.push({
          auctionId: auction._id,
          playerName: player.name,
          winningBid: auction.currentBid,
          winner: auction.currentBidder.username
        });
      } catch (err) {
        console.error(`Error processing auction ${auction._id}:`, err);
        results.errors++;
        results.details.push({
          auctionId: auction._id,
          error: err.message
        });
      }
    }

    res.json({
      message: `Processed ${results.processed} auctions with ${results.errors} errors`,
      results
    });
  } catch (err) {
    console.error('Error processing completed auctions:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};
