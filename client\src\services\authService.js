import axios from 'axios';

// Use relative URLs instead of absolute URLs to leverage the proxy
// axios.defaults.baseURL = 'http://localhost:5000';

// Set to false to use real database authentication
const USE_MOCK_AUTH = false;

/**
 * Register a new user
 * @param {Object} userData - User data for registration
 * @returns {Promise} - Response from API
 */
export const register = async (userData) => {
  try {
    const response = await axios.post('/api/auth/register', userData);
    
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      return response.data;
    }
    return response.data;
  } catch (error) {
    // Enhanced error handling
    if (error.response) {
      // Server responded with an error status code
      const errorMessage = error.response.data.message || 
                          error.response.data.error || 
                          `Server error: ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      // Request made but no response received
      console.error('Network error:', error.request);
      throw new Error('Network error. Please check your connection and try again.');
    } else {
      // Something happened in setting up the request
      console.error('Request setup error:', error.message);
      throw new Error('An unexpected error occurred. Please try again later.');
    }
  }
};

/**
 * Login a user
 * @param {string} emailOrUsername - User's email or username
 * @param {string} password - User's password
 * @returns {Promise} - Response from API
 */
export const login = async (emailOrUsername, password) => {
  try {
    if (USE_MOCK_AUTH) {
      const response = await axios.post('/api/auth/login-mock', { email: emailOrUsername, password });
      
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
      }
      return response.data;
    } else {
      const response = await axios.post('/api/auth/login', { email: emailOrUsername, password });
      
      if (response.data.token) {
        localStorage.setItem('token', response.data.token);
      }
      return response.data;
    }
  } catch (error) {
    // Enhanced error handling
    if (error.response) {
      // Server responded with an error status code
      const errorMessage = error.response.data.msg || 
                          error.response.data.error || 
                          `Server error: ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      // Request made but no response received
      console.error('Network error:', error.request);
      throw new Error('Network error. Please check your connection and try again.');
    } else {
      // Something happened in setting up the request
      console.error('Request setup error:', error.message);
      throw new Error('An unexpected error occurred. Please try again later.');
    }
  }
};

/**
 * Get current user profile
 * @returns {Promise} - Response from API
 */
export const getCurrentUser = async () => {
  if (USE_MOCK_AUTH) {
    return {
      _id: 'mock_user_id',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'team_owner',
      teamName: 'Test Cricket Team',
      virtualCurrency: 10000,
      profilePicture: '',
      createdAt: new Date().toISOString()
    };
  }
  
  const response = await axios.get('/api/auth/me');
  return response.data;
};

/**
 * Update user profile
 * @param {Object} userData - Updated user data
 * @returns {Promise} - Response from API
 */
export const updateProfile = async (userData) => {
  if (USE_MOCK_AUTH) {
    return {
      user: {
        ...getCurrentUser(),
        ...userData,
        password: undefined,
        newPassword: undefined
      },
      passwordChanged: !!userData.newPassword,
      msg: userData.newPassword 
        ? 'Profile updated successfully. Please login again with your new password.' 
        : 'Profile updated successfully.'
    };
  }

  const response = await axios.put('/api/auth/profile', userData);
  return response.data;
};

/**
 * Set auth token in axios defaults
 * @param {string} token - JWT token
 */
export const setAuthToken = (token) => {
  if (token) {
    axios.defaults.headers.common['x-auth-token'] = token;
  } else {
    delete axios.defaults.headers.common['x-auth-token'];
  }
};

// Create a named object for export
const authService = {
  register,
  login,
  getCurrentUser,
  updateProfile,
  setAuthToken
};

export default authService;
