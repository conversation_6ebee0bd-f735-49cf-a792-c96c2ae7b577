/**
 * OCR Routes
 * 
 * Clean, simple routes for OCR processing
 */

const express = require('express');
const router = express.Router();
const ocrController = require('../controllers/ocrController');
const { authenticateToken } = require('../middlewares/auth');

/**
 * @route   POST /api/ocr/process
 * @desc    Process an image with OCR to extract cricket scorecard data
 * @access  Private
 */
router.post('/process', authenticateToken, ocrController.processImage);

/**
 * @route   GET /api/ocr/test
 * @desc    Test OCR service
 * @access  Public
 */
router.get('/test', ocrController.testOCR);

module.exports = router;
