/**
 * Local Database Verification Script
 * 
 * This script helps you verify what data exists in your local MongoDB
 * before migrating to Atlas.
 * 
 * Usage: node scripts/checkLocalDatabase.js
 */

const mongoose = require('mongoose');

// Import all models
const User = require('../models/User');
const Team = require('../models/Team');
const Player = require('../models/Player');
const Tournament = require('../models/Tournament');
const Auction = require('../models/Auction');
const Template = require('../models/Template');
const OCRSettings = require('../models/OCRSettings');

// Try different common database names
const POSSIBLE_DBS = [
  'mongodb://localhost:27017/cricket24',
  'mongodb://localhost:27017/rpl-new',
  'mongodb://localhost:27017/rpl',
  'mongodb://localhost:27017/cricket'
];

class DatabaseChecker {
  constructor() {
    this.foundDatabase = null;
    this.collections = [
      { name: 'Users', model: User },
      { name: 'Teams', model: Team },
      { name: 'Players', model: Player },
      { name: 'Tournaments', model: Tournament },
      { name: 'Auctions', model: Auction },
      { name: 'Templates', model: Template },
      { name: 'OCRSettings', model: OCRSettings }
    ];
  }

  async findDatabase() {
    console.log('🔍 Searching for your local MongoDB database...\n');
    
    for (const dbUrl of POSSIBLE_DBS) {
      try {
        console.log(`Trying: ${dbUrl}`);
        await mongoose.connect(dbUrl);
        
        // Check if any of our collections have data
        let hasData = false;
        for (const collection of this.collections) {
          const count = await collection.model.countDocuments();
          if (count > 0) {
            hasData = true;
            break;
          }
        }
        
        if (hasData) {
          console.log(`✅ Found database with data: ${dbUrl}\n`);
          this.foundDatabase = dbUrl;
          return true;
        } else {
          console.log(`   📭 Database exists but appears empty`);
        }
        
        await mongoose.disconnect();
      } catch (error) {
        console.log(`   ❌ Cannot connect: ${error.message}`);
      }
    }
    
    return false;
  }

  async checkCollections() {
    if (!this.foundDatabase) {
      console.log('❌ No database found with data');
      return;
    }

    console.log('📊 DATABASE ANALYSIS');
    console.log('='.repeat(50));
    console.log(`Database: ${this.foundDatabase}`);
    console.log('='.repeat(50));

    let totalDocuments = 0;
    const collectionStats = [];

    for (const collection of this.collections) {
      try {
        const count = await collection.model.countDocuments();
        const sampleDoc = count > 0 ? await collection.model.findOne().lean() : null;
        
        collectionStats.push({
          name: collection.name,
          count,
          hasData: count > 0,
          sampleFields: sampleDoc ? Object.keys(sampleDoc).slice(0, 5) : []
        });
        
        totalDocuments += count;
        
        if (count > 0) {
          console.log(`✅ ${collection.name.padEnd(15)}: ${count.toString().padStart(4)} documents`);
          if (sampleDoc) {
            console.log(`   Sample fields: ${Object.keys(sampleDoc).slice(0, 5).join(', ')}`);
          }
        } else {
          console.log(`📭 ${collection.name.padEnd(15)}: ${count.toString().padStart(4)} documents (empty)`);
        }
      } catch (error) {
        console.log(`❌ ${collection.name.padEnd(15)}: Error - ${error.message}`);
      }
    }

    console.log('='.repeat(50));
    console.log(`📈 Total documents: ${totalDocuments}`);
    
    // Show detailed analysis
    this.showDetailedAnalysis(collectionStats, totalDocuments);
  }

  showDetailedAnalysis(stats, total) {
    console.log('\n🔍 DETAILED ANALYSIS');
    console.log('='.repeat(50));
    
    const nonEmptyCollections = stats.filter(s => s.hasData);
    const emptyCollections = stats.filter(s => !s.hasData);
    
    if (nonEmptyCollections.length > 0) {
      console.log(`✅ Collections with data: ${nonEmptyCollections.length}`);
      nonEmptyCollections.forEach(col => {
        console.log(`   • ${col.name}: ${col.count} documents`);
      });
    }
    
    if (emptyCollections.length > 0) {
      console.log(`\n📭 Empty collections: ${emptyCollections.length}`);
      emptyCollections.forEach(col => {
        console.log(`   • ${col.name}`);
      });
    }
    
    // Migration readiness assessment
    console.log('\n🎯 MIGRATION READINESS');
    console.log('='.repeat(50));
    
    if (total === 0) {
      console.log('❌ No data found - nothing to migrate');
      console.log('💡 Make sure you\'re connected to the right database');
    } else if (total < 10) {
      console.log('⚠️  Very little data found');
      console.log('💡 This might be a test database');
    } else {
      console.log('✅ Good amount of data found - ready for migration!');
      console.log(`📊 ${total} documents across ${nonEmptyCollections.length} collections`);
    }
    
    // Show next steps
    console.log('\n🚀 NEXT STEPS');
    console.log('='.repeat(50));
    
    if (total > 0) {
      console.log('1. 💾 Create backup: node scripts/backupLocalData.js');
      console.log('2. 🔧 Set up Atlas connection in .env file');
      console.log('3. 🚀 Run migration: node scripts/migrateToAtlas.js');
      console.log('4. ✅ Verify migration completed successfully');
      
      console.log('\n📝 IMPORTANT NOTES:');
      console.log('• Always backup before migrating!');
      console.log('• Update the LOCAL_DB variable in migration scripts if needed');
      console.log(`• Your database URL: ${this.foundDatabase}`);
    } else {
      console.log('1. 🔍 Check if MongoDB is running');
      console.log('2. 🔍 Verify database name and connection');
      console.log('3. 🔍 Use MongoDB Compass to browse your databases');
    }
  }

  async listAllDatabases() {
    try {
      console.log('\n📋 AVAILABLE DATABASES');
      console.log('='.repeat(50));
      
      await mongoose.connect('mongodb://localhost:27017/');
      const admin = mongoose.connection.db.admin();
      const result = await admin.listDatabases();
      
      console.log('Found databases:');
      result.databases.forEach(db => {
        console.log(`  • ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
      });
      
      await mongoose.disconnect();
    } catch (error) {
      console.log('❌ Could not list databases:', error.message);
    }
  }

  async cleanup() {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

// Main execution
async function main() {
  const checker = new DatabaseChecker();
  
  try {
    console.log('🔍 LOCAL MONGODB DATABASE CHECKER');
    console.log('='.repeat(50));
    console.log('This script will help you find and analyze your local MongoDB data\n');
    
    // First, try to find the database with data
    const found = await checker.findDatabase();
    
    if (found) {
      // Analyze the collections
      await checker.checkCollections();
    } else {
      console.log('\n❌ No database found with RPL data');
      console.log('\n💡 Let\'s check what databases are available...');
      await checker.listAllDatabases();
      
      console.log('\n🔧 TROUBLESHOOTING:');
      console.log('1. Make sure MongoDB is running');
      console.log('2. Check if you have data in MongoDB Compass');
      console.log('3. Verify the database name in the migration scripts');
      console.log('4. Try connecting with MongoDB Compass first');
    }
    
  } catch (error) {
    console.error('\n❌ Error during database check:', error.message);
    console.log('\n💡 Make sure MongoDB is running and accessible');
  } finally {
    await checker.cleanup();
    process.exit(0);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⏹️  Database check interrupted by user');
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = DatabaseChecker;