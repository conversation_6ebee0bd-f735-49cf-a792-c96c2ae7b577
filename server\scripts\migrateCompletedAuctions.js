/**
 * Migration script to process existing completed auctions
 * This script will find all completed auctions and assign players to winners
 * 
 * Usage: node scripts/migrateCompletedAuctions.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Auction = require('../models/Auction');
const Player = require('../models/Player');

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB connected');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    return false;
  }
};

// Process completed auctions
const processExistingCompletedAuctions = async () => {
  try {
    console.log('Processing existing completed auctions...');
    
    const now = new Date();
    
    // Find all auctions that are completed or have ended
    const completedAuctions = await Auction.find({
      $or: [
        { status: 'completed' },
        { 
          status: 'live',
          endTime: { $lte: now }
        }
      ],
      isActive: true,
      currentBidder: { $ne: null } // Only process auctions with a bidder
    }).populate('player').populate('currentBidder');
    
    console.log(`Found ${completedAuctions.length} completed auctions to process`);
    
    const results = {
      processed: 0,
      skipped: 0,
      errors: 0,
      details: []
    };
    
    // Process each auction
    for (const auction of completedAuctions) {
      try {
        console.log(`Processing auction for player: ${auction.player.name}`);
        
        // Update auction status if it's still 'live'
        if (auction.status === 'live') {
          auction.status = 'completed';
          await auction.save();
        }
        
        // Get the player
        const player = auction.player;
        
        // Check if player already has auction win information
        if (player.auctionWin && player.auctionWin.auctionId) {
          console.log(`Player ${player.name} already has auction win information. Skipping.`);
          results.skipped++;
          results.details.push({
            auctionId: auction._id,
            playerName: player.name,
            status: 'skipped',
            reason: 'Player already has auction win information'
          });
          continue;
        }
        
        // Update player owner and auction win information
        player.owner = auction.currentBidder._id;
        player.isAvailableOnMarket = false;
        player.auctionWin = {
          auctionId: auction._id,
          amount: auction.currentBid,
          date: auction.endTime || now
        };
        
        await player.save();
        
        console.log(`Player ${player.name} assigned to ${auction.currentBidder.username} for ${auction.currentBid} credits`);
        
        results.processed++;
        results.details.push({
          auctionId: auction._id,
          playerName: player.name,
          winningBid: auction.currentBid,
          winner: auction.currentBidder.username,
          status: 'processed'
        });
      } catch (err) {
        console.error(`Error processing auction ${auction._id}:`, err);
        results.errors++;
        results.details.push({
          auctionId: auction._id,
          error: err.message,
          status: 'error'
        });
      }
    }
    
    console.log(`Migration completed:`);
    console.log(`- Processed: ${results.processed}`);
    console.log(`- Skipped: ${results.skipped}`);
    console.log(`- Errors: ${results.errors}`);
    
    return results;
  } catch (err) {
    console.error('Error processing completed auctions:', err);
    throw err;
  }
};

// Main function
const main = async () => {
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) {
      console.error('Failed to connect to database. Exiting...');
      process.exit(1);
    }
    
    // Process existing completed auctions
    await processExistingCompletedAuctions();
    
    // Disconnect from database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Run the script
main();
