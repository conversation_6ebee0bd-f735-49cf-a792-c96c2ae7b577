import axios from 'axios';
import { API_URL } from '../config';

const API = axios.create({
  baseURL: `${API_URL}/tournaments`,
});

// Add auth token to requests if available
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');

  if (token) {
    config.headers['x-auth-token'] = token;
  }

  return config;
});

// Get all tournaments with optional filters
export const getTournaments = async (filters = {}) => {
  try {
    const { page = 1, limit = 10, status, format, search } = filters;

    const response = await API.get('/', {
      params: {
        page,
        limit,
        status,
        format,
        search
      }
    });

    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get tournament by ID
export const getTournamentById = async (id) => {
  try {
    const response = await API.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Create a new tournament
export const createTournament = async (tournamentData) => {
  try {
    const response = await API.post('/', tournamentData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Update a tournament
export const updateTournament = async (id, tournamentData) => {
  try {
    const response = await API.put(`/${id}`, tournamentData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Delete a tournament
export const deleteTournament = async (id) => {
  try {
    const response = await API.delete(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Register team for tournament
export const registerTeam = async (id) => {
  try {
    const response = await API.post(`/${id}/register`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Unregister team from tournament
export const unregisterTeam = async (id) => {
  try {
    const response = await API.post(`/${id}/unregister`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get next round number for a match between two teams
export const getNextRoundNumber = async (tournamentId, team1Id, team2Id) => {
  try {
    const response = await API.get(`/${tournamentId}/next-round/${team1Id}/${team2Id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Add a match result
export const addMatch = async (tournamentId, matchData) => {
  try {
    const response = await API.post(`/${tournamentId}/matches`, matchData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Process match outcome with advanced calculation
export const processMatchOutcome = async (tournamentId, matchData) => {
  try {
    const response = await axios.post(`${API_URL}/match-outcome/process-match`, {
      ...matchData,
      tournamentId
    }, {
      headers: {
        'x-auth-token': localStorage.getItem('token')
      }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get matches between two teams
export const getMatchesBetweenTeams = async (tournamentId, team1Id, team2Id) => {
  try {
    const response = await API.get(`/${tournamentId}/matches/${team1Id}/${team2Id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export default {
  getTournaments,
  getTournamentById,
  createTournament,
  updateTournament,
  deleteTournament,
  registerTeam,
  unregisterTeam,
  getNextRoundNumber,
  addMatch,
  processMatchOutcome,
  getMatchesBetweenTeams
};
