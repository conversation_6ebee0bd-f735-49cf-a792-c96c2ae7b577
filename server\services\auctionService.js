const cacheService = require('./cacheService');
const redisService = require('../config/redis');
const Auction = require('../models/Auction');

class AuctionService {
  constructor() {
    this.activeAuctions = new Map();
    this.isRedisAvailable = false;
    this.isInitialized = false;
    this.lastRedisCheck = 0;
    this.redisCheckInterval = 30000; // Check Redis status every 30 seconds
  }

  async initialize() {
    if (this.isInitialized) {
      // Only recheck Redis status periodically
      const now = Date.now();
      if (now - this.lastRedisCheck < this.redisCheckInterval) {
        return;
      }
      this.lastRedisCheck = now;
    }

    this.isRedisAvailable = redisService.isRedisConnected();
    if (!this.isRedisAvailable && !this.isInitialized) {
      console.log('⚠️ AuctionService running without Redis - using memory-only storage');
    }
    this.isInitialized = true;
  }

  async initializeAuction(auctionId, auctionData = null) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      let auction;
      if (auctionData) {
        // Use provided auction data (for new auctions)
        auction = auctionData;
      } else {
        // Fetch from database
        auction = await Auction.findById(auctionId)
          .populate('player', 'name')
          .populate('currentBidder', 'username');
      }
      
      if (!auction) return null;

      const auctionState = {
        id: auctionId,
        playerId: auction.playerId || auction.player?._id,
        playerName: auction.playerName || auction.player?.name,
        currentBid: auction.currentBid,
        currentBidder: auction.currentBidder,
        bidsCount: auction.bids?.length || 0,
        timeRemaining: this.getTimeRemaining(auction.endTime),
        status: this.getAuctionStatus(auction),
        lastUpdate: new Date(),
        bids: auction.bids || []
      };

      // Store in cache (Redis or memory)
      await cacheService.setAuctionState(auctionId, auctionState);
      
      // Store in memory for quick access
      this.activeAuctions.set(auctionId, auctionState);
      
      return auctionState;
    } catch (error) {
      console.error('Error initializing auction:', error);
      return null;
    }
  }

  async placeBid(auctionId, userId, amount, username) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      // Get current auction state from cache
      let auctionState = await cacheService.getAuctionState(auctionId);
      
      if (!auctionState) {
        auctionState = await this.initializeAuction(auctionId);
      }

      if (!auctionState) {
        throw new Error('Auction not found');
      }

      // Validate bid
      if (amount <= auctionState.currentBid) {
        throw new Error('Bid must be higher than current bid');
      }

      // Create bid object
      const newBid = {
        bidder: userId,
        bidderName: username,
        amount: amount,
        timestamp: new Date()
      };

      // Update auction state
      auctionState.currentBid = amount;
      auctionState.currentBidder = { _id: userId, username };
      auctionState.bidsCount += 1;
      auctionState.lastUpdate = new Date();
      
      // Add bid to bids array
      if (!auctionState.bids) auctionState.bids = [];
      auctionState.bids.push(newBid);

      // Update cache
      await cacheService.setAuctionState(auctionId, auctionState);
      this.activeAuctions.set(auctionId, auctionState);

      // Publish real-time update
      await this.publishBidUpdate(auctionId, {
        type: 'NEW_BID',
        auctionId,
        currentBid: amount,
        currentBidder: { _id: userId, username },
        bidsCount: auctionState.bidsCount,
        timestamp: new Date()
      });

      return auctionState;
    } catch (error) {
      console.error('Error placing bid:', error);
      throw error;
    }
  }

  async publishBidUpdate(auctionId, data) {
    try {
      if (this.isRedisAvailable) {
        const publisher = redisService.getPublisher();
        if (publisher) {
          await publisher.publish(`auction:${auctionId}`, JSON.stringify(data));
          await publisher.publish('auction:global', JSON.stringify(data));
        }
      } else {
        // If Redis is not available, we'll rely on Socket.io direct broadcasting
        console.log('Redis not available - bid update will be handled by Socket.io');
      }
    } catch (error) {
      console.error('Error publishing bid update:', error);
    }
  }

  getTimeRemaining(endTime) {
    const now = new Date();
    const end = new Date(endTime);
    const remaining = end - now;
    return Math.max(0, remaining);
  }

  getAuctionStatus(auction) {
    const now = new Date();
    if (!auction.isActive) return 'cancelled';
    if (now < auction.startTime) return 'scheduled';
    if (now >= auction.startTime && now < auction.endTime) return 'live';
    return 'completed';
  }

  async getActiveAuctions() {
    try {
      if (!this.isInitialized) await this.initialize();
      
      if (this.isRedisAvailable) {
        const auctions = await Auction.find({
          status: 'live',
          endTime: { $gt: new Date() }
        }).populate('player', 'name').populate('currentBidder', 'username');

        const activeStates = [];
        for (const auction of auctions) {
          let state = await cacheService.getAuctionState(auction._id);
          if (!state) {
            state = await this.initializeAuction(auction._id);
          }
          if (state) {
            activeStates.push(state);
          }
        }

        return activeStates;
      } else {
        // Fallback to memory storage
        const auctions = [];
        for (const [auctionId, auctionState] of this.activeAuctions) {
          if (auctionState.status === 'live') {
            auctions.push(auctionState);
          }
        }
        return auctions;
      }
    } catch (error) {
      console.error('Error getting active auctions:', error);
      return [];
    }
  }

  async updateAuctionTimer(auctionId) {
    try {
      const auction = await Auction.findById(auctionId);
      if (!auction) return null;

      const timeRemaining = this.getTimeRemaining(auction.endTime);
      const status = this.getAuctionStatus(auction);

      // Update cache with new time
      let auctionState = await cacheService.getAuctionState(auctionId);
      if (auctionState) {
        auctionState.timeRemaining = timeRemaining;
        auctionState.status = status;
        auctionState.lastUpdate = new Date();
        await cacheService.setAuctionState(auctionId, auctionState);

        // Publish timer update
        await this.publishBidUpdate(auctionId, {
          type: 'TIMER_UPDATE',
          auctionId,
          timeRemaining,
          status,
          timestamp: new Date()
        });
      }

      return { timeRemaining, status };
    } catch (error) {
      console.error('Error updating auction timer:', error);
      return null;
    }
  }

  async processCompletedAuctions() {
    try {
      const completedAuctions = await Auction.find({
        status: 'live',
        endTime: { $lt: new Date() }
      });

      for (const auction of completedAuctions) {
        // Update auction status
        auction.status = 'completed';
        await auction.save();

        // Clear from cache
        await cacheService.del(`auction:${auction._id}`);
        this.activeAuctions.delete(auction._id.toString());

        // Publish completion event
        await this.publishBidUpdate(auction._id, {
          type: 'AUCTION_COMPLETED',
          auctionId: auction._id,
          finalBid: auction.currentBid,
          winner: auction.currentBidder,
          timestamp: new Date()
        });
      }

      return completedAuctions.length;
    } catch (error) {
      console.error('Error processing completed auctions:', error);
      return 0;
    }
  }

  async updateAuctionTimers() {
    try {
      if (!this.isInitialized) await this.initialize();
      
      const activeAuctions = await this.getActiveAuctions();
      
      for (const auctionState of activeAuctions) {
        // Get auction from database to get accurate endTime
        const auction = await Auction.findById(auctionState.id);
        if (!auction) continue;
        
        const timeRemaining = this.getTimeRemaining(auction.endTime);
        
        if (timeRemaining <= 0) {
          // Auction has ended
          auctionState.status = 'completed';
          auctionState.timeRemaining = 0;
          
          await this.processCompletedAuction(auctionState.id);
        } else {
          // Update time remaining
          auctionState.timeRemaining = timeRemaining;
        }
        
        // Update cache
        await cacheService.setAuctionState(auctionState.id, auctionState);
        this.activeAuctions.set(auctionState.id, auctionState);
        
        // Publish timer update
        await this.publishBidUpdate(auctionState.id, {
          type: 'timer_update',
          auctionId: auctionState.id,
          timeRemaining: auctionState.timeRemaining,
          status: auctionState.status
        });
      }
    } catch (error) {
      console.error('Error updating auction timers:', error);
    }
  }

  async processCompletedAuction(auctionId) {
    try {
      if (!this.isInitialized) await this.initialize();
      
      const auction = await Auction.findById(auctionId);
      if (!auction) return;

      // Update auction status in database
      auction.status = 'completed';
      auction.isActive = false;
      await auction.save();

      // Remove from active auctions
      this.activeAuctions.delete(auctionId);
      await cacheService.deleteAuctionState(auctionId);

      // Publish completion event
      await this.publishBidUpdate(auctionId, {
        type: 'auction_completed',
        auctionId,
        finalBid: auction.currentBid,
        winner: auction.currentBidder
      });

      console.log(`Auction ${auctionId} completed`);
    } catch (error) {
      console.error('Error processing completed auction:', error);
    }
  }

  // Start auction timer service
  startTimerService() {
    setInterval(async () => {
      await this.updateAuctionTimers();
    }, 1000); // Update every second
  }
}

module.exports = new AuctionService();