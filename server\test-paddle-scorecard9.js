const OCRService = require('./services/ocrService');
const path = require('path');

async function testPaddleOCROnScorecard9() {
  console.log('🚀 Testing PaddleOCR on scorecard9.png...\n');

  try {
    // Test image path
    const imagePath = path.join(__dirname, 'uploads/scorecards/scorecard9.png');
    
    console.log(`📸 Testing with image: ${imagePath}`);

    // Initialize OCR service
    const ocrService = new OCRService();

    // Test with PaddleOCR coordinate extraction
    console.log('\n🧪 PaddleOCR Coordinate Extraction');
    console.log('================================================');
    
    try {
      const paddleResult = await ocrService.processImageWithPaddleOCRCoordinates(imagePath);
      
      console.log('✅ PaddleOCR Coordinate Results:');
      console.log(`Team 1: "${paddleResult.team1}"`);
      console.log(`Team 2: "${paddleResult.team2}"`);
      
      // Look for JOHN MORTIMORE in the batsmen lists
      console.log('\n👥 Looking for JOHN MORTIMORE:');
      
      // Check team 1 batsmen
      const team1Match = paddleResult.team1Batsmen.find(player => 
        player.name.toUpperCase().includes('MORTIMORE') || 
        player.name.toUpperCase().includes('JOHN')
      );
      
      if (team1Match) {
        console.log(`Found in Team 1: ${team1Match.name} -> ${team1Match.runs}(${team1Match.balls})`);
      }
      
      // Check team 2 batsmen
      const team2Match = paddleResult.team2Batsmen.find(player => 
        player.name.toUpperCase().includes('MORTIMORE') || 
        player.name.toUpperCase().includes('JOHN')
      );
      
      if (team2Match) {
        console.log(`Found in Team 2: ${team2Match.name} -> ${team2Match.runs}(${team2Match.balls})`);
      }
      
      if (!team1Match && !team2Match) {
        console.log('❌ JOHN MORTIMORE not found in either team');
        
        // Print all batsmen to help identify
        console.log('\nAll Team 1 Batsmen:');
        paddleResult.team1Batsmen.forEach(player => {
          console.log(`${player.name} -> ${player.runs}(${player.balls})`);
        });
        
        console.log('\nAll Team 2 Batsmen:');
        paddleResult.team2Batsmen.forEach(player => {
          console.log(`${player.name} -> ${player.runs}(${player.balls})`);
        });
      }

    } catch (paddleError) {
      console.log(`❌ PaddleOCR Test Failed: ${paddleError.message}`);
      console.error(paddleError);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testPaddleOCROnScorecard9();