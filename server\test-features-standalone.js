const path = require('path');
const fs = require('fs');

// Mock Player data for testing
const mockPlayers = [
  { _id: '1', name: '<PERSON>', team: 'Team A' },
  { _id: '2', name: '<PERSON>', team: 'Team A' },
  { _id: '3', name: '<PERSON>', team: 'Team B' },
  { _id: '4', name: '<PERSON>', team: 'Team B' },
  { _id: '5', name: '<PERSON>', team: 'Team C' },
  { _id: '6', name: '<PERSON>', team: 'Team C' }
];

// Mock the Player model
const Player = {
  find: (query) => {
    return Promise.resolve(mockPlayers.filter(player => {
      if (query.name && query.name.$regex) {
        const regex = new RegExp(query.name.$regex, query.name.$options || 'i');
        return regex.test(player.name);
      }
      return true;
    }));
  },
  findById: (id) => {
    return Promise.resolve(mockPlayers.find(player => player._id === id));
  }
};

// Import service classes
const PlayerMatchingService = require('./services/playerMatchingService');
const MatchOutcomeService = require('./services/matchOutcomeService');

// Create service instances
const playerMatchingService = new PlayerMatchingService();
const matchOutcomeService = new MatchOutcomeService();

async function testPlayerMatching() {
  console.log('\n🔍 TESTING PLAYER MATCHING');
  console.log('=' .repeat(50));
  
  const testCases = [
    { input: 'John Smith', expected: 'John Smith' },
    { input: 'john smith', expected: 'John Smith' },
    { input: 'J Smith', expected: 'John Smith' },
    { input: 'Mike J', expected: 'Mike Johnson' },
    { input: 'David', expected: 'David Wilson' },
    { input: 'Unknown Player', expected: null }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    try {
      console.log(`\nTesting: "${testCase.input}"`);
      
      // Test player matching
      const result = await playerMatchingService.matchPlayer(testCase.input);
      
      if (result && result.player) {
        console.log(`   Found: ${result.player.name} (confidence: ${result.confidence})`);
        if (result.player.name === testCase.expected) {
          console.log('   ✅ PASSED');
          passed++;
        } else {
          console.log(`   ❌ FAILED (expected: ${testCase.expected})`);
          failed++;
        }
      } else {
        console.log('   No match found');
        if (testCase.expected === null) {
          console.log('   ✅ PASSED');
          passed++;
        } else {
          console.log(`   ❌ FAILED (expected: ${testCase.expected})`);
          failed++;
        }
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      failed++;
    }
  }
  
  return { passed, failed };
}

async function testMatchOutcome() {
  console.log('\n🏏 TESTING MATCH OUTCOME CALCULATION');
  console.log('=' .repeat(50));
  
  const testCases = [
    {
      name: 'Team A wins by runs',
      teamA: { name: 'Team A', runs: 150, wickets: 5, overs: 20 },
      teamB: { name: 'Team B', runs: 120, wickets: 10, overs: 18.3 },
      expected: 'Team A won by 30 runs'
    },
    {
      name: 'Team B wins by wickets',
      teamA: { name: 'Team A', runs: 140, wickets: 10, overs: 19.2 },
      teamB: { name: 'Team B', runs: 141, wickets: 6, overs: 18.5 },
      expected: 'Team B won by 4 wickets'
    },
    {
      name: 'Match tied',
      teamA: { name: 'Team A', runs: 150, wickets: 8, overs: 20 },
      teamB: { name: 'Team B', runs: 150, wickets: 9, overs: 20 },
      expected: 'Match tied'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    try {
      console.log(`\nTesting: ${testCase.name}`);
      console.log(`   Team A: ${testCase.teamA.runs}/${testCase.teamA.wickets} (${testCase.teamA.overs})`);
      console.log(`   Team B: ${testCase.teamB.runs}/${testCase.teamB.wickets} (${testCase.teamB.overs})`);
      
      // Create mock OCR data format
      const mockOcrData = {
        team1Score: {
          runs: testCase.teamA.runs,
          wickets: testCase.teamA.wickets,
          overs: testCase.teamA.overs
        },
        team2Score: {
          runs: testCase.teamB.runs,
          wickets: testCase.teamB.wickets,
          overs: testCase.teamB.overs
        },
        team1Name: testCase.teamA.name,
        team2Name: testCase.teamB.name,
        resultText: ''
      };
      
      const result = await matchOutcomeService.calculateMatchOutcome(mockOcrData, 'test-tournament');
      
      console.log(`   Result: ${result.outcome}`);
      
      if (result.outcome === testCase.expected) {
        console.log('   ✅ PASSED');
        passed++;
      } else {
        console.log(`   ❌ FAILED (expected: ${testCase.expected})`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      failed++;
    }
  }
  
  return { passed, failed };
}

async function runAllTests() {
  console.log('🚀 STARTING STANDALONE FEATURE TESTS');
  console.log('=' .repeat(60));
  console.log('Note: These tests use mock data and do not require database connection');
  
  try {
    const playerResults = await testPlayerMatching();
    const outcomeResults = await testMatchOutcome();
    
    const totalPassed = playerResults.passed + outcomeResults.passed;
    const totalFailed = playerResults.failed + outcomeResults.failed;
    const totalTests = totalPassed + totalFailed;
    const successRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0;
    
    console.log('\n📊 FINAL TEST RESULTS');
    console.log('=' .repeat(30));
    console.log(`Player Matching - Passed: ${playerResults.passed}, Failed: ${playerResults.failed}`);
    console.log(`Match Outcome - Passed: ${outcomeResults.passed}, Failed: ${outcomeResults.failed}`);
    console.log(`\nTotal Tests: ${totalTests}`);
    console.log(`Tests Passed: ${totalPassed}`);
    console.log(`Tests Failed: ${totalFailed}`);
    console.log(`Success Rate: ${successRate}%`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Both features are working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Review the implementation.');
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    console.error(error.stack);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests, testPlayerMatching, testMatchOutcome };