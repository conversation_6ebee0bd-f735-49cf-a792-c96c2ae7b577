Player matching service initialized successfully
🧪 CLEAN OCR EXTRACTION TEST
============================
Testing actual extraction output without hardcoded team names
Scorecard: scorecard1.jpg

📸 Processing: scorecard1.jpg
⏳ Extracting data...

Processing image with OCR.Space: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
🔧 Enhancing image for better OCR accuracy...
Enhancing image for OCR: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1.jpg
Enhanced image saved to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1_enhanced.jpg
✅ Using enhanced image: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\uploads\scorecards\scorecard1_enhanced.jpg
Calling OCR.Space API...
OCR.Space API response received
Saved OCR.Space raw response to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\ocr-output\extracted\2025-06-09T15-40-13-279Z_scorecard1_ocrspace_raw.json
Parsing OCR.Space response...
OCR.Space extracted 514 characters of text
Coordinate overlay available: true
Using coordinate-based extraction...
Starting coordinate-based cricket data extraction...
Parsed 190 text elements from overlay (including line-level elements)
Parsed 190 text elements with coordinates
Starting priority-based venue extraction...
Found venue with AT pattern: "KIA OVAL" from line: "T20 COPY AT KIA OVAL"
Extracted player of match: "Ali Akbar"
Extracted overs - Team 1: 20, Team 2: 12.5
Extracting scores using coordinate-based position detection...
Score element: 209-7 at (1810, 265)
Score element: 3-21 at (1869, 327)
Score element: 2-48 at (1869, 381)
Score element: 1-15 at (1869, 435)
Score element: 1-46 at (1869, 488)
Score element: 3-24 at (1869, 649)
Score element: 2-6 at (1875, 700)
Score element: 2-22 at (1872, 756)
Score element: 1-5 at (1881, 807)
Image dimensions detected: 2028 x 1110
Standalone number 60 at (848, 327) not in team score area - skipped
Standalone number 60 at (848, 327) not in team score area - skipped
Standalone number 29 at (851, 381) not in team score area - skipped
Standalone number 17 at (848, 649) not in team score area - skipped
Standalone number 17 at (848, 649) not in team score area - skipped
Standalone number 16 at (848, 700) not in team score area - skipped
Standalone number 16 at (848, 700) not in team score area - skipped
Standalone number 10 at (848, 810) not in team score area - skipped
Standalone number 10 at (848, 810) not in team score area - skipped
Standalone number 40 at (926, 327) not in team score area - skipped
Standalone number 14 at (926, 381) not in team score area - skipped
Standalone number 10 at (926, 435) not in team score area - skipped
Standalone number 18 at (926, 488) not in team score area - skipped
Standalone number 11 at (926, 649) not in team score area - skipped
Standalone number 11 at (926, 804) not in team score area - skipped
Element "96" at 91.9%, 52.8% is in team score area
Team score detected at (1863, 586): 96 runs [91.9%, 52.8%]
Standalone number 113 at (1814, 908) not in team score area - skipped
Found 10 score elements with coordinates
Score: 209-7 at (1810, 265)
Score: 3-21 at (1869, 327)
Score: 2-48 at (1869, 381)
Score: 1-15 at (1869, 435)
Score: 1-46 at (1869, 488)
Score: 3-24 at (1869, 649)
Score: 2-6 at (1875, 700)
Score: 2-22 at (1872, 756)
Score: 1-5 at (1881, 807)
Score: 96-10 at (1863, 586)
Extracted overs - Team 1: 20, Team 2: 12.5
OVERS data: Team 1: 20, Team 2: 12.5
Found 2 OVERS markers:
"OVERS: 20.0" at (970, 277)
"OVERS: 12.5" at (970, 595)
OVERS markers at Y=277, Y=595, distance=318, buffer=25.4
Smart boundaries calculated:
Team 1 score section: Y 0-302.44
Team 2 score section: Y 569.56-1110
Team 1 scores in section: 209-7
Team 2 scores in section: 3-24, 2-6, 2-22, 1-5, 96-10
Selecting best team score from: 209-7 (dash)
Found 1 dash scores, selecting highest: 209-7
Team 1 score: 209-7 from section (dash)
Selecting best team score from: 3-24 (dash), 2-6 (dash), 2-22 (dash), 1-5 (dash), 96-10 (standalone)
Found 1 standalone scores, selecting first: 96-10
Team 2 score: 96-10 from section (standalone)
Using Team 1/Team 2 approach with OVERS markers as section boundaries...
Found 2 OVERS markers: [ '"OVERS: 20.0" at (970, 277)', '"OVERS: 12.5" at (970, 595)' ]
Using section-based approach - Team 1: "Team 1", Team 2: "Team 2"
Users will map actual team names in the Add Match Result form
Extracting player statistics using coordinate analysis...
Looking for teams: "Team 1" and "Team 2"
Found 2 OVERS markers: [ '"OVERS: 20.0" at (970, 277)', '"OVERS: 12.5" at (970, 595)' ]
Image height detected: 1110px
OVERS markers at Y=277, Y=595, distance=318, buffer=25.4
Found 5 batsmen between Y 277-595 (X < 500): [
  '"YOUNIS KHAN" at (140, 327)',
  '"ALI AKBAR" at (140, 380)',
  '"NAVJOT SINGH SIDHU" at (143, 435)',
  '"KEITH MILLER" at (140, 488)',
  '"INVINCIBLES" at (134, 589)'
]
🧠 Applying intelligent player filtering...
Analyzing "YOUNIS KHAN": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: false,
  decision: 'KEEP'
}
Analyzing "ALI AKBAR": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: false,
  decision: 'KEEP'
}
Analyzing "NAVJOT SINGH SIDHU": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: false,
  decision: 'KEEP'
}
Analyzing "KEITH MILLER": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: false,
  decision: 'KEEP'
}
Analyzing "INVINCIBLES": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: true,
  decision: 'KEEP'
}
🧠 Intelligent filtering: 5 → 5 players
Found 4 batsmen between Y 595-1110 (X < 500): [
  '"JOHN REID" at (140, 649)',
  '"VAIBHAV BANGA" at (137, 700)',
  '"BRENDON MCCULLUM" at (140, 753)',
  '"ALAN MELVILLE" at (140, 807)'
]
🧠 Applying intelligent player filtering...
Analyzing "JOHN REID": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: true,
  decision: 'KEEP'
}
Analyzing "VAIBHAV BANGA": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: true,
  decision: 'KEEP'
}
Analyzing "BRENDON MCCULLUM": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: true,
  decision: 'KEEP'
}
Analyzing "ALAN MELVILLE": {
  hasStats: true,
  notIsolated: false,
  inBattingArea: true,
  isTeam2Area: true,
  decision: 'KEEP'
}
🧠 Intelligent filtering: 4 → 4 players
Using smart OVERS-relative boundaries:
- Team 1 batsmen: Y 277-595 (X < 500)
- Team 2 batsmen: Y 595-1110 (X < 500)
- Team 2 bowlers: Y 277-595 (X > 500)
- Team 1 bowlers: Y 595-1110 (X > 500)
Extracted players - Team 1 Batsmen: 4, Team 2 Batsmen: 4
Team 1 Batsmen: [YOUNIS KHAN, ALI AKBAR, NAVJOT SINGH SIDHU, KEITH MILLER]
Team 2 Batsmen: [JOHN REID, VAIBHAV BANGA, BRENDON MCCULLUM, ALAN MELVILLE]
Found 8 bowling figures: [
  '"3-21" at (1869, 327)',
  '"2-48" at (1869, 381)',
  '"1-15" at (1869, 435)',
  '"1-46" at (1869, 488)',
  '"3-24" at (1869, 649)',
  '"2-6" at (1875, 700)',
  '"2-22" at (1872, 756)',
  '"1-5" at (1881, 807)'
]
Found 8 bowling stats: [
  '3-21', '2-48',
  '1-15', '1-46',
  '3-24', '2-6',
  '2-22', '1-5'
]

=== Matching stats for YOUNIS KHAN at (140, 327) ===
All elements in same row: [
  '"YOUNIS KHAN" at (140, 327)',
  '"YOUNIS" at (140, 327)',
  '"KHAN" at (212, 327)',
  '"60" at (848, 327)',
  '"60" at (848, 327)',
  '"(40)" at (926, 327)',
  '"(" at (926, 327)',
  '"40" at (926, 327)',
  '")" at (926, 327)',
  '"JAMES FAULKNER" at (1081, 327)',
  '"JAMES" at (1081, 327)',
  '"FAULKNER" at (1147, 327)',
  '"3-21" at (1869, 327)',
  '"3" at (1869, 327)',
  '"-" at (1869, 327)',
  '"21" at (1869, 327)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(40)" at (926, 327)' ]
Fallback method found runs: 60
All runs options: [ '60 (fallback, confidence: 0.70)' ]
Selected runs: 60 using method: fallback
Using original OCR value: 60
Final match: YOUNIS KHAN -> 60(40)

=== Matching stats for ALI AKBAR at (140, 380) ===
All elements in same row: [
  '"ALI AKBAR" at (140, 380)',
  '"ALI" at (140, 380)',
  '"AKBAR" at (174, 381)',
  '"29*" at (851, 381)',
  '"29" at (851, 381)',
  '"*" at (851, 381)',
  '"(14)" at (926, 381)',
  '"(" at (926, 381)',
  '"14" at (926, 381)',
  '")" at (926, 381)',
  '"MATT POTTS" at (1080, 380)',
  '"MATT" at (1080, 380)',
  '"POTTS" at (1138, 381)',
  '"2-48" at (1869, 381)',
  '"2" at (1869, 381)',
  '"-" at (1869, 381)',
  '"48" at (1869, 381)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(14)" at (926, 381)' ]
Fallback method found runs: 29*
All runs options: [ '29* (fallback, confidence: 0.70)' ]
Selected runs: 29* using method: fallback
Using original OCR value: 29*
Final match: ALI AKBAR -> 29*(14)

=== Matching stats for NAVJOT SINGH SIDHU at (143, 435) ===
All elements in same row: [
  '"NAVJOT SINGH SIDHU" at (143, 435)',
  '"NAVJOT" at (143, 435)',
  '"SINGH" at (220, 435)',
  '"SIDHU" at (279, 435)',
  '"24" at (848, 435)',
  '"24" at (848, 435)',
  '"(10)" at (926, 435)',
  '"(" at (926, 435)',
  '"10" at (926, 435)',
  '")" at (926, 435)',
  '"JOHN REID" at (1084, 435)',
  '"JOHN" at (1084, 435)',
  '"REID" at (1137, 435)',
  '"1-15" at (1869, 435)',
  '"1" at (1869, 435)',
  '"-" at (1869, 435)',
  '"15" at (1869, 435)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(10)" at (926, 435)' ]
Fallback method found runs: 24
All runs options: [ '24 (fallback, confidence: 0.70)' ]
Selected runs: 24 using method: fallback
Using original OCR value: 24
Final match: NAVJOT SINGH SIDHU -> 24(10)

=== Matching stats for KEITH MILLER at (140, 488) ===
All elements in same row: [
  '"KEITH MILLER" at (140, 488)',
  '"KEITH" at (140, 488)',
  '"MILLER" at (197, 488)',
  '"21" at (848, 488)',
  '"21" at (848, 488)',
  '"(18)" at (926, 488)',
  '"(" at (926, 488)',
  '"18" at (926, 488)',
  '")" at (926, 488)',
  '"MITCHELL JOHNSON" at (1081, 488)',
  '"MITCHELL" at (1081, 488)',
  '"JOHNSON" at (1177, 488)',
  '"1-46" at (1869, 488)',
  '"1" at (1869, 488)',
  '"-" at (1869, 488)',
  '"46" at (1869, 488)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(18)" at (926, 488)' ]
Fallback method found runs: 21
All runs options: [ '21 (fallback, confidence: 0.70)' ]
Selected runs: 21 using method: fallback
Using original OCR value: 21
Final match: KEITH MILLER -> 21(18)

=== Matching stats for JOHN REID at (140, 649) ===
All elements in same row: [
  '"JOHN REID" at (140, 649)',
  '"JOHN" at (140, 649)',
  '"REID" at (195, 649)',
  '"17" at (848, 649)',
  '"17" at (848, 649)',
  '"(11)" at (926, 649)',
  '"(" at (926, 649)',
  '"11" at (926, 649)',
  '")" at (926, 649)',
  '"NAVEEN-UL-HAQ MURID" at (1084, 649)',
  '"NAVEEN" at (1084, 649)',
  '"-" at (1084, 649)',
  '"UL" at (1084, 649)',
  '"-" at (1084, 649)',
  '"HAQ" at (1084, 649)',
  '"MURID" at (1230, 649)',
  '"3-24" at (1869, 649)',
  '"3" at (1869, 649)',
  '"-" at (1869, 649)',
  '"24" at (1869, 649)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(11)" at (926, 649)' ]
Fallback method found runs: 17
All runs options: [ '17 (fallback, confidence: 0.70)' ]
Selected runs: 17 using method: fallback
Using original OCR value: 17
Final match: JOHN REID -> 17(11)

=== Matching stats for VAIBHAV BANGA at (137, 700) ===
All elements in same row: [
  '"VAIBHAV BANGA" at (137, 700)',
  '"VAIBHAV" at (137, 700)',
  '"BANGA" at (226, 700)',
  '"16" at (848, 700)',
  '"16" at (848, 700)',
  '"(8)" at (935, 700)',
  '"(" at (935, 700)',
  '"8" at (935, 700)',
  '")" at (935, 700)',
  '"ALI AKBAR" at (1080, 700)',
  '"ALI" at (1080, 700)',
  '"AKBAR" at (1115, 699)',
  '"2-6" at (1875, 700)',
  '"2" at (1875, 700)',
  '"-" at (1875, 700)',
  '"6" at (1875, 700)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(8)" at (935, 700)' ]
Fallback method found runs: 16
All runs options: [ '16 (fallback, confidence: 0.70)' ]
Selected runs: 16 using method: fallback
Using original OCR value: 16
Final match: VAIBHAV BANGA -> 16(8)

=== Matching stats for BRENDON MCCULLUM at (140, 753) ===
All elements in same row: [
  '"BRENDON MCCULLUM" at (140, 753)',
  '"BRENDON" at (140, 753)',
  '"MCCULLUM" at (230, 753)',
  '"15" at (848, 753)',
  '"15" at (848, 753)',
  '"(7)" at (935, 753)',
  '"(" at (935, 753)',
  '"7" at (935, 753)',
  '")" at (935, 753)',
  '"USAMA MIR" at (1080, 753)',
  '"USAMA" at (1080, 753)',
  '"MIR" at (1149, 753)',
  '"2-22" at (1872, 756)',
  '"2" at (1872, 756)',
  '"-" at (1872, 756)',
  '"22" at (1872, 756)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(7)" at (935, 753)' ]
Fallback method found runs: 15
All runs options: [ '15 (fallback, confidence: 0.70)' ]
Selected runs: 15 using method: fallback
Using original OCR value: 15
Final match: BRENDON MCCULLUM -> 15(7)

=== Matching stats for ALAN MELVILLE at (140, 807) ===
All elements in same row: [
  '"ALAN MELVILLE" at (140, 807)',
  '"ALAN" at (140, 807)',
  '"MELVILLE" at (194, 807)',
  '"10" at (848, 810)',
  '"10" at (848, 810)',
  '"(11)" at (926, 804)',
  '"(" at (926, 804)',
  '"11" at (926, 804)',
  '")" at (926, 804)',
  '"TRENT BOULT" at (1081, 807)',
  '"TRENT" at (1081, 807)',
  '"BOULT" at (1150, 807)',
  '"1-5" at (1881, 807)',
  '"1" at (1881, 807)',
  '"-" at (1881, 807)',
  '"5" at (1881, 807)'
]
Found 0 runs elements: []
Found 1 balls elements: [ '"(11)" at (926, 804)' ]
Fallback method found runs: 10
All runs options: [ '10 (fallback, confidence: 0.70)' ]
Selected runs: 10 using method: fallback
Using original OCR value: 10
Final match: ALAN MELVILLE -> 10(11)

=== Matching bowling stats for JAMES FAULKNER at (1081, 327) ===
Found 1 bowling figures: [ '"3-21" at (1869, 327)' ]
Matched: JAMES FAULKNER -> 3-21

=== Matching bowling stats for MATT POTTS at (1080, 380) ===
Found 1 bowling figures: [ '"2-48" at (1869, 381)' ]
Matched: MATT POTTS -> 2-48

=== Matching bowling stats for JOHN REID at (1084, 435) ===
Found 1 bowling figures: [ '"1-15" at (1869, 435)' ]
Matched: JOHN REID -> 1-15

=== Matching bowling stats for MITCHELL JOHNSON at (1081, 488) ===
Found 1 bowling figures: [ '"1-46" at (1869, 488)' ]
Matched: MITCHELL JOHNSON -> 1-46

=== Matching bowling stats for NAVEEN-UL-HAQ MURID at (1084, 649) ===
Found 1 bowling figures: [ '"3-24" at (1869, 649)' ]
Matched: NAVEEN-UL-HAQ MURID -> 3-24

=== Matching bowling stats for ALI AKBAR at (1080, 700) ===
Found 1 bowling figures: [ '"2-6" at (1875, 700)' ]
Matched: ALI AKBAR -> 2-6

=== Matching bowling stats for USAMA MIR at (1080, 753) ===
Found 1 bowling figures: [ '"2-22" at (1872, 756)' ]
Matched: USAMA MIR -> 2-22

=== Matching bowling stats for TRENT BOULT at (1081, 807) ===
Found 1 bowling figures: [ '"1-5" at (1881, 807)' ]
Matched: TRENT BOULT -> 1-5
Coordinate-based player extraction completed: { team1Batsmen: 4, team2Batsmen: 4, team2Bowlers: 4, team1Bowlers: 4 }
Coordinate-based cricket data extraction completed: {
  team1: 'Team 1',
  team2: 'Team 2',
  venue: 'KIA OVAL',
  team1Score: { runs: 209, wickets: 7, overs: 20 },
  team2Score: { runs: 96, wickets: 10, overs: 12.5 },
  playerOfMatch: 'Ali Akbar',
  confidence: 'medium',
  elementsProcessed: 190
}
Saved OCR.Space cricket data to: C:\Users\<USER>\OneDrive\Documents\rpl-new\server\ocr-output\processed\2025-06-09T15-40-13-279Z_scorecard1_ocrspace_cricket.json
OCR.Space processing completed successfully
📊 RAW EXTRACTION RESULTS:
==========================
Team 1: "Team 1"
Team 2: "Team 2"
Venue: KIA OVAL
Team 1 Score: 209-7 (20 overs)
Team 2 Score: 96-10 (12.5 overs)
Player of Match: Ali Akbar
Extraction Method: OCR.Space API - Coordinate-Based Parser
Confidence: medium

🏏 TEAM 1 BATTING:
  1. YOUNIS KHAN: 60(40) - SR: 150.00
  2. ALI AKBAR: 29*(14) - SR: 207.14
  3. NAVJOT SINGH SIDHU: 24(10) - SR: 240.00
  4. KEITH MILLER: 21(18) - SR: 116.67

🏏 TEAM 2 BATTING:
  1. JOHN REID: 17(11) - SR: 154.55
  2. VAIBHAV BANGA: 16(8) - SR: 200.00
  3. BRENDON MCCULLUM: 15(7) - SR: 214.29
  4. ALAN MELVILLE: 10(11) - SR: 90.91

🎳 TEAM 1 BOWLING:
  1. NAVEEN-UL-HAQ MURID: 3-24
  2. ALI AKBAR: 2-6
  3. USAMA MIR: 2-22
  4. TRENT BOULT: 1-5

🎳 TEAM 2 BOWLING:
  1. JAMES FAULKNER: 3-21
  2. MATT POTTS: 2-48
  3. JOHN REID: 1-15
  4. MITCHELL JOHNSON: 1-46

==================================================
📋 EXTRACTION SUMMARY:
✅ Team Names: "Team 1" vs "Team 2"
✅ Total Batsmen: 8
✅ Total Bowlers: 8
✅ Venue: KIA OVAL
✅ Player of Match: Ali Akbar

🎯 CONCLUSION:
This shows the ACTUAL extraction output without any hardcoded team names.
The team names you see above are exactly what the OCR extracted from the image.
