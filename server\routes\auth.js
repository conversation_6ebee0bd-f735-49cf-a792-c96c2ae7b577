const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticateToken } = require('../middlewares/auth');

// Add mock registration endpoint for testing without MongoDB
router.post('/register-mock', (req, res) => {
  try {
    const { username, email, password, role, teamName } = req.body;
    
    console.log('Mock registration received:', { username, email, role, teamName });
    
    // Return a mock JWT token for testing
    const token = 'mock_jwt_token_for_testing_purposes_only';
    
    res.json({ 
      token,
      message: 'Mock registration successful! (No database connection required)'
    });
  } catch (err) {
    console.error('Mock registration error:', err.message);
    res.status(500).json({ error: 'Server error', details: err.message });
  }
});

// Add mock login endpoint for testing without MongoDB
router.post('/login-mock', (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('Mock login received:', { email });
    
    // Return a mock JWT token for testing
    const token = 'mock_jwt_token_for_testing_purposes_only';
    
    res.json({ 
      token,
      message: 'Mock login successful! (No database connection required)'
    });
  } catch (err) {
    console.error('Mock login error:', err.message);
    res.status(500).json({ error: 'Server error', details: err.message });
  }
});

// Original endpoints
// @route   POST api/auth/register
// @desc    Register a user
// @access  Public
router.post('/register', authController.register);

// @route   POST api/auth/login
// @desc    Login user & get token
// @access  Public
router.post('/login', authController.login);

// @route   GET api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authenticateToken, authController.getCurrentUser);

// @route   PUT api/auth/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', authenticateToken, authController.updateProfile);

module.exports = router;
