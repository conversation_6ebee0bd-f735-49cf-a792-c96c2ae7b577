import React, { useState } from 'react';
import {
  Con<PERSON>er,
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  TextField,
  Alert,
  Paper,
  CircularProgress
} from '@mui/material';
import axios from 'axios';
import { API_URL } from '../../config';

const TestPlayerCreation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [response, setResponse] = useState(null);

  const handleTestCreation = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setResponse(null);

      // Call the test endpoint
      const result = await axios.post(`${API_URL}/players/test`, {
        testData: 'This is a test'
      });

      console.log('Test endpoint response:', result.data);
      setSuccess('Test player created successfully!');
      setResponse(result.data);
    } catch (err) {
      console.error('Test endpoint error:', err);
      setError(err.response?.data?.error || err.message);
      setResponse(err.response?.data);
    } finally {
      setLoading(false);
    }
  };

  const handleDirectCreation = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setResponse(null);

      // Get token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No authentication token found. Please log in first.');
        return;
      }

      // Call the regular player creation endpoint
      const result = await axios.post(
        `${API_URL}/players`,
        {
          name: 'Test Direct Player',
          nationality: 'Test Country',
          age: 25,
          height: '180 cm',
          type: 'Batsman',
          battingHand: 'RHB',
          bowlingHand: 'None',
          ratings: {
            overall: 70
          }
        },
        {
          headers: {
            'x-auth-token': token,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Direct creation response:', result.data);
      setSuccess('Player created directly successfully!');
      setResponse(result.data);
    } catch (err) {
      console.error('Direct creation error:', err);
      setError(err.response?.data?.error || err.message);
      setResponse(err.response?.data);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Test Player Creation
        </Typography>
        <Typography variant="body1" paragraph>
          This page is for testing player creation functionality.
        </Typography>

        <Box sx={{ mb: 4 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleTestCreation}
            disabled={loading}
            sx={{ mr: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Test Player Creation'}
          </Button>

          <Button
            variant="contained"
            color="secondary"
            onClick={handleDirectCreation}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Direct Player Creation'}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {response && (
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom>
              Response:
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={10}
              value={JSON.stringify(response, null, 2)}
              InputProps={{
                readOnly: true,
              }}
              variant="outlined"
            />
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default TestPlayerCreation;
