/**
 * Match Outcome Calculation Test Script
 * 
 * Tests the Match Outcome Calculation feature independently
 * 
 * Usage: node test-match-outcome.js [test-type]
 * Examples:
 *   node test-match-outcome.js single     # Test single match outcome
 *   node test-match-outcome.js scenarios  # Test various match scenarios
 *   node test-match-outcome.js scorecard  # Test with real scorecard data
 */

const path = require('path');
const MatchOutcomeService = require('./services/matchOutcomeService');
const OCRService = require('./services/ocrService');
const Tournament = require('./models/Tournament');
const Team = require('./models/Team');
const connectDB = require('./config/db');
require('dotenv').config();

class MatchOutcomeTest {
  constructor() {
    this.matchOutcomeService = new MatchOutcomeService();
    this.ocrService = new OCRService();
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  /**
   * Test match outcome calculation with mock data
   */
  async testSingleMatchOutcome() {
    console.log('🏆 SINGLE MATCH OUTCOME TEST');
    console.log('============================\n');

    // Mock OCR data for testing
    const mockOCRData = {
      success: true,
      team1: 'Mumbai Indians',
      team2: 'Chennai Super Kings',
      venue: 'Wankhede Stadium',
      team1Score: {
        runs: 185,
        wickets: 6,
        overs: 20.0
      },
      team2Score: {
        runs: 178,
        wickets: 8,
        overs: 20.0
      },
      team1Batsmen: [
        { name: 'Rohit Sharma', runs: 45, balls: 32, strikeRate: 140.6 },
        { name: 'Quinton de Kock', runs: 38, balls: 28, strikeRate: 135.7 },
        { name: 'Suryakumar Yadav', runs: 52, balls: 36, strikeRate: 144.4 },
        { name: 'Kieron Pollard', runs: 28, balls: 18, strikeRate: 155.6 },
        { name: 'Hardik Pandya', runs: 15, balls: 8, strikeRate: 187.5 }
      ],
      team2Batsmen: [
        { name: 'Faf du Plessis', runs: 42, balls: 35, strikeRate: 120.0 },
        { name: 'Ruturaj Gaikwad', runs: 35, balls: 28, strikeRate: 125.0 },
        { name: 'Ambati Rayudu', runs: 48, balls: 32, strikeRate: 150.0 },
        { name: 'MS Dhoni', runs: 25, balls: 20, strikeRate: 125.0 },
        { name: 'Ravindra Jadeja', runs: 18, balls: 12, strikeRate: 150.0 }
      ],
      team1Bowlers: [
        { name: 'Jasprit Bumrah', wickets: 2, runs: 28, overs: 4 },
        { name: 'Trent Boult', wickets: 3, runs: 32, overs: 4 },
        { name: 'Rahul Chahar', wickets: 1, runs: 35, overs: 4 }
      ],
      team2Bowlers: [
        { name: 'Deepak Chahar', wickets: 2, runs: 35, overs: 4 },
        { name: 'Shardul Thakur', wickets: 1, runs: 42, overs: 4 },
        { name: 'Ravindra Jadeja', wickets: 2, runs: 28, overs: 4 }
      ]
    };

    const mockTournamentId = 'test-tournament-' + Date.now();

    try {
      console.log('⏳ Calculating match outcome...');
      
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(mockOCRData, mockTournamentId);
      
      console.log('\n📊 MATCH OUTCOME RESULTS:');
      console.log('=========================');
      console.log(`Winner: ${outcome.winner}`);
      console.log(`Margin: ${outcome.margin}`);
      console.log(`Match Type: ${outcome.matchType}`);
      console.log(`Team 1 (${mockOCRData.team1}): ${outcome.team1Points} points`);
      console.log(`Team 2 (${mockOCRData.team2}): ${outcome.team2Points} points`);
      
      if (outcome.team1Stats) {
        console.log(`\n📈 ${mockOCRData.team1} Statistics:`);
        console.log(`   Net Run Rate: ${outcome.team1Stats.netRunRate?.toFixed(3) || 'N/A'}`);
        console.log(`   Total Runs: ${outcome.team1Stats.totalRuns}`);
        console.log(`   Total Overs: ${outcome.team1Stats.totalOvers}`);
      }
      
      if (outcome.team2Stats) {
        console.log(`\n📈 ${mockOCRData.team2} Statistics:`);
        console.log(`   Net Run Rate: ${outcome.team2Stats.netRunRate?.toFixed(3) || 'N/A'}`);
        console.log(`   Total Runs: ${outcome.team2Stats.totalRuns}`);
        console.log(`   Total Overs: ${outcome.team2Stats.totalOvers}`);
      }
      
      if (outcome.playerPerformances && outcome.playerPerformances.length > 0) {
        console.log('\n🏏 TOP PLAYER PERFORMANCES:');
        outcome.playerPerformances.slice(0, 5).forEach((perf, i) => {
          console.log(`   ${i + 1}. ${perf.playerName}: ${perf.runs} runs, ${perf.wickets || 0} wickets, ${perf.points} points`);
        });
      }
      
      // Validate the outcome
      this.validateOutcome(outcome, mockOCRData);
      
      this.testResults.passed++;
      console.log('\n✅ Single match outcome test PASSED\n');
      
    } catch (error) {
      console.log('❌ Single match outcome test FAILED:', error.message);
      this.testResults.failed++;
    }
    
    this.testResults.total++;
  }

  /**
   * Test various match scenarios
   */
  async testMatchScenarios() {
    console.log('🎯 MATCH SCENARIOS TEST');
    console.log('=======================\n');

    const scenarios = [
      {
        name: 'Normal Win by Runs',
        team1Score: { runs: 180, wickets: 6, overs: 20.0 },
        team2Score: { runs: 165, wickets: 10, overs: 19.2 },
        expectedWinner: 'team1',
        expectedMarginType: 'runs'
      },
      {
        name: 'Win by Wickets',
        team1Score: { runs: 150, wickets: 8, overs: 20.0 },
        team2Score: { runs: 151, wickets: 6, overs: 18.4 },
        expectedWinner: 'team2',
        expectedMarginType: 'wickets'
      },
      {
        name: 'Super Over Scenario',
        team1Score: { runs: 175, wickets: 7, overs: 20.0 },
        team2Score: { runs: 175, wickets: 9, overs: 20.0 },
        expectedWinner: null, // Tie
        expectedMarginType: 'tie'
      },
      {
        name: 'High Scoring Match',
        team1Score: { runs: 220, wickets: 4, overs: 20.0 },
        team2Score: { runs: 215, wickets: 8, overs: 20.0 },
        expectedWinner: 'team1',
        expectedMarginType: 'runs'
      },
      {
        name: 'Low Scoring Match',
        team1Score: { runs: 95, wickets: 10, overs: 18.2 },
        team2Score: { runs: 96, wickets: 7, overs: 19.1 },
        expectedWinner: 'team2',
        expectedMarginType: 'wickets'
      },
      {
        name: 'Team2 is Home & Home Team Batted Second',
        team1Score: { runs: 164, wickets: 2, overs: 18.2 },
        team2Score: { runs: 162, wickets: 5, overs: 20.0 },
        expectedWinner: 'team1',
        expectedMarginType: 'wickets',
        team1IsHomeTeam: false,
        homeTeamBattedFirst: false
      }
    ];

    for (const scenario of scenarios) {
      await this.testScenario(scenario);
    }

    this.displayResults();
  }

  /**
   * Test a specific match scenario
   */
  async testScenario(scenario) {
    console.log(`📋 Testing: ${scenario.name}`);
    console.log(`   Team 1 Score: ${scenario.team1Score.runs}-${scenario.team1Score.wickets}`);
    console.log(`   Team 2 Score: ${scenario.team2Score.runs}-${scenario.team2Score.wickets}`);
    
    const mockData = {
      success: true,
      team1: 'Team A',
      team2: 'Team B',
      venue: 'Test Stadium',
      team1Score: scenario.team1Score,
      team2Score: scenario.team2Score,
      team1Batsmen: [
        { name: 'Player A1', runs: 45, balls: 32 },
        { name: 'Player A2', runs: 38, balls: 28 }
      ],
      team2Batsmen: [
        { name: 'Player B1', runs: 42, balls: 35 },
        { name: 'Player B2', runs: 35, balls: 28 }
      ]
    };

    // Create a mock match object with team1IsHomeTeam and homeTeamBattedFirst if provided
    const mockMatch = {};
    if (scenario.team1IsHomeTeam !== undefined) {
      mockMatch.team1IsHomeTeam = scenario.team1IsHomeTeam;
      console.log(`   Setting team1IsHomeTeam: ${scenario.team1IsHomeTeam}`);
    }
    if (scenario.homeTeamBattedFirst !== undefined) {
      mockMatch.homeTeamBattedFirst = scenario.homeTeamBattedFirst;
      console.log(`   Setting homeTeamBattedFirst: ${scenario.homeTeamBattedFirst}`);
    }

    try {
      console.log('   Calling calculateMatchOutcome with mockMatch:', JSON.stringify(mockMatch));
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(mockData, 'test-tournament', mockMatch);
      
      console.log(`   Result: ${outcome.winner || 'Tie'} ${outcome.resultDescription || outcome.margin}`);
      
      // Validate scenario expectations
      let passed = true;
      
      if (scenario.expectedWinner && outcome.winner !== scenario.expectedWinner) {
        console.log(`   ❌ Expected winner: ${scenario.expectedWinner}, got: ${outcome.winner}`);
        passed = false;
      }
      
      if (scenario.expectedMarginType === 'tie' && outcome.winner !== null) {
        console.log(`   ❌ Expected tie, got winner: ${outcome.winner}`);
        passed = false;
      } else if (scenario.expectedMarginType && outcome.resultDescription) {
        // Check if the margin type (runs/wickets) is correct
        const hasExpectedMarginType = outcome.resultDescription.toLowerCase().includes(scenario.expectedMarginType);
        if (!hasExpectedMarginType) {
          console.log(`   ❌ Expected margin type: ${scenario.expectedMarginType}, got: ${outcome.resultDescription}`);
          passed = false;
        } else {
          console.log(`   ✅ Margin type check passed: ${outcome.resultDescription}`);
        }
      }
      
      if (passed) {
        console.log('   ✅ Scenario PASSED');
        this.testResults.passed++;
      } else {
        console.log('   ❌ Scenario FAILED');
        this.testResults.failed++;
      }
      
    } catch (error) {
      console.log(`   ❌ Scenario ERROR: ${error.message}`);
      this.testResults.failed++;
    }
    
    this.testResults.total++;
    console.log('');
  }

  /**
   * Test with real scorecard data
   */
  async testWithScorecardData(scorecardName = 'scorecard1.jpg') {
    console.log('📸 REAL SCORECARD DATA TEST');
    console.log('===========================\n');
    
    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);
    
    try {
      console.log(`📸 Processing scorecard: ${scorecardName}`);
      
      // Extract data using OCR
      const ocrResult = await this.ocrService.processImageWithOCRSpace(imagePath);
      
      if (!ocrResult.success) {
        throw new Error('OCR extraction failed: ' + ocrResult.error);
      }
      
      console.log('✅ OCR extraction successful');
      console.log(`   Team 1: ${ocrResult.team1} (${ocrResult.team1Score.runs}-${ocrResult.team1Score.wickets})`);
      console.log(`   Team 2: ${ocrResult.team2} (${ocrResult.team2Score.runs}-${ocrResult.team2Score.wickets})`);
      
      // Calculate match outcome
      console.log('\n⏳ Calculating match outcome from real data...');
      const outcome = await this.matchOutcomeService.calculateMatchOutcome(ocrResult, 'test-tournament');
      
      console.log('\n📊 REAL MATCH OUTCOME:');
      console.log('======================');
      console.log(`Winner: ${outcome.winner}`);
      console.log(`Margin: ${outcome.margin}`);
      console.log(`${ocrResult.team1}: ${outcome.team1Points} points`);
      console.log(`${ocrResult.team2}: ${outcome.team2Points} points`);
      
      if (outcome.team1Stats && outcome.team2Stats) {
        console.log(`\nNet Run Rates:`);
        console.log(`   ${ocrResult.team1}: ${outcome.team1Stats.netRunRate?.toFixed(3) || 'N/A'}`);
        console.log(`   ${ocrResult.team2}: ${outcome.team2Stats.netRunRate?.toFixed(3) || 'N/A'}`);
      }
      
      this.testResults.passed++;
      console.log('\n✅ Real scorecard test PASSED\n');
      
    } catch (error) {
      console.log('❌ Real scorecard test FAILED:', error.message);
      this.testResults.failed++;
    }
    
    this.testResults.total++;
  }

  /**
   * Validate match outcome results
   */
  validateOutcome(outcome, ocrData) {
    const validations = [];
    
    // Check if winner is determined correctly
    if (ocrData.team1Score.runs > ocrData.team2Score.runs) {
      validations.push({
        test: 'Winner determination',
        passed: outcome.winner === 'team1',
        message: `Team 1 scored more runs, should win`
      });
    } else if (ocrData.team2Score.runs > ocrData.team1Score.runs) {
      validations.push({
        test: 'Winner determination',
        passed: outcome.winner === 'team2',
        message: `Team 2 scored more runs, should win`
      });
    }
    
    // Check points allocation
    validations.push({
      test: 'Points allocation',
      passed: (outcome.team1Points + outcome.team2Points) <= 2,
      message: 'Total points should not exceed 2 per match'
    });
    
    // Check if margin is calculated
    validations.push({
      test: 'Margin calculation',
      passed: outcome.margin && outcome.margin.length > 0,
      message: 'Margin should be calculated and non-empty'
    });
    
    console.log('\n🔍 VALIDATION RESULTS:');
    validations.forEach(validation => {
      if (validation.passed) {
        console.log(`   ✅ ${validation.test}: PASSED`);
      } else {
        console.log(`   ❌ ${validation.test}: FAILED - ${validation.message}`);
      }
    });
  }

  /**
   * Display test results summary
   */
  displayResults() {
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    console.log(`Tests Passed: ${this.testResults.passed}`);
    console.log(`Tests Failed: ${this.testResults.failed}`);
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%\n`);
    
    if (this.testResults.passed === this.testResults.total) {
      console.log('🎉 ALL TESTS PASSED! Match outcome calculation is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Review the match outcome calculation logic.');
    }
  }
}

// Main execution
async function main() {
  // Initialize database connection
  console.log('🔌 Connecting to database...');
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('⚠️  Database connection failed, continuing with limited functionality...');
  } else {
    console.log('✅ Database connected successfully\n');
  }
  
  const tester = new MatchOutcomeTest();
  const testType = process.argv[2] || 'single';
  const scorecardName = process.argv[3];
  
  console.log('🚀 MATCH OUTCOME CALCULATION TEST SUITE');
  console.log('=======================================\n');
  
  switch (testType.toLowerCase()) {
    case 'single':
      await tester.testSingleMatchOutcome();
      break;
      
    case 'scenarios':
      await tester.testMatchScenarios();
      break;
      
    case 'scorecard':
      await tester.testWithScorecardData(scorecardName);
      break;
      
    case 'all':
      await tester.testSingleMatchOutcome();
      await tester.testMatchScenarios();
      await tester.testWithScorecardData(scorecardName || 'scorecard1.jpg');
      break;
      
    default:
      console.log('❌ Invalid test type. Available options:');
      console.log('   single     - Test single match outcome calculation');
      console.log('   scenarios  - Test various match scenarios');
      console.log('   scorecard  - Test with real scorecard data');
      console.log('   all        - Run all tests');
      break;
  }
}

// Handle errors and run
main().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});