/* Player Form Styles */

/* Fix for the Player Type and Batting Hand buttons */
.playing-style-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* Make the buttons wider and ensure text is fully visible */
.playing-style-container button,
.playing-style-container .MuiButtonBase-root,
.playing-style-container .MuiButton-root,
.playing-style-container [role="button"] {
  min-width: 120px !important;
  width: auto !important;
  padding: 8px 16px !important;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  font-size: 14px !important;
}

/* Ensure dropdown menus are wide enough */
.MuiSelect-select,
.MuiMenuItem-root {
  min-width: 150px !important;
  white-space: nowrap !important;
}

/* Fix for the bowling hand dropdown */
.bowling-hand-select {
  min-width: 180px !important;
}

/* Improve overall form layout */
.player-form-container {
  max-width: 800px !important;
  margin: 0 auto !important;
}

.player-form-section {
  margin-bottom: 20px !important;
  padding: 15px !important;
  border-radius: 8px !important;
}

/* Make sure labels are fully visible */
.MuiFormLabel-root,
.MuiInputLabel-root {
  white-space: nowrap !important;
  overflow: visible !important;
}
