/**
 * Command-line script to export IPL player names to CSV
 *
 * Usage: node scripts/exportPlayerNames.js
 */
const { exportAllPlayerNames } = require('../utils/robustIplScraper');

console.log('Starting IPL player name export script...');

exportAllPlayerNames()
  .then(outputPath => {
    console.log(`Export completed successfully!`);
    console.log(`CSV file saved to: ${outputPath}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error during export:', error);
    process.exit(1);
  });
