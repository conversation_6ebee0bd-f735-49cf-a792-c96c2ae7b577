const path = require('path');
const fs = require('fs');

// Simple test for core functionality
async function testCoreFeatures() {
  const results = {
    playerMatching: { tested: false, working: false, error: null },
    matchOutcome: { tested: false, working: false, error: null }
  };

  // Test Player Matching Service
  try {
    const PlayerMatchingService = require('./services/playerMatchingService');
    const playerService = new PlayerMatchingService();
    
    // Test basic functionality
    const testResult = await playerService.matchPlayer('Test Player');
    results.playerMatching.tested = true;
    results.playerMatching.working = testResult && typeof testResult === 'object';
    
    if (results.playerMatching.working) {
      console.log('✅ Player Matching Service: WORKING');
    } else {
      console.log('❌ Player Matching Service: NOT WORKING');
    }
  } catch (error) {
    results.playerMatching.tested = true;
    results.playerMatching.error = error.message;
    console.log('❌ Player Matching Service: ERROR -', error.message);
  }

  // Test Match Outcome Service
  try {
    const MatchOutcomeService = require('./services/matchOutcomeService');
    const matchService = new MatchOutcomeService();
    
    // Test basic functionality with simple mock data
    const mockData = {
      team1Score: { runs: 150, wickets: 5, overs: 20 },
      team2Score: { runs: 120, wickets: 10, overs: 18.3 },
      team1Name: 'Team A',
      team2Name: 'Team B',
      resultText: ''
    };
    
    const testResult = await matchService.calculateMatchOutcome(mockData, 'test-tournament');
    results.matchOutcome.tested = true;
    results.matchOutcome.working = testResult && typeof testResult === 'object';
    
    if (results.matchOutcome.working) {
      console.log('✅ Match Outcome Service: WORKING');
      console.log('   Sample result:', testResult.resultDescription || 'Result calculated');
    } else {
      console.log('❌ Match Outcome Service: NOT WORKING');
    }
  } catch (error) {
    results.matchOutcome.tested = true;
    results.matchOutcome.error = error.message;
    console.log('❌ Match Outcome Service: ERROR -', error.message);
  }

  // Summary
  console.log('\n=== FEATURE TEST SUMMARY ===');
  console.log('Player Matching:', results.playerMatching.working ? 'WORKING' : 'FAILED');
  console.log('Match Outcome:', results.matchOutcome.working ? 'WORKING' : 'FAILED');
  
  const workingFeatures = [results.playerMatching.working, results.matchOutcome.working].filter(Boolean).length;
  console.log(`\nWorking Features: ${workingFeatures}/2`);
  
  if (workingFeatures === 2) {
    console.log('🎉 Both features are functional!');
  } else {
    console.log('⚠️  Some features need attention.');
  }

  return results;
}

// Run the test
if (require.main === module) {
  testCoreFeatures().catch(console.error);
}

module.exports = { testCoreFeatures };