const axios = require('axios');
const fs = require('fs');
const path = require('path');
const cheerio = require('cheerio');

async function fetchPlayerName(id) {
    try {
        console.log(`Trying to fetch name for player ID ${id}...`);
        
        // Try multiple IPL URLs and data sources
        const urls = [
            `https://www.iplt20.com/stats/all-time/batting?id=${id}`,
            `https://www.iplt20.com/stats/all-time/bowling?id=${id}`,
            `https://www.iplt20.com/teams/squad/${id}`,
            `https://www.iplt20.com/auction/2024/player-list/${id}`
        ];

        for (const url of urls) {
            try {
                console.log(`Trying URL: ${url}`);
                const response = await axios.get(url, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                    }
                });
                
                const $ = cheerio.load(response.data);
                
                // Try various selectors that might contain player name
                const nameSelectors = [
                    '.player-name',
                    '.stats-player-name',
                    '.player-details__name',
                    '.profile-header__player-name',
                    '.squad-player-name',
                    'h1:contains("Player Profile")',
                    '.player-profile h1',
                    // Try table cells that might contain player data
                    'td:first-child',
                    'th:contains("Player Name") + td'
                ];

                for (const selector of nameSelectors) {
                    const element = $(selector).first();
                    const name = element.text().trim();
                    
                    if (name && 
                        name.length > 2 && 
                        !name.toLowerCase().includes('ipl') &&
                        !name.toLowerCase().includes('error') &&
                        !name.includes('404')) {
                            
                        console.log(`Found name for ID ${id}: ${name}`);
                        return name;
                    }
                }

                // Try finding name in any table cell
                $('td').each((i, elem) => {
                    const cellText = $(elem).text().trim();
                    if (cellText && 
                        cellText.length > 2 && 
                        cellText.length < 40 &&
                        /^[A-Za-z\s]+$/.test(cellText)) {
                        console.log(`Found possible name in table: ${cellText}`);
                        return cellText;
                    }
                });

            } catch (error) {
                console.log(`Failed with URL ${url}: ${error.message}`);
                continue;
            }
        }
        
        return null;
    } catch (error) {
        console.log(`Error fetching name for ID ${id}: ${error.message}`);
        return null;
    }
}

async function fetchPlayerNameFromCricinfo(id) {
    try {
        console.log(`Trying to fetch name for player ID ${id} from Cricinfo...`);
        
        // First try to search for the player
        const searchUrl = `https://www.espncricinfo.com/ci/content/player/search.html?search=${id}`;
        const response = await axios.get(searchUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });
        
        const $ = cheerio.load(response.data);
        
        // Look for player links in search results
        const playerLinks = $('a[href*="/ci/content/player"]');
        
        if (playerLinks.length > 0) {
            const firstResult = playerLinks.first();
            const name = firstResult.text().trim();
            if (name && name.length > 2) {
                console.log(`Found name on Cricinfo: ${name}`);
                return name;
            }
        }
        
        return null;
    } catch (error) {
        console.log(`Error fetching from Cricinfo: ${error.message}`);
        return null;
    }
}

async function fetchPlayerNameFromSquads(id) {
    const teams = [
        'chennai-super-kings',
        'delhi-capitals',
        'gujarat-titans',
        'kolkata-knight-riders',
        'lucknow-super-giants',
        'mumbai-indians',
        'punjab-kings',
        'rajasthan-royals',
        'royal-challengers-bangalore',
        'sunrisers-hyderabad'
    ];

    for (const team of teams) {
        try {
            console.log(`Checking ${team} squad for player ID ${id}...`);
            const url = `https://www.iplt20.com/teams/${team}/squad`;
            
            const response = await axios.get(url, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                }
            });

            const $ = cheerio.load(response.data);
            
            // Look for player cards or list items
            const playerElements = $('.player-card, .squad-player, [data-player-id]');
            
            playerElements.each((i, elem) => {
                const playerId = $(elem).attr('data-player-id');
                if (playerId === id.toString()) {
                    const name = $(elem).find('.player-name, .name, h3').first().text().trim();
                    if (name) {
                        console.log(`Found player name: ${name}`);
                        return name;
                    }
                }
            });
        } catch (error) {
            console.log(`Error checking ${team}: ${error.message}`);
            continue;
        }
    }
    return null;
}

async function findValidPlayerIds(start = 1, end = 10) {
    const foundIds = [];
    const playerData = {};

    console.log('Starting player ID search...');
    
    // First find valid IDs by checking image existence
    for (let id = start; id <= end; id++) {
        const imageUrl = `https://documents.iplt20.com/ipl/IPLHeadshot2025/${id}.png`;
        
        try {
            const response = await axios.head(imageUrl);
            if (response.status === 200) {
                console.log(`Found valid player ID: ${id}`);
                foundIds.push(id);
                
                // Try to fetch the player name with increased timeout
                const name = await fetchPlayerName(id);
                if (name) {
                    playerData[id] = { 
                        name: name,
                        imageUrl: imageUrl
                    };
                } else {
                    // If name not found, try Cricinfo
                    const cricinfoName = await fetchPlayerNameFromCricinfo(id);
                    if (cricinfoName) {
                        playerData[id] = { 
                            name: cricinfoName,
                            imageUrl: imageUrl
                        };
                    } else {
                        // If still no name, check squad pages
                        const squadName = await fetchPlayerNameFromSquads(id);
                        if (squadName) {
                            playerData[id] = { 
                                name: squadName,
                                imageUrl: imageUrl
                            };
                        }
                    }
                }
                
                // Increased delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        } catch (error) {
            // Skip invalid IDs silently
        }
    }

    // Save both IDs and player data
    const idsOutputPath = path.join(__dirname, 'found_player_ids.json');
    fs.writeFileSync(idsOutputPath, JSON.stringify(foundIds, null, 2));

    const dataOutputPath = path.join(__dirname, 'ipl_player_data.json');
    fs.writeFileSync(dataOutputPath, JSON.stringify(playerData, null, 2));

    console.log('Results:');
    console.log('---------');
    console.log('Valid player IDs found:', foundIds);
    console.log('Players with names found:', Object.keys(playerData).length);
    console.log('Player data saved to:', dataOutputPath);
    
    // Print out the names we found
    for (const [id, data] of Object.entries(playerData)) {
        console.log(`ID ${id}: ${data.name}`);
    }
    
    return { foundIds, playerData };
}

async function fetchPlayerNamesFromTeam(teamUrl) {
    try {
        console.log(`Fetching players from: ${teamUrl}`);
        const response = await axios.get(teamUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        const $ = cheerio.load(response.data);
        const players = {};

        // Find all img tags that have IPLHeadshot2025 in their src
        $('img[src*="IPLHeadshot2025"]').each((i, elem) => {
            const src = $(elem).attr('src') || $(elem).attr('data-src');
            if (src) {
                // Extract ID from the image URL
                const idMatch = src.match(/IPLHeadshot2025\/(\d+)\.png/);
                if (idMatch) {
                    const id = idMatch[1];
                    // Find the player name in the next div with class 'ih-p-name'
                    const nameElem = $(elem).closest('.ih-p-circle').next('.ih-p-name').find('h2');
                    if (nameElem.length > 0) {
                        const name = nameElem.text().trim();
                        players[id] = {
                            name: name,
                            imageUrl: src
                        };
                        console.log(`Found player: ${name} (ID: ${id})`);
                    }
                }
            }
        });

        return players;
    } catch (error) {
        console.error(`Error fetching from ${teamUrl}:`, error.message);
        return {};
    }
}

async function getAllPlayerNames() {
    const teams = [
        'delhi-capitals',
        'mumbai-indians',
        'chennai-super-kings',
        'kolkata-knight-riders',
        'royal-challengers-bangalore',
        'punjab-kings',
        'rajasthan-royals',
        'sunrisers-hyderabad',
        'gujarat-titans',
        'lucknow-super-giants'
    ];

    const allPlayers = {};
    
    for (const team of teams) {
        const teamUrl = `https://www.iplt20.com/teams/${team}/squad/2025`;
        console.log(`Processing team: ${team}`);
        
        const teamPlayers = await fetchPlayerNamesFromTeam(teamUrl);
        Object.assign(allPlayers, teamPlayers);
        
        // Add a delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Save the results
    const outputPath = path.join(__dirname, 'ipl_player_data.json');
    fs.writeFileSync(outputPath, JSON.stringify(allPlayers, null, 2));

    console.log('\nResults:');
    console.log('---------');
    console.log('Total players found:', Object.keys(allPlayers).length);
    console.log('Data saved to:', outputPath);
    
    // Print all players found
    console.log('\nPlayers found:');
    for (const [id, data] of Object.entries(allPlayers)) {
        console.log(`ID ${id}: ${data.name}`);
    }

    return allPlayers;
}

// Run the script
findValidPlayerIds().catch(console.error);
getAllPlayerNames().catch(console.error);