<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 1000 1080" width="1000" height="1080" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(0)" d="m0 0h1e3v1080h-1e3z" fill="#010101"/>
<path transform="translate(0 1e3)" d="m0 0h1e3v80h-1e3z" fill="#111224"/>
<path transform="translate(471,299)" d="m0 0h19l20 2 23 5 23 8 22 11 15 10 14 11 15 14-1 2-12-10-19-12-19-9-21-7-21-4-9-1h-29l-21 3-19 5-16 6-23 12-14 10-15 13-7 7-9 11-11 16-11 21-7 19-5 22-2 16v27l3 21 5 19 7 19 10 19 12 17 12 13 1 3 4 2 7 7 16 12 21 12 20 8 18 5 18 3 11 1h19l24-3 23-6 19-8 17-9 11-8 2 1-11 10-14 10-21 12-25 10-24 6-13 2-12 1h-22l-24-3-21-5-22-8-23-12-17-12-14-12-13-13-11-14-10-15-11-21-6-15-6-22-3-17-1-11v-27l3-23 5-20 6-17 10-21 10-16 11-14 12-13 7-7 14-11 13-9 16-9 21-9 21-6 22-4z" fill="#FEFEFE"/>
<path transform="translate(128,504)" d="m0 0h141l4 33 6 25 7 21 13 27 10 16 12 16 12 13 17 17 17 13 19 12 22 11 25 9 20 5 19 3 30 2v137l-2-2-13-38-25-75-31-7-27-9-29-14-18-11-16-12-11-9-5 2-16 10-44 28-4 2 2-5 13-20 14-22 11-17 2-3-1-4-12-16-13-21-11-22-9-24-6-22-1-8-9-2-60-20-41-14-12-4z" fill="#FEFEFE"/>
<path transform="translate(505,136)" d="m0 0 2 1 35 105 2 7 10 1 22 6 22 8 17 8 19 11 17 12 10 8 10 9 12-6 22-13 36-21v3l-12 17-32 46 6 8 10 15 9 15 13 27 9 27 4 17v3l6 1 41 14 57 19 17 6v1h-140l-3-27-4-21-8-26-8-18-10-19-12-18-1-3-3-1-7-8-14-15-5-6-11-9-13-10-14-9-16-9-24-10-23-7-21-4-27-3z" fill="#FDFDFD"/>
<path transform="translate(729,504)" d="m0 0h144l-2 2-69 23-41 14-12 4-4 15-6 20-10 24-11 21-12 18-10 13v4l16 25 26 39-1 2-19-12-48-30-4 2-12 10-17 12-15 9-15 8-25 10-20 6-8 1-3 11-27 80-8 23h-1v-132l24-3 25-6 20-7 22-10 20-12 12-9 11-9 31-31 12-17 11-19 8-17 7-19 6-24 3-18z" fill="#FDFDFD"/>
<path transform="translate(491,136)" d="m0 0h1v132l-33 4-26 6-20 7-25 12-15 9-14 10-12 11-8 8-8 7-7 7-13 17-12 19-13 27-8 24-5 22-4 33h-138l1-2 98-33 18-6h2l2-12 7-24 9-22 10-20 12-19 10-13 10-12-6-11-15-26-17-29-2-5 11 7 15 11 19 13 20 14 5-2 11-9 19-12 23-12 19-8 23-7 18-4 14-41 19-56z" fill="#FEFEFE"/>
<path transform="translate(366,452)" d="m0 0h48l12 3 8 5 7 8 3 8v15l-4 10-6 7-12 6 9 10 7 8 9 10 2 4h-22l-11-12-9-11-6-7-18-1v31h-17z" fill="#FEFEFE"/>
<path transform="translate(499,452)" d="m0 0h48l12 3 8 6 6 7 4 10v15l-4 10-6 8-10 6-8 2h-33v28h-17l-1-1v-93z" fill="#FEFEFE"/>
<path transform="translate(640,452)" d="m0 0h17l1 1v77h48v16h-66z" fill="#FEFEFE"/>
<path transform="translate(516,469)" d="m0 0h29l8 3 5 6 2 6-1 8-4 6-6 4-5 1h-27l-1-1z" fill="#010102"/>
<path transform="translate(383,469)" d="m0 0h32l6 3 4 4 1 3v10l-3 5-5 4-3 1h-32z" fill="#010102"/>
<path transform="translate(317,655)" d="m0 0 7 6 11 11-4 4-41 26-4 2 2-4 11-18 15-23z" fill="#010102"/>
<path transform="translate(294,284)" d="m0 0 5 2 14 10 23 16 6 4-6 7-10 9h-4l-9-15-14-24z" fill="#010102"/>
<path transform="translate(680,654)" d="m0 0 5 5 10 16 10 15 7 11h-3l-20-12-19-12-6-4 2-4 11-11z" fill="#010102"/>
<path transform="translate(713,297)" d="m0 0h2l-2 5-8 11-16 23-5 7-4-2-9-9-5-7 13-8 26-15z" fill="#010102"/>
<path transform="translate(162,1034)" d="m0 0h7l5 5 1 6-3 7-3 2h-7l-5-5-1-6 3-7z" fill="#FEFEFE"/>
<path transform="translate(131,1028)" d="m0 0h7l5 3-1 3-6-2-6 1v4l9 2 5 5-1 6-5 4h-9l-4-1 1-4 10 1 3-2-1-4-10-4-2-2v-6l3-3z" fill="#FEFEFE"/>
<path transform="translate(194,1028)" d="m0 0h4l1 11 6-5 4 1-6 6 2 5 5 8h-5l-5-7v-2h-2v9h-4z" fill="#FEFEFE"/>
<path transform="translate(100,1036)" d="m0 0h6l3 3 1 2v6l-2 4-2 1h-6l-3-3-1-2v-6l2-4z" fill="#101122"/>
<path transform="translate(55,1034)" d="m0 0h6l4 4v7h-13l2 5 2 1h9v2l-2 1h-8l-4-4-1-2v-8l3-5z" fill="#FEFEFE"/>
<path transform="translate(163,1037)" d="m0 0h5l3 4v6l-4 4-5-1-2-3v-6z" fill="#0F1020"/>
<path transform="translate(183,1034)" d="m0 0h7v4h-8l-1 8 1 4h8v4h-7l-4-2-2-4v-8l2-4z" fill="#FEFEFE"/>
<path transform="translate(30,1028)" d="m0 0 3 1 7 20 1 1 8-22h2l-3 11-5 14-4 1-7-19z" fill="#FEFEFE"/>
<path transform="translate(100,1034)" d="m0 0h6l5 4 1 2v8l-4 5-2 1h-6l-5-4-1-2v-8l4-5zm0 2-3 3-1 2v6l2 4 2 1h6l3-3 1-2v-6l-2-4-2-1z" fill="#FEFEFE"/>
<path transform="translate(147,1028)" d="m0 0h3v6h5v3h-5l1 12 4 2-1 3-5-1-2-2z" fill="#FEFEFE"/>
<path transform="translate(966,1039)" d="m0 0 5 1-4 2h-2v2l5 1 2 4-1 3-6 1-3-3v-6l2-4z" fill="#C8C8CC"/>
<path transform="translate(912,1039)" d="m0 0 5 1-4 2h-2v2l5 1 1 1v5l-2 2-5-1-2-2v-6l2-4z" fill="#C4C4C8"/>
<path transform="translate(802,1043)" d="m0 0 7 1 1 6-3 3-6-1-1-1v-6z" fill="#FEFEFE"/>
<path transform="translate(767,1043)" d="m0 0 6 1 1 1-1 7-6 1-3-2v-6z" fill="#FEFEFE"/>
<path transform="translate(850,1043)" d="m0 0 6 1 1 6-3 3-6-1-1-1v-6z" fill="#FEFEFE"/>
<path transform="translate(74,1034)" d="m0 0h7l2 2-2 1h-7l-3 5 1 7 2 2h9l-1 3h-8l-5-5-1-2v-6l4-6z" fill="#FEFEFE"/>
<path transform="translate(859,1043)" d="m0 0h7 3l5 1v9h-2v-8l-3 1-1 7h-2l-1-7h-2l-2 7h-2z" fill="#FEFEFE"/>
<path transform="translate(946,1039)" d="m0 0h3v9h2v2h-2v3h-2v-3h-7l4-8z" fill="#D1D1D4"/>
<path transform="translate(889,1039)" d="m0 0h3v9h2v2h-2v3h-2v-3h-7l4-8z" fill="#D1D1D4"/>
<path transform="translate(901,1039)" d="m0 0h3v9h2v2h-2v3h-2v-3h-6l1-5z" fill="#D0D1D4"/>
<path transform="translate(55,1036)" d="m0 0 7 1 1 6h-11l1-5z" fill="#101123"/>
<path transform="translate(86,1028)" d="m0 0h2v6h4v2h-4l1 12 1 3 2 1v2l-5-2-1-3z" fill="#FEFEFE"/>
<path transform="translate(931,1039)" d="m0 0h7v2h-5v3l5 1 1 5-3 3-6-1v-2h5v-3l-5-1z" fill="#FEFEFE"/>
<path transform="translate(921,1039)" d="m0 0h5l2 5-6 6 6 1v2h-9l1-5 4-3 1-4-6 1z" fill="#FEFEFE"/>
<path transform="translate(738,1043)" d="m0 0h5l2 5h-7l1 2h5l-1 3-6-1-1-1v-6z" fill="#ECECED"/>
<path transform="translate(785,1039)" d="m0 0h5l1 3h-5v2l5 3 1 3-2 3-7-1v-2l6 1-1-3-4-2-1-5z" fill="#FEFEFE"/>
<path transform="translate(822,1039)" d="m0 0h2v7l4-3 2 1-1 5 2 4h-3l-2-5h-2v5h-2z" fill="#FEFEFE"/>
<path transform="translate(723,1039)" d="m0 0 3 1 2 9h2l2-9h3l-4 12h-4z" fill="#FEFEFE"/>
<path transform="translate(115,1034)" d="m0 0h2l1 2 5-2v2l-5 3-1 15h-2z" fill="#FEFEFE"/>
<path transform="translate(213,1026)" d="m0 0 7 1 1 7-2 1-3-2-2 1v-6z" fill="#9D9DA4"/>
<path transform="translate(956,1039)" d="m0 0h2v12h3v2h-8v-2h3l-1-8-2-1z" fill="#FEFEFE"/>
<path transform="translate(839,1043)" d="m0 0h5v3h-4v4h5l-1 3h-5l-2-2v-6z" fill="#FEFEFE"/>
<path transform="translate(815,1043)" d="m0 0 5 1-1 2h-4v4h5l-1 3h-5l-2-2v-6z" fill="#FEFEFE"/>
<path transform="translate(750,1043)" d="m0 0 5 1-1 2h-4v4h5l-1 3h-5l-2-2v-6z" fill="#FEFEFE"/>
<path transform="translate(758,1041)" d="m0 0h2v2h2v2h-2v6h2v2l-4-1v-7h-2v-2h2z" fill="#FEFEFE"/>
<path transform="translate(794,1041)" d="m0 0h2v2h2v2h-2v6h2v2l-4-1-1-9z" fill="#FEFEFE"/>
<path transform="translate(850,1045)" d="m0 0 5 1-1 5-5-1z" fill="#0F0F1F"/>
<path transform="translate(803,1045)" d="m0 0 5 1-1 5-5-1z" fill="#0E0E1D"/>
<path transform="translate(767,1045)" d="m0 0 5 1-1 5-5-1z" fill="#0E0E1D"/>
<path transform="translate(776,1043)" d="m0 0h4 2v2l-3 3-1 5h-2z" fill="#FEFEFE"/>
</svg>
