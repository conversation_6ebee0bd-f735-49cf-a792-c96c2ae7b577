#!/bin/sh
set -e

echo "=== Starting Dokploy Build Script ==="

# Install all dependencies at root
echo "--- Installing all dependencies at root ---"
npm ci && ls -la node_modules

# Install client dependencies
echo "--- Installing client dependencies ---"
cd client && npm install

# Navigate back to root and clean client build directory
echo "--- Cleaning client build directory ---"
cd ..
rm -rf client/build

# Build client with memory limit and set REACT_APP_API_URL
echo "--- Building client ---"
export REACT_APP_API_URL=https://rpl.xendekweb.com/api
cd client && CI=false NODE_OPTIONS=--max-old-space-size=4096 npm run build

# Install server dependencies
echo "--- Installing server dependencies ---"
cd ../server && npm install
echo "--- Build complete ---"
