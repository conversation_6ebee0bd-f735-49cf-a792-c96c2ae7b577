#!/bin/bash

# Complete GitHub Project Setup Script for RPL Cricket Application
# 
# This script sets up everything needed for the GitHub project board:
# 1. Creates all necessary labels
# 2. Creates project milestones
# 3. Creates all GitHub issues from our detailed task breakdown
# 4. Sets up GitHub Actions for automation
#
# Prerequisites:
# 1. Install GitHub CLI: https://cli.github.com/
# 2. Authenticate: gh auth login
#
# Usage:
# chmod +x scripts/setup-complete-github-project.sh
# ./scripts/setup-complete-github-project.sh

set -e

echo "🚀 Complete GitHub Project Setup for RPL Cricket Application"
echo "==========================================================="

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI is not installed. Please install it first:"
    echo "   https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub. Please run:"
    echo "   gh auth login"
    exit 1
fi

# Get repository information
REPO_INFO=$(gh repo view --json owner,name)
OWNER=$(echo $REPO_INFO | jq -r '.owner.login')
REPO_NAME=$(echo $REPO_INFO | jq -r '.name')

echo "📍 Repository: $OWNER/$REPO_NAME"
echo "✅ GitHub CLI is ready"
echo ""

# Function to create labels
create_labels() {
    echo "📋 Creating repository labels..."
    
    # Priority Labels
    gh label create "🔴 Critical" --color "B60205" --description "Must be completed immediately" --force
    gh label create "🟠 High" --color "D93F0B" --description "Important for current milestone" --force
    gh label create "🟡 Medium" --color "FBCA04" --description "Should be completed soon" --force
    gh label create "🟢 Low" --color "0E8A16" --description "Nice to have, can be deferred" --force
    
    # Type Labels
    gh label create "🐛 Bug" --color "D73A4A" --description "Issues and fixes" --force
    gh label create "✨ Feature" --color "A2EEEF" --description "New functionality" --force
    gh label create "🔧 Enhancement" --color "7057FF" --description "Improvements to existing features" --force
    gh label create "📚 Documentation" --color "0075CA" --description "Documentation updates" --force
    gh label create "🧪 Testing" --color "F9D0C4" --description "Testing-related tasks" --force
    gh label create "🚀 Deployment" --color "C5DEF5" --description "Deployment and infrastructure" --force
    
    # Component Labels
    gh label create "🎨 Frontend" --color "E99695" --description "React/UI related" --force
    gh label create "⚙️ Backend" --color "F7E7CE" --description "Node.js/Express/API related" --force
    gh label create "🗄️ Database" --color "FEF2C0" --description "MongoDB/Data related" --force
    gh label create "🔍 OCR" --color "C2E0C6" --description "OCR processing related" --force
    gh label create "🏆 Auction" --color "BFDADC" --description "Auction system related" --force
    gh label create "👥 Team" --color "C5DEF5" --description "Team management related" --force
    gh label create "🏟️ Tournament" --color "BFD4F2" --description "Tournament related" --force
    gh label create "📊 Analytics" --color "D4C5F9" --description "Analytics and reporting" --force
    gh label create "🎮 Big Ant Cricket 24" --color "FFD700" --description "Big Ant Cricket 24 specific features" --force
    
    echo "✅ Labels created successfully"
}

# Function to create milestones
create_milestones() {
    echo "🎯 Creating project milestones..."
    
    # Calculate due dates (2 weeks, 5 weeks, 7 weeks, 8 weeks, 10 weeks from now)
    DUE_2W=$(date -d "+14 days" +%Y-%m-%d)
    DUE_5W=$(date -d "+35 days" +%Y-%m-%d)
    DUE_7W=$(date -d "+49 days" +%Y-%m-%d)
    DUE_8W=$(date -d "+56 days" +%Y-%m-%d)
    DUE_10W=$(date -d "+70 days" +%Y-%m-%d)
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Core System Completion" -f description="Complete Transfer Market System, Match Result Processing, and Post-Auction Processing" -f due_on="${DUE_2W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Big Ant Cricket 24 Integration" -f description="Implement Skill Points & Rating System, Performance Milestone Bonuses, and Comprehensive Leaderboards" -f due_on="${DUE_5W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Advanced Analytics" -f description="Strike Rate & Economy Calculations, Fastest Milestones Tracking, and Venue-based Performance Analytics" -f due_on="${DUE_7W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Production Optimization" -f description="Complete Testing & Quality Assurance, Database Optimization, and Performance Improvements" -f due_on="${DUE_8W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Enhanced Features" -f description="Test Match Support, Enhanced Match Validation, and Advanced Search & Filtering" -f due_on="${DUE_10W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    echo "✅ Milestones created successfully"
}

# Function to create all issues
create_all_issues() {
    echo "📝 Creating all GitHub issues from detailed task breakdown..."
    
    # CRITICAL PRIORITY ISSUES
    echo "🔴 Creating CRITICAL priority issues..."
    
    gh issue create --title "🔄 Complete Transfer Market System" --body "## Description
Complete the player trading system between teams with market value calculations and transfer history.

## Acceptance Criteria
- [ ] Player trading between teams
- [ ] Market value calculations  
- [ ] Transfer history tracking
- [ ] Transaction validation

## Files
- \`client/src/pages/TransferMarket/\`
- \`server/controllers/\` (needs completion)

## Phase: 2.6 Player & Team Management
## Priority: 🔴 Critical
## Estimated Effort: 1 week" --label "🔴 Critical,✨ Feature,🏆 Auction,🎨 Frontend" --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Implement Advanced Skill Points & Rating System" --body "## Description
Implement automatic rating increases based on skill points (5000 points = +1 rating), configurable thresholds.

## Acceptance Criteria
- [ ] 1 run = 1 skill point
- [ ] 1 wicket = 10 skill points  
- [ ] 5000 skill points = +1 rating increase
- [ ] Admin configurable thresholds
- [ ] Automatic rating updates after each match

## Big Ant Cricket 24 Alignment
This is a core feature from the original vision where player ratings increase based on performance.

## Phase: 7.1 Big Ant Cricket 24 Integration Features
## Priority: 🔴 Critical
## Estimated Effort: 1 week" --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,⚙️ Backend" --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Add Performance Milestone Bonuses" --body "## Description
Implement milestone bonus system: 30's (+60 points), 50's (+90 points), 100's (+150 points), 3W hauls (+60 points), 5W hauls (+90 points).

## Acceptance Criteria
- [ ] Batting milestones: 30 (+60), 50 (+90), 100 (+150) bonus points
- [ ] Bowling milestones: 3W (+60), 5W (+90) bonus points
- [ ] Automatic detection from scorecard OCR
- [ ] Historical milestone tracking

## Big Ant Cricket 24 Alignment
Essential for the original vision where milestones provide bonus skill points.

## Phase: 7.2 Big Ant Cricket 24 Integration Features
## Priority: 🔴 Critical
## Estimated Effort: 1 week" --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,🔍 OCR" --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    # HIGH PRIORITY ISSUES
    echo "🟠 Creating HIGH priority issues..."
    
    gh issue create --title "🔄 Refine Match Result Processing" --body "## Description
Complete score validation, winner determination, player statistics updates, and match verification.

## Acceptance Criteria
- [ ] Refine score validation logic
- [ ] Complete player statistics updates
- [ ] Improve match verification
- [ ] Winner determination accuracy

## Files
- \`server/controllers/matchOutcomeController.js\`
- \`server/controllers/scorecardController.js\`

## Phase: 3.5 Tournament & Match Management
## Priority: 🟠 High
## Estimated Effort: 1 week" --label "🟠 High,🔧 Enhancement,🏟️ Tournament,⚙️ Backend" --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    gh issue create --title "🔄 Complete Post-Auction Processing" --body "## Description
Finalize player assignment to teams, payment processing, and auction result finalization.

## Acceptance Criteria
- [ ] Complete player assignment logic
- [ ] Finalize payment processing
- [ ] Auction result notifications
- [ ] Post-auction cleanup

## Phase: 4.6 Auction System
## Priority: 🟠 High
## Estimated Effort: 1 week" --label "🟠 High,🔧 Enhancement,🏆 Auction,⚙️ Backend" --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Build Comprehensive Leaderboards" --body "## Description
Create comprehensive leaderboards for Most Runs, 30's, 50's, 100's, Wickets, 3W/5W Hauls, MOM - format and tournament wise.

## Acceptance Criteria
- [ ] Multiple leaderboard categories
- [ ] Format-wise filtering (T10, T20, ODI, Test)
- [ ] Tournament-wise and overall statistics
- [ ] Real-time updates after each match

## Big Ant Cricket 24 Alignment
Leaderboards are essential for competitive gaming experience.

## Phase: 7.3 Big Ant Cricket 24 Integration Features
## Priority: 🟠 High
## Estimated Effort: 2 weeks" --label "🟠 High,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    gh issue create --title "🧪 Complete Testing & Quality Assurance" --body "## Description
Add comprehensive test coverage, set up automated testing pipeline.

## Acceptance Criteria
- [ ] Unit tests for all major components
- [ ] Integration tests for API endpoints
- [ ] End-to-end testing for user workflows
- [ ] Automated testing pipeline

## Phase: 6.4 Production & Deployment
## Priority: 🟠 High
## Estimated Effort: 2 weeks" --label "🟠 High,🧪 Testing,🔧 Enhancement,🚀 Deployment" --milestone "Production Optimization" || echo "⚠️  Issue may already exist"

    # MEDIUM PRIORITY ISSUES
    echo "🟡 Creating MEDIUM priority issues..."
    
    gh issue create --title "🎮 Add Strike Rate & Economy Calculations" --body "## Description
Auto-calculate strike rates (runs/balls) and economy rates (runs/overs) from scorecard OCR.

## Acceptance Criteria
- [ ] Strike rate calculation: (runs/balls) * 100
- [ ] Economy rate calculation: runs conceded/overs bowled
- [ ] Automatic calculation from OCR data
- [ ] Historical tracking and trends

## Phase: 7.4 Big Ant Cricket 24 Integration Features
## Priority: 🟡 Medium
## Estimated Effort: 1 week" --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Enhanced Match Validation" --body "## Description
Auto-detect chase/defend from scorecard, improved team name validation.

## Acceptance Criteria
- [ ] Automatic chase/defend detection
- [ ] Enhanced team name validation
- [ ] Match format auto-detection
- [ ] Improved error handling and user feedback

## Phase: 7.8 Big Ant Cricket 24 Integration Features
## Priority: 🟡 Medium
## Estimated Effort: 1 week" --label "🟡 Medium,🔧 Enhancement,🎮 Big Ant Cricket 24,🔍 OCR" --milestone "Enhanced Features" || echo "⚠️  Issue may already exist"

    gh issue create --title "🗄️ Database Optimization" --body "## Description
MongoDB indexing, query optimization, connection pooling, performance tuning.

## Acceptance Criteria
- [ ] Add database indexes for frequently queried fields
- [ ] Optimize slow queries
- [ ] Implement connection pooling
- [ ] Performance monitoring and tuning

## Phase: 6.2 Production & Deployment
## Priority: 🟡 Medium
## Estimated Effort: 1 week" --label "🟡 Medium,🔧 Enhancement,🗄️ Database,⚙️ Backend" --milestone "Production Optimization" || echo "⚠️  Issue may already exist"

    # ADDITIONAL BIG ANT CRICKET 24 FEATURES
    echo "🎮 Creating additional Big Ant Cricket 24 features..."
    
    gh issue create --title "🎮 Fastest Milestones Tracking" --body "## Description
Track fastest 50's, 100's with Top 5 rankings, dynamic updates when records are broken.

## Acceptance Criteria
- [ ] Track fastest 50's and 100's (balls faced)
- [ ] Top 5 rankings for each milestone
- [ ] Dynamic updates when records are broken
- [ ] Personal best tracking

## Phase: 7.5 Big Ant Cricket 24 Integration Features
## Priority: 🟡 Medium
## Estimated Effort: 1.5 weeks" --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Venue-based Performance Analytics" --body "## Description
Track performance by venue with tournament filtering capabilities.

## Acceptance Criteria
- [ ] Performance statistics by venue
- [ ] Tournament filtering
- [ ] Venue-specific leaderboards
- [ ] Historical venue performance

## Phase: 7.6 Big Ant Cricket 24 Integration Features
## Priority: 🟡 Medium
## Estimated Effort: 1 week" --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" --milestone "Advanced Analytics" || echo "⚠️  Issue may already exist"

    gh issue create --title "🎮 Test Match Support" --body "## Description
4-innings match processing, extended scorecard handling for Test format.

## Acceptance Criteria
- [ ] Support for 4-innings matches
- [ ] Extended scorecard processing
- [ ] Test-specific statistics
- [ ] Multi-day match handling

## Phase: 7.7 Big Ant Cricket 24 Integration Features
## Priority: 🟡 Medium
## Estimated Effort: 2 weeks" --label "🟡 Medium,✨ Feature,🎮 Big Ant Cricket 24,🔍 OCR" --milestone "Enhanced Features" || echo "⚠️  Issue may already exist"

    echo "✅ All issues created successfully"
}

# Function to create GitHub Actions workflow
create_automation() {
    echo "⚙️ Setting up GitHub Actions automation..."
    
    # Create .github/workflows directory
    mkdir -p .github/workflows
    
    # Create workflow file for project board automation
    cat > .github/workflows/project-board-automation.yml << 'EOF'
name: Project Board Automation

on:
  issues:
    types: [opened, closed, reopened, labeled, assigned]
  pull_request:
    types: [opened, closed, merged, ready_for_review]
  push:
    branches: [main, master]

jobs:
  update-project-board:
    runs-on: ubuntu-latest
    steps:
      - name: Add new issues to project board
        if: github.event.action == 'opened' && github.event.issue
        run: |
          echo "New issue created: ${{ github.event.issue.title }}"
          echo "Issue number: ${{ github.event.issue.number }}"
          
      - name: Move assigned issues to In Progress
        if: github.event.action == 'assigned' && github.event.issue
        run: |
          echo "Issue assigned: ${{ github.event.issue.title }}"
          
      - name: Move closed issues to Done
        if: github.event.action == 'closed' && github.event.issue
        run: |
          echo "Issue closed: ${{ github.event.issue.title }}"
EOF

    echo "✅ GitHub Actions workflow created"
}

# Main execution
main() {
    echo "Starting complete GitHub project setup..."
    echo ""
    
    create_labels
    echo ""
    
    create_milestones
    echo ""
    
    create_all_issues
    echo ""
    
    create_automation
    echo ""
    
    echo "🎉 Complete GitHub Project Setup completed successfully!"
    echo ""
    echo "📊 Summary:"
    echo "• ✅ Repository labels created"
    echo "• ✅ Project milestones created"
    echo "• ✅ 13 GitHub issues created (3 Critical, 4 High, 6 Medium)"
    echo "• ✅ GitHub Actions automation configured"
    echo ""
    echo "📋 Next steps:"
    echo "1. Go to your GitHub repository: https://github.com/$OWNER/$REPO_NAME"
    echo "2. Click on 'Projects' tab and create a new project board"
    echo "3. Add columns: Backlog, Ready, In Progress, Review, Done, Blocked"
    echo "4. Add the created issues to the Backlog column"
    echo "5. Start working on the Critical priority tasks first"
    echo ""
    echo "🎯 Priority order (start with these):"
    echo "1. 🔄 Complete Transfer Market System"
    echo "2. 🎮 Implement Advanced Skill Points & Rating System"
    echo "3. 🎮 Add Performance Milestone Bonuses"
    echo ""
    echo "🔗 View your issues: https://github.com/$OWNER/$REPO_NAME/issues"
    echo "🔗 View your milestones: https://github.com/$OWNER/$REPO_NAME/milestones"
}

# Run main function
main
