# IPL Player Data Scraper

This script scrapes player data from the IPL website and prepares it for use in the cricket player cards.

## Features

- Scrapes player names and images from the IPL website
- Downloads player images to your local project
- Generates a JSON file with player data
- Adds default values for player statistics

## Setup

1. Navigate to the scripts directory:
   ```
   cd scripts
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the script:
   ```
   npm start
   ```

## Output

The script will:
1. Create a directory at `client/public/uploads/players/ipl` for player images
2. Download all player images to this directory
3. Create a JSON file at `client/public/data/ipl-players.json` with player data

## Using the Data

You can use the generated JSON data to populate your player cards:

```javascript
// Example code to load and use the player data
import React, { useState, useEffect } from 'react';
import CricketPlayerCard from '../components/CricketPlayerCard';

const PlayerGallery = () => {
  const [players, setPlayers] = useState([]);
  
  useEffect(() => {
    // Load the player data
    fetch('/data/ipl-players.json')
      .then(response => response.json())
      .then(data => setPlayers(data))
      .catch(error => console.error('Error loading player data:', error));
  }, []);
  
  return (
    <div className="player-gallery">
      {players.map(player => (
        <CricketPlayerCard 
          key={player.name}
          player={player}
          selected={false}
          onSelect={() => {}}
          onEdit={() => {}}
          onDelete={() => {}}
        />
      ))}
    </div>
  );
};

export default PlayerGallery;
```

## Customization

You can modify the script to:
- Scrape different teams by changing the `teamUrl` variable
- Add more player details by extending the scraping logic
- Customize the default values for player statistics
