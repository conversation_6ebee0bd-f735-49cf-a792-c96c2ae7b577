import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Button,
  Chip,
  Typography,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Gavel as GavelIcon,
  AccessTime as AccessTimeIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { formatDistanceToNow, differenceInSeconds } from 'date-fns';
import './AuctionPlayerCard.css';

const AuctionPlayerCard = ({ auction, onBid, currentUser, onEdit, onDelete, isAdmin = false }) => {
  const [timeLeft, setTimeLeft] = useState('');
  const [status, setStatus] = useState({ text: '', color: '' });
  const timerRef = useRef(null);

  // Update time left and status
  const updateTimeLeft = useCallback(() => {
    const now = new Date();
    const startTime = new Date(auction.startTime);
    const endTime = new Date(auction.endTime);

    // Determine status based on auction status
    if (!auction.isActive) {
      setStatus({ text: 'Inactive', color: 'default' });
      setTimeLeft('Inactive');
      return;
    }

    if (auction.status === 'scheduled' || now < startTime) {
      setStatus({ text: 'Scheduled', color: 'info' });
      setTimeLeft(`Starts ${formatDistanceToNow(startTime, { addSuffix: true })}`);
      return;
    }

    if (auction.status === 'live' || (now >= startTime && now < endTime)) {
      setStatus({ text: 'Live', color: 'success' });

      // Calculate time left
      const secondsLeft = differenceInSeconds(endTime, now);
      if (secondsLeft <= 0) {
        // If time is up, mark as completed instead of "Ending..."
        if (auction.currentBidder) {
          setStatus({ text: 'Completed', color: 'primary' });
          setTimeLeft('Auction ended');
        } else {
          setStatus({ text: 'Expired', color: 'error' });
          setTimeLeft('No bids received');
        }
        return;
      }

      // Format time left
      const hours = Math.floor(secondsLeft / 3600);
      const minutes = Math.floor((secondsLeft % 3600) / 60);
      const seconds = secondsLeft % 60;

      if (hours > 0) {
        setTimeLeft(`${hours}h ${minutes}m ${seconds}s`);
      } else if (minutes > 0) {
        setTimeLeft(`${minutes}m ${seconds}s`);
      } else {
        setTimeLeft(`${seconds}s`);
      }
      return;
    }

    if (auction.status === 'completed' || auction.status === 'cancelled' || now >= endTime) {
      if (auction.currentBidder) {
        setStatus({ text: 'Completed', color: 'primary' });
        setTimeLeft('Auction ended');
      } else {
        setStatus({ text: 'Expired', color: 'error' });
        setTimeLeft('No bids received');
      }
      return;
    }
  }, [auction]);

  // Set up timer
  useEffect(() => {
    updateTimeLeft();

    // Update every second for live auctions
    timerRef.current = setInterval(() => {
      updateTimeLeft();
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [auction, updateTimeLeft]);

  // Check if current user is the highest bidder
  const isHighestBidder = auction?.currentBidder && auction?.currentBidder._id === currentUser?.id;

  // Check if auction is completed and user is the winner
  const isWinner = auction?.status === 'completed' && isHighestBidder;

  // Calculate minimum bid
  const minimumBid = (auction?.currentBid || 0) + (auction?.minimumBidIncrement || 100);

  // Get player type abbreviation
  const getPlayerTypeAbbr = (type) => {
    if (!type) return 'BAT';

    switch (type.toLowerCase()) {
      case 'batsman':
        return 'BAT';
      case 'bowler':
        return 'BWL';
      case 'batting allrounder':
        return 'BAR';
      case 'bowling allrounder':
        return 'BAR';
      case 'allrounder':
        return 'ARD';
      case 'wicket keeper':
        return 'WKT';
      default:
        return 'BAT';
    }
  };

  // Get rating color
  const getRatingColor = (rating) => {
    if (!rating) return '#ffffff';

    if (rating >= 90) return '#97ea97'; // Green
    if (rating >= 80) return '#f5dd5d'; // Amber
    if (rating >= 70) return '#6cabdd'; // Blue
    if (rating >= 60) return '#d580ff'; // Purple
    return '#ffffff'; // White
  };

  return (
    <div
      className="cricket-player-card auction-card"
      onClick={(e) => {
        // Prevent click if it's on an admin control
        if (e.target.closest('.admin-controls')) {
          e.stopPropagation();
          return;
        }
      }}
      style={{
        backgroundImage: `url(${process.env.PUBLIC_URL}/card-backgrounds/cricket-card-bg.png)`
      }}
    >
      {/* Status Chip */}
      <div className="auction-status">
        <Chip
          label={status.text}
          color={status.color}
          size="small"
          sx={{ fontWeight: 'bold', height: 20 }}
        />
      </div>
      
      {/* Admin Controls */}
      {isAdmin && (
        <div className="admin-controls">
          <Tooltip title="Edit Auction">
            <IconButton 
              size="small" 
              onClick={(e) => {
                e.stopPropagation();
                onEdit(auction);
              }}
              sx={{ 
                bgcolor: 'primary.main', 
                color: 'white',
                '&:hover': { bgcolor: 'primary.dark' },
                mr: 0.5,
                zIndex: 100,
                position: 'relative',
                pointerEvents: 'auto'
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete Auction">
            <IconButton 
              size="small" 
              onClick={(e) => {
                e.stopPropagation();
                onDelete(auction);
              }}
              sx={{ 
                bgcolor: 'error.main', 
                color: 'white',
                '&:hover': { bgcolor: 'error.dark' },
                zIndex: 100,
                position: 'relative',
                pointerEvents: 'auto'
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </div>
      )}

      {/* Your Bid Indicator */}
      {isHighestBidder && !isWinner && (
        <div className="your-bid-indicator">
          <Chip
            label="Your Bid"
            color="success"
            size="small"
            sx={{ fontWeight: 'bold', height: 20 }}
          />
        </div>
      )}

      {/* Won Indicator */}
      {isWinner && (
        <div className="your-bid-indicator">
          <Chip
            label="Won"
            color="primary"
            size="small"
            sx={{
              fontWeight: 'bold',
              height: 20,
              backgroundColor: '#4caf50',
              color: 'white'
            }}
          />
        </div>
      )}

      <div className="player-card-top">
        <div className="player-master-info">
          <div className="player-rating">
            <span style={{ color: getRatingColor(auction?.player?.ratings?.overall) }}>
              {auction?.player?.ratings?.overall || '??'}
            </span>
          </div>
          <div className="player-position">
            <span>{getPlayerTypeAbbr(auction?.player?.type)}</span>
          </div>
          <div className="player-nation">
            <img
              src={`${process.env.PUBLIC_URL}/flags/${auction?.player?.nationality?.toLowerCase().replace(/\s+/g, '-')}.svg`}
              alt={auction?.player?.nationality}
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = `${process.env.PUBLIC_URL}/flags/default-flag.svg`;
              }}
            />
          </div>
        </div>
        <div className="player-picture">
          <img
            src={auction?.player?.image || '/default-player.png'}
            alt={auction?.player?.name || 'Player'}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = '/default-player.png';
            }}
          />
        </div>
      </div>

      <div className="player-card-bottom">
        {/* Player name directly on card background */}
        <div className="player-name-direct">
          <span>{auction?.player?.name?.toUpperCase() || 'PLAYER'}</span>
        </div>
        <div className="player-info">
          {/* Player Info - 2 Column Grid */}
          <div className="player-info-grid">
            <div className="info-row">
              <div className="info-cell">
                <span className="info-label">BAT:</span>
                <span className="info-value">{auction?.player?.battingHand || 'RHB'}</span>
              </div>
              <div className="info-cell">
                <span className="info-label">BWL:</span>
                <span className="info-value">{auction?.player?.bowlingHand && auction?.player?.bowlingHand !== 'None' ? auction?.player?.bowlingHand : '-'}</span>
              </div>
            </div>
            <div className="info-row">
              <div className="info-cell">
                <span className="info-label">HGT:</span>
                <span className="info-value">{auction?.player?.height || '-'}</span>
              </div>
              <div className="info-cell">
                <span className="info-label">TYP:</span>
                <span className="info-value">{auction?.player?.type || 'Batsman'}</span>
              </div>
            </div>
          </div>

          {/* Auction Info */}
          <div className="auction-info">
            <div className="current-bid">
              <span className="bid-label">Current Bid</span>
              <span className="bid-amount">{(auction?.currentBid || 0).toLocaleString()} $</span>
              {auction?.currentBidder && (
                <span className="bidder-info">
                  {isHighestBidder
                    ? 'You are the highest bidder!'
                    : `Highest: ${auction.currentBidder?.username || 'Unknown'}`
                  }
                </span>
              )}
            </div>

            <div className="time-left">
              <AccessTimeIcon fontSize="small" />
              <span>{timeLeft}</span>
            </div>
          </div>

          {/* Bid Button - Moved inside player-info */}
          <div className="auction-actions">
            <Button
              fullWidth
              variant="contained"
              color="primary"
              size="small"
              startIcon={<GavelIcon />}
              onClick={(e) => {
                e.stopPropagation();
                onBid(auction, minimumBid);
              }}
              disabled={status.text !== 'Live' || isHighestBidder || new Date() >= new Date(auction.endTime)}
              sx={{
                borderRadius: '20px',
                textTransform: 'none',
                fontWeight: 'bold'
              }}
            >
              {isHighestBidder ? 'Highest Bid' : `Bid ${minimumBid.toLocaleString()} $`}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuctionPlayerCard;
