import axios from 'axios';
import { API_URL } from '../config';

const API = axios.create({
  baseURL: `${API_URL}/export`,
});

// Add auth token to requests if available
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  
  if (token) {
    config.headers['x-auth-token'] = token;
  }
  
  return config;
});

/**
 * Export all IPL players to CSV
 * @returns {Promise} - Promise resolving to export result
 */
export const exportIplPlayers = async () => {
  try {
    console.log('Calling API to export IPL players...');
    
    const response = await API.post('/ipl-players');
    console.log('Export response:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Error exporting IPL players:', error);
    throw error.response?.data || error.message;
  }
};

/**
 * Get download URL for an exported file
 * @param {string} filename - Filename to download
 * @returns {string} - Download URL
 */
export const getDownloadUrl = (filename) => {
  return `${API_URL}/export/downloads/${filename}`;
};

export default {
  exportIplPlayers,
  getDownloadUrl
};
