import React, { useState } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Divider,
  Alert,
  Button
} from '@mui/material';
import ImportIplPlayers from './ImportIplPlayers';

/**
 * Test component for the IPL Player Import feature
 * This component allows testing the import functionality without affecting the database
 */
const TestIplImport = () => {
  const [importedPlayers, setImportedPlayers] = useState([]);
  const [showResults, setShowResults] = useState(false);

  // This function simulates what would happen when players are imported
  // In a real implementation, this would call your API to save players
  const handleImportPlayers = (players) => {
    console.log('Players to import:', players);
    setImportedPlayers(players);
    setShowResults(true);
  };

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 3, my: 3 }}>
        <Typography variant="h4" gutterBottom>
          Test IPL Player Import
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          This is a test interface for the IPL Player Import feature. 
          Players will not be saved to the database.
        </Alert>
        
        <ImportIplPlayers onImportPlayers={handleImportPlayers} />
        
        {showResults && (
          <>
            <Divider sx={{ my: 4 }} />
            
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h5">
                Import Results ({importedPlayers.length} players)
              </Typography>
              
              <Button 
                variant="outlined" 
                onClick={() => setShowResults(false)}
              >
                Clear Results
              </Button>
            </Box>
            
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                bgcolor: '#f5f5f5', 
                maxHeight: '400px', 
                overflow: 'auto',
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }}
            >
              <pre>{JSON.stringify(importedPlayers, null, 2)}</pre>
            </Paper>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default TestIplImport;
