import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Alert,
  IconButton,
  useMediaQuery,
  useTheme,
  Paper,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Upload as UploadIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  SelectAll as SelectAllIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { createPlayer, getPlayers, deletePlayer, updatePlayer } from '../../services/playerService';
import { uploadPlayerImage } from '../../services/uploadService';
import ImportPlayersDialog from '../../components/players/ImportPlayersDialog';
import CricketPlayerCard from '../../components/CricketPlayerCard';
import MatchPlayerPhotos from '../../components/MatchPlayerPhotos';
import ExportIplPlayers from '../../components/ExportIplPlayers';

const PlayerManagement = () => {
  const { user, token } = useAuth();
  const theme = useTheme();
  const [players, setPlayers] = useState([]);
  const [allPlayers, setAllPlayers] = useState([]);
  const [filteredPlayers, setFilteredPlayers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [photoMatchDialogOpen, setPhotoMatchDialogOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  // Bulk actions states
  const [selectedPlayers, setSelectedPlayers] = useState([]);
  const [confirmBulkDeleteOpen, setConfirmBulkDeleteOpen] = useState(false);

  // Filter states
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    battingHand: '',
    bowlingHand: '',
    nationality: ''
  });

  // Unique values for filters
  const [uniqueNationalities, setUniqueNationalities] = useState([]);

  // Check authentication status
  useEffect(() => {
    console.log('Current user:', user);
    console.log('Token exists:', !!token);
    console.log('User role:', user?.role);

    if (!user) {
      setError('You must be logged in to access this page');
    } else if (user.role !== 'admin') {
      setError('You must be an admin to access this page');
    }
  }, [user, token]);

  const [formData, setFormData] = useState({
    name: '',
    nationality: '',
    age: 25, // Default age value since we're removing the field from the form
    height: '',
    type: '',
    battingHand: '',
    bowlingHand: 'None',
    ratings: {
      overall: 70
    }
  });

  const playerTypes = ['Batsman', 'Bowler', 'Batting Allrounder', 'Bowling Allrounder', 'Allrounder', 'Wicket Keeper'];
  const battingHands = [
    { value: 'RHB', label: 'Right Hand Bat' },
    { value: 'LHB', label: 'Left Hand Bat' }
  ];
  const bowlingHands = [
    { value: 'None', label: 'Does Not Bowl' },
    { value: 'RF', label: 'Right Fast' },
    { value: 'LF', label: 'Left Fast' },
    { value: 'RFM', label: 'Right Fast Medium' },
    { value: 'LFM', label: 'Left Fast Medium' },
    { value: 'RM', label: 'Right Medium' },
    { value: 'LM', label: 'Left Medium' },
    { value: 'RHWS', label: 'Right Wrist Spin' },
    { value: 'LHWS', label: 'Left Wrist Spin' },
    { value: 'RHFS', label: 'Right Finger Spin' },
    { value: 'LHFS', label: 'Left Finger Spin' }
  ];

  useEffect(() => {
    fetchPlayers();
  }, []);

  // Apply filters whenever allPlayers or filters change
  useEffect(() => {
    if (allPlayers.length > 0) {
      applyFilters();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allPlayers, filters]);

  const fetchPlayers = async () => {
    try {
      setLoading(true);
      const response = await getPlayers();
      setAllPlayers(response.players);

      // Extract unique nationalities for the filter dropdown
      const nationalities = [...new Set(response.players.map(player => player.nationality))].sort();
      setUniqueNationalities(nationalities);
    } catch (err) {
      setError('Failed to fetch players');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Filter handling functions
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSearchChange = (e) => {
    setFilters(prev => ({
      ...prev,
      search: e.target.value
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      type: '',
      battingHand: '',
      bowlingHand: '',
      nationality: ''
    });
  };

  const applyFilters = () => {
    let result = [...allPlayers];

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(player =>
        player.name.toLowerCase().includes(searchTerm) ||
        player.nationality.toLowerCase().includes(searchTerm)
      );
    }

    // Apply type filter
    if (filters.type) {
      result = result.filter(player => player.type === filters.type);
    }

    // Apply batting hand filter
    if (filters.battingHand) {
      result = result.filter(player => player.battingHand === filters.battingHand);
    }

    // Apply bowling hand filter
    if (filters.bowlingHand) {
      result = result.filter(player => player.bowlingHand === filters.bowlingHand);
    }

    // Apply nationality filter
    if (filters.nationality) {
      result = result.filter(player => player.nationality === filters.nationality);
    }

    setFilteredPlayers(result);
    setPlayers(result);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name.includes('ratings.')) {
      const ratingField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        ratings: {
          ...prev.ratings,
          [ratingField]: Number(value)
        }
      }));
    } else if (name === 'height') {
      // Format height to ensure it includes "cm"
      let heightValue = value;
      if (heightValue && !heightValue.toLowerCase().includes('cm')) {
        heightValue = `${heightValue} cm`;
      }
      setFormData(prev => ({
        ...prev,
        [name]: heightValue
      }));
      console.log('Height entered (formatted):', heightValue);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.nationality || !formData.height ||
        !formData.type || !formData.battingHand) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      let imageUrl = selectedPlayer?.image || '/uploads/players/default.png';

      if (imageFile) {
        const imageFormData = new FormData();
        // Field name must match multer's single('playerImage')
        imageFormData.append('playerImage', imageFile);
        const uploadResult = await uploadPlayerImage(imageFormData);
        imageUrl = uploadResult.filePath;
      }

      // FIXED: Use the state formData instead of the form element
      const playerData = {
        ...formData, // Use the state formData, not the form element
        image: imageUrl
      };

      console.log('Sending player data:', playerData);

      if (selectedPlayer) {
        await updatePlayer(selectedPlayer._id, playerData);
        setSuccess('Player updated successfully');
      } else {
        await createPlayer(playerData);
        setSuccess('Player created successfully');
      }
      setDialogOpen(false);
      fetchPlayers();
    } catch (err) {
      setError(err.message || 'Failed to save player');
    }
  };

  const handleDelete = async (playerId) => {
    if (window.confirm('Are you sure you want to delete this player?')) {
      try {
        await deletePlayer(playerId);
        setSuccess('Player deleted successfully');
        fetchPlayers();
      } catch (err) {
        setError(err.message || 'Failed to delete player');
      }
    }
  };

  // Bulk action functions
  const togglePlayerSelection = (playerId) => {
    setSelectedPlayers(prev => {
      if (prev.includes(playerId)) {
        return prev.filter(id => id !== playerId);
      } else {
        return [...prev, playerId];
      }
    });
  };

  const selectAllPlayers = () => {
    if (selectedPlayers.length === players.length) {
      // If all are selected, deselect all
      setSelectedPlayers([]);
    } else {
      // Otherwise select all
      setSelectedPlayers(players.map(player => player._id));
    }
  };

  const handleBulkDelete = async () => {
    setConfirmBulkDeleteOpen(true);
  };

  const confirmBulkDelete = async () => {
    try {
      setLoading(true);
      // Delete each selected player
      for (const playerId of selectedPlayers) {
        await deletePlayer(playerId);
      }

      setSuccess(`Successfully deleted ${selectedPlayers.length} players`);
      setSelectedPlayers([]);
      setConfirmBulkDeleteOpen(false);
      fetchPlayers();
    } catch (err) {
      setError(err.message || 'Failed to delete players');
    } finally {
      setLoading(false);
    }
  };

  const cancelBulkDelete = () => {
    setConfirmBulkDeleteOpen(false);
  };

  const handleEdit = (player) => {
    setSelectedPlayer(player);
    setFormData({
      name: player.name,
      nationality: player.nationality,
      // Age field is now optional, but we'll keep it if it exists
      ...(player.age && { age: player.age }),
      height: player.height,
      type: player.type,
      battingHand: player.battingHand,
      bowlingHand: player.bowlingHand,
      ratings: player.ratings
    });
    setImagePreview(player.image);
    setDialogOpen(true);
  };

  const handleAdd = () => {
    setSelectedPlayer(null);
    setFormData({
      name: '',
      nationality: '',
      // Age field removed as it's no longer required
      height: '',
      type: '',
      battingHand: '',
      bowlingHand: 'None',
      ratings: {
        overall: 70
      }
    });
    setImagePreview(null);
    setDialogOpen(true);
  };

  // Handler for opening the photo matching dialog
  const handleOpenPhotoMatch = () => {
    setPhotoMatchDialogOpen(true);
  };

  // Handler for photo matching completion
  const handlePhotoMatchComplete = (result) => {
    // Refresh the player list to show updated photos
    fetchPlayers();
    setSuccess(`Updated photos for ${result.results.filter(r => r.success).length} players`);
    setPhotoMatchDialogOpen(false);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
        <Typography variant="h4" component="h1">
          Player Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<SelectAllIcon />}
            onClick={selectAllPlayers}
            sx={{ mr: 2 }}
          >
            {selectedPlayers.length === players.length && players.length > 0 ? 'Deselect All' : 'Select All'}
          </Button>
          <Button
            variant="contained"
            startIcon={<UploadIcon />}
            onClick={() => setImportDialogOpen(true)}
            sx={{ mr: 2 }}
          >
            Import Players
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleOpenPhotoMatch}
            sx={{ mr: 2 }}
          >
            Match Player Photos
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
          >
            Add Player
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {/* Export IPL Players Component */}
      <ExportIplPlayers />

      {/* Filter Section */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          borderRadius: 2,
          backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2" sx={{ display: 'flex', alignItems: 'center' }}>
            <FilterIcon sx={{ mr: 1 }} /> Filters
          </Typography>
          <Button
            size="small"
            startIcon={<ClearIcon />}
            onClick={clearFilters}
            disabled={!Object.values(filters).some(value => value !== '')}
          >
            Clear Filters
          </Button>
        </Box>

        <Grid container spacing={2}>
          {/* Search */}
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              label="Search by name or nationality"
              name="search"
              value={filters.search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
              }}
              sx={{ backgroundColor: 'background.paper' }}
            />
          </Grid>

          {/* Player Type Filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small" sx={{ backgroundColor: 'background.paper' }}>
              <InputLabel>Player Type</InputLabel>
              <Select
                name="type"
                value={filters.type}
                onChange={handleFilterChange}
                label="Player Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {playerTypes.map(type => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Batting Hand Filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small" sx={{ backgroundColor: 'background.paper' }}>
              <InputLabel>Batting Hand</InputLabel>
              <Select
                name="battingHand"
                value={filters.battingHand}
                onChange={handleFilterChange}
                label="Batting Hand"
              >
                <MenuItem value="">All</MenuItem>
                {battingHands.map(hand => (
                  <MenuItem key={hand.value} value={hand.value}>{hand.label}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Bowling Hand Filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small" sx={{ backgroundColor: 'background.paper' }}>
              <InputLabel>Bowling Hand</InputLabel>
              <Select
                name="bowlingHand"
                value={filters.bowlingHand}
                onChange={handleFilterChange}
                label="Bowling Hand"
              >
                <MenuItem value="">All</MenuItem>
                {bowlingHands.map(hand => (
                  <MenuItem key={hand.value} value={hand.value}>{hand.label}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Nationality Filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small" sx={{ backgroundColor: 'background.paper' }}>
              <InputLabel>Nationality</InputLabel>
              <Select
                name="nationality"
                value={filters.nationality}
                onChange={handleFilterChange}
                label="Nationality"
              >
                <MenuItem value="">All Countries</MenuItem>
                {uniqueNationalities.map(country => (
                  <MenuItem key={country} value={country}>{country}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Active Filters */}
        {Object.values(filters).some(value => value !== '') && (
          <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {filters.search && (
              <Chip
                label={`Search: ${filters.search}`}
                onDelete={() => setFilters(prev => ({ ...prev, search: '' }))}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
            {filters.type && (
              <Chip
                label={`Type: ${filters.type}`}
                onDelete={() => setFilters(prev => ({ ...prev, type: '' }))}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
            {filters.battingHand && (
              <Chip
                label={`Batting: ${battingHands.find(h => h.value === filters.battingHand)?.label || filters.battingHand}`}
                onDelete={() => setFilters(prev => ({ ...prev, battingHand: '' }))}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
            {filters.bowlingHand && (
              <Chip
                label={`Bowling: ${bowlingHands.find(h => h.value === filters.bowlingHand)?.label || filters.bowlingHand}`}
                onDelete={() => setFilters(prev => ({ ...prev, bowlingHand: '' }))}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
            {filters.nationality && (
              <Chip
                label={`Country: ${filters.nationality}`}
                onDelete={() => setFilters(prev => ({ ...prev, nationality: '' }))}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Box>
        )}
      </Paper>

      {loading ? (
        <LinearProgress />
      ) : (
        <>
          {players.length === 0 ? (
            <Paper
              sx={{
                p: 4,
                textAlign: 'center',
                backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
                borderRadius: 2,
                border: '1px dashed',
                borderColor: 'divider'
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No players found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Try adjusting your filters or add new players
              </Typography>
            </Paper>
          ) : (
            <>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Showing {players.length} {players.length === 1 ? 'player' : 'players'}
                  {Object.values(filters).some(value => value !== '') && ' with applied filters'}
                </Typography>
              </Box>

              <Grid container spacing={3}>
                {players.map((player) => (
                  <Grid item xs={12} sm={6} md={4} key={player._id}>
                    <CricketPlayerCard
                      player={player}
                      selected={selectedPlayers.includes(player._id)}
                      onSelect={togglePlayerSelection}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                    />
                  </Grid>
                ))}
              </Grid>
            </>
          )}
        </>
      )}

      {/* Create/Edit Player Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
        fullScreen={useMediaQuery(theme.breakpoints.down('sm'))}
        PaperProps={{
          sx: {
            minHeight: { xs: '100%', sm: '70vh' },
            maxHeight: '90vh',
            minWidth: { xs: '100%', sm: '600px' },
            p: { xs: 1, sm: 3 },
            backgroundColor: theme => theme.palette.mode === 'dark' ? 'background.paper' : '#fff',
            overflowY: 'auto'
          }
        }}
      >
        <DialogTitle
          sx={{
            pb: { xs: 2, sm: 3 },
            borderBottom: 1,
            borderColor: 'divider',
            mb: { xs: 2, sm: 3 }
          }}
        >
          <Typography variant="h5" component="div" fontWeight="medium" color="text.primary">
            {selectedPlayer ? 'Edit Player' : 'Create New Player'}
          </Typography>
        </DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <Grid container spacing={{ xs: 2, sm: 4 }}>
              {/* Basic Info Section */}
              <Grid item xs={12}>
                <Box sx={{
                  p: { xs: 2, sm: 3 },
                  mb: { xs: 2, sm: 3 },
                  backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider'
                }}>
                  <Typography variant="h6" gutterBottom sx={{
                    color: 'primary.main',
                    mb: { xs: 2, sm: 3 },
                    fontWeight: 'medium'
                  }}>
                    Basic Information
                  </Typography>
                  <Grid container spacing={{ xs: 2, sm: 4 }}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        variant="outlined"
                        sx={{
                          backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderWidth: '2px'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            fontWeight: 500,
                            color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.7)',
                            transform: 'translate(14px, 12px) scale(1)',
                            '&.MuiInputLabel-shrink': {
                              transform: 'translate(14px, -6px) scale(0.75)',
                              backgroundColor: theme => theme.palette.mode === 'dark' ? 'background.paper' : '#fff',
                              padding: '0 4px'
                            }
                          },
                          '& .MuiInputBase-input': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            padding: { xs: '12px 14px', sm: '14px 16px' },
                            height: 'auto'
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Nationality"
                        name="nationality"
                        value={formData.nationality}
                        onChange={handleChange}
                        required
                        variant="outlined"
                        sx={{
                          backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderWidth: '2px'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            fontWeight: 500,
                            color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.7)',
                            transform: 'translate(14px, 12px) scale(1)',
                            '&.MuiInputLabel-shrink': {
                              transform: 'translate(14px, -6px) scale(0.75)',
                              backgroundColor: theme => theme.palette.mode === 'dark' ? 'background.paper' : '#fff',
                              padding: '0 4px'
                            }
                          },
                          '& .MuiInputBase-input': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            padding: { xs: '12px 14px', sm: '14px 16px' },
                            height: 'auto'
                          }
                        }}
                      />
                    </Grid>

                     <Grid item xs={12} md={6}>
                       <TextField
                         fullWidth
                         label="Height (cm)"
                         name="height"
                         value={formData.height}
                         onChange={handleChange}
                         required
                         placeholder="175 cm"
                         variant="outlined"
                         helperText="Height in cm (will be formatted automatically)"
                         sx={{
                           backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                           '& .MuiOutlinedInput-root': {
                             '& fieldset': {
                               borderWidth: '2px'
                             }
                           },
                           '& .MuiInputLabel-root': {
                             fontSize: { xs: '0.875rem', sm: '1rem' },
                             fontWeight: 500,
                             color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.7)',
                             transform: 'translate(14px, 12px) scale(1)',
                             '&.MuiInputLabel-shrink': {
                               transform: 'translate(14px, -6px) scale(0.75)',
                               backgroundColor: theme => theme.palette.mode === 'dark' ? 'background.paper' : '#fff',
                               padding: '0 4px'
                             }
                           },
                           '& .MuiInputBase-input': {
                             fontSize: { xs: '0.875rem', sm: '1rem' },
                             padding: { xs: '12px 14px', sm: '14px 16px' },
                             height: 'auto'
                           }
                         }}
                       />
                     </Grid>

                     <Grid item xs={12} md={6}>
                       <TextField
                         fullWidth
                         label="Player Rating"
                         name="ratings.overall"
                         type="number"
                         value={formData.ratings.overall}
                         onChange={handleChange}
                         required
                         variant="outlined"
                         sx={{
                           backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                           '& .MuiOutlinedInput-root': {
                             '& fieldset': {
                               borderWidth: '2px'
                             }
                           },
                           '& .MuiInputLabel-root': {
                             fontSize: { xs: '0.875rem', sm: '1rem' },
                             fontWeight: 500,
                             color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.7)',
                             transform: 'translate(14px, 12px) scale(1)',
                             '&.MuiInputLabel-shrink': {
                               transform: 'translate(14px, -6px) scale(0.75)',
                               backgroundColor: theme => theme.palette.mode === 'dark' ? 'background.paper' : '#fff',
                               padding: '0 4px'
                             }
                           },
                           '& .MuiInputBase-input': {
                             fontSize: { xs: '0.875rem', sm: '1rem' },
                             padding: { xs: '12px 14px', sm: '14px 16px' },
                             height: 'auto'
                           }
                         }}
                         InputProps={{
                           inputProps: {
                             min: 40,
                             max: 99
                           }
                         }}
                       />
                     </Grid>
                  </Grid>
                </Box>
              </Grid>

              {/* Playing Style Section */}
              <Grid item xs={12}>
                <Box sx={{
                  p: { xs: 2, sm: 4 },
                  mb: 3,
                  backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
                  borderRadius: 2,
                  border: '2px solid',
                  borderColor: 'primary.main',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}>
                  <Typography variant="h6" gutterBottom sx={{
                    color: 'primary.main',
                    mb: 4,
                    fontWeight: 'bold',
                    fontSize: '1.25rem'
                  }}
                  className="playing-style-heading">
                    Playing Style
                  </Typography>
                  <Grid container spacing={{ xs: 2, sm: 4 }} className="playing-style">
                    {/* Changed layout to use 2 columns instead of 3 for more space */}
                    <Grid item xs={12} sm={6}>
                      <FormControl
                        fullWidth
                        required
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'transparent',
                            borderRadius: 2,
                            '& fieldset': {
                              borderWidth: '2px'
                            },
                            '&:hover fieldset': {
                              borderColor: 'primary.main'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            backgroundColor: 'transparent',
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'rgba(0, 0, 0, 0.6)',
                            px: 1,
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            fontWeight: 500,
                            transform: 'translate(14px, 12px) scale(1)',
                            '&.Mui-focused': {
                              color: 'primary.main'
                            },
                            '&.MuiInputLabel-shrink': {
                              transform: 'translate(14px, -6px) scale(0.75)',
                              backgroundColor: 'transparent',
                              padding: '0 4px'
                            }
                          },
                          '& .MuiSelect-select': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            padding: { xs: '12px 14px', sm: '14px 16px' },
                            width: '100%'
                          }
                        }}
                      >
                        <InputLabel id="player-type-label">Player Type</InputLabel>
                        <Select
                          labelId="player-type-label"
                          name="type"
                          value={formData.type}
                          onChange={handleChange}
                          label="Player Type"
                          displayEmpty
                          sx={{
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                            '& .MuiSelect-icon': {
                              color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit'
                            },
                            '& .MuiSelect-select': {
                              backgroundColor: 'transparent !important'
                            }
                          }}
                          MenuProps={{
                            sx: {
                              zIndex: 9999,
                              '& .MuiPaper-root': {
                                backgroundColor: theme => theme.palette.mode === 'dark' ? '#333' : '#fff',
                              },
                              '& .MuiMenuItem-root': {
                                color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                              }
                            }
                          }}
                        >
                          {playerTypes.map((type) => (
                            <MenuItem key={type} value={type}>
                              {type}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl
                        fullWidth
                        required
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'transparent',
                            borderRadius: 2,
                            '& fieldset': {
                              borderWidth: '2px'
                            },
                            '&:hover fieldset': {
                              borderColor: 'primary.main'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            backgroundColor: 'transparent',
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'rgba(0, 0, 0, 0.6)',
                            px: 1,
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            fontWeight: 500,
                            transform: 'translate(14px, 12px) scale(1)',
                            '&.Mui-focused': {
                              color: 'primary.main'
                            },
                            '&.MuiInputLabel-shrink': {
                              transform: 'translate(14px, -6px) scale(0.75)',
                              backgroundColor: 'transparent',
                              padding: '0 4px'
                            }
                          },
                          '& .MuiSelect-select': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            padding: { xs: '12px 14px', sm: '14px 16px' },
                            width: '100%'
                          }
                        }}
                      >
                        <InputLabel id="batting-hand-label">Batting Hand</InputLabel>
                        <Select
                          labelId="batting-hand-label"
                          name="battingHand"
                          value={formData.battingHand}
                          onChange={handleChange}
                          label="Batting Hand"
                          displayEmpty
                          sx={{
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                            '& .MuiSelect-icon': {
                              color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit'
                            },
                            '& .MuiSelect-select': {
                              backgroundColor: 'transparent !important'
                            }
                          }}
                          MenuProps={{
                            sx: {
                              zIndex: 9999,
                              '& .MuiPaper-root': {
                                backgroundColor: theme => theme.palette.mode === 'dark' ? '#333' : '#fff',
                              },
                              '& .MuiMenuItem-root': {
                                color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                              }
                            }
                          }}
                        >
                          {battingHands.map((style) => (
                            <MenuItem key={style.value} value={style.value}>
                              {style.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl
                        fullWidth
                        required
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'transparent',
                            borderRadius: 2,
                            '& fieldset': {
                              borderWidth: '2px'
                            },
                            '&:hover fieldset': {
                              borderColor: 'primary.main'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            backgroundColor: 'transparent',
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'rgba(0, 0, 0, 0.6)',
                            px: 1,
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            fontWeight: 500,
                            transform: 'translate(14px, 12px) scale(1)',
                            '&.Mui-focused': {
                              color: 'primary.main'
                            },
                            '&.MuiInputLabel-shrink': {
                              transform: 'translate(14px, -6px) scale(0.75)',
                              backgroundColor: 'transparent',
                              padding: '0 4px'
                            }
                          },
                          '& .MuiSelect-select': {
                            fontSize: { xs: '0.875rem', sm: '1rem' },
                            padding: { xs: '12px 14px', sm: '14px 16px' },
                            width: '100%'
                          }
                        }}
                      >
                        <InputLabel id="bowling-hand-label">Bowling Hand</InputLabel>
                        <Select
                          labelId="bowling-hand-label"
                          name="bowlingHand"
                          value={formData.bowlingHand}
                          onChange={handleChange}
                          label="Bowling Hand"
                          displayEmpty
                          sx={{
                            color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                            '& .MuiSelect-icon': {
                              color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit'
                            },
                            '& .MuiSelect-select': {
                              backgroundColor: 'transparent !important'
                            }
                          }}
                          MenuProps={{
                            sx: {
                              zIndex: 9999,
                              '& .MuiPaper-root': {
                                backgroundColor: theme => theme.palette.mode === 'dark' ? '#333' : '#fff',
                              },
                              '& .MuiMenuItem-root': {
                                color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                              }
                            }
                          }}
                        >
                          {bowlingHands.map((style) => (
                            <MenuItem key={style.value} value={style.value}>
                              {style.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>

              {/* Image Upload Section */}
              <Grid item xs={12}>
                <Box sx={{
                  p: 3,
                  backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider'
                }}>
                  <Typography variant="h6" gutterBottom sx={{
                    color: 'primary.main',
                    mb: 3,
                    fontWeight: 'medium'
                  }}>
                    Player Image
                  </Typography>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 3,
                    flexWrap: 'wrap'
                  }}>
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                      size="large"
                      sx={{
                        backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                        height: '56px',
                        borderWidth: '2px',
                        padding: '0 20px',
                        fontSize: '1rem',
                        '&:hover': {
                          borderWidth: '2px',
                          backgroundColor: '#f5f5f5'
                        }
                      }}
                    >
                      Choose Image
                      <input
                        accept="image/*"
                        type="file"
                        hidden
                        onChange={handleImageChange}
                        id="player-image-upload"
                      />
                    </Button>
                    {imagePreview && (
                      <Box sx={{
                        width: 140,
                        height: 140,
                        borderRadius: 2,
                        overflow: 'hidden',
                        border: '2px solid',
                        borderColor: 'divider',
                        backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <img
                          src={imagePreview}
                          alt="Preview"
                          style={{
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectFit: 'contain'
                          }}
                        />
                      </Box>
                    )}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions sx={{
            p: { xs: 2, sm: 3 },
            borderTop: '1px solid',
            borderColor: 'divider',
            backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#f8f9fa',
            gap: 2,
            justifyContent: 'center'
          }}>
            <Button
              onClick={() => setDialogOpen(false)}
              variant="outlined"
              size="large"
              sx={{
                backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.09)' : '#fff',
                borderWidth: '2px',
                minWidth: { xs: '120px', sm: '140px' },
                height: { xs: '48px', sm: '56px' },
                fontSize: { xs: '0.875rem', sm: '1rem' },
                '&:hover': {
                  borderWidth: '2px',
                  backgroundColor: '#f5f5f5'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              sx={{
                minWidth: { xs: '120px', sm: '140px' },
                height: { xs: '48px', sm: '56px' },
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 'bold'
              }}
            >
              {selectedPlayer ? 'Update Player' : 'Create Player'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Import Dialog */}
      <ImportPlayersDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onSuccess={(result) => {
          setSuccess(`Successfully imported ${result.playersImported} players${result.errors ? ` with ${result.errors.length} errors` : ''}`);
          fetchPlayers();
        }}
      />

      {/* Match Player Photos Dialog */}
      <Dialog
        open={photoMatchDialogOpen}
        onClose={() => setPhotoMatchDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent>
          <MatchPlayerPhotos
            players={players}
            onComplete={handlePhotoMatchComplete}
          />
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Confirmation Dialog */}
      <Dialog
        open={confirmBulkDeleteOpen}
        onClose={cancelBulkDelete}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ pb: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          Confirm Bulk Delete
        </DialogTitle>
        <DialogContent sx={{ py: 3 }}>
          <Typography variant="body1">
            Are you sure you want to delete {selectedPlayers.length} selected player{selectedPlayers.length !== 1 ? 's' : ''}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Button onClick={cancelBulkDelete} variant="outlined">
            Cancel
          </Button>
          <Button onClick={confirmBulkDelete} variant="contained" color="error" startIcon={<DeleteIcon />}>
            Delete {selectedPlayers.length} Player{selectedPlayers.length !== 1 ? 's' : ''}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Actions Toolbar */}
      {selectedPlayers.length > 0 && (
        <Paper
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(8px)',
            boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
            borderTop: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body1" sx={{ mr: 2 }}>
              <strong>{selectedPlayers.length}</strong> player{selectedPlayers.length !== 1 ? 's' : ''} selected
            </Typography>
            <Button
              size="small"
              variant="outlined"
              onClick={() => setSelectedPlayers([])}
              startIcon={<CloseIcon />}
              sx={{ mr: 1 }}
            >
              Clear Selection
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={selectAllPlayers}
              startIcon={<SelectAllIcon />}
            >
              {selectedPlayers.length === players.length ? 'Deselect All' : 'Select All'}
            </Button>
          </Box>
          <Box>
            <Button
              variant="contained"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBulkDelete}
            >
              Delete Selected
            </Button>
          </Box>
        </Paper>
      )}
    </Container>
  );
};

export default PlayerManagement;