const mongoose = require('mongoose');
require('dotenv').config();

async function checkLatestMatch() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cricket24');
    console.log('Connected to database');
    
    // Find the tournament
    const Tournament = require('./models/Tournament');
    const tournament = await Tournament.findById('681e581dfe3bc0a0414dd937');
    
    if (!tournament) {
      console.log('Tournament not found');
      return;
    }
    
    // Find the latest match (last one in the array)
    let latestMatch = null;
    let latestDate = new Date(0); // Start with epoch
    
    for (const phase of tournament.phases) {
      for (const match of phase.matches) {
        if (match.createdAt && match.createdAt > latestDate) {
          latestMatch = match;
          latestDate = match.createdAt;
        }
      }
    }
    
    if (!latestMatch) {
      console.log('No matches found');
      return;
    }
    
    console.log('🔍 LATEST MATCH:');
    console.log('Match ID:', latestMatch._id);
    console.log('Created At:', latestMatch.createdAt);
    console.log('Status:', latestMatch.status);
    console.log('Home Team:', latestMatch.homeTeam);
    console.log('Away Team:', latestMatch.awayTeam);
    
    console.log('\n📊 Match Result:');
    console.log('Winner:', latestMatch.result?.winner);
    console.log('Home Team Score:', latestMatch.result?.homeTeamScore);
    console.log('Away Team Score:', latestMatch.result?.awayTeamScore);
    
    console.log('\n📸 Scorecard Images:');
    console.log('scorecardImages length:', latestMatch.result?.scorecardImages?.length || 0);
    console.log('scorecardImages:', JSON.stringify(latestMatch.result?.scorecardImages, null, 2));
    
    console.log('\n🔍 Other image fields:');
    console.log('match.scorecardImage:', latestMatch.scorecardImage ? 'Present' : 'Not present');
    console.log('match.ocrData:', latestMatch.ocrData ? 'Present' : 'Not present');
    
    if (latestMatch.result?.scorecardImages?.length > 0) {
      console.log('\n✅ SUCCESS: scorecardImages are present!');
    } else {
      console.log('\n❌ ISSUE: scorecardImages are missing!');
      
      // Check if there are any other fields that might contain the image
      console.log('\n🔍 Checking for image in other fields:');
      console.log('Full match object keys:', Object.keys(latestMatch.toObject()));
      
      if (latestMatch.scorecardImage) {
        console.log('Found scorecardImage:', latestMatch.scorecardImage);
      }
      
      if (latestMatch.ocrData) {
        console.log('Found ocrData keys:', Object.keys(latestMatch.ocrData));
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkLatestMatch();
