<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct OCR Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1 {
      color: #333;
      text-align: center;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .form-container {
      flex: 1;
      min-width: 300px;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .results-container {
      flex: 2;
      min-width: 500px;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, select, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .image-preview {
      margin-top: 20px;
      text-align: center;
    }
    .image-preview img {
      max-width: 100%;
      max-height: 300px;
      border: 1px solid #ddd;
    }
    .results {
      margin-top: 20px;
    }
    .results pre {
      background-color: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .terminal {
      background-color: #000;
      color: #0f0;
      font-family: monospace;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
      height: 300px;
      overflow-y: auto;
    }
    .settings-group {
      margin-top: 15px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .settings-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .settings-row {
      display: flex;
      margin-bottom: 10px;
    }
    .settings-row label {
      flex: 1;
      margin-right: 10px;
    }
    .settings-row input {
      flex: 2;
    }
  </style>
</head>
<body>
  <h1>Direct OCR Test</h1>
  
  <div class="container">
    <div class="form-container">
      <form id="ocrForm">
        <div class="form-group">
          <label for="imageFile">Upload Image:</label>
          <input type="file" id="imageFile" name="image" accept="image/*">
        </div>
        
        <div class="form-group">
          <label for="scorecardSelect">Or Select Sample Scorecard:</label>
          <select id="scorecardSelect">
            <option value="">-- Select a sample --</option>
            <!-- Will be populated dynamically -->
          </select>
        </div>
        
        <div class="settings-group">
          <div class="settings-title">PaddleOCR Settings:</div>
          
          <div class="settings-row">
            <label for="det_db_thresh">det_db_thresh:</label>
            <input type="number" id="det_db_thresh" name="det_db_thresh" value="0.3" step="0.05" min="0.05" max="0.95">
          </div>
          
          <div class="settings-row">
            <label for="det_db_box_thresh">det_db_box_thresh:</label>
            <input type="number" id="det_db_box_thresh" name="det_db_box_thresh" value="0.4" step="0.05" min="0.05" max="0.95">
          </div>
          
          <div class="settings-row">
            <label for="det_db_unclip_ratio">det_db_unclip_ratio:</label>
            <input type="number" id="det_db_unclip_ratio" name="det_db_unclip_ratio" value="1.5" step="0.1" min="0.5" max="3.0">
          </div>
          
          <div class="settings-row">
            <label for="drop_score">drop_score:</label>
            <input type="number" id="drop_score" name="drop_score" value="0.5" step="0.05" min="0.05" max="0.95">
          </div>
        </div>
        
        <button type="submit">Process Image</button>
      </form>
      
      <div class="image-preview">
        <h3>Image Preview</h3>
        <img id="preview" src="" alt="Preview will appear here">
      </div>
    </div>
    
    <div class="results-container">
      <h2>OCR Results</h2>
      
      <div class="results">
        <h3>Terminal Output</h3>
        <div class="terminal" id="terminalOutput">Waiting for processing...</div>
        
        <h3>Detected Text</h3>
        <pre id="detectedText">Waiting for processing...</pre>
        
        <h3>JSON Response</h3>
        <pre id="jsonResponse">Waiting for processing...</pre>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const ocrForm = document.getElementById('ocrForm');
      const imageFile = document.getElementById('imageFile');
      const scorecardSelect = document.getElementById('scorecardSelect');
      const preview = document.getElementById('preview');
      const terminalOutput = document.getElementById('terminalOutput');
      const detectedText = document.getElementById('detectedText');
      const jsonResponse = document.getElementById('jsonResponse');
      
      // Load sample scorecards
      fetch('/api/samples')
        .then(response => response.json())
        .then(data => {
          if (data.samples && data.samples.length > 0) {
            data.samples.forEach(sample => {
              const option = document.createElement('option');
              option.value = sample.path;
              option.textContent = sample.name;
              scorecardSelect.appendChild(option);
            });
          } else {
            const option = document.createElement('option');
            option.value = 'uploads/scorecards/clear1.png';
            option.textContent = 'Sample 1';
            scorecardSelect.appendChild(option);
            
            const option2 = document.createElement('option');
            option2.value = 'uploads/temp/test.png';
            option2.textContent = 'Google Logo';
            scorecardSelect.appendChild(option2);
          }
        })
        .catch(error => {
          console.error('Error loading samples:', error);
          // Add some default options
          const option = document.createElement('option');
          option.value = 'uploads/scorecards/clear1.png';
          option.textContent = 'Sample 1';
          scorecardSelect.appendChild(option);
          
          const option2 = document.createElement('option');
          option2.value = 'uploads/temp/test.png';
          option2.textContent = 'Google Logo';
          scorecardSelect.appendChild(option2);
        });
      
      // Handle image file selection
      imageFile.addEventListener('change', function() {
        if (this.files && this.files[0]) {
          const reader = new FileReader();
          reader.onload = function(e) {
            preview.src = e.target.result;
            scorecardSelect.value = '';
          };
          reader.readAsDataURL(this.files[0]);
        }
      });
      
      // Handle sample selection
      scorecardSelect.addEventListener('change', function() {
        if (this.value) {
          preview.src = '/' + this.value;
          imageFile.value = '';
        }
      });
      
      // Handle form submission
      ocrForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        terminalOutput.textContent = 'Processing...';
        detectedText.textContent = 'Processing...';
        jsonResponse.textContent = 'Processing...';
        
        // Create form data
        const formData = new FormData();
        
        // Add image file or scorecard path
        if (imageFile.files && imageFile.files[0]) {
          formData.append('image', imageFile.files[0]);
        } else if (scorecardSelect.value) {
          formData.append('scorecardPath', scorecardSelect.value);
        } else {
          alert('Please select an image or sample scorecard');
          return;
        }
        
        // Add settings
        const settings = {
          det_db_thresh: parseFloat(document.getElementById('det_db_thresh').value),
          det_db_box_thresh: parseFloat(document.getElementById('det_db_box_thresh').value),
          det_db_unclip_ratio: parseFloat(document.getElementById('det_db_unclip_ratio').value),
          drop_score: parseFloat(document.getElementById('drop_score').value)
        };
        
        formData.append('settings', JSON.stringify(settings));
        
        // Send request
        fetch('/api/process', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Display results
          terminalOutput.textContent = data.rawOutput || 'No terminal output';
          
          if (data.lines && data.lines.length > 0) {
            detectedText.textContent = data.lines.join('\n');
          } else {
            detectedText.textContent = 'No text detected';
          }
          
          jsonResponse.textContent = JSON.stringify(data, null, 2);
        })
        .catch(error => {
          console.error('Error:', error);
          terminalOutput.textContent = 'Error: ' + error.message;
          detectedText.textContent = 'Error: ' + error.message;
          jsonResponse.textContent = 'Error: ' + error.message;
        });
      });
    });
  </script>
</body>
</html>
