import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import OCRDataViewer from './OCRDataViewer';
import MatchFormFields from './MatchFormFields';
import PlayerMatchingViewer from './PlayerMatchingViewer';
import { addMatch, processMatchOutcome } from '../../services/tournamentService';
import { processOCRPlayerMatching } from '../../services/playerMatchingService';
import { useAuth } from '../../hooks/useAuth';

/**
 * Match Form Step Component
 * 
 * Displays OCR data in JSON format and auto-populated form with validation highlighting
 */
const MatchFormStep = ({ tournament, ocrData, uploadedImage, onSubmit, onBack, onError }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({});
  const [validationIssues, setValidationIssues] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [showOCRData, setShowOCRData] = useState(true);
  const [playerMatchingData, setPlayerMatchingData] = useState(null);
  const [playerMatchingLoading, setPlayerMatchingLoading] = useState(false);

  useEffect(() => {
    if (ocrData) {
      // Auto-populate form and identify validation issues
      const { populatedData, issues } = populateFormFromOCR(ocrData, tournament);
      setFormData(populatedData);
      setValidationIssues(issues);
      
      // If OCR failed, show a message but still allow manual entry
      if (!ocrData.success) {
        onError(ocrData.ocrMessage || 'OCR processing failed. Please enter match details manually.');
      }
      
      // Process player matching if OCR was successful
      if (ocrData.success) {
        processPlayerMatching(ocrData);
      }
    }
  }, [ocrData, tournament, onError]);

  const processPlayerMatching = async (ocr) => {
    setPlayerMatchingLoading(true);
    try {
      const matchingResults = await processOCRPlayerMatching(ocr);
      setPlayerMatchingData(matchingResults.playerMatching);
      
      // Update validation issues based on player matching results
      if (matchingResults.playerMatching?.processed) {
        const { results } = matchingResults.playerMatching;
        const playerIssues = [];
        
        // Add issues for players requiring manual verification
        if (results.manualVerificationRequired?.length > 0) {
          playerIssues.push({
            field: 'players',
            message: `${results.manualVerificationRequired.length} players need manual verification`,
            severity: 'warning'
          });
        }
        
        // Add issues for players with no matches
        if (results.noMatches?.length > 0) {
          playerIssues.push({
            field: 'players',
            message: `${results.noMatches.length} players could not be matched`,
            severity: 'error'
          });
        }
        
        setValidationIssues(prev => [...prev, ...playerIssues]);
      }
    } catch (error) {
      console.error('Player matching failed:', error);
      setPlayerMatchingData({
        processed: false,
        error: error.message
      });
    } finally {
      setPlayerMatchingLoading(false);
    }
  };

  const populateFormFromOCR = (ocr, tournament) => {
    const issues = [];
    
    // Check if OCR was successful
    const isOcrFailed = !ocr.success;
    
    // Extract and validate team names
    let team1Id = '';
    let team2Id = '';
    
    if (ocr.team1 && ocr.team1 !== 'Team 1') {
      const matchedTeam = tournament.registeredTeams?.find(team => 
        team.teamName.toLowerCase().includes(ocr.team1.toLowerCase()) ||
        ocr.team1.toLowerCase().includes(team.teamName.toLowerCase())
      );
      if (matchedTeam) {
        team1Id = matchedTeam._id;
      } else {
        issues.push({
          field: 'team1',
          message: `Team "${ocr.team1}" not found in tournament. Please select manually.`,
          severity: 'warning'
        });
      }
    } else {
      issues.push({
        field: 'team1',
        message: 'Team 1 name not detected. Please select manually.',
        severity: 'error'
      });
    }

    if (ocr.team2 && ocr.team2 !== 'Team 2') {
      const matchedTeam = tournament.registeredTeams?.find(team => 
        team.teamName.toLowerCase().includes(ocr.team2.toLowerCase()) ||
        ocr.team2.toLowerCase().includes(team.teamName.toLowerCase())
      );
      if (matchedTeam) {
        team2Id = matchedTeam._id;
      } else {
        issues.push({
          field: 'team2',
          message: `Team "${ocr.team2}" not found in tournament. Please select manually.`,
          severity: 'warning'
        });
      }
    } else {
      issues.push({
        field: 'team2',
        message: 'Team 2 name not detected. Please select manually.',
        severity: 'error'
      });
    }

    // Validate scores
    if (!ocr.team1Score?.runs && !ocr.team2Score?.runs) {
      issues.push({
        field: 'scores',
        message: 'Scores not detected properly. Please verify and enter manually.',
        severity: 'warning'
      });
    }

    // Validate venue
    if (!ocr.venue || ocr.venue === 'Unknown Venue' || ocr.venue === 'UNKNOWN VENUE') {
      issues.push({
        field: 'venue',
        message: 'Venue not detected. Please enter manually.',
        severity: 'warning'
      });
    }

    // Determine winner
    let winnerId = '';
    console.log('Winner determination debug:', {
      resultText: ocr.resultText,
      team1: ocr.team1,
      team2: ocr.team2,
      team1Score: ocr.team1Score,
      team2Score: ocr.team2Score,
      team1Id,
      team2Id
    });
    
    if (ocr.resultText && ocr.resultText.includes('WON')) {
      console.log('Checking result text for winner...');
      if (ocr.resultText.includes(ocr.team1)) {
        winnerId = team1Id;
        console.log('Winner from result text: team1', winnerId);
      } else if (ocr.resultText.includes(ocr.team2)) {
        winnerId = team2Id;
        console.log('Winner from result text: team2', winnerId);
      }
    } else if (ocr.team1Score && ocr.team2Score) {
      // Determine winner based on scores if result text doesn't indicate winner
      const team1Runs = ocr.team1Score.runs || 0;
      const team2Runs = ocr.team2Score.runs || 0;
      
      console.log('Determining winner from scores:', { team1Runs, team2Runs });
      
      if (team1Runs > team2Runs) {
        winnerId = team1Id;
        console.log('Winner from scores: team1', winnerId);
      } else if (team2Runs > team1Runs) {
        winnerId = team2Id;
        console.log('Winner from scores: team2', winnerId);
      }
      // If runs are equal, leave winnerId empty (could be a tie)
    }
    
    console.log('Final winnerId:', winnerId);

    // Check if verification was performed
    const wasVerified = ocr.verification && ocr.verification.method === 'google-vision';
    
    // Add a note about OCR status
    let ocrStatusNote = '';
    if (!ocr.success) {
      ocrStatusNote = 'OCR processing failed. Please enter match details manually. ';
    } else if (ocr.ocrMethod === 'paddleocr-fallback') {
      ocrStatusNote = 'Primary OCR failed, data extracted using fallback method. ';
    }
    
    const populatedData = {
      team1: team1Id,
      team2: team2Id,
      team1Name: ocr.team1 || '',
      team2Name: ocr.team2 || '',
      isTeam1Home: true, // Default to Team 1 as home team
      homeTeamBattedFirst: true, // Default to home team batting first
      venue: ocr.venue || '',
      date: new Date(),
      team1Score: {
        runs: ocr.team1Score?.runs || 0,
        wickets: ocr.team1Score?.wickets || 0,
        overs: ocr.team1Score?.overs || 0
      },
      team2Score: {
        runs: ocr.team2Score?.runs || 0,
        wickets: ocr.team2Score?.wickets || 0,
        overs: ocr.team2Score?.overs || 0
      },
      winnerId,
      playerOfMatch: ocr.playerOfMatch || '',
      resultText: ocr.resultText || '',
      team1Batsmen: ocr.team1Batsmen || [],
      team1Bowlers: ocr.team1Bowlers || [],
      team2Batsmen: ocr.team2Batsmen || [],
      team2Bowlers: ocr.team2Bowlers || [],
      matchNotes: `${ocrStatusNote}${wasVerified ? 'Player scores verified with Google Vision. ' : ''}${ocr.ocrMessage || ''}`
    };

    return { populatedData, issues };
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => {
      const updatedData = {
        ...prev,
        [field]: value
      };

      // Auto-determine winner when teams are selected and scores are available
      if ((field === 'team1' || field === 'team2') && updatedData.team1 && updatedData.team2) {
        const team1Score = updatedData.team1Score;
        const team2Score = updatedData.team2Score;
        
        if (team1Score && team2Score && team1Score.runs !== undefined && team2Score.runs !== undefined) {
          const team1Runs = team1Score.runs || 0;
          const team2Runs = team2Score.runs || 0;
          const team1Wickets = team1Score.wickets || 0;
          const team2Wickets = team2Score.wickets || 0;
          const team1Overs = team1Score.overs || 0;
          const team2Overs = team2Score.overs || 0;
          
          console.log('Auto-determining winner after team selection:', {
            team1: updatedData.team1,
            team2: updatedData.team2,
            team1Score: { runs: team1Runs, wickets: team1Wickets, overs: team1Overs },
            team2Score: { runs: team2Runs, wickets: team2Wickets, overs: team2Overs }
          });
          
          // Cricket winner determination logic
          let winnerId = '';
          let winnerText = '';
          
          if (team1Runs > team2Runs) {
            // Team 1 scored more runs
            winnerId = updatedData.team1;
            const margin = team1Runs - team2Runs;
            winnerText = `Team 1 won by ${margin} runs`;
          } else if (team2Runs > team1Runs) {
            // Team 2 scored more runs
            winnerId = updatedData.team2;
            
            // Determine if team2 batted first or second based on homeTeamBattedFirst
            // Check if user and user.team are defined to prevent errors
            const userTeam = user && user.team ? user.team : null;
            const team1IsHomeTeam = userTeam && updatedData.team1 === userTeam;
            const team2IsHomeTeam = userTeam ? !team1IsHomeTeam : false;
            
            // If team2 is home team, use homeTeamBattedFirst directly
            // If team1 is home team, use the inverse of homeTeamBattedFirst
            // Default to using homeTeamBattedFirst value if user team info is not available
            const team2BattedFirst = userTeam ? 
              (team2IsHomeTeam ? updatedData.homeTeamBattedFirst : !updatedData.homeTeamBattedFirst) : 
              !updatedData.homeTeamBattedFirst; // Default logic if user team is unknown
            
            console.log('Team 2 win logic:', {
              userTeam,
              team1: updatedData.team1,
              team2: updatedData.team2,
              team1IsHomeTeam,
              team2IsHomeTeam,
              homeTeamBattedFirst: updatedData.homeTeamBattedFirst,
              team2BattedFirst
            });
            
            if (team2BattedFirst) {
              // If team2 batted first, they won by runs
              const margin = team2Runs - team1Runs;
              winnerText = `Team 2 won by ${margin} runs`;
            } else {
              // If team2 batted second, they won by wickets
              const wicketsRemaining = 10 - team2Wickets;
              winnerText = `Team 2 won by ${wicketsRemaining} wickets`;
            }
          } else if (team1Runs === team2Runs && team1Runs > 0) {
            // Equal runs - check wickets for tie-breaker
            if (team1Wickets < team2Wickets) {
              winnerId = updatedData.team1;
              winnerText = `Team 1 won (fewer wickets lost: ${team1Wickets} vs ${team2Wickets})`;
            } else if (team2Wickets < team1Wickets) {
              winnerId = updatedData.team2;
              winnerText = `Team 2 won (fewer wickets lost: ${team2Wickets} vs ${team1Wickets})`;
            } else {
              winnerText = 'Match tied (equal runs and wickets)';
            }
          }
          
          updatedData.winnerId = winnerId;
          console.log('Winner determined:', winnerText, { winnerId });
        }
      }

      return updatedData;
    });

    // Remove validation issue for this field if it's been corrected
    setValidationIssues(prev => prev.filter(issue => issue.field !== field));
  };

  const handlePlayerUpdate = (ocrName, matchingData) => {
    // Update player matching data when a manual match is confirmed
    setPlayerMatchingData(prev => {
      if (!prev || !prev.processed) return prev;
      
      const updatedResults = { ...prev.results };
      
      // Remove from manual verification and no matches
      updatedResults.manualVerificationRequired = updatedResults.manualVerificationRequired?.filter(
        p => p.ocrName !== ocrName
      ) || [];
      updatedResults.noMatches = updatedResults.noMatches?.filter(
        p => p.ocrName !== ocrName
      ) || [];
      
      // Add to automatic matches
      updatedResults.automaticMatches = updatedResults.automaticMatches || [];
      updatedResults.automaticMatches.push({
        ocrName,
        matchedPlayer: matchingData.matchedPlayer,
        confidence: matchingData.confidence
      });
      
      return {
        ...prev,
        results: updatedResults
      };
    });
    
    // Remove player-related validation issues if all players are now matched
    setValidationIssues(prev => {
      const playerIssues = prev.filter(issue => issue.field === 'players');
      const otherIssues = prev.filter(issue => issue.field !== 'players');
      
      if (playerMatchingData?.processed) {
        const { results } = playerMatchingData;
        const pendingPlayers = (results.manualVerificationRequired?.length || 0) + 
                              (results.noMatches?.length || 0) - 1; // -1 for the player just matched
        
        if (pendingPlayers === 0) {
          return otherIssues; // Remove all player issues
        }
      }
      
      return prev;
    });
  };

  const handleMatchingComplete = () => {
    // Refresh player matching data or perform any cleanup
    console.log('Player matching completed');
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      onError(null);

      // Validate required fields
      if (!formData.team1 || !formData.team2) {
        onError('Please select both teams');
        return;
      }

      // Validate user has a team
      if (!user || !user.team) {
        onError('You must be associated with a team to submit matches');
        return;
      }

      // Determine opponent team and match details based on isTeam1Home
      let opponentTeamId;
      let isHomeMatch;
      
      // Use the isTeam1Home property to determine which team is home
      if (formData.team1 === user.team) {
        opponentTeamId = formData.team2;
        isHomeMatch = formData.isTeam1Home; // Use the selected home team setting
      } else if (formData.team2 === user.team) {
        opponentTeamId = formData.team1;
        isHomeMatch = !formData.isTeam1Home; // Inverse of isTeam1Home if user is team2
      } else {
        onError('Your team must be one of the participating teams');
        return;
      }

      // Transform data to match server expectations
      const transformedMatchData = {
        opponentTeamId,
        isHomeMatch,
        date: formData.date || new Date(),
        venue: formData.venue || 'Default Venue',
        homeTeamBattedFirst: formData.homeTeamBattedFirst, // Include which team batted first
        result: {
          winnerId: formData.winnerId && formData.winnerId.trim() !== '' ? formData.winnerId : null,
          team1Score: formData.team1Score,
          team2Score: formData.team2Score,
          resultText: formData.resultText,
          playerOfMatch: formData.playerOfMatch,
          team1Batsmen: formData.team1Batsmen,
          team1Bowlers: formData.team1Bowlers,
          team2Batsmen: formData.team2Batsmen,
          team2Bowlers: formData.team2Bowlers,
          matchNotes: formData.matchNotes,
          // Include uploaded scorecard image if available
          ...(uploadedImage && {
            scorecardImages: [{
              url: uploadedImage,
              uploadedBy: user._id,
              uploadedAt: new Date(),
              isVerified: false
            }]
          })
        }
      };

      let result;

      console.log('MatchFormStep uploadedImage:', uploadedImage);
      console.log('MatchFormStep submitting transformedMatchData:', transformedMatchData);
      console.log('MatchFormStep scorecardImages in transformedMatchData.result:', transformedMatchData.result.scorecardImages);

      console.log('Processing match with advanced outcome calculation...');
      // First create the basic match to get an ID
      const basicResult = await addMatch(tournament._id, transformedMatchData);
      console.log('Basic match created:', basicResult);
      
      // Always process with advanced outcome calculation using the match ID
      // Use the _id from the match object if matchId is not present
      const matchId = basicResult.matchId || (basicResult.match && basicResult.match._id);
      if (matchId) {
        try {
          // For manual matches without OCR data, create a synthetic OCR data structure
          const processData = ocrData ? {
            ocrData: ocrData,
            matchId: matchId
          } : {
            ocrData: {
              success: true,
              team1: formData.team1Name || 'Team 1',
              team2: formData.team2Name || 'Team 2',
              team1Score: formData.team1Score,
              team2Score: formData.team2Score,
              venue: formData.venue,
              resultText: formData.resultText,
              playerOfMatch: formData.playerOfMatch,
              team1Batsmen: formData.team1Batsmen || [],
              team1Bowlers: formData.team1Bowlers || [],
              team2Batsmen: formData.team2Batsmen || [],
              team2Bowlers: formData.team2Bowlers || [],
              ocrMethod: 'manual',
              ocrMessage: 'Manual entry - NRR calculated'
            },
            matchId: matchId
          };
          
          // Recursively sanitize any playerPerformances array to replace 'team1'/'team2' with ObjectIds
          function sanitizePlayerPerformances(obj) {
            if (!obj || typeof obj !== 'object') return;
            for (const key in obj) {
              if (Array.isArray(obj[key]) && key === 'playerPerformances') {
                obj[key] = obj[key].map(perf => {
                  if (perf.team === 'team1') return { ...perf, team: formData.team1 };
                  if (perf.team === 'team2') return { ...perf, team: formData.team2 };
                  return perf;
                });
              } else if (typeof obj[key] === 'object') {
                sanitizePlayerPerformances(obj[key]);
              }
            }
          }
          if (processData.ocrData) {
            sanitizePlayerPerformances(processData.ocrData);
            console.log('DEBUG: ocrData sent to processMatchOutcome:', JSON.stringify(processData.ocrData, null, 2));
          }
          // Add explicit team1Id and team2Id mapping
          processData.team1Id = formData.team1;
          processData.team2Id = formData.team2;
          processData.homeTeamBattedFirst = formData.homeTeamBattedFirst;
          processData.team1IsHomeTeam = formData.isTeam1Home;
          console.log('DEBUG: processMatchOutcome payload', processData);
          console.log('DEBUG: Submitting processMatchOutcome with matchId:', matchId, 'tournamentId:', tournament._id);
          const advancedResult = await processMatchOutcome(tournament._id, processData);
          console.log('Advanced processing completed:', advancedResult);
          result = advancedResult;
        } catch (advancedError) {
          console.warn('Advanced processing failed, using basic result:', advancedError);
          result = basicResult;
        }
      } else {
        result = basicResult;
      }

      onSubmit(result);
    } catch (error) {
      console.error('Error submitting match:', error);
      onError(error.response?.data?.msg || error.message || 'Failed to submit match');
    } finally {
      setSubmitting(false);
    }
  };

  const getValidationSummary = () => {
    const errors = validationIssues.filter(issue => issue.severity === 'error').length;
    const warnings = validationIssues.filter(issue => issue.severity === 'warning').length;
    return { errors, warnings };
  };

  const { errors, warnings } = getValidationSummary();

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Review & Edit Match Details
      </Typography>
      
      {/* Validation Summary */}
      {validationIssues.length > 0 && (
        <Alert 
          severity={errors > 0 ? 'error' : 'warning'} 
          sx={{ mb: 3 }}
          icon={errors > 0 ? <WarningIcon /> : <CheckIcon />}
        >
          <Typography variant="subtitle2">
            OCR Processing Summary: {errors} errors, {warnings} warnings
          </Typography>
          <Typography variant="body2">
            {errors > 0 
              ? 'Please review and correct the highlighted fields before submitting.'
              : 'Minor issues detected. Please review the highlighted fields.'
            }
          </Typography>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* OCR Data Viewer */}
        <Grid item xs={12} lg={4}>
          <Accordion expanded={showOCRData} onChange={() => setShowOCRData(!showOCRData)}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ViewIcon />
                <Typography variant="h6">Extracted OCR Data</Typography>
                <Chip 
                  label={ocrData?.success ? 'Success' : 'Failed'} 
                  color={ocrData?.success ? 'success' : 'error'} 
                  size="small" 
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <OCRDataViewer data={ocrData} />
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Match Form */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <EditIcon />
                <Typography variant="h6">Match Details Form</Typography>
              </Box>
              
              <MatchFormFields
                formData={formData}
                tournament={tournament}
                validationIssues={validationIssues}
                onChange={handleFormChange}
                uploadedImage={uploadedImage}
              />

              <Divider sx={{ my: 3 }} />

              {/* Player Matching Section */}
              {ocrData?.success && (
                <Box sx={{ mb: 3 }}>
                  {playerMatchingLoading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 3 }}>
                      <CircularProgress size={24} />
                      <Typography>Processing player matching...</Typography>
                    </Box>
                  ) : (
                    <PlayerMatchingViewer
                      playerMatchingData={playerMatchingData}
                      onPlayerUpdate={handlePlayerUpdate}
                      onMatchingComplete={handleMatchingComplete}
                      showSummary={true}
                    />
                  )}
                </Box>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button onClick={onBack} color="inherit">
                  Back to Upload
                </Button>
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={submitting || errors > 0}
                  size="large"
                >
                  {submitting ? 'Submitting...' : 'Submit Match Result'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MatchFormStep;
