const mongoose = require('mongoose');

const ocrSettingsSchema = new mongoose.Schema({
  primaryMethod: {
    type: String,
    enum: ['ocrspace', 'googlevision', 'paddleocr'],
    default: 'ocrspace',
    required: true
  },
  fallbackMethod: {
    type: String,
    enum: ['googlevision', 'paddleocr', 'none'],
    default: 'googlevision',
    required: true
  },
  ocrSpaceTimeout: {
    type: Number,
    default: 60000, // 60 seconds
    min: 1000,     // 1 second minimum
    max: 300000,   // 5 minutes maximum
    required: true
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
});

// Add findOneOrCreate static method
ocrSettingsSchema.statics.findOneOrCreate = async function(condition) {
  const settings = await this.findOne(condition);
  return settings || await this.create(condition);
};

module.exports = mongoose.model('OCRSettings', ocrSettingsSchema);