/**
 * Local MongoDB Backup Script
 * 
 * This script creates a JSON backup of your local MongoDB data
 * before migrating to Atlas. Always backup before migration!
 * 
 * Usage: node scripts/backupLocalData.js
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Import all models
const User = require('../models/User');
const Team = require('../models/Team');
const Player = require('../models/Player');
const Tournament = require('../models/Tournament');
const Auction = require('../models/Auction');
const Template = require('../models/Template');
const OCRSettings = require('../models/OCRSettings');

// Local database configuration
const LOCAL_DB = 'mongodb://localhost:27017/cricket24'; // Update if your local DB name is different

class LocalDataBackup {
  constructor() {
    this.backupDir = path.join(__dirname, '..', 'backups');
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.backupFile = path.join(this.backupDir, `local-backup-${this.timestamp}.json`);
  }

  async ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log('📁 Created backup directory:', this.backupDir);
    }
  }

  async connectToLocal() {
    try {
      console.log('🔌 Connecting to local MongoDB...');
      await mongoose.connect(LOCAL_DB);
      console.log('✅ Connected to local MongoDB');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to local MongoDB:', error.message);
      console.log('💡 Make sure your local MongoDB is running');
      return false;
    }
  }

  async backupCollection(name, Model) {
    try {
      console.log(`📦 Backing up ${name}...`);
      const data = await Model.find({}).lean();
      console.log(`   Found ${data.length} documents`);
      return { [name.toLowerCase()]: data };
    } catch (error) {
      console.error(`❌ Error backing up ${name}:`, error.message);
      return { [name.toLowerCase()]: [] };
    }
  }

  async createBackup() {
    console.log('🚀 Starting Local MongoDB Backup\n');
    console.log('Database:', LOCAL_DB);
    console.log('Backup file:', this.backupFile);
    console.log('\n' + '='.repeat(60));

    // Ensure backup directory exists
    await this.ensureBackupDirectory();

    // Connect to local database
    const connected = await this.connectToLocal();
    if (!connected) {
      return false;
    }

    // Backup all collections
    const collections = [
      { name: 'Users', model: User },
      { name: 'Teams', model: Team },
      { name: 'Players', model: Player },
      { name: 'Tournaments', model: Tournament },
      { name: 'Auctions', model: Auction },
      { name: 'Templates', model: Template },
      { name: 'OCRSettings', model: OCRSettings }
    ];

    const backupData = {
      metadata: {
        backupDate: new Date().toISOString(),
        sourceDatabase: LOCAL_DB,
        version: '1.0'
      },
      collections: {}
    };

    let totalDocuments = 0;
    for (const collection of collections) {
      const data = await this.backupCollection(collection.name, collection.model);
      Object.assign(backupData.collections, data);
      totalDocuments += Object.values(data)[0].length;
    }

    // Write backup file
    try {
      fs.writeFileSync(this.backupFile, JSON.stringify(backupData, null, 2));
      console.log('\n' + '='.repeat(60));
      console.log('✅ BACKUP COMPLETED SUCCESSFULLY!');
      console.log('='.repeat(60));
      console.log(`📁 Backup file: ${this.backupFile}`);
      console.log(`📊 Total documents: ${totalDocuments}`);
      console.log(`💾 File size: ${(fs.statSync(this.backupFile).size / 1024 / 1024).toFixed(2)} MB`);
      
      console.log('\n📋 BACKUP SUMMARY:');
      Object.entries(backupData.collections).forEach(([collection, data]) => {
        console.log(`   ${collection.padEnd(15)}: ${data.length} documents`);
      });
      
      return true;
    } catch (error) {
      console.error('❌ Failed to write backup file:', error.message);
      return false;
    }
  }

  async cleanup() {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('\n🔌 Disconnected from MongoDB');
    }
  }
}

// Restore function (bonus feature)
async function restoreFromBackup(backupFilePath) {
  if (!fs.existsSync(backupFilePath)) {
    console.error('❌ Backup file not found:', backupFilePath);
    return false;
  }

  try {
    console.log('🔄 Restoring from backup:', backupFilePath);
    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
    
    await mongoose.connect(LOCAL_DB);
    console.log('✅ Connected to local MongoDB');

    const models = {
      users: User,
      teams: Team,
      players: Player,
      tournaments: Tournament,
      auctions: Auction,
      templates: Template,
      ocrsettings: OCRSettings
    };

    for (const [collectionName, documents] of Object.entries(backupData.collections)) {
      const Model = models[collectionName.toLowerCase()];
      if (Model && documents.length > 0) {
        console.log(`📦 Restoring ${collectionName}: ${documents.length} documents`);
        await Model.deleteMany({}); // Clear existing data
        await Model.insertMany(documents);
        console.log(`   ✅ Restored ${collectionName}`);
      }
    }

    console.log('\n✅ Restore completed successfully!');
    await mongoose.disconnect();
    return true;
  } catch (error) {
    console.error('❌ Restore failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const backup = new LocalDataBackup();
  
  try {
    const success = await backup.createBackup();
    if (success) {
      console.log('\n🎯 NEXT STEPS:');
      console.log('1. Keep this backup file safe');
      console.log('2. Run the migration script: node scripts/migrateToAtlas.js');
      console.log('3. Test your application with Atlas');
      console.log('\n💡 To restore this backup later, run:');
      console.log(`   node -e "require('./scripts/backupLocalData.js').restoreFromBackup('${backup.backupFile}')"`);;
    }
  } catch (error) {
    console.error('\n❌ Backup failed:', error.message);
  } finally {
    await backup.cleanup();
    process.exit(0);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n\n⏹️  Backup interrupted by user');
  process.exit(0);
});

if (require.main === module) {
  main();
}

module.exports = { LocalDataBackup, restoreFromBackup };