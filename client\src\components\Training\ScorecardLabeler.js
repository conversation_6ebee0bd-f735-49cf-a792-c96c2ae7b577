import React, { useState, useEffect, useRef } from 'react';
import { API_URL } from '../../config';
import {
  uploadAndProcessScorecard,
  getScorecardsImages,
  getScorecardAnnotation,
  saveScorecardAnnotation,
  getModelStatus,
  trainModel,
  checkTrainingServerAvailability,
  findTrainingServer,
  updateTrainingApiUrl,
  testServerUrl,
  clearAnnotations
} from '../../services/trainingService';
import CricketBallIcon from './CricketBallIcon';
import ImageSelectionModal from './ImageSelectionModal';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid, // Note: Using legacy Grid component - consider upgrading to Grid v2 in the future
  IconButton,
  InputLabel,
  ListSubheader,
  MenuItem,
  Modal,
  Paper,
  Select,
  Switch,
  TextField,
  Typography,
  Chip,
  Alert,
  Snackbar
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  School as TrainIcon,
  Image as ImageIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  BarChart as ChartIcon,
  DeleteForever as DeleteIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

/**
 * Scorecard Labeler Component
 *
 * This component provides a UI for labeling cricket scorecard images
 * to train a machine learning model for automatic data extraction.
 */
const ScorecardLabeler = () => {
  // State for file upload
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  // State for images and annotations
  const [images, setImages] = useState([]);
  const [currentImage, setCurrentImage] = useState(null);
  const [textElements, setTextElements] = useState([]);
  const [selectedElement, setSelectedElement] = useState(null);
  const [loading, setLoading] = useState(false);

  // State for categories
  const [categories, setCategories] = useState([]);
  const [customCategories, setCustomCategories] = useState([]);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [showAddCategory, setShowAddCategory] = useState(false);

  // State for model training
  const [training, setTraining] = useState(false);
  const [modelStatus, setModelStatus] = useState(null);

  // State for training server availability
  const [trainingServerAvailable, setTrainingServerAvailable] = useState(null);

  // State for training server URL
  const [trainingServerUrl, setTrainingServerUrl] = useState(localStorage.getItem('training_api_url') || 'http://localhost:5001/api');

  // State for simulation mode
  const [simulationMode, setSimulationMode] = useState(false);

  // State for OCR method selection
  const [ocrMethod, setOcrMethod] = useState('auto');

  // Function to update training server URL
  const handleUpdateServerUrl = async (newUrl) => {
    try {
      showNotification(`Testing connection to ${newUrl}...`, 'info');

      // Test if the URL is valid before updating
      const isValid = await testServerUrl(newUrl);

      if (isValid) {
        // Update the URL if it's valid
        updateTrainingApiUrl(newUrl);
        setTrainingServerUrl(newUrl);
        setTrainingServerAvailable(true);

        showNotification(`Connected to training server at ${newUrl}`, 'success');

        // Refresh data
        fetchImages();
        fetchCategories();
        fetchModelStatus();
      } else {
        // Try adding /api if it's not already there
        if (!newUrl.endsWith('/api')) {
          const urlWithApi = newUrl.endsWith('/') ? `${newUrl}api` : `${newUrl}/api`;
          console.log(`Trying with /api: ${urlWithApi}`);

          const isValidWithApi = await testServerUrl(urlWithApi);

          if (isValidWithApi) {
            // Update the URL if it's valid with /api
            updateTrainingApiUrl(urlWithApi);
            setTrainingServerUrl(urlWithApi);
            setTrainingServerAvailable(true);

            showNotification(`Connected to training server at ${urlWithApi}`, 'success');

            // Refresh data
            fetchImages();
            fetchCategories();
            fetchModelStatus();
            return;
          }
        }

        showNotification(`Could not connect to training server at ${newUrl}`, 'error');
      }
    } catch (error) {
      console.error('Error updating training server URL:', error);
      showNotification(`Error connecting to ${newUrl}: ${error.message}`, 'error');
    }
  };

  // Function to auto-detect training server
  const handleAutoDetectServer = async () => {
    try {
      showNotification('Searching for training server...', 'info');

      const foundUrl = await findTrainingServer();

      if (foundUrl) {
        setTrainingServerUrl(foundUrl);
        setTrainingServerAvailable(true);
        showNotification(`Found training server at ${foundUrl}`, 'success');

        // Refresh data
        fetchImages();
        fetchCategories();
        fetchModelStatus();
      } else {
        showNotification('Could not find training server on any common port', 'error');
      }
    } catch (error) {
      console.error('Error auto-detecting training server:', error);
      showNotification(`Error searching for server: ${error.message}`, 'error');
    }
  };

  // Function to test common URLs
  const handleTestCommonUrls = async () => {
    try {
      showNotification('Testing common server configurations...', 'info');

      // Common configurations to try - prioritizing port 5001 which is the correct training server port
      const commonUrls = [
        'http://localhost:5001/api',
        'http://localhost:5001',
        'http://localhost:5000/api',
        'http://localhost:5000',
        'http://localhost:3001/api',
        'http://localhost:3001',
        'http://localhost:8080/api',
        'http://localhost:8080',
        'http://localhost:4000/api',
        'http://localhost:4000',
        'http://localhost:3000/api',
        'http://localhost:3000'
      ];

      for (const url of commonUrls) {
        showNotification(`Testing ${url}...`, 'info');
        const isValid = await testServerUrl(url);

        if (isValid) {
          updateTrainingApiUrl(url);
          setTrainingServerUrl(url);
          setTrainingServerAvailable(true);
          showNotification(`Connected to training server at ${url}`, 'success');

          // Refresh data
          fetchImages();
          fetchCategories();
          fetchModelStatus();
          return;
        }
      }

      showNotification('Could not find training server with any common configuration', 'error');
    } catch (error) {
      console.error('Error testing common URLs:', error);
      showNotification(`Error testing common URLs: ${error.message}`, 'error');
    }
  };

  // State for notifications
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });

  // State for training stats modal
  const [statsModalOpen, setStatsModalOpen] = useState(false);
  const [trainingStats, setTrainingStats] = useState(null);
  const [loadingStats, setLoadingStats] = useState(false);

  // State for confirmation dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [clearingAnnotations, setClearingAnnotations] = useState(false);

  // State for image selection modal
  const [imageSelectionModalOpen, setImageSelectionModalOpen] = useState(false);

  // Refs
  const fileInputRef = useRef(null);
  const imageRef = useRef(null);

  // Function to open training stats modal
  const handleOpenStatsModal = async () => {
    setStatsModalOpen(true);
    setLoadingStats(true);

    try {
      // Fetch the latest model status
      const status = await getModelStatus();

      // Get additional stats about labeled images
      const images = await getScorecardsImages();

      // Prepare stats data
      const stats = {
        ...status,
        totalImages: images ? images.length : 0,
        labeledImages: status.labeled_images || 0,
        lastTrainedDate: status.last_trained ? new Date(status.last_trained * 1000) : null,
        accuracy: status.accuracy || 0.85, // Default or from API
        categories: categories.length,
        serverUrl: trainingServerUrl
      };

      setTrainingStats(stats);
    } catch (error) {
      console.error('Error fetching training stats:', error);
      showNotification('Error fetching training statistics', 'error');
    } finally {
      setLoadingStats(false);
    }
  };

  // Function to close training stats modal
  const handleCloseStatsModal = () => {
    setStatsModalOpen(false);
  };

  // Function to open confirmation dialog
  const handleOpenConfirmDialog = () => {
    setConfirmDialogOpen(true);
  };

  // Function to close confirmation dialog
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  // Function to open image selection modal
  const handleOpenImageSelectionModal = () => {
    setImageSelectionModalOpen(true);
  };

  // Function to close image selection modal
  const handleCloseImageSelectionModal = () => {
    setImageSelectionModalOpen(false);
  };

  // Function to clear all annotations
  const handleClearAllAnnotations = async () => {
    setClearingAnnotations(true);

    try {
      const result = await clearAnnotations();

      // Show success notification
      showNotification(`Successfully cleared ${result.clearedCount || 'all'} annotations`, 'success');

      // Refresh model status to update the stats
      await fetchModelStatus();

      // Close the confirmation dialog
      setConfirmDialogOpen(false);
    } catch (error) {
      console.error('Error clearing annotations:', error);
      showNotification(`Error clearing annotations: ${error.message}`, 'error');
    } finally {
      setClearingAnnotations(false);
    }
  };

  // Function to clear selected annotations
  const handleClearSelectedAnnotations = async (selectedImages) => {
    setClearingAnnotations(true);

    try {
      const result = await clearAnnotations(selectedImages);

      // Show success notification
      showNotification(`Successfully cleared ${result.clearedCount} annotations`, 'success');

      // Refresh model status to update the stats
      await fetchModelStatus();
    } catch (error) {
      console.error('Error clearing selected annotations:', error);
      showNotification(`Error clearing annotations: ${error.message}`, 'error');
    } finally {
      setClearingAnnotations(false);
    }
  };

  // Load images and categories on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      // Check if training server is available
      try {
        // Force a fresh check for server availability
        const available = await checkTrainingServerAvailability(true);
        setTrainingServerAvailable(available);

        if (available) {
          showNotification('Training server is available and connected', 'success');
        } else {
          showNotification('Training server is not available. Using sample data.', 'warning');
        }
      } catch (error) {
        console.error('Error checking training server availability:', error);
        setTrainingServerAvailable(false);
        showNotification('Error connecting to training server. Using sample data.', 'warning');
      }

      // Load data regardless of server availability (functions will handle errors)
      await fetchImages();
      await fetchCategories();
      await fetchModelStatus();
    };

    loadInitialData();

    // Set up an interval to periodically check server availability
    const serverCheckInterval = setInterval(async () => {
      try {
        const available = await checkTrainingServerAvailability(true);

        // Only update if the status has changed
        if (available !== trainingServerAvailable) {
          setTrainingServerAvailable(available);

          if (available) {
            showNotification('Training server is now available', 'success');
            // Refresh model status when server becomes available
            fetchModelStatus();
          } else {
            showNotification('Training server is no longer available', 'warning');
          }
        }
      } catch (error) {
        console.error('Error in periodic server check:', error);
      }
    }, 60000); // Check every minute

    // Clean up the interval on component unmount
    return () => clearInterval(serverCheckInterval);
  }, [trainingServerAvailable]);

  // Fetch available images
  const fetchImages = async () => {
    try {
      console.log('Fetching available scorecard images');

      // Check if training server is available first
      const serverAvailable = await checkTrainingServerAvailability();

      if (serverAvailable) {
        try {
          // Get images from the server
          const images = await getScorecardsImages();

          if (images && images.length > 0) {
            setImages(images);
            console.log('Loaded images from server:', images);
          } else {
            console.log('No images found on server');
            setImages([]);
          }
        } catch (fetchError) {
          console.warn('Error fetching images from server:', fetchError);
          // Don't show notification - server might not be running yet
          setImages([]);
        }
      } else {
        // Training server not available, use empty images array
        console.log('Training server not available, using empty images array');
        setImages([]);
      }
    } catch (error) {
      console.error('Error in fetch images process:', error);
      setImages([]);
    }
  };

  // Fetch available categories
  const fetchCategories = async () => {
    // Define standard categories
    const standardCategories = [
      'team1_name', 'team2_name',
      'team1_score', 'team2_score',
      'venue', 'match_result', 'player_of_match',
      'player_name', 'batsman_name', 'bowler_name',
      'batsman_runs', 'batsman_balls',
      'bowler_wickets', 'bowler_runs', 'bowler_figures'
    ];

    // Define team-specific categories
    const teamSpecificCategories = [
      'team1_player_name', 'team2_player_name',
      'team1_batsman_name', 'team2_batsman_name',
      'team1_batsman_runs', 'team2_batsman_runs',
      'team1_batsman_balls', 'team2_batsman_balls',
      'team1_bowler_name', 'team2_bowler_name',
      'team1_bowler_wickets', 'team2_bowler_wickets',
      'team1_bowler_runs', 'team2_bowler_runs',
      'team1_bowler_figures', 'team2_bowler_figures'
    ];

    // Combine all default categories
    const defaultCategories = [...standardCategories, ...teamSpecificCategories];

    // Load any custom categories from localStorage
    try {
      const savedCustomCategories = localStorage.getItem('custom_categories');
      if (savedCustomCategories) {
        const parsedCustomCategories = JSON.parse(savedCustomCategories);
        setCustomCategories(parsedCustomCategories);
        console.log('Loaded custom categories from localStorage:', parsedCustomCategories);
      }
    } catch (error) {
      console.error('Error loading custom categories from localStorage:', error);
      setCustomCategories([]);
    }

    // Skip API call and use default categories + custom categories
    console.log('Using default categories and custom categories');
    setCategories([...defaultCategories]);
    return;

    // The code below is disabled until the Flask server is set up
    /*
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`);
      setCategories(response.data.categories);
    } catch (error) {
      console.error('Error fetching categories:', error);
      // Use default categories if server is not running
      setCategories([
        'team1_name', 'team2_name',
        'team1_score', 'team2_score',
        'team1_overs', 'team2_overs',
        'team1_batsman_name', 'team2_batsman_name',
        'team1_batsman_runs', 'team2_batsman_runs',
        'team1_batsman_balls', 'team2_batsman_balls',
        'team1_bowler_name', 'team2_bowler_name',
        'team1_bowler_figures', 'team2_bowler_figures',
        'team1_bowler_wickets', 'team1_bowler_runs',
        'team2_bowler_wickets', 'team2_bowler_runs',
        'venue', 'player_of_match', 'match_time'
      ]);
    }
    */
  };

  // Fetch model status
  const fetchModelStatus = async () => {
    try {
      console.log('Fetching model status');

      // Force a new check for server availability
      const serverAvailable = await checkTrainingServerAvailability(true);

      if (serverAvailable) {
        try {
          // Get model status from the server
          const status = await getModelStatus();

          if (status) {
            setModelStatus({
              ...status,
              server_unavailable: false
            });
            console.log('Loaded model status from server:', status);
          } else {
            throw new Error('Invalid model status');
          }
        } catch (statusError) {
          console.warn('Error fetching model status from server:', statusError);

          // Set default model status if server is not running
          setModelStatus({
            trained: false,
            labeled_images: 0,
            server_unavailable: false
          });
        }
      } else {
        // Training server not available, use default model status
        console.log('Training server not available, using default model status');
        setModelStatus({
          trained: false,
          labeled_images: 0,
          server_unavailable: true
        });
      }
    } catch (error) {
      console.error('Error in fetch model status process:', error);

      // Set default model status
      setModelStatus({
        trained: false,
        labeled_images: 0,
        server_unavailable: true
      });
    }
  };

  // Handle file selection
  const handleFileChange = (event) => {
    setFile(event.target.files[0]);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!file) return;

    setUploading(true);
    setUploadError(null);

    try {
      // Process the image with OCR using the selected method
      console.log(`Uploading and processing image with OCR method: ${ocrMethod}...`);
      console.log('OCR method being sent to server:', ocrMethod);
      const result = await uploadAndProcessScorecard(file, { ocrMethod });
      console.log('OCR processing result:', result);

      // Update state with the new image and text elements
      const filename = result.filename || `scorecard_${Date.now()}.jpg`;

      // Log the extraction method from the result
      console.log('Extraction method from result:', result.extractionMethod);
      console.log('OCR data extraction method:', result.ocrData?.extractionMethod);

      // Use the actual text elements from OCR if available, otherwise fall back to fake data
      const extractedTextElements = result.textElements || generateFakeTextElements();

      setImages([...images, filename]);
      setCurrentImage(filename);
      setTextElements(extractedTextElements);
      setSelectedElement(null);

      // Clear the file input
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      showNotification('Image uploaded and processed successfully', 'success');
    } catch (error) {
      console.error('Error uploading and processing image:', error);
      setUploadError('Error processing image');

      // Fall back to fake data if OCR processing fails
      const fakeFilename = `scorecard_${Date.now()}.jpg`;
      const fakeTextElements = generateFakeTextElements();

      setImages([...images, fakeFilename]);
      setCurrentImage(fakeFilename);
      setTextElements(fakeTextElements);
      setSelectedElement(null);

      showNotification('OCR processing failed, using sample data instead', 'warning');
    } finally {
      setUploading(false);
    }
  };

  // Generate fake text elements for demo purposes
  const generateFakeTextElements = () => {
    return [
      {
        id: 0,
        text: 'TEAM ALPHA',
        confidence: 0.95,
        x: 100,
        y: 50,
        width: 150,
        height: 30,
        box: [[50, 35], [200, 35], [200, 65], [50, 65]],
        category: null
      },
      {
        id: 1,
        text: 'TEAM BETA',
        confidence: 0.93,
        x: 400,
        y: 50,
        width: 150,
        height: 30,
        box: [[350, 35], [500, 35], [500, 65], [350, 65]],
        category: null
      },
      {
        id: 2,
        text: '156/4',
        confidence: 0.98,
        x: 100,
        y: 100,
        width: 80,
        height: 25,
        box: [[60, 87], [140, 87], [140, 112], [60, 112]],
        category: null
      },
      {
        id: 3,
        text: '142/8',
        confidence: 0.97,
        x: 400,
        y: 100,
        width: 80,
        height: 25,
        box: [[360, 87], [440, 87], [440, 112], [360, 112]],
        category: null
      },
      {
        id: 4,
        text: 'JOHN SMITH',
        confidence: 0.91,
        x: 100,
        y: 150,
        width: 120,
        height: 20,
        box: [[40, 140], [160, 140], [160, 160], [40, 160]],
        category: null
      },
      {
        id: 5,
        text: '78*',
        confidence: 0.99,
        x: 200,
        y: 150,
        width: 40,
        height: 20,
        box: [[180, 140], [220, 140], [220, 160], [180, 160]],
        category: null
      },
      {
        id: 6,
        text: '(52)',
        confidence: 0.96,
        x: 250,
        y: 150,
        width: 40,
        height: 20,
        box: [[230, 140], [270, 140], [270, 160], [230, 160]],
        category: null
      },
      {
        id: 7,
        text: 'CRICKET STADIUM',
        confidence: 0.89,
        x: 250,
        y: 200,
        width: 200,
        height: 25,
        box: [[150, 187], [350, 187], [350, 212], [150, 212]],
        category: null
      },
      {
        id: 8,
        text: 'TEAM ALPHA WON BY 14 RUNS',
        confidence: 0.94,
        x: 250,
        y: 250,
        width: 300,
        height: 30,
        box: [[100, 235], [400, 235], [400, 265], [100, 265]],
        category: null
      }
    ];
  };

  // Handle image selection
  const handleSelectImage = async (imageName) => {
    setLoading(true);
    setCurrentImage(imageName);

    try {
      // Try to load annotations from the server
      console.log('Loading annotations for image:', imageName);

      try {
        // Get annotations from the server
        const annotations = await getScorecardAnnotation(imageName);

        if (annotations && annotations.length > 0) {
          // Use the annotations from the server
          setTextElements(annotations);
          console.log('Loaded annotations from server:', annotations);
        } else {
          throw new Error('No annotations found');
        }
      } catch (annotationError) {
        console.warn('Error loading annotations, using sample data:', annotationError);

        // Fall back to fake data if annotations can't be loaded
        const fakeTextElements = generateFakeTextElements();
        setTextElements(fakeTextElements);
      }
    } catch (error) {
      console.error('Error loading image:', error);
      showNotification('Error loading image', 'error');

      // Fall back to fake data
      const fakeTextElements = generateFakeTextElements();
      setTextElements(fakeTextElements);
    } finally {
      setLoading(false);
    }
  };

  // Handle element selection
  const handleSelectElement = (element) => {
    setSelectedElement(element);
  };

  // Handle category change
  const handleCategoryChange = (event) => {
    if (!selectedElement) return;

    const category = event.target.value;
    console.log(`Changing category to: ${category}`);

    // Update the selected element
    const updatedElement = { ...selectedElement, category };

    // Update the text elements array
    const updatedElements = textElements.map(el =>
      el.id === selectedElement.id ? updatedElement : el
    );

    // Force a re-render by creating a new array
    setTextElements([...updatedElements]);
    setSelectedElement(updatedElement);

    // Show a notification for feedback
    showNotification(`Category set to: ${category || 'None'}`, 'success');
  };

  // Handle clear category
  const handleClearCategory = () => {
    if (!selectedElement) return;

    // Update the selected element
    const updatedElement = { ...selectedElement, category: null };

    // Update the text elements array
    const updatedElements = textElements.map(el =>
      el.id === selectedElement.id ? updatedElement : el
    );

    setTextElements(updatedElements);
    setSelectedElement(updatedElement);

    showNotification('Category cleared', 'info');
  };

  // Handle adding a custom category
  const handleAddCustomCategory = () => {
    if (!newCategoryName.trim()) {
      showNotification('Please enter a category name', 'warning');
      return;
    }

    // Format the category name (lowercase with underscores)
    const formattedName = newCategoryName.trim().toLowerCase().replace(/\s+/g, '_');

    // Check if the category already exists
    if ([...categories, ...customCategories].includes(formattedName)) {
      showNotification('This category already exists', 'warning');
      return;
    }

    // Add the new category
    const updatedCustomCategories = [...customCategories, formattedName];
    setCustomCategories(updatedCustomCategories);

    // Important: Create a new array for categories to ensure React detects the change
    const updatedCategories = [...categories];
    updatedCategories.push(formattedName);
    setCategories(updatedCategories);

    // Save to localStorage
    localStorage.setItem('custom_categories', JSON.stringify(updatedCustomCategories));

    // Reset the input and hide the form
    setNewCategoryName('');
    setShowAddCategory(false);

    showNotification(`Added custom category: ${formattedName}`, 'success');

    // Force a re-render of the dropdown
    setTimeout(() => {
      // This empty setState forces a re-render
      setCategories([...updatedCategories]);
    }, 100);
  };

  // Handle removing a custom category
  const handleRemoveCustomCategory = (categoryToRemove) => {
    // Remove the category
    const updatedCustomCategories = customCategories.filter(cat => cat !== categoryToRemove);
    setCustomCategories(updatedCustomCategories);

    // Create a new array for categories to ensure React detects the change
    const updatedCategories = categories.filter(cat => cat !== categoryToRemove);
    setCategories(updatedCategories);

    // Save to localStorage
    localStorage.setItem('custom_categories', JSON.stringify(updatedCustomCategories));

    // Update any text elements that use this category
    const updatedElements = textElements.map(el =>
      el.category === categoryToRemove ? { ...el, category: null } : el
    );
    setTextElements(updatedElements);

    // If the selected element uses this category, update it
    if (selectedElement && selectedElement.category === categoryToRemove) {
      setSelectedElement({ ...selectedElement, category: null });
    }

    showNotification(`Removed custom category: ${categoryToRemove}`, 'info');

    // Force a re-render of the dropdown
    setTimeout(() => {
      // This empty setState forces a re-render
      setCategories([...updatedCategories]);
    }, 100);
  };

  // Handle text change
  const handleTextChange = (event) => {
    if (!selectedElement) return;

    const text = event.target.value;

    // Update the selected element
    const updatedElement = { ...selectedElement, text };

    // Update the text elements array
    const updatedElements = textElements.map(el =>
      el.id === selectedElement.id ? updatedElement : el
    );

    setTextElements(updatedElements);
    setSelectedElement(updatedElement);
  };

  // Save annotations
  const handleSaveAnnotations = async () => {
    if (!currentImage || !textElements.length) return;

    try {
      console.log('Saving annotations for image:', currentImage);
      console.log('Text elements to save:', textElements.length);

      // Check if training server is available first
      console.log('Checking training server availability before saving...');
      const serverAvailable = await checkTrainingServerAvailability(true);
      console.log('Server availability check result:', serverAvailable);

      if (serverAvailable) {
        try {
          console.log('Attempting to save annotations to server...');
          // Save annotations to the server
          const saveResult = await saveScorecardAnnotation(currentImage, textElements);
          console.log('Save result:', saveResult);
          showNotification('Annotations saved successfully', 'success');
        } catch (saveError) {
          console.warn('Error saving annotations to server:', saveError);
          showNotification('Error saving to server: ' + saveError.message, 'warning');

          // Also save locally as fallback
          console.log('Saving locally as fallback...');
          localStorage.setItem(`scorecard_annotations_${currentImage}`, JSON.stringify(textElements));
          showNotification('Annotations also saved locally as backup', 'info');
        }
      } else {
        // Server is not available, save locally
        console.log('Training server not available, saving locally');
        localStorage.setItem(`scorecard_annotations_${currentImage}`, JSON.stringify(textElements));
        showNotification('Annotations saved locally (server unavailable)', 'info');
      }

      // Refresh model status after saving
      console.log('Refreshing model status after saving...');
      fetchModelStatus();
    } catch (error) {
      console.error('Error in save annotations process:', error);
      showNotification('Error saving annotations', 'error');

      // Try to save locally as last resort
      try {
        console.log('Saving locally as last resort...');
        localStorage.setItem(`scorecard_annotations_${currentImage}`, JSON.stringify(textElements));
        showNotification('Annotations saved locally as fallback', 'info');
      } catch (localError) {
        console.error('Failed to save locally:', localError);
      }
    }
  };

  // Train model
  const handleTrainModel = async () => {
    setTraining(true);

    try {
      console.log('Training model...');
      showNotification('Training model... This may take a minute.', 'info');

      // Always use simulation mode if it's enabled
      const useSimulation = simulationMode;

      // Call the API to train the model with simulation if needed
      const result = await trainModel(useSimulation);
      console.log('Training result:', result);

      if (result && result.simulated) {
        // This was a simulated training
        showNotification(result.message || 'Model training simulated', 'info');

        // If this was due to an error, show a warning
        if (result.originalError) {
          showNotification(`Original error: ${result.originalError}`, 'warning');
        }

        // Update model status with simulated values
        setModelStatus(prevStatus => ({
          ...prevStatus,
          trained: true,
          last_trained: new Date().toISOString(),
          labeled_images: prevStatus?.labeled_images || 0,
          server_unavailable: false
        }));
      } else if (result && result.success) {
        // This was a successful real training
        showNotification(result.message || 'Model trained successfully', 'success');
      } else if (result) {
        // We got a result but don't know if it was successful
        showNotification('Model trained, but status is unknown', 'info');
      } else {
        // No result returned
        showNotification('Model training completed, but no result returned', 'info');
      }

      // Refresh model status after training (unless we already updated it for simulation)
      if (!result || !result.simulated) {
        fetchModelStatus();
      }
    } catch (error) {
      console.error('Error in model training process:', error);

      // Extract the error message
      const errorMessage = error.message || 'Unknown error';

      // Show a more user-friendly error message
      if (errorMessage.includes('timeout')) {
        showNotification('Training request timed out. The model may still be training on the server.', 'warning');
      } else if (errorMessage.includes('not found') || errorMessage.includes('404')) {
        showNotification('Training endpoint not found. The server might not support model training.', 'error');
        showNotification('Try enabling simulation mode to continue without a training server.', 'info');
      } else if (errorMessage.includes('method not allowed') || errorMessage.includes('405')) {
        showNotification('The server does not support the training method.', 'error');
        showNotification('Try enabling simulation mode to continue without a training server.', 'info');
      } else if (errorMessage.includes('server error') || errorMessage.includes('500')) {
        showNotification('Server error while training model. Check the server logs for details.', 'error');
      } else {
        showNotification('Error training model: ' + errorMessage, 'error');
      }
    } finally {
      setTraining(false);
    }
  };

  // Show notification
  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Get color for category
  const getCategoryColor = (category) => {
    if (!category) return 'default';

    // Map categories to colors
    const colorMap = {
      team1_name: 'primary',
      team2_name: 'secondary',
      team1_score: 'error',
      team2_score: 'warning',
      venue: 'info',
      match_result: 'success',
      player_of_match: 'error'
    };

    return colorMap[category] || 'default';
  };

  // Handle auto-assign teams
  const handleAutoAssignTeams = () => {
    if (!textElements.length) {
      showNotification('No text elements to assign teams to', 'warning');
      return;
    }

    console.log('Starting team auto-assignment...');

    // Create a deep copy of the text elements to work with
    const updatedElements = JSON.parse(JSON.stringify(textElements));

    // Reset any existing team-specific categories to their generic equivalents
    updatedElements.forEach((element, index) => {
      if (element.category && element.category.startsWith('team')) {
        // Convert team-specific categories back to generic ones
        if (element.category.includes('player_name')) {
          updatedElements[index].category = 'player_name';
        } else if (element.category.includes('batsman_name')) {
          updatedElements[index].category = 'batsman_name';
        } else if (element.category.includes('batsman_runs')) {
          updatedElements[index].category = 'batsman_runs';
        } else if (element.category.includes('batsman_balls')) {
          updatedElements[index].category = 'batsman_balls';
        } else if (element.category.includes('bowler_name')) {
          updatedElements[index].category = 'bowler_name';
        } else if (element.category.includes('bowler_figures')) {
          updatedElements[index].category = 'bowler_figures';
        } else if (element.category.includes('bowler_wickets')) {
          updatedElements[index].category = 'bowler_wickets';
        } else if (element.category.includes('bowler_runs')) {
          updatedElements[index].category = 'bowler_runs';
        }
      }
    });

    // Step 1: Identify player names
    // In cricket scorecards, player names are usually longer text elements
    const playerNamePattern = /^[A-Z][A-Za-z\s\-']+$/;
    const playerElements = updatedElements.filter(el => {
      // Check if it looks like a player name (all caps or proper case, not just numbers)
      const text = el.text.trim();
      return text.length > 5 &&
             !text.match(/^\d+$/) && // Not just numbers
             (text.match(playerNamePattern) || text === text.toUpperCase()) &&
             text !== 'PROCEED' &&
             text !== 'BACK' &&
             !text.includes('SCORECARD') &&
             !text.includes('OVAL') &&
             !text.includes('STADIUM');
    });

    console.log(`Found ${playerElements.length} potential player names`);

    // Mark these as player_name
    playerElements.forEach(element => {
      const index = updatedElements.findIndex(el => el.id === element.id);
      if (index !== -1 && !updatedElements[index].category) {
        updatedElements[index].category = 'player_name';
      }
    });

    // Step 2: Identify numeric values
    const numericPattern = /^[0-9]+(\.[0-9]+)?$/;
    const wicketRunsPattern = /^[0-9]+-[0-9]+$/;

    updatedElements.forEach((element, index) => {
      if (element.category) return; // Skip already categorized elements

      const text = element.text.trim();

      // Check for bowling figures (e.g., "4-34")
      if (wicketRunsPattern.test(text)) {
        updatedElements[index].category = 'bowler_figures';
        return;
      }

      // Check for numeric values
      if (numericPattern.test(text)) {
        const num = parseInt(text, 10);

        // Categorize based on value
        if (num < 10) {
          // Small numbers are likely wickets
          updatedElements[index].category = 'bowler_wickets';
        } else if (num < 50) {
          // Medium numbers could be runs or balls
          updatedElements[index].category = 'batsman_runs';
        } else {
          // Larger numbers are likely runs
          updatedElements[index].category = 'batsman_runs';
        }
      }
    });

    // Step 3: Group elements by rows (elements with similar y-coordinates)
    const rowTolerance = 10; // pixels
    const rows = [];

    updatedElements.forEach(element => {
      // Find a row that this element belongs to
      const rowIndex = rows.findIndex(row =>
        Math.abs(row.y - element.y) < rowTolerance
      );

      if (rowIndex >= 0) {
        // Add to existing row
        rows[rowIndex].elements.push(element);
      } else {
        // Create new row
        rows.push({
          y: element.y,
          elements: [element]
        });
      }
    });

    // Sort rows by y-coordinate
    rows.sort((a, b) => a.y - b.y);

    // Sort elements within each row by x-coordinate
    rows.forEach(row => {
      row.elements.sort((a, b) => a.x - b.x);
    });

    console.log(`Identified ${rows.length} rows in the scorecard`);

    // Step 4: Find rows that contain player names
    const playerRows = rows.filter(row =>
      row.elements.some(el => el.category === 'player_name')
    );

    console.log(`Found ${playerRows.length} rows with player names`);

    // Step 5: Identify team1 and team2 based on vertical position
    // In cricket scorecards, typically the first half of player rows are team1
    // and the second half are team2
    if (playerRows.length >= 2) {
      const midpoint = Math.floor(playerRows.length / 2);

      // First half of player rows are team1
      for (let i = 0; i < midpoint; i++) {
        const row = playerRows[i];

        // Find player name in this row
        const playerElement = row.elements.find(el => el.category === 'player_name');
        if (!playerElement) continue;

        // Mark this player as team1
        const playerIndex = updatedElements.findIndex(el => el.id === playerElement.id);
        if (playerIndex !== -1) {
          updatedElements[playerIndex].category = 'team1_player_name';

          // Now look for stats in the same row
          row.elements.forEach(element => {
            if (element === playerElement) return; // Skip the player name

            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex === -1) return;

            // Add team1 prefix to any generic categories
            const category = updatedElements[elementIndex].category;
            if (category === 'batsman_runs') {
              updatedElements[elementIndex].category = 'team1_batsman_runs';
            } else if (category === 'batsman_balls') {
              updatedElements[elementIndex].category = 'team1_batsman_balls';
            } else if (category === 'bowler_figures') {
              updatedElements[elementIndex].category = 'team1_bowler_figures';
            } else if (category === 'bowler_wickets') {
              updatedElements[elementIndex].category = 'team1_bowler_wickets';
            } else if (category === 'bowler_runs') {
              updatedElements[elementIndex].category = 'team1_bowler_runs';
            }
          });
        }
      }

      // Second half of player rows are team2
      for (let i = midpoint; i < playerRows.length; i++) {
        const row = playerRows[i];

        // Find player name in this row
        const playerElement = row.elements.find(el => el.category === 'player_name');
        if (!playerElement) continue;

        // Mark this player as team2
        const playerIndex = updatedElements.findIndex(el => el.id === playerElement.id);
        if (playerIndex !== -1) {
          updatedElements[playerIndex].category = 'team2_player_name';

          // Now look for stats in the same row
          row.elements.forEach(element => {
            if (element === playerElement) return; // Skip the player name

            const elementIndex = updatedElements.findIndex(el => el.id === element.id);
            if (elementIndex === -1) return;

            // Add team2 prefix to any generic categories
            const category = updatedElements[elementIndex].category;
            if (category === 'batsman_runs') {
              updatedElements[elementIndex].category = 'team2_batsman_runs';
            } else if (category === 'batsman_balls') {
              updatedElements[elementIndex].category = 'team2_batsman_balls';
            } else if (category === 'bowler_figures') {
              updatedElements[elementIndex].category = 'team2_bowler_figures';
            } else if (category === 'bowler_wickets') {
              updatedElements[elementIndex].category = 'team2_bowler_wickets';
            } else if (category === 'bowler_runs') {
              updatedElements[elementIndex].category = 'team2_bowler_runs';
            }
          });
        }
      }
    }

    // Step 6: Handle special cases for team names
    // Team names are often short (like "SCOTLAND" or country names)
    updatedElements.forEach((element, index) => {
      const text = element.text.trim();

      // Check for potential team names
      if (text === text.toUpperCase() &&
          text.length > 3 &&
          text.length < 15 &&
          !text.match(/^\d+$/) && // Not just numbers
          !element.category) {

        // Check if this is in the top half of the scorecard
        const elementRow = rows.find(row => row.elements.some(el => el.id === element.id));
        if (elementRow) {
          const rowIndex = rows.indexOf(elementRow);
          const isTopHalf = rowIndex < rows.length / 2;

          // Assign team1 or team2 based on position
          updatedElements[index].category = isTopHalf ? 'team1_name' : 'team2_name';
        }
      }
    });

    // Step 7: Update the text elements state with the modified elements
    console.log('Updated elements after team assignment:', updatedElements);
    setTextElements(updatedElements);

    // Update the selected element if it was modified
    if (selectedElement) {
      const updatedSelectedElement = updatedElements.find(el => el.id === selectedElement.id);
      if (updatedSelectedElement && updatedSelectedElement.category !== selectedElement.category) {
        setSelectedElement(updatedSelectedElement);
      }
    }

    showNotification('Teams auto-assigned successfully', 'success');
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{
        mb: 4,
        p: 3,
        backgroundColor: '#1e2a3a',
        borderRadius: '4px',
        border: '1px solid #2d4055',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: { xs: 'flex-start', md: 'center' },
        justifyContent: 'space-between'
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ color: '#fff', fontWeight: 'bold' }}>
            Cricket Scorecard Labeler
          </Typography>
          <Typography variant="body1" sx={{ color: '#90caf9' }} paragraph>
            Upload cricket scorecard images and label text elements to train a machine learning model for automatic data extraction.
          </Typography>
        </Box>
        <Button
          variant="contained"
          color="primary"
          startIcon={<ChartIcon />}
          onClick={handleOpenStatsModal}
          sx={{
            mt: { xs: 2, md: 0 },
            backgroundColor: '#2196f3',
            '&:hover': {
              backgroundColor: '#1976d2'
            },
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(33, 150, 243, 0.3)'
          }}
        >
          View Training Stats
        </Button>
      </Box>

      <Grid container spacing={4}>
        {/* Left sidebar - Image list */}
        <Grid item xs={12} md={3}>
          <Paper sx={{
            p: 2,
            height: '100%',
            backgroundColor: '#1e2a3a',
            border: '1px solid #2d4055',
            borderRadius: '4px',
            color: '#fff'
          }}>
            <Typography variant="h6" gutterBottom sx={{
              borderBottom: '1px solid #2d4055',
              paddingBottom: '8px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              Images
              <IconButton
                size="small"
                onClick={fetchImages}
                sx={{
                  color: '#90caf9',
                  '&:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    color: '#2196f3'
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Typography>

            <Box sx={{
              mb: 3,
              mt: 2,
              backgroundColor: '#263445',
              padding: '20px',
              borderRadius: '8px',
              border: '2px solid #2d4055',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
            }}>
              <Typography variant="subtitle2" sx={{
                color: '#90caf9',
                mb: 2,
                display: 'flex',
                alignItems: 'center'
              }}>
                <UploadIcon sx={{ fontSize: '16px', mr: 1 }} />
                Upload New Scorecard
              </Typography>

              <Box sx={{
                border: '2px dashed #2d4055',
                borderRadius: '8px',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                transition: 'all 0.2s ease',
                '&:hover': {
                  borderColor: '#90caf9',
                  backgroundColor: 'rgba(33, 150, 243, 0.05)'
                }
              }}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="upload-image"
                  type="file"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                />
                <label htmlFor="upload-image" style={{ width: '100%', cursor: 'pointer' }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <ImageIcon sx={{
                      fontSize: '48px',
                      color: '#90caf9',
                      opacity: 0.7,
                      mb: 2
                    }} />
                    <Typography variant="body1" sx={{ color: '#fff', mb: 1, fontWeight: 'medium' }}>
                      Drag & drop or click to select
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#7a97b9', textAlign: 'center' }}>
                      Supported formats: JPG, PNG, JPEG
                    </Typography>
                  </Box>
                </label>
              </Box>

              {file && (
                <Box sx={{
                  mt: 3,
                  backgroundColor: 'rgba(33, 150, 243, 0.1)',
                  padding: '12px',
                  borderRadius: '6px',
                  border: '1px solid rgba(33, 150, 243, 0.3)'
                }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ImageIcon sx={{ color: '#90caf9', mr: 1, fontSize: '20px' }} />
                      <Typography variant="body2" noWrap sx={{
                        color: '#fff',
                        fontWeight: 'medium',
                        maxWidth: '180px'
                      }}>
                        {file.name}
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={() => {
                        setFile(null);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = '';
                        }
                      }}
                      sx={{
                        color: '#90caf9',
                        '&:hover': {
                          color: '#f44336',
                          backgroundColor: 'rgba(244, 67, 54, 0.1)'
                        }
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  {/* OCR Method Selection */}
                  <FormControl
                    fullWidth
                    variant="outlined"
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        color: '#fff',
                        '& fieldset': {
                          borderColor: 'rgba(144, 202, 249, 0.5)',
                        },
                        '&:hover fieldset': {
                          borderColor: '#90caf9',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#2196f3',
                        },
                      },
                      '& .MuiInputLabel-root': {
                        color: '#90caf9',
                      },
                      '& .MuiSelect-icon': {
                        color: '#90caf9',
                      }
                    }}
                  >
                    <InputLabel id="ocr-method-label">OCR Method</InputLabel>
                    <Select
                      labelId="ocr-method-label"
                      value={ocrMethod}
                      onChange={(e) => setOcrMethod(e.target.value)}
                      label="OCR Method"
                    >
                      <MenuItem value="auto">Auto (Default)</MenuItem>
                      <MenuItem value="paddle">PaddleOCR (Direct)</MenuItem>
                      <MenuItem value="google">Google Vision API</MenuItem>
                      <MenuItem value="tesseract">Tesseract.js</MenuItem>
                    </Select>
                  </FormControl>

                  <Button
                    variant="contained"
                    onClick={handleUpload}
                    disabled={uploading}
                    fullWidth
                    sx={{
                      backgroundColor: '#2196f3',
                      '&:hover': {
                        backgroundColor: '#1976d2'
                      },
                      '&.Mui-disabled': {
                        backgroundColor: 'rgba(33, 150, 243, 0.3)',
                        color: 'rgba(255, 255, 255, 0.5)'
                      },
                      borderRadius: '6px',
                      padding: '10px',
                      fontWeight: 'bold'
                    }}
                  >
                    {uploading ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <CircularProgress size={16} sx={{ mr: 1, color: '#fff' }} />
                        Uploading...
                      </Box>
                    ) : 'Upload & Process'}
                  </Button>
                </Box>
              )}

              {uploadError && (
                <Box sx={{
                  mt: 2,
                  p: 2,
                  backgroundColor: 'rgba(244, 67, 54, 0.1)',
                  borderRadius: '6px',
                  border: '1px solid rgba(244, 67, 54, 0.3)',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <CloseIcon sx={{ color: '#f44336', mr: 1, fontSize: '20px' }} />
                  <Typography variant="body2" sx={{ color: '#f44336' }}>
                    {uploadError}
                  </Typography>
                </Box>
              )}
            </Box>

            <Divider sx={{
              my: 2,
              borderColor: '#2d4055',
              '&::before, &::after': {
                borderColor: '#2d4055',
              }
            }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{
                color: '#90caf9',
                mb: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ImageIcon sx={{ fontSize: '16px', mr: 1 }} />
                  Available Scorecards
                </Box>
                <Chip
                  label={`${images.length} total`}
                  size="small"
                  sx={{
                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                    color: '#90caf9',
                    fontWeight: 'bold',
                    border: '1px solid rgba(33, 150, 243, 0.3)'
                  }}
                />
              </Typography>

              <Box
                sx={{
                  maxHeight: 'calc(100vh - 450px)',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: '8px',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#263445',
                    borderRadius: '4px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#90caf9',
                    borderRadius: '4px',
                  },
                  pr: 1
                }}
              >
                {images.length === 0 ? (
                  <Box sx={{
                    p: 4,
                    textAlign: 'center',
                    backgroundColor: '#263445',
                    borderRadius: '8px',
                    border: '2px solid #2d4055',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <ImageIcon sx={{
                      fontSize: '40px',
                      color: '#90caf9',
                      opacity: 0.5,
                      mb: 2
                    }} />
                    <Typography variant="body1" sx={{ color: '#90caf9', mb: 1 }}>
                      No scorecards yet
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#7a97b9', textAlign: 'center' }}>
                      Upload a new scorecard to get started
                    </Typography>
                  </Box>
                ) : (
                  <Grid container spacing={2}>
                    {images.map((image, index) => (
                      <Grid item xs={12} key={index}>
                        <Box
                          onClick={() => handleSelectImage(image)}
                          sx={{
                            borderRadius: '8px',
                            border: '2px solid',
                            borderColor: currentImage === image ? '#f50057' : '#2d4055',
                            backgroundColor: currentImage === image ? 'rgba(245, 0, 87, 0.1)' : '#263445',
                            overflow: 'hidden',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            boxShadow: currentImage === image ?
                                      '0 0 10px rgba(245, 0, 87, 0.3)' :
                                      '0 2px 6px rgba(0, 0, 0, 0.2)',
                            '&:hover': {
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
                              transform: 'translateY(-2px)',
                              borderColor: currentImage === image ? '#f50057' : '#90caf9'
                            }
                          }}
                        >
                          {/* Image header */}
                          <Box sx={{
                            backgroundColor: currentImage === image ? 'rgba(245, 0, 87, 0.2)' : 'rgba(33, 150, 243, 0.1)',
                            padding: '8px 12px',
                            borderBottom: '1px solid',
                            borderColor: currentImage === image ? 'rgba(245, 0, 87, 0.3)' : '#2d4055',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}>
                            <Typography variant="subtitle2" sx={{
                              color: currentImage === image ? '#f50057' : '#90caf9',
                              fontWeight: 'bold',
                              fontSize: '12px'
                            }}>
                              Scorecard {index + 1}
                            </Typography>
                            <Chip
                              label={currentImage === image ? 'Selected' : 'Click to select'}
                              size="small"
                              sx={{
                                height: '20px',
                                fontSize: '10px',
                                backgroundColor: currentImage === image ? 'rgba(245, 0, 87, 0.2)' : 'rgba(255, 255, 255, 0.1)',
                                color: currentImage === image ? '#f50057' : '#90caf9',
                                border: '1px solid',
                                borderColor: currentImage === image ? 'rgba(245, 0, 87, 0.3)' : 'rgba(144, 202, 249, 0.2)'
                              }}
                            />
                          </Box>

                          {/* Image content */}
                          <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
                            <Box sx={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '4px',
                              backgroundColor: 'rgba(0, 0, 0, 0.3)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              mr: 2,
                              border: '1px solid #2d4055'
                            }}>
                              <ImageIcon sx={{
                                color: currentImage === image ? '#f50057' : '#90caf9',
                                fontSize: '24px'
                              }} />
                            </Box>
                            <Box>
                              <Typography variant="body2" sx={{
                                color: '#fff',
                                fontWeight: currentImage === image ? 'bold' : 'medium'
                              }}>
                                {image.substring(0, 12) + '...'}
                              </Typography>
                              <Typography variant="caption" sx={{ color: '#7a97b9' }}>
                                {`Uploaded ${new Date().toLocaleDateString()}`}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Main content - Image and text elements */}
        <Grid item xs={12} md={6}>
          <Paper sx={{
            p: 2,
            height: '100%',
            backgroundColor: '#1e2a3a',
            border: '1px solid #2d4055',
            borderRadius: '4px',
            color: '#fff'
          }}>
            {currentImage ? (
              <>
                <Typography variant="h6" gutterBottom sx={{
                  borderBottom: '1px solid #2d4055',
                  paddingBottom: '8px',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <ImageIcon sx={{ mr: 1, color: '#90caf9' }} />
                  Current Image: <span style={{ color: '#90caf9', marginLeft: '8px' }}>{currentImage.substring(0, 8)}...</span>
                </Typography>

                {loading ? (
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    p: 4,
                    height: '300px',
                    backgroundColor: '#263445',
                    borderRadius: '4px',
                    border: '1px solid #2d4055'
                  }}>
                    <CircularProgress sx={{ color: '#2196f3', mb: 2 }} />
                    <Typography variant="body1" sx={{ color: '#90caf9' }}>
                      Loading scorecard data...
                    </Typography>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      position: 'relative',
                      maxHeight: 'calc(100vh - 200px)', // Increased height
                      overflow: 'auto',
                      overflowX: 'hidden' // Prevent horizontal scrolling
                    }}
                  >
                    {/* Simulated scorecard image */}
                    <div
                      style={{
                        width: '100%',
                        minHeight: '800px', // Increased from fixed 450px to auto-adjusting minimum height
                        backgroundColor: '#1e2a3a',
                        border: '2px solid #2d4055',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                        position: 'relative',
                        overflow: 'auto', // Changed from 'hidden' to 'auto' to enable scrolling
                        borderRadius: '8px',
                        padding: '24px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
                      }}
                      ref={imageRef}
                    >
                      {/* Scorecard header */}
                      <div style={{
                        width: '100%',
                        textAlign: 'center',
                        marginBottom: '24px',
                        borderBottom: '1px solid #2d4055',
                        paddingBottom: '12px',
                        position: 'relative',
                        zIndex: 5
                      }}>
                        <Typography variant="h5" style={{ color: '#fff', fontWeight: 'bold', letterSpacing: '0.5px' }}>
                          Cricket Scorecard
                        </Typography>
                        <Typography variant="caption" style={{
                          color: '#90caf9',
                          marginTop: '4px',
                          display: 'block',
                          fontSize: '10px'
                        }}>
                          {currentImage ? currentImage.substring(0, 20) + '...' : 'No image selected'}
                        </Typography>
                      </div>

                      {/* Team sections */}
                      <div style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        marginBottom: '24px'
                      }}>
                        {/* Team 1 */}
                        <div style={{
                          width: '48%',
                          backgroundColor: '#263445',
                          padding: '16px',
                          borderRadius: '8px',
                          border: '1px solid #2d4055',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                        }}>
                          <Typography variant="subtitle1" style={{ color: '#fff', textAlign: 'center', fontWeight: 'bold' }}>
                            TEAM ALPHA
                          </Typography>
                          <Typography variant="h4" style={{ color: '#4caf50', textAlign: 'center', fontWeight: 'bold', margin: '8px 0' }}>
                            156/4
                          </Typography>
                          <Typography variant="body1" style={{ color: '#90caf9', textAlign: 'center' }}>
                            (20.0 overs)
                          </Typography>
                        </div>

                        {/* Team 2 */}
                        <div style={{
                          width: '48%',
                          backgroundColor: '#263445',
                          padding: '16px',
                          borderRadius: '8px',
                          border: '1px solid #2d4055',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                        }}>
                          <Typography variant="subtitle1" style={{ color: '#fff', textAlign: 'center', fontWeight: 'bold' }}>
                            TEAM BETA
                          </Typography>
                          <Typography variant="h4" style={{ color: '#f44336', textAlign: 'center', fontWeight: 'bold', margin: '8px 0' }}>
                            142/8
                          </Typography>
                          <Typography variant="body1" style={{ color: '#90caf9', textAlign: 'center' }}>
                            (20.0 overs)
                          </Typography>
                        </div>
                      </div>

                      {/* Match result */}
                      <div style={{
                        width: '100%',
                        backgroundColor: '#263445',
                        padding: '16px',
                        borderRadius: '8px',
                        marginBottom: '24px',
                        border: '1px solid #2d4055',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                      }}>
                        <Typography variant="h6" style={{ color: '#fff', textAlign: 'center', fontWeight: 'bold' }}>
                          TEAM ALPHA WON BY 14 RUNS
                        </Typography>
                      </div>

                      {/* Player stats section */}
                      <div style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between'
                      }}>
                        {/* Top batsman */}
                        <div style={{
                          width: '48%',
                          backgroundColor: '#263445',
                          padding: '12px',
                          borderRadius: '8px',
                          border: '1px solid #2d4055',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                        }}>
                          <Typography variant="subtitle2" style={{ color: '#90caf9', textAlign: 'center', fontSize: '12px' }}>
                            TOP BATSMAN
                          </Typography>
                          <Typography variant="body1" style={{ color: '#fff', textAlign: 'center', fontWeight: 'bold' }}>
                            JOHN SMITH
                          </Typography>
                          <Typography variant="body1" style={{ color: '#4caf50', textAlign: 'center', fontWeight: 'bold' }}>
                            78* (52)
                          </Typography>
                        </div>

                        {/* Venue */}
                        <div style={{
                          width: '48%',
                          backgroundColor: '#263445',
                          padding: '12px',
                          borderRadius: '8px',
                          border: '1px solid #2d4055',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                        }}>
                          <Typography variant="subtitle2" style={{ color: '#90caf9', textAlign: 'center', fontSize: '12px' }}>
                            VENUE
                          </Typography>
                          <Typography variant="body1" style={{ color: '#fff', textAlign: 'center', fontWeight: 'bold' }}>
                            CRICKET STADIUM
                          </Typography>
                        </div>
                      </div>

                      {/* Fixed grid layout for scorecard elements */}
                      <div style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '10px',
                        boxSizing: 'border-box',
                        overflow: 'auto'
                      }}>
                        {/* SECTION HEADERS */}
                        <div style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '5px',
                          padding: '5px',
                          backgroundColor: 'rgba(0,0,0,0.3)',
                          borderRadius: '4px'
                        }}>
                          <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#90caf9' }}>Team Alpha</div>
                          <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#90caf9' }}>Team Beta</div>
                        </div>

                        {/* TEAM SCORES SECTION */}
                        <div style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '15px'
                        }}>
                          {/* Team Alpha Score Card */}
                          <div style={{
                            width: '48%',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            border: '1px solid rgba(76, 175, 80, 0.3)',
                            borderRadius: '4px',
                            padding: '10px',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center'
                          }}>
                            {textElements.filter(el => el.text && el.text.includes('156/4')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '24px',
                                  fontWeight: 'bold',
                                  color: '#4caf50',
                                  cursor: 'pointer',
                                  padding: '5px 10px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('overs') && !el.text.includes('Beta')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '14px',
                                  color: '#aaa',
                                  marginTop: '5px',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>

                          {/* Team Beta Score Card */}
                          <div style={{
                            width: '48%',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            border: '1px solid rgba(255, 152, 0, 0.3)',
                            borderRadius: '4px',
                            padding: '10px',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center'
                          }}>
                            {textElements.filter(el => el.text && el.text.includes('142/8')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '24px',
                                  fontWeight: 'bold',
                                  color: '#ff9800',
                                  cursor: 'pointer',
                                  padding: '5px 10px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('overs') && !el.text.includes('Alpha')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '14px',
                                  color: '#aaa',
                                  marginTop: '5px',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* MATCH RESULT SECTION */}
                        <div style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          marginBottom: '15px'
                        }}>
                          {textElements.filter(el => el.text && el.text.toLowerCase().includes('won by')).map(element => (
                            <div
                              key={element.id}
                              onClick={() => handleSelectElement(element)}
                              style={{
                                fontSize: '16px',
                                fontWeight: 'bold',
                                color: 'white',
                                backgroundColor: 'rgba(33, 150, 243, 0.7)',
                                padding: '8px 16px',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none'
                              }}
                            >
                              {element.text}
                              {element.category && (
                                <span style={{
                                  fontSize: '10px',
                                  backgroundColor: '#1565c0',
                                  color: 'white',
                                  padding: '2px 4px',
                                  borderRadius: '2px',
                                  marginLeft: '5px'
                                }}>
                                  {element.category}
                                </span>
                              )}
                            </div>
                          ))}
                        </div>

                        {/* TOP PLAYER AND VENUE SECTION */}
                        <div style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '15px'
                        }}>
                          {/* Top Player Info */}
                          <div style={{
                            width: '48%',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            border: '1px solid rgba(33, 150, 243, 0.3)',
                            borderRadius: '4px',
                            padding: '10px'
                          }}>
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('top batsman')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '12px',
                                  color: '#aaa',
                                  marginBottom: '5px',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('john smith')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '16px',
                                  fontWeight: 'bold',
                                  color: 'white',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                            {textElements.filter(el => el.text && el.text.includes('78*')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '14px',
                                  color: '#4caf50',
                                  marginTop: '5px',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>

                          {/* Venue Info */}
                          <div style={{
                            width: '48%',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            border: '1px solid rgba(33, 150, 243, 0.3)',
                            borderRadius: '4px',
                            padding: '10px'
                          }}>
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('venue')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '12px',
                                  color: '#aaa',
                                  marginBottom: '5px',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                            {textElements.filter(el => el.text && el.text.toLowerCase().includes('stadium')).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '16px',
                                  fontWeight: 'bold',
                                  color: 'white',
                                  cursor: 'pointer',
                                  padding: '3px 6px',
                                  borderRadius: '4px',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : 'none',
                                  backgroundColor: element.category ? 'rgba(33, 150, 243, 0.1)' : 'transparent'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* STATISTICS DIVIDER */}
                        <div style={{
                          width: '100%',
                          margin: '30px 0 20px 0',
                          textAlign: 'center',
                          position: 'relative',
                          height: '20px'
                        }}>
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            left: 0,
                            right: 0,
                            height: '2px',
                            backgroundColor: '#2d4055'
                          }}></div>
                          <span style={{
                            position: 'relative',
                            backgroundColor: '#1e2a3a',
                            padding: '0 15px',
                            color: '#90caf9',
                            fontWeight: 'bold',
                            fontSize: '16px'
                          }}>
                            DETAILED STATISTICS
                          </span>
                        </div>

                        {/* BATTING STATISTICS SECTION */}
                        <div style={{
                          width: '100%',
                          marginBottom: '15px'
                        }}>
                          <div style={{
                            fontSize: '14px',
                            fontWeight: 'bold',
                            color: '#90caf9',
                            marginBottom: '10px',
                            padding: '5px',
                            backgroundColor: 'rgba(0,0,0,0.3)',
                            borderRadius: '4px'
                          }}>
                            Batting Statistics
                          </div>

                          <div style={{
                            width: '100%',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            border: '1px solid rgba(76, 175, 80, 0.3)',
                            borderRadius: '4px',
                            padding: '10px',
                            marginBottom: '10px'
                          }}>
                            <div style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              borderBottom: '1px solid rgba(255,255,255,0.1)',
                              paddingBottom: '5px',
                              marginBottom: '10px'
                            }}>
                              <div style={{ width: '40%', fontSize: '14px', fontWeight: 'bold', color: '#fff' }}>Batsman</div>
                              <div style={{ width: '15%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Runs</div>
                              <div style={{ width: '15%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Balls</div>
                              <div style={{ width: '15%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>4s</div>
                              <div style={{ width: '15%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>6s</div>
                            </div>

                            {/* Batsman rows - we'll create 4 sample rows */}
                            {[
                              { name: 'John Smith', runs: '78*', balls: '52', fours: '6', sixes: '3' },
                              { name: 'David Warner', runs: '45', balls: '38', fours: '4', sixes: '1' },
                              { name: 'Steve Smith', runs: '23', balls: '19', fours: '2', sixes: '0' },
                              { name: 'Glenn Maxwell', runs: '10', balls: '8', fours: '1', sixes: '0' }
                            ].map((batsman, index) => (
                              <div
                                key={index}
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  padding: '8px 0',
                                  borderBottom: index < 3 ? '1px solid rgba(255,255,255,0.05)' : 'none',
                                  cursor: 'pointer'
                                }}
                                onClick={() => {
                                  // Find the element with this batsman's name if it exists
                                  const element = textElements.find(el =>
                                    el.text && el.text.toLowerCase().includes(batsman.name.toLowerCase())
                                  );
                                  if (element) handleSelectElement(element);
                                }}
                              >
                                <div style={{ width: '40%', fontSize: '14px', color: '#fff' }}>{batsman.name}</div>
                                <div style={{ width: '15%', fontSize: '14px', color: '#4caf50', textAlign: 'center' }}>{batsman.runs}</div>
                                <div style={{ width: '15%', fontSize: '14px', color: '#aaa', textAlign: 'center' }}>{batsman.balls}</div>
                                <div style={{ width: '15%', fontSize: '14px', color: '#2196f3', textAlign: 'center' }}>{batsman.fours}</div>
                                <div style={{ width: '15%', fontSize: '14px', color: '#f50057', textAlign: 'center' }}>{batsman.sixes}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* BOWLING STATISTICS SECTION */}
                        <div style={{
                          width: '100%',
                          marginBottom: '15px'
                        }}>
                          <div style={{
                            fontSize: '14px',
                            fontWeight: 'bold',
                            color: '#90caf9',
                            marginBottom: '10px',
                            padding: '5px',
                            backgroundColor: 'rgba(0,0,0,0.3)',
                            borderRadius: '4px'
                          }}>
                            Bowling Statistics
                          </div>

                          <div style={{
                            width: '100%',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            border: '1px solid rgba(255, 152, 0, 0.3)',
                            borderRadius: '4px',
                            padding: '10px'
                          }}>
                            <div style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              borderBottom: '1px solid rgba(255,255,255,0.1)',
                              paddingBottom: '5px',
                              marginBottom: '10px'
                            }}>
                              <div style={{ width: '30%', fontSize: '14px', fontWeight: 'bold', color: '#fff' }}>Bowler</div>
                              <div style={{ width: '14%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Overs</div>
                              <div style={{ width: '14%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Maidens</div>
                              <div style={{ width: '14%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Runs</div>
                              <div style={{ width: '14%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Wickets</div>
                              <div style={{ width: '14%', fontSize: '14px', fontWeight: 'bold', color: '#fff', textAlign: 'center' }}>Econ</div>
                            </div>

                            {/* Bowler rows - we'll create 4 sample rows */}
                            {[
                              { name: 'Mitchell Starc', overs: '4.0', maidens: '0', runs: '32', wickets: '3', economy: '8.00' },
                              { name: 'Pat Cummins', overs: '4.0', maidens: '0', runs: '28', wickets: '2', economy: '7.00' },
                              { name: 'Josh Hazlewood', overs: '4.0', maidens: '1', runs: '24', wickets: '2', economy: '6.00' },
                              { name: 'Adam Zampa', overs: '4.0', maidens: '0', runs: '36', wickets: '1', economy: '9.00' }
                            ].map((bowler, index) => (
                              <div
                                key={index}
                                style={{
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                  padding: '8px 0',
                                  borderBottom: index < 3 ? '1px solid rgba(255,255,255,0.05)' : 'none',
                                  cursor: 'pointer'
                                }}
                                onClick={() => {
                                  // Find the element with this bowler's name if it exists
                                  const element = textElements.find(el =>
                                    el.text.toLowerCase().includes(bowler.name.toLowerCase())
                                  );
                                  if (element) handleSelectElement(element);
                                }}
                              >
                                <div style={{ width: '30%', fontSize: '14px', color: '#fff' }}>{bowler.name}</div>
                                <div style={{ width: '14%', fontSize: '14px', color: '#aaa', textAlign: 'center' }}>{bowler.overs}</div>
                                <div style={{ width: '14%', fontSize: '14px', color: '#2196f3', textAlign: 'center' }}>{bowler.maidens}</div>
                                <div style={{ width: '14%', fontSize: '14px', color: '#ff9800', textAlign: 'center' }}>{bowler.runs}</div>
                                <div style={{ width: '14%', fontSize: '14px', color: '#f50057', textAlign: 'center' }}>{bowler.wickets}</div>
                                <div style={{ width: '14%', fontSize: '14px', color: '#9c27b0', textAlign: 'center' }}>{bowler.economy}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* OTHER ELEMENTS SECTION */}
                        <div style={{
                          width: '100%',
                          marginTop: '10px'
                        }}>
                          <div style={{
                            fontSize: '14px',
                            fontWeight: 'bold',
                            color: '#90caf9',
                            marginBottom: '10px',
                            padding: '5px',
                            backgroundColor: 'rgba(0,0,0,0.3)',
                            borderRadius: '4px'
                          }}>
                            Other Elements
                          </div>
                          <div style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '10px'
                          }}>
                            {textElements.filter(el => {
                              // Skip elements without text
                              if (!el || !el.text) return false;

                              try {
                                const text = el.text.toLowerCase();
                                return !text.includes('team alpha') &&
                                       !text.includes('team beta') &&
                                       !text.includes('156/4') &&
                                       !text.includes('142/8') &&
                                       !text.includes('overs') &&
                                       !text.includes('won by') &&
                                       !text.includes('top batsman') &&
                                       !text.includes('john smith') &&
                                       !text.includes('78*') &&
                                       !text.includes('venue') &&
                                       !text.includes('stadium');
                              } catch (error) {
                                console.warn('Error filtering text element:', error);
                                return false;
                              }
                            }).map(element => (
                              <div
                                key={element.id}
                                onClick={() => handleSelectElement(element)}
                                style={{
                                  fontSize: '14px',
                                  color: '#fff',
                                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                  padding: '8px 12px',
                                  borderRadius: '4px',
                                  cursor: 'pointer',
                                  border: selectedElement && selectedElement.id === element.id ? '2px solid #f50057' : '1px solid #2d4055',
                                  maxWidth: '30%',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap'
                                }}
                              >
                                {element.text}
                                {element.category && (
                                  <span style={{
                                    fontSize: '10px',
                                    backgroundColor: '#2196f3',
                                    color: 'white',
                                    padding: '2px 4px',
                                    borderRadius: '2px',
                                    marginLeft: '5px'
                                  }}>
                                    {element.category}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Box>
                )}

                <Box sx={{
                  mt: 3,
                  display: 'flex',
                  justifyContent: 'space-between',
                  backgroundColor: '#263445',
                  padding: '16px',
                  borderRadius: '4px',
                  border: '1px solid #2d4055'
                }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SaveIcon />}
                    onClick={handleSaveAnnotations}
                    sx={{
                      backgroundColor: '#2196f3',
                      '&:hover': {
                        backgroundColor: '#1976d2'
                      },
                      fontWeight: 'bold',
                      boxShadow: '0 2px 8px rgba(33, 150, 243, 0.3)'
                    }}
                  >
                    Save Annotations
                  </Button>

                  <Button
                    variant="outlined"
                    color="secondary"
                    startIcon={<TrainIcon />}
                    onClick={handleTrainModel}
                    disabled={training}
                    sx={{
                      borderColor: training ? 'rgba(156, 39, 176, 0.3)' : 'rgba(156, 39, 176, 0.5)',
                      color: training ? 'rgba(156, 39, 176, 0.5)' : '#9c27b0',
                      '&:hover': {
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        borderColor: '#9c27b0'
                      },
                      fontWeight: 'medium'
                    }}
                  >
                    {training ? 'Training...' : 'Train Model'}
                  </Button>
                </Box>
              </>
            ) : (
              <Box sx={{
                p: 4,
                textAlign: 'center',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '400px',
                backgroundColor: '#1e2a3a',
                borderRadius: '4px',
                border: '1px solid #2d4055'
              }}>
                <CricketBallIcon
                  size={80}
                  color="#c62828"
                />
                <Typography variant="h6" sx={{ color: '#90caf9', mb: 2 }}>
                  No Scorecard Selected
                </Typography>
                <Typography variant="body1" sx={{ color: '#7a97b9', maxWidth: '400px' }}>
                  Select an image from the list or upload a new scorecard to begin labeling elements for machine learning training.
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Right sidebar - Text elements and editing */}
        <Grid item xs={12} md={3}>
          <Paper sx={{
            p: 2,
            height: '100%',
            backgroundColor: '#1e2a3a',
            border: '1px solid #2d4055',
            borderRadius: '4px',
            color: '#fff'
          }}>
            <Typography variant="h6" gutterBottom sx={{
              borderBottom: '1px solid #2d4055',
              paddingBottom: '8px',
              fontWeight: 'bold'
            }}>
              Text Elements
            </Typography>

            {currentImage ? (
              <>
                <Typography variant="subtitle2" sx={{
                  color: '#90caf9',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <span>Found {textElements.length} elements</span>
                  <Chip
                    label={`${textElements.filter(el => el && el.category).length} labeled`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(33, 150, 243, 0.2)',
                      color: '#90caf9',
                      fontWeight: 'bold',
                      border: '1px solid rgba(33, 150, 243, 0.3)'
                    }}
                  />
                </Typography>

                <Box sx={{
                  maxHeight: 'calc(100vh - 380px)',
                  overflow: 'auto',
                  '&::-webkit-scrollbar': {
                    width: '8px',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#263445',
                    borderRadius: '4px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#90caf9',
                    borderRadius: '4px',
                  },
                  pr: 1
                }}>
                  {textElements.map((element) => (
                    <Box
                      key={element.id}
                      sx={{
                        mb: 2,
                        borderRadius: '6px',
                        border: '2px solid',
                        borderColor: selectedElement && selectedElement.id === element.id ? '#f50057' :
                                    element.category ? '#2196f3' : '#2d4055',
                        backgroundColor: selectedElement && selectedElement.id === element.id ? 'rgba(245, 0, 87, 0.1)' : '#263445',
                        overflow: 'hidden',
                        transition: 'all 0.2s ease',
                        boxShadow: selectedElement && selectedElement.id === element.id ?
                                  '0 0 10px rgba(245, 0, 87, 0.3)' :
                                  '0 2px 6px rgba(0, 0, 0, 0.2)',
                        '&:hover': {
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      {/* Element header with category if exists */}
                      {element.category && (
                        <Box sx={{
                          backgroundColor: 'rgba(33, 150, 243, 0.2)',
                          padding: '4px 10px',
                          borderBottom: '1px solid #2d4055',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <Typography variant="caption" sx={{
                            color: '#90caf9',
                            fontWeight: 'bold',
                            fontSize: '11px',
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px'
                          }}>
                            {element.category}
                          </Typography>
                          <Chip
                            label={`ID: ${element.id}`}
                            size="small"
                            sx={{
                              height: '18px',
                              fontSize: '10px',
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              color: '#90caf9'
                            }}
                          />
                        </Box>
                      )}

                      {/* Element content */}
                      <Box
                        sx={{
                          p: 2,
                          cursor: 'pointer'
                        }}
                        onClick={() => handleSelectElement(element)}
                      >
                        <Typography variant="body1" sx={{
                          color: '#fff',
                          fontWeight: 'medium',
                          mb: 1
                        }}>
                          {element.text}
                        </Typography>

                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center'
                          }}>
                            <Typography variant="caption" sx={{
                              color: element.confidence > 0.9 ? '#4caf50' :
                                    element.confidence > 0.7 ? '#ff9800' : '#f44336',
                              fontWeight: 'bold',
                              mr: 1
                            }}>
                              {(element.confidence * 100).toFixed(1)}%
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#7a97b9' }}>
                              confidence
                            </Typography>
                          </Box>

                          {!element.category && (
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{
                                minWidth: 0,
                                padding: '2px 8px',
                                fontSize: '10px',
                                borderColor: 'rgba(33, 150, 243, 0.5)',
                                color: '#90caf9',
                                '&:hover': {
                                  backgroundColor: 'rgba(33, 150, 243, 0.1)',
                                  borderColor: '#2196f3'
                                }
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectElement(element);
                              }}
                            >
                              Label
                            </Button>
                          )}
                        </Box>
                      </Box>
                    </Box>
                  ))}
                </Box>

                {selectedElement && (
                  <Box sx={{
                    mt: 3,
                    backgroundColor: '#263445',
                    padding: '20px',
                    borderRadius: '8px',
                    border: '2px solid #2d4055',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      mb: 2
                    }}>
                      <Typography variant="h6" sx={{
                        color: '#fff',
                        fontWeight: 'bold',
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                        <span style={{
                          display: 'inline-block',
                          width: '12px',
                          height: '12px',
                          backgroundColor: '#f50057',
                          borderRadius: '50%',
                          marginRight: '8px'
                        }}></span>
                        Edit Element
                      </Typography>

                      <Chip
                        label={`ID: ${selectedElement.id}`}
                        size="small"
                        sx={{
                          backgroundColor: 'rgba(255, 255, 255, 0.1)',
                          color: '#90caf9',
                          border: '1px solid rgba(144, 202, 249, 0.2)'
                        }}
                      />
                    </Box>

                    <Box sx={{
                      backgroundColor: 'rgba(0, 0, 0, 0.2)',
                      padding: '12px',
                      borderRadius: '4px',
                      mb: 3,
                      border: '1px solid rgba(45, 64, 85, 0.6)'
                    }}>
                      <Typography variant="body2" sx={{ color: '#90caf9', mb: 1 }}>
                        Original Text:
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#fff', fontWeight: 'medium' }}>
                        "{selectedElement.text}"
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        mt: 1
                      }}>
                        <Typography variant="caption" sx={{
                          color: selectedElement.confidence > 0.9 ? '#4caf50' :
                                selectedElement.confidence > 0.7 ? '#ff9800' : '#f44336',
                          fontWeight: 'bold',
                          mr: 1
                        }}>
                          {(selectedElement.confidence * 100).toFixed(1)}% confidence
                        </Typography>
                      </Box>
                    </Box>

                    <Typography variant="subtitle2" sx={{ color: '#90caf9', mb: 1 }}>
                      Edit Text
                    </Typography>
                    <TextField
                      value={selectedElement.text}
                      onChange={handleTextChange}
                      fullWidth
                      variant="outlined"
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: '#2d4055',
                            borderWidth: '2px'
                          },
                          '&:hover fieldset': {
                            borderColor: '#90caf9',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#2196f3',
                          },
                          color: '#fff',
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          borderRadius: '6px'
                        }
                      }}
                    />

                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      mb: 1
                    }}>
                      <Typography variant="subtitle2" sx={{ color: '#90caf9' }}>
                        Assign Category
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          color="secondary"
                          onClick={handleAutoAssignTeams}
                          sx={{
                            fontSize: '0.7rem',
                            padding: '2px 8px',
                            minWidth: 'auto',
                            borderColor: 'rgba(156, 39, 176, 0.5)',
                            color: '#9c27b0',
                            '&:hover': {
                              borderColor: '#9c27b0',
                              backgroundColor: 'rgba(156, 39, 176, 0.1)',
                            },
                          }}
                        >
                          Auto-Assign Teams
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          color="primary"
                          onClick={() => setShowAddCategory(!showAddCategory)}
                          sx={{
                            fontSize: '0.7rem',
                            padding: '2px 8px',
                            minWidth: 'auto',
                            borderColor: 'rgba(33, 150, 243, 0.5)',
                            color: '#2196f3',
                            '&:hover': {
                              borderColor: '#2196f3',
                              backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            },
                          }}
                        >
                          {showAddCategory ? 'Cancel' : 'Add Custom'}
                        </Button>
                      </Box>
                    </Box>

                    {showAddCategory && (
                      <Box sx={{
                        mb: 2,
                        p: 2,
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        borderRadius: '4px',
                        border: '1px solid rgba(33, 150, 243, 0.3)'
                      }}>
                        <Typography variant="caption" sx={{ color: '#90caf9', mb: 1, display: 'block' }}>
                          Create a new custom category
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <TextField
                            value={newCategoryName}
                            onChange={(e) => setNewCategoryName(e.target.value)}
                            placeholder="New category name"
                            size="small"
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                color: '#fff',
                                backgroundColor: 'rgba(0, 0, 0, 0.2)',
                                '& fieldset': {
                                  borderColor: 'rgba(144, 202, 249, 0.3)',
                                },
                              },
                            }}
                          />
                          <Button
                            variant="contained"
                            color="primary"
                            onClick={handleAddCustomCategory}
                            size="small"
                            sx={{
                              minWidth: 'auto',
                              backgroundColor: '#2196f3',
                              '&:hover': {
                                backgroundColor: '#1976d2',
                              },
                            }}
                          >
                            Add
                          </Button>
                        </Box>
                        {customCategories.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="caption" sx={{ color: '#90caf9', mb: 1, display: 'block' }}>
                              Your custom categories:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              {customCategories.map((cat) => (
                                <Chip
                                  key={cat}
                                  label={cat}
                                  size="small"
                                  onDelete={() => handleRemoveCustomCategory(cat)}
                                  sx={{
                                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                                    color: '#90caf9',
                                    border: '1px solid rgba(33, 150, 243, 0.3)',
                                  }}
                                />
                              ))}
                            </Box>
                          </Box>
                        )}
                      </Box>
                    )}

                    <FormControl fullWidth sx={{ mb: 3 }}>
                      <Select
                        value={selectedElement.category || ''}
                        onChange={handleCategoryChange}
                        displayEmpty
                        sx={{
                          color: '#fff',
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#2d4055',
                            borderWidth: '2px'
                          },
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#90caf9',
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: '#2196f3',
                          },
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          borderRadius: '6px'
                        }}
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              backgroundColor: '#1e2a3a',
                              color: '#fff',
                              maxHeight: '300px',
                              '& .MuiMenuItem-root': {
                                padding: '10px 16px',
                                '&:hover': {
                                  backgroundColor: '#344458',
                                },
                                '&.Mui-selected': {
                                  backgroundColor: 'rgba(33, 150, 243, 0.2)',
                                  '&:hover': {
                                    backgroundColor: 'rgba(33, 150, 243, 0.3)',
                                  }
                                }
                              }
                            }
                          }
                        }}
                        renderValue={(selected) => {
                          if (!selected) {
                            return <Typography sx={{ color: '#7a97b9', fontStyle: 'italic' }}>Select a category...</Typography>;
                          }
                          return selected;
                        }}
                      >
                        <MenuItem value="" sx={{ color: '#7a97b9' }}>
                          <em>None (Clear Category)</em>
                        </MenuItem>

                        {/* Standard Categories */}
                        {[
                          <ListSubheader key="standard-header" sx={{ backgroundColor: '#1e2a3a', color: '#90caf9', fontWeight: 'bold' }}>
                            Standard Categories
                          </ListSubheader>,
                          ...[
                            'team1_name', 'team2_name',
                            'team1_score', 'team2_score',
                            'venue', 'match_result', 'player_of_match',
                            'player_name', 'batsman_name', 'bowler_name',
                            'batsman_runs', 'batsman_balls',
                            'bowler_wickets', 'bowler_runs', 'bowler_figures'
                          ].map((category) => (
                            <MenuItem key={category} value={category}>
                              {category}
                            </MenuItem>
                          ))
                        ]}

                        {/* Team-specific categories */}
                        {[
                          <ListSubheader key="team-specific-header" sx={{ backgroundColor: '#1e2a3a', color: '#90caf9', fontWeight: 'bold' }}>
                            Team-Specific Categories
                          </ListSubheader>,
                          ...[
                            'team1_player_name', 'team2_player_name',
                            'team1_batsman_name', 'team2_batsman_name',
                            'team1_batsman_runs', 'team2_batsman_runs',
                            'team1_batsman_balls', 'team2_batsman_balls',
                            'team1_bowler_name', 'team2_bowler_name',
                            'team1_bowler_wickets', 'team2_bowler_wickets',
                            'team1_bowler_runs', 'team2_bowler_runs',
                            'team1_bowler_figures', 'team2_bowler_figures'
                          ].map((category) => (
                            <MenuItem key={category} value={category}>
                              {category}
                            </MenuItem>
                          ))
                        ]}

                        {/* Custom Categories */}
                        {customCategories.length > 0 && [
                          <ListSubheader key="custom-header" sx={{ backgroundColor: '#1e2a3a', color: '#90caf9', fontWeight: 'bold' }}>
                            Custom Categories
                          </ListSubheader>,
                          ...customCategories.map((category) => (
                            <MenuItem key={category} value={category}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                                {category}
                                <Chip
                                  label="Custom"
                                  size="small"
                                  sx={{
                                    height: '16px',
                                    fontSize: '0.6rem',
                                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                                    color: '#90caf9',
                                    ml: 1
                                  }}
                                />
                              </Box>
                            </MenuItem>
                          ))
                        ]}
                      </Select>
                    </FormControl>

                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between'
                    }}>
                      <Button
                        variant="contained"
                        color="primary"
                        sx={{
                          backgroundColor: '#2196f3',
                          '&:hover': {
                            backgroundColor: '#1976d2'
                          },
                          fontWeight: 'bold',
                          boxShadow: '0 2px 8px rgba(33, 150, 243, 0.3)',
                          borderRadius: '6px',
                          padding: '8px 16px'
                        }}
                      >
                        Apply Changes
                      </Button>

                      {selectedElement.category && (
                        <Button
                          variant="outlined"
                          color="error"
                          onClick={() => handleClearCategory()}
                          sx={{
                            borderColor: 'rgba(244, 67, 54, 0.5)',
                            color: '#f44336',
                            borderWidth: '2px',
                            '&:hover': {
                              backgroundColor: 'rgba(244, 67, 54, 0.1)',
                              borderColor: '#f44336',
                              borderWidth: '2px'
                            },
                            borderRadius: '6px',
                            padding: '8px 16px'
                          }}
                        >
                          Clear Category
                        </Button>
                      )}
                    </Box>
                  </Box>
                )}
              </>
            ) : (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '200px',
                backgroundColor: '#263445',
                borderRadius: '4px',
                border: '1px solid #2d4055',
                padding: '20px'
              }}>
                <Typography variant="body1" sx={{ color: '#90caf9', textAlign: 'center', mb: 2 }}>
                  No image selected
                </Typography>
                <Typography variant="body2" sx={{ color: '#7a97b9', textAlign: 'center' }}>
                  Select an image from the list or upload a new one to begin labeling
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Model status card */}
      <Paper sx={{
        p: 4,
        mt: 4,
        backgroundColor: '#1e2a3a',
        border: '2px solid #2d4055',
        borderRadius: '8px',
        color: '#fff',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.3)'
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #2d4055',
          paddingBottom: '16px',
          marginBottom: '24px'
        }}>
          <Typography variant="h5" sx={{
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center'
          }}>
            <TrainIcon sx={{ mr: 1.5, color: '#90caf9', fontSize: '28px' }} />
            Model Status
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={simulationMode}
                  onChange={(e) => setSimulationMode(e.target.checked)}
                  color="secondary"
                />
              }
              label={
                <Typography variant="body2" sx={{ color: '#9c27b0' }}>
                  Simulation Mode
                </Typography>
              }
              sx={{ mr: 2 }}
            />

            <Button
              variant="contained"
              startIcon={training ? <CircularProgress size={16} sx={{ color: '#fff' }} /> : <TrainIcon />}
              onClick={handleTrainModel}
              disabled={training || (!trainingServerAvailable && !simulationMode)}
              sx={{
                backgroundColor: simulationMode ? '#9c27b0' : '#2196f3',
                '&:hover': {
                  backgroundColor: simulationMode ? '#7b1fa2' : '#1976d2'
                },
                '&.Mui-disabled': {
                  backgroundColor: simulationMode ? 'rgba(156, 39, 176, 0.3)' : 'rgba(33, 150, 243, 0.3)',
                  color: 'rgba(255, 255, 255, 0.5)'
                },
                fontWeight: 'bold',
                borderRadius: '6px',
                padding: '8px 16px'
              }}
            >
              {training ? 'Training...' : simulationMode ? 'Simulate Training' : 'Train Model'}
            </Button>

            {simulationMode && (
              <Chip
                label="Simulation Mode Active"
                color="secondary"
                size="small"
                sx={{ fontWeight: 'bold' }}
              />
            )}
          </Box>
        </Box>

        {/* Server Configuration */}
        <Box sx={{
          p: 3,
          backgroundColor: '#1e2a3a',
          borderRadius: '8px',
          border: '2px solid #2d4055',
          mb: 3
        }}>
          <Typography variant="h6" sx={{ color: '#fff', mb: 2, fontWeight: 'bold' }}>
            Training Server Configuration
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#90caf9', mr: 2, minWidth: '120px' }}>
              Server Status:
            </Typography>
            <Chip
              label={trainingServerAvailable ? 'Connected' : 'Disconnected'}
              size="small"
              color={trainingServerAvailable ? 'success' : 'error'}
              sx={{ fontWeight: 'bold' }}
            />
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#90caf9', mr: 2, minWidth: '120px' }}>
              Server URL:
            </Typography>
            <TextField
              value={trainingServerUrl}
              onChange={(e) => setTrainingServerUrl(e.target.value)}
              size="small"
              variant="outlined"
              sx={{
                flex: 1,
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(0, 0, 0, 0.2)',
                  color: '#fff',
                  '& fieldset': {
                    borderColor: 'rgba(144, 202, 249, 0.3)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(144, 202, 249, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2196f3',
                  },
                },
              }}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              size="small"
              onClick={handleAutoDetectServer}
              startIcon={<SearchIcon />}
              sx={{
                borderColor: 'rgba(33, 150, 243, 0.5)',
                color: '#2196f3',
                '&:hover': {
                  borderColor: '#2196f3',
                  backgroundColor: 'rgba(33, 150, 243, 0.1)',
                },
              }}
            >
              Auto-Detect
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              size="small"
              onClick={handleTestCommonUrls}
              startIcon={<RefreshIcon />}
              sx={{
                borderColor: 'rgba(156, 39, 176, 0.5)',
                color: '#9c27b0',
                '&:hover': {
                  borderColor: '#9c27b0',
                  backgroundColor: 'rgba(156, 39, 176, 0.1)',
                },
              }}
            >
              Test Common URLs
            </Button>

            <Button
              variant="contained"
              color="primary"
              size="small"
              onClick={() => handleUpdateServerUrl(trainingServerUrl)}
              startIcon={<CheckIcon />}
              sx={{
                backgroundColor: '#2196f3',
                '&:hover': {
                  backgroundColor: '#1976d2',
                },
              }}
            >
              Connect
            </Button>
          </Box>
        </Box>

        {modelStatus ? (
          modelStatus.server_unavailable ? (
            <Box sx={{
              p: 4,
              textAlign: 'center',
              backgroundColor: 'rgba(255, 152, 0, 0.1)',
              borderRadius: '8px',
              border: '2px solid rgba(255, 152, 0, 0.3)',
              mb: 3
            }}>
              <Typography variant="h6" sx={{ color: '#ff9800', mb: 2, fontWeight: 'bold' }}>
                Training Server Unavailable
              </Typography>
              <Typography variant="body1" sx={{ color: '#fff', mb: 3 }}>
                The training server is currently not available. You can still upload and label scorecards, but model training and persistence will not be available.
              </Typography>
              <Typography variant="body2" sx={{ color: '#90caf9' }}>
                All labeled data will be stored locally in your browser session. To enable full functionality, please start the training server.
              </Typography>
            </Box>
          ) : (
          <Grid container spacing={4} sx={{ mt: 1 }}>
            <Grid item xs={12} md={4}>
              <Card sx={{
                backgroundColor: '#263445',
                border: '2px solid #2d4055',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)'
                }
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%'
                  }}>
                    <Box sx={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(33, 150, 243, 0.1)',
                      border: '2px solid rgba(33, 150, 243, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2
                    }}>
                      <Typography variant="h3" sx={{
                        color: '#2196f3',
                        fontWeight: 'bold'
                      }}>
                        {modelStatus.labeled_images}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ color: '#fff', mb: 1, fontWeight: 'bold' }}>
                      Labeled Images
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#90caf9', textAlign: 'center' }}>
                      Total number of images with labeled elements
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{
                backgroundColor: '#263445',
                border: '2px solid #2d4055',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)'
                }
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%'
                  }}>
                    <Box sx={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      backgroundColor: modelStatus.trained ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                      border: `2px solid ${modelStatus.trained ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)'}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2
                    }}>
                      {modelStatus.trained ? (
                        <CheckIcon sx={{ color: '#4caf50', fontSize: '40px' }} />
                      ) : (
                        <CloseIcon sx={{ color: '#f44336', fontSize: '40px' }} />
                      )}
                    </Box>
                    <Typography variant="h6" sx={{ color: '#fff', mb: 1, fontWeight: 'bold' }}>
                      {modelStatus.trained ? 'Model Trained' : 'Not Trained'}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#90caf9', textAlign: 'center' }}>
                      {modelStatus.trained ?
                        'The model has been trained and is ready to use' :
                        'Train the model to enable automatic extraction'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{
                backgroundColor: '#263445',
                border: '2px solid #2d4055',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 16px rgba(0, 0, 0, 0.3)'
                }
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%'
                  }}>
                    <Box sx={{
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(156, 39, 176, 0.1)',
                      border: '2px solid rgba(156, 39, 176, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 2
                    }}>
                      <Typography variant="body1" sx={{
                        color: '#9c27b0',
                        fontWeight: 'bold',
                        fontSize: '12px',
                        textAlign: 'center',
                        lineHeight: 1.2
                      }}>
                        {modelStatus.trained ? (
                          new Date(modelStatus.last_trained * 1000).toLocaleDateString()
                        ) : (
                          'Never'
                        )}
                      </Typography>
                    </Box>
                    <Typography variant="h6" sx={{ color: '#fff', mb: 1, fontWeight: 'bold' }}>
                      Last Trained
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#90caf9', textAlign: 'center' }}>
                      {modelStatus.trained ?
                        `Last trained at ${new Date(modelStatus.last_trained * 1000).toLocaleTimeString()}` :
                        'Model has not been trained yet'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          )
        ) : (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: 6
          }}>
            <CircularProgress sx={{ color: '#2196f3', mb: 3 }} />
            <Typography variant="body1" sx={{ color: '#90caf9' }}>
              Loading model status...
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Notification snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        sx={{
          '& .MuiSnackbarContent-root': {
            backgroundColor: '#1e2a3a',
            color: '#fff'
          }
        }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{
            width: '100%',
            backgroundColor: notification.severity === 'success' ? 'rgba(76, 175, 80, 0.1)' :
                             notification.severity === 'error' ? 'rgba(244, 67, 54, 0.1)' :
                             notification.severity === 'warning' ? 'rgba(255, 152, 0, 0.1)' : 'rgba(33, 150, 243, 0.1)',
            color: notification.severity === 'success' ? '#4caf50' :
                   notification.severity === 'error' ? '#f44336' :
                   notification.severity === 'warning' ? '#ff9800' : '#2196f3',
            border: `1px solid ${notification.severity === 'success' ? 'rgba(76, 175, 80, 0.3)' :
                                notification.severity === 'error' ? 'rgba(244, 67, 54, 0.3)' :
                                notification.severity === 'warning' ? 'rgba(255, 152, 0, 0.3)' : 'rgba(33, 150, 243, 0.3)'}`,
            '& .MuiAlert-icon': {
              color: notification.severity === 'success' ? '#4caf50' :
                     notification.severity === 'error' ? '#f44336' :
                     notification.severity === 'warning' ? '#ff9800' : '#2196f3',
            }
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Training Stats Modal */}
      <Modal
        open={statsModalOpen}
        onClose={handleCloseStatsModal}
        aria-labelledby="training-stats-modal"
        aria-describedby="detailed-training-statistics"
      >
        <Box sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: { xs: '90%', sm: '80%', md: '70%', lg: '60%' },
          maxWidth: 800,
          maxHeight: '90vh',
          overflow: 'auto',
          bgcolor: '#1e2a3a',
          border: '2px solid #2d4055',
          borderRadius: '8px',
          boxShadow: 24,
          p: 4,
          color: '#fff'
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
              <ChartIcon sx={{ mr: 1.5, color: '#90caf9', fontSize: '28px' }} />
              Training Statistics
            </Typography>
            <IconButton onClick={handleCloseStatsModal} sx={{ color: '#90caf9' }}>
              <CloseIcon />
            </IconButton>
          </Box>

          {loadingStats ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <CircularProgress sx={{ color: '#2196f3' }} />
            </Box>
          ) : trainingStats ? (
            <>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={4}>
                  <Card sx={{
                    backgroundColor: '#263445',
                    border: '2px solid #2d4055',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                    height: '100%'
                  }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ color: '#90caf9', mb: 2, fontWeight: 'bold' }}>
                        Model Status
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Box sx={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          backgroundColor: trainingStats.trained ? 'rgba(76, 175, 80, 0.1)' : 'rgba(244, 67, 54, 0.1)',
                          border: `2px solid ${trainingStats.trained ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)'}`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 2
                        }}>
                          {trainingStats.trained ? (
                            <CheckIcon sx={{ color: '#4caf50', fontSize: '24px' }} />
                          ) : (
                            <CloseIcon sx={{ color: '#f44336', fontSize: '24px' }} />
                          )}
                        </Box>
                        <Typography variant="body1" sx={{ color: '#fff' }}>
                          {trainingStats.trained ? 'Trained' : 'Not Trained'}
                        </Typography>
                      </Box>
                      {trainingStats.lastTrainedDate && (
                        <Typography variant="body2" sx={{ color: '#90caf9', mt: 2 }}>
                          Last Trained: {trainingStats.lastTrainedDate.toLocaleString()}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card sx={{
                    backgroundColor: '#263445',
                    border: '2px solid #2d4055',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                    height: '100%'
                  }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ color: '#90caf9', mb: 2, fontWeight: 'bold' }}>
                        Images
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Total Images:</Typography>
                        <Typography variant="body1" sx={{ color: '#2196f3', fontWeight: 'bold' }}>
                          {trainingStats.totalImages}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Labeled Images:</Typography>
                        <Typography variant="body1" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                          {trainingStats.labeledImages}
                        </Typography>
                      </Box>
                      {trainingStats.totalImages > 0 && (
                        <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #2d4055' }}>
                          <Typography variant="body2" sx={{ color: '#fff', mb: 1 }}>Labeling Progress:</Typography>
                          <Box sx={{ width: '100%', bgcolor: 'rgba(33, 150, 243, 0.1)', borderRadius: 1, height: 10, overflow: 'hidden' }}>
                            <Box
                              sx={{
                                width: `${(trainingStats.labeledImages / trainingStats.totalImages) * 100}%`,
                                bgcolor: '#2196f3',
                                height: '100%'
                              }}
                            />
                          </Box>
                          <Typography variant="body2" sx={{ color: '#90caf9', mt: 0.5, textAlign: 'right' }}>
                            {Math.round((trainingStats.labeledImages / trainingStats.totalImages) * 100)}%
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Card sx={{
                    backgroundColor: '#263445',
                    border: '2px solid #2d4055',
                    borderRadius: '8px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                    height: '100%'
                  }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ color: '#90caf9', mb: 2, fontWeight: 'bold' }}>
                        Model Performance
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Accuracy:</Typography>
                        <Typography variant="body1" sx={{ color: '#ff9800', fontWeight: 'bold' }}>
                          {Math.round(trainingStats.accuracy * 100)}%
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Categories:</Typography>
                        <Typography variant="body1" sx={{ color: '#9c27b0', fontWeight: 'bold' }}>
                          {trainingStats.categories}
                        </Typography>
                      </Box>
                      {trainingStats.accuracy && (
                        <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #2d4055' }}>
                          <Typography variant="body2" sx={{ color: '#fff', mb: 1 }}>Accuracy Rating:</Typography>
                          <Box sx={{ width: '100%', bgcolor: 'rgba(255, 152, 0, 0.1)', borderRadius: 1, height: 10, overflow: 'hidden' }}>
                            <Box
                              sx={{
                                width: `${trainingStats.accuracy * 100}%`,
                                bgcolor: '#ff9800',
                                height: '100%'
                              }}
                            />
                          </Box>
                          <Typography variant="body2" sx={{ color: '#ff9800', mt: 0.5, textAlign: 'right' }}>
                            {Math.round(trainingStats.accuracy * 100)}%
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ color: '#90caf9', mb: 2, fontWeight: 'bold' }}>
                  Server Information
                </Typography>
                <Paper sx={{ p: 2, backgroundColor: '#263445', border: '1px solid #2d4055', borderRadius: '4px' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Server Status:</Typography>
                        <Chip
                          label={trainingServerAvailable ? 'Connected' : 'Disconnected'}
                          size="small"
                          color={trainingServerAvailable ? 'success' : 'error'}
                          sx={{ fontWeight: 'bold' }}
                        />
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" sx={{ color: '#fff' }}>Server URL:</Typography>
                        <Typography variant="body2" sx={{ color: '#2196f3' }}>
                          {trainingStats.serverUrl}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Admin Actions */}
                <Box sx={{
                  p: 2,
                  backgroundColor: 'rgba(244, 67, 54, 0.1)',
                  border: '1px solid rgba(244, 67, 54, 0.3)',
                  borderRadius: '4px',
                  mb: 2
                }}>
                  <Typography variant="h6" sx={{ color: '#f44336', mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                    <WarningIcon sx={{ mr: 1 }} />
                    Admin Actions
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#fff', mb: 2 }}>
                    These actions will permanently affect the training data. Use with caution.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={handleOpenImageSelectionModal}
                      startIcon={<DeleteIcon />}
                      disabled={!trainingStats.labeledImages || trainingStats.labeledImages === 0}
                      sx={{
                        borderColor: 'rgba(33, 150, 243, 0.5)',
                        color: '#2196f3',
                        '&:hover': {
                          borderColor: '#2196f3',
                          backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        },
                      }}
                    >
                      Clear Selected Images
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={handleOpenConfirmDialog}
                      startIcon={<DeleteIcon />}
                      disabled={!trainingStats.labeledImages || trainingStats.labeledImages === 0}
                      sx={{
                        borderColor: 'rgba(244, 67, 54, 0.5)',
                        color: '#f44336',
                        '&:hover': {
                          borderColor: '#f44336',
                          backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        },
                      }}
                    >
                      Clear All Labeled Images
                    </Button>
                  </Box>
                </Box>

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={fetchModelStatus}
                    startIcon={<RefreshIcon />}
                    sx={{
                      borderColor: 'rgba(33, 150, 243, 0.5)',
                      color: '#2196f3',
                      '&:hover': {
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                      },
                    }}
                  >
                    Refresh Stats
                  </Button>

                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleCloseStatsModal}
                    sx={{
                      backgroundColor: '#2196f3',
                      '&:hover': {
                        backgroundColor: '#1976d2',
                      },
                    }}
                  >
                    Close
                  </Button>
                </Box>
              </Box>
            </>
          ) : (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" color="error" gutterBottom>
                Error Loading Statistics
              </Typography>
              <Typography variant="body1" paragraph>
                Could not load training statistics. Please check if the training server is running.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                onClick={fetchModelStatus}
                startIcon={<RefreshIcon />}
              >
                Retry
              </Button>
            </Box>
          )}
        </Box>
      </Modal>

      {/* Confirmation Dialog for Clearing Annotations */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCloseConfirmDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            backgroundColor: '#1e2a3a',
            color: '#fff',
            border: '1px solid #2d4055',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'
          }
        }}
      >
        <DialogTitle id="alert-dialog-title" sx={{
          display: 'flex',
          alignItems: 'center',
          color: '#f44336',
          borderBottom: '1px solid rgba(244, 67, 54, 0.3)'
        }}>
          <WarningIcon sx={{ mr: 1 }} />
          {"Clear All Labeled Images?"}
        </DialogTitle>
        <DialogContent sx={{ mt: 2 }}>
          <DialogContentText id="alert-dialog-description" sx={{ color: '#fff' }}>
            This action will permanently delete all labeled image annotations. This cannot be undone.
            <Box sx={{ mt: 2, p: 2, backgroundColor: 'rgba(244, 67, 54, 0.1)', borderRadius: '4px' }}>
              <Typography variant="body2" sx={{ color: '#f44336', fontWeight: 'bold' }}>
                Warning: If you clear the annotations, you will need to label all images again before training the model.
              </Typography>
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(45, 64, 85, 0.5)' }}>
          <Button
            onClick={handleCloseConfirmDialog}
            sx={{ color: '#90caf9' }}
            disabled={clearingAnnotations}
          >
            Cancel
          </Button>
          <Button
            onClick={handleClearAllAnnotations}
            color="error"
            variant="contained"
            disabled={clearingAnnotations}
            startIcon={clearingAnnotations ? <CircularProgress size={16} sx={{ color: '#fff' }} /> : <DeleteIcon />}
            sx={{
              backgroundColor: '#f44336',
              '&:hover': {
                backgroundColor: '#d32f2f'
              }
            }}
            autoFocus
          >
            {clearingAnnotations ? 'Clearing...' : 'Clear All Annotations'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Selection Modal */}
      <ImageSelectionModal
        open={imageSelectionModalOpen}
        onClose={handleCloseImageSelectionModal}
        onClearSelected={handleClearSelectedAnnotations}
      />
    </Container>
  );
};

export default ScorecardLabeler;
