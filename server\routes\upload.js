const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { authenticateToken } = require('../middlewares/auth');
const roleAuth = require('../middlewares/roleAuth');
const { upload } = require('../utils/fileUpload');

// @route   POST api/upload/profile
// @desc    Upload profile image
// @access  Private
router.post(
  '/profile',
  authenticateToken, 
  upload.single('profileImage'),
  uploadController.uploadProfileImage
);

// @route   POST api/upload/player
// @desc    Upload single player image
// @access  Private (Admin only)
router.post(
  '/player',
  [authenticateToken, roleAuth(['admin'])],
  upload.single('playerImage'),
  uploadController.uploadPlayerImage
);

// @route   POST api/upload/players-bulk
// @desc    Upload multiple player images
// @access  Private (Admin only)
router.post(
  '/players-bulk',
  [authenticateToken, roleAuth(['admin'])],
  upload.array('playerImages', 50), // Allow up to 50 images at once
  uploadController.uploadBulkPlayerImages
);

// @route   POST api/upload/scorecard
// @desc    Upload scorecard image
// @access  Private
router.post(
  '/scorecard',
  authenticateToken,
  upload.single('scorecard'),
  uploadController.uploadScorecardImage
);

module.exports = router;
