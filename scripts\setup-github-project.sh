#!/bin/bash

# GitHub Project Board Setup Script
# 
# This script uses GitHub CLI to set up the project board with all tasks,
# labels, and automation for the RPL Cricket Application.
#
# Prerequisites:
# 1. Install GitHub CLI: https://cli.github.com/
# 2. Authenticate: gh auth login
#
# Usage:
# chmod +x scripts/setup-github-project.sh
# ./scripts/setup-github-project.sh

set -e

echo "🚀 Setting up GitHub Project Board for RPL Cricket Application"
echo "=============================================================="

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI is not installed. Please install it first:"
    echo "   https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    echo "❌ Not authenticated with GitHub. Please run:"
    echo "   gh auth login"
    exit 1
fi

# Get repository information
REPO_INFO=$(gh repo view --json owner,name)
OWNER=$(echo $REPO_INFO | jq -r '.owner.login')
REPO_NAME=$(echo $REPO_INFO | jq -r '.name')

echo "📍 Repository: $OWNER/$REPO_NAME"
echo ""

# Function to create labels
create_labels() {
    echo "📋 Creating repository labels..."
    
    # Priority Labels
    gh label create "🔴 Critical" --color "B60205" --description "Must be completed immediately" --force
    gh label create "🟠 High" --color "D93F0B" --description "Important for current milestone" --force
    gh label create "🟡 Medium" --color "FBCA04" --description "Should be completed soon" --force
    gh label create "🟢 Low" --color "0E8A16" --description "Nice to have, can be deferred" --force
    
    # Type Labels
    gh label create "🐛 Bug" --color "D73A4A" --description "Issues and fixes" --force
    gh label create "✨ Feature" --color "A2EEEF" --description "New functionality" --force
    gh label create "🔧 Enhancement" --color "7057FF" --description "Improvements to existing features" --force
    gh label create "📚 Documentation" --color "0075CA" --description "Documentation updates" --force
    gh label create "🧪 Testing" --color "F9D0C4" --description "Testing-related tasks" --force
    gh label create "🚀 Deployment" --color "C5DEF5" --description "Deployment and infrastructure" --force
    
    # Component Labels
    gh label create "🎨 Frontend" --color "E99695" --description "React/UI related" --force
    gh label create "⚙️ Backend" --color "F7E7CE" --description "Node.js/Express/API related" --force
    gh label create "🗄️ Database" --color "FEF2C0" --description "MongoDB/Data related" --force
    gh label create "🔍 OCR" --color "C2E0C6" --description "OCR processing related" --force
    gh label create "🏆 Auction" --color "BFDADC" --description "Auction system related" --force
    gh label create "👥 Team" --color "C5DEF5" --description "Team management related" --force
    gh label create "🏟️ Tournament" --color "BFD4F2" --description "Tournament related" --force
    gh label create "📊 Analytics" --color "D4C5F9" --description "Analytics and reporting" --force
    gh label create "🎮 Big Ant Cricket 24" --color "FFD700" --description "Big Ant Cricket 24 specific features" --force
    
    # Status Labels
    gh label create "🆕 New" --color "EDEDED" --description "Newly created task" --force
    gh label create "🔄 In Development" --color "FBCA04" --description "Currently being developed" --force
    gh label create "⏳ Waiting" --color "D4C5F9" --description "Waiting for external input" --force
    gh label create "🧪 Testing" --color "F9D0C4" --description "In testing phase" --force
    gh label create "📝 Needs Review" --color "C2E0C6" --description "Awaiting code review" --force
    gh label create "✅ Done" --color "0E8A16" --description "Completed and verified" --force
    
    echo "✅ Labels created successfully"
}

# Function to create milestones
create_milestones() {
    echo "🎯 Creating project milestones..."
    
    # Calculate due dates (2 weeks, 5 weeks, 7 weeks, 8 weeks, 10 weeks from now)
    DUE_2W=$(date -d "+14 days" +%Y-%m-%d)
    DUE_5W=$(date -d "+35 days" +%Y-%m-%d)
    DUE_7W=$(date -d "+49 days" +%Y-%m-%d)
    DUE_8W=$(date -d "+56 days" +%Y-%m-%d)
    DUE_10W=$(date -d "+70 days" +%Y-%m-%d)
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Core System Completion" -f description="Complete Transfer Market System, Match Result Processing, and Post-Auction Processing" -f due_on="${DUE_2W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Big Ant Cricket 24 Integration" -f description="Implement Skill Points & Rating System, Performance Milestone Bonuses, and Comprehensive Leaderboards" -f due_on="${DUE_5W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Advanced Analytics" -f description="Strike Rate & Economy Calculations, Fastest Milestones Tracking, and Venue-based Performance Analytics" -f due_on="${DUE_7W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Production Optimization" -f description="Complete Testing & Quality Assurance, Database Optimization, and Performance Improvements" -f due_on="${DUE_8W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    gh api repos/$OWNER/$REPO_NAME/milestones -X POST -f title="Enhanced Features" -f description="Test Match Support, Enhanced Match Validation, and Advanced Search & Filtering" -f due_on="${DUE_10W}T23:59:59Z" || echo "⚠️  Milestone may already exist"
    
    echo "✅ Milestones created successfully"
}

# Function to create high-priority issues
create_priority_issues() {
    echo "📝 Creating high-priority GitHub issues..."
    
    # Issue 1: Transfer Market System
    gh issue create \
        --title "🔄 Complete Transfer Market System" \
        --body "## Description
Complete the player trading system between teams with market value calculations and transfer history.

## Acceptance Criteria
- [ ] Player trading between teams
- [ ] Market value calculations  
- [ ] Transfer history tracking
- [ ] Transaction validation

## Files
- \`client/src/pages/TransferMarket/\`
- \`server/controllers/\` (needs completion)

## Phase
2.6: Player & Team Management

## Priority
🟠 High" \
        --label "🟠 High,✨ Feature,👥 Team,🎨 Frontend" \
        --milestone "Core System Completion" || echo "⚠️  Issue may already exist"

    # Issue 2: Skill Points & Rating System
    gh issue create \
        --title "🎮 Implement Advanced Skill Points & Rating System" \
        --body "## Description
Implement automatic rating increases based on skill points (5000 points = +1 rating), configurable thresholds.

## Acceptance Criteria
- [ ] 1 run = 1 skill point
- [ ] 1 wicket = 10 skill points  
- [ ] 5000 skill points = +1 rating increase
- [ ] Admin configurable thresholds
- [ ] Automatic rating updates after each match

## Big Ant Cricket 24 Alignment
This is a core feature from the original vision where player ratings increase based on performance.

## Phase
7.1: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical" \
        --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,⚙️ Backend" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    # Issue 3: Performance Milestone Bonuses
    gh issue create \
        --title "🎮 Add Performance Milestone Bonuses" \
        --body "## Description
Implement milestone bonus system: 30's (+60 points), 50's (+90 points), 100's (+150 points), 3W hauls (+60 points), 5W hauls (+90 points).

## Acceptance Criteria
- [ ] Batting milestones: 30 (+60), 50 (+90), 100 (+150) bonus points
- [ ] Bowling milestones: 3W (+60), 5W (+90) bonus points
- [ ] Automatic detection from scorecard OCR
- [ ] Historical milestone tracking

## Big Ant Cricket 24 Alignment
Essential for the original vision where milestones provide bonus skill points.

## Phase
7.2: Big Ant Cricket 24 Integration Features

## Priority
🔴 Critical" \
        --label "🔴 Critical,✨ Feature,🎮 Big Ant Cricket 24,🔍 OCR" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    # Issue 4: Comprehensive Leaderboards
    gh issue create \
        --title "🎮 Build Comprehensive Leaderboards" \
        --body "## Description
Create comprehensive leaderboards for Most Runs, 30's, 50's, 100's, Wickets, 3W/5W Hauls, MOM - format and tournament wise.

## Acceptance Criteria
- [ ] Multiple leaderboard categories
- [ ] Format-wise filtering (T10, T20, ODI, Test)
- [ ] Tournament-wise and overall statistics
- [ ] Real-time updates after each match

## Big Ant Cricket 24 Alignment
Leaderboards are essential for competitive gaming experience.

## Phase
7.3: Big Ant Cricket 24 Integration Features

## Priority
🟠 High" \
        --label "🟠 High,✨ Feature,🎮 Big Ant Cricket 24,📊 Analytics" \
        --milestone "Big Ant Cricket 24 Integration" || echo "⚠️  Issue may already exist"

    echo "✅ Priority issues created successfully"
}

# Function to create GitHub Actions workflow
create_automation() {
    echo "⚙️ Setting up GitHub Actions automation..."
    
    # Create .github/workflows directory
    mkdir -p .github/workflows
    
    # Create workflow file
    cat > .github/workflows/project-board-automation.yml << 'EOF'
name: Project Board Automation

on:
  issues:
    types: [opened, closed, reopened, labeled]
  pull_request:
    types: [opened, closed, merged, ready_for_review]
  push:
    branches: [main, master]

jobs:
  update-project-board:
    runs-on: ubuntu-latest
    steps:
      - name: Move new issues to Backlog
        if: github.event.action == 'opened' && github.event.issue
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: RPL Cricket Project Board
          column: Backlog
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Move assigned issues to Ready
        if: github.event.action == 'labeled' && contains(github.event.label.name, 'assigned')
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: RPL Cricket Project Board
          column: Ready
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Move in-progress issues
        if: github.event.action == 'labeled' && contains(github.event.label.name, 'In Development')
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: RPL Cricket Project Board
          column: In Progress
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Move closed issues to Done
        if: github.event.action == 'closed' && github.event.issue
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: RPL Cricket Project Board
          column: Done
          repo-token: ${{ secrets.GITHUB_TOKEN }}
EOF

    echo "✅ GitHub Actions workflow created"
}

# Function to create project board
create_project_board() {
    echo "📋 Creating GitHub Project Board..."
    
    # Note: GitHub CLI doesn't have direct project board creation yet
    # This will create a classic project board via API
    gh api repos/$OWNER/$REPO_NAME/projects -X POST \
        -f name="RPL Cricket - Big Ant Cricket 24 Tournament System" \
        -f body="Complete project management for the RPL Cricket Application with systematic tracking of all features and Big Ant Cricket 24 integration." || echo "⚠️  Project board may already exist"
    
    echo "✅ Project board creation initiated"
    echo "📝 Please manually create columns: Backlog, Ready, In Progress, Review, Done, Blocked"
}

# Main execution
main() {
    echo "Starting setup process..."
    echo ""
    
    create_labels
    echo ""
    
    create_milestones
    echo ""
    
    create_priority_issues
    echo ""
    
    create_automation
    echo ""
    
    create_project_board
    echo ""
    
    echo "🎉 GitHub Project Board setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Go to your GitHub repository: https://github.com/$OWNER/$REPO_NAME"
    echo "2. Click on 'Projects' tab"
    echo "3. Open the created project board"
    echo "4. Create columns: Backlog, Ready, In Progress, Review, Done, Blocked"
    echo "5. Add the created issues to appropriate columns"
    echo "6. Configure additional automation rules as needed"
    echo ""
    echo "🔄 The GitHub Actions workflow will automatically:"
    echo "   • Move new issues to Backlog"
    echo "   • Move assigned issues to Ready"
    echo "   • Move in-progress issues to In Progress"
    echo "   • Move closed issues to Done"
}

# Run main function
main
