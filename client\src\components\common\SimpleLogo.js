import React from 'react';
import { Box, Typography } from '@mui/material';
import SportsCricketIcon from '@mui/icons-material/SportsCricket';

/**
 * Simple logo component that generates a default team logo
 * @param {Object} props - Component props
 * @param {string} props.teamName - Team name to display in the logo
 * @param {string} props.primaryColor - Primary color for the logo
 * @param {string} props.secondaryColor - Secondary color for the logo
 * @param {number} props.size - Size of the logo in pixels
 */
const SimpleLogo = ({ teamName = 'Team', primaryColor = '#1e88e5', secondaryColor = '#bbdefb', size = 200 }) => {
  // Get the first letter of each word in the team name
  const initials = teamName
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);

  return (
    <Box
      sx={{
        width: size,
        height: size,
        borderRadius: '50%',
        bgcolor: secondaryColor,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        border: `4px solid ${primaryColor}`,
        boxShadow: 2,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '30%',
          bgcolor: primaryColor,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <SportsCricketIcon sx={{ color: 'white', fontSize: size / 5 }} />
      </Box>
      
      <Typography
        variant="h2"
        sx={{
          fontWeight: 'bold',
          color: primaryColor,
          fontSize: size / 3,
          mt: size / 10
        }}
      >
        {initials}
      </Typography>
      
      <Typography
        variant="caption"
        sx={{
          color: 'text.secondary',
          fontSize: size / 15,
          mt: 1
        }}
      >
        {teamName}
      </Typography>
    </Box>
  );
};

export default SimpleLogo;
