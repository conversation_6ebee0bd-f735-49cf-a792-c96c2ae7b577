import React, { useState, useRef, useEffect } from 'react';
import './TemplateBuilder.css';

const TemplateBuilder = () => {
  const canvasRef = useRef(null);
  const imageRef = useRef(null);
  const [image, setImage] = useState(null);
  const [regions, setRegions] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentFieldType, setCurrentFieldType] = useState('');
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [currentRegion, setCurrentRegion] = useState(null);
  const [mappingMode, setMappingMode] = useState('draw'); // 'draw' or 'click'
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [zoomLevel, setZoomLevel] = useState(1);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [savedTemplates, setSavedTemplates] = useState([]);
  const [showTemplateList, setShowTemplateList] = useState(false);
  const [originalImageData, setOriginalImageData] = useState(null);
  const [originalImageName, setOriginalImageName] = useState(null);

  const fieldTypes = [
    // Match Information
    { value: 'match_venue', label: 'Match Venue', color: '#FF6B6B' },
    { value: 'match_time', label: 'Match Time', color: '#4ECDC4' },
    { value: 'player_of_match', label: 'Player Of The Match', color: '#FFD93D' },

    // Team Names
    { value: 'team1_name', label: 'Team 1 Name', color: '#45B7D1' },
    { value: 'team2_name', label: 'Team 2 Name', color: '#96CEB4' },

    // Team Scores
    { value: 'team1_runs_scored', label: 'Team 1 Runs Scored', color: '#FFEAA7' },
    { value: 'team2_runs_scored', label: 'Team 2 Runs Scored', color: '#DDA0DD' },

    // Team Overs
    { value: 'team1_overs_played', label: 'Team 1 Overs Played', color: '#98D8C8' },
    { value: 'team2_overs_played', label: 'Team 2 Overs Played', color: '#F7DC6F' },

    // Team Wickets
    { value: 'team1_wickets_lost', label: 'Team 1 Wickets Lost', color: '#BB8FCE' },
    { value: 'team2_wickets_lost', label: 'Team 2 Wickets Lost', color: '#85C1E9' },

    // Team 1 Batsmen
    { value: 'team1_batsman_name', label: 'Team 1 Batsman Name', color: '#F8C471' },
    { value: 'team1_batsman_runs_scored', label: 'Team 1 Batsman Runs Scored', color: '#82E0AA' },
    { value: 'team1_batsman_balls_faced', label: 'Team 1 Batsman Balls Faced', color: '#F9E79F' },

    // Team 1 Bowlers
    { value: 'team1_bowler_name', label: 'Team 1 Bowler Name', color: '#D7BDE2' },
    { value: 'team1_bowler_figure', label: 'Team 1 Bowler Figure (e.g., 2-49)', color: '#FF6B6B' },
    { value: 'team1_bowler_wickets_taken', label: 'Team 1 Bowler Wickets (Individual)', color: '#A9DFBF' },
    { value: 'team1_bowler_runs_conceded', label: 'Team 1 Bowler Runs (Individual)', color: '#FFCCCB' },

    // Team 2 Batsmen
    { value: 'team2_batsman_name', label: 'Team 2 Batsman Name', color: '#FFB6C1' },
    { value: 'team2_batsman_runs_scored', label: 'Team 2 Batsman Runs Scored', color: '#98FB98' },
    { value: 'team2_batsman_balls_faced', label: 'Team 2 Batsman Balls Faced', color: '#F0E68C' },

    // Team 2 Bowlers
    { value: 'team2_bowler_name', label: 'Team 2 Bowler Name', color: '#DDA0DD' },
    { value: 'team2_bowler_figure', label: 'Team 2 Bowler Figure (e.g., 4-67)', color: '#45B7D1' },
    { value: 'team2_bowler_wickets_taken', label: 'Team 2 Bowler Wickets (Individual)', color: '#87CEEB' },
    { value: 'team2_bowler_runs_conceded', label: 'Team 2 Bowler Runs (Individual)', color: '#F5DEB3' }
  ];

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setImage(img);
          setupCanvas(img);
          // Store original image data for template saving
          setOriginalImageData(e.target.result);
          setOriginalImageName(file.name);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const setupCanvas = (img) => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Set canvas size to match image
    const maxWidth = 1200;
    const maxHeight = 800;
    let { width, height } = img;

    if (width > maxWidth) {
      height = (height * maxWidth) / width;
      width = maxWidth;
    }
    if (height > maxHeight) {
      width = (width * maxHeight) / height;
      height = maxHeight;
    }

    canvas.width = width;
    canvas.height = height;

    // Draw image
    ctx.drawImage(img, 0, 0, width, height);

    // Store scale factors for coordinate conversion
    canvas.scaleX = img.width / width;
    canvas.scaleY = img.height / height;
  };

  const getMousePos = (e) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  const handleMouseDown = (e) => {
    const pos = getMousePos(e);

    // Check if clicking on existing region for dragging/resizing
    const clickedRegion = findRegionAtPosition(pos.x, pos.y);

    if (clickedRegion) {
      // Click on existing region to select/drag it
      setSelectedRegion(clickedRegion);
      // Always start dragging when clicking on a region (no need for Shift)
      setIsDragging(true);
      setDragOffset({
        x: pos.x - clickedRegion.x,
        y: pos.y - clickedRegion.y
      });
      return;
    }

    if (!currentFieldType) {
      alert('Please select a field type first!');
      return;
    }

    if (mappingMode === 'click') {
      // Click-to-place mode: create small region at click position
      const newRegion = {
        id: Date.now(),
        fieldType: currentFieldType,
        x: pos.x - 25, // Center the region on click
        y: pos.y - 15,
        width: 50,
        height: 30,
        originalX: Math.round((pos.x - 25) * canvasRef.current.scaleX),
        originalY: Math.round((pos.y - 15) * canvasRef.current.scaleY),
        originalWidth: Math.round(50 * canvasRef.current.scaleX),
        originalHeight: Math.round(30 * canvasRef.current.scaleY)
      };

      setRegions(prev => [...prev, newRegion]);
      setSelectedRegion(newRegion);
    } else {
      // Draw mode: start drawing region
      setIsDrawing(true);
      setStartPos(pos);
      setCurrentRegion({
        x: pos.x,
        y: pos.y,
        width: 0,
        height: 0,
        fieldType: currentFieldType
      });
    }
  };

  const handleMouseMove = (e) => {
    const pos = getMousePos(e);

    // Change cursor when hovering over regions
    const canvas = canvasRef.current;
    if (canvas) {
      const hoveredRegion = findRegionAtPosition(pos.x, pos.y);
      canvas.style.cursor = hoveredRegion ? 'move' : 'default';
    }

    if (isDragging && selectedRegion) {
      // Drag existing region with smooth movement
      const newX = pos.x - dragOffset.x;
      const newY = pos.y - dragOffset.y;

      // Constrain to canvas bounds
      const canvas = canvasRef.current;
      const constrainedX = Math.max(0, Math.min(newX, canvas.width - selectedRegion.width));
      const constrainedY = Math.max(0, Math.min(newY, canvas.height - selectedRegion.height));

      setRegions(prev => prev.map(region =>
        region.id === selectedRegion.id
          ? {
              ...region,
              x: constrainedX,
              y: constrainedY,
              originalX: Math.round(constrainedX * canvasRef.current.scaleX),
              originalY: Math.round(constrainedY * canvasRef.current.scaleY)
            }
          : region
      ));

      setSelectedRegion(prev => ({
        ...prev,
        x: constrainedX,
        y: constrainedY
      }));

      redrawCanvas();
      return;
    }

    if (!isDrawing) return;

    const newRegion = {
      ...currentRegion,
      width: pos.x - startPos.x,
      height: pos.y - startPos.y
    };
    setCurrentRegion(newRegion);

    // Redraw canvas
    redrawCanvas();
  };

  const handleMouseUp = (e) => {
    if (isDragging) {
      setIsDragging(false);
      // Keep region selected for further adjustments
      return;
    }

    if (!isDrawing) return;

    setIsDrawing(false);

    if (currentRegion && Math.abs(currentRegion.width) > 10 && Math.abs(currentRegion.height) > 10) {
      // Normalize region (handle negative width/height)
      const normalizedRegion = {
        id: Date.now(),
        fieldType: currentRegion.fieldType,
        x: currentRegion.width < 0 ? currentRegion.x + currentRegion.width : currentRegion.x,
        y: currentRegion.height < 0 ? currentRegion.y + currentRegion.height : currentRegion.y,
        width: Math.abs(currentRegion.width),
        height: Math.abs(currentRegion.height),
        // Convert to original image coordinates
        originalX: Math.round((currentRegion.width < 0 ? currentRegion.x + currentRegion.width : currentRegion.x) * canvasRef.current.scaleX),
        originalY: Math.round((currentRegion.height < 0 ? currentRegion.y + currentRegion.height : currentRegion.y) * canvasRef.current.scaleY),
        originalWidth: Math.round(Math.abs(currentRegion.width) * canvasRef.current.scaleX),
        originalHeight: Math.round(Math.abs(currentRegion.height) * canvasRef.current.scaleY)
      };

      setRegions(prev => [...prev, normalizedRegion]);
      // Auto-select the newly created region for immediate adjustment
      setSelectedRegion(normalizedRegion);
    }

    setCurrentRegion(null);
  };

  // Helper function to find region at position
  const findRegionAtPosition = (x, y) => {
    return regions.find(region =>
      x >= region.x && x <= region.x + region.width &&
      y >= region.y && y <= region.y + region.height
    );
  };

  const redrawCanvas = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Clear and redraw image
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    if (image) {
      ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
    }

    // Draw existing regions
    regions.forEach(region => {
      drawRegion(ctx, region);
    });

    // Draw current region being drawn
    if (currentRegion) {
      drawRegion(ctx, currentRegion, true);
    }
  };

  const drawRegion = (ctx, region, isTemporary = false) => {
    const fieldType = fieldTypes.find(ft => ft.value === region.fieldType);
    const color = fieldType ? fieldType.color : '#FF0000';
    const isSelected = selectedRegion && selectedRegion.id === region.id;

    ctx.strokeStyle = isSelected ? '#FF0000' : color;
    ctx.lineWidth = 1; // Consistent thin lines
    ctx.fillStyle = color + (isSelected ? '30' : '15'); // More transparent fill

    // Set line dash for selected regions for better precision
    if (isSelected) {
      ctx.setLineDash([3, 2]); // Dashed line for selected
    } else {
      ctx.setLineDash([]); // Solid line for unselected
    }

    ctx.fillRect(region.x, region.y, region.width, region.height);
    ctx.strokeRect(region.x, region.y, region.width, region.height);

    // Reset line dash
    ctx.setLineDash([]);

    // Only show labels for selected regions to reduce clutter
    if (isSelected) {
      ctx.font = 'bold 12px Arial';

      // Position label above region if there's space, otherwise inside
      const labelY = region.y > 20 ? region.y - 5 : region.y + 15;
      const labelText = fieldType ? fieldType.label : region.fieldType;

      // Add white background for label readability
      const textMetrics = ctx.measureText(labelText);
      ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
      ctx.fillRect(region.x, labelY - 12, textMetrics.width + 6, 14);

      // Add border around label background
      ctx.strokeStyle = '#333';
      ctx.lineWidth = 1;
      ctx.strokeRect(region.x, labelY - 12, textMetrics.width + 6, 14);

      // Use dark text for high contrast
      ctx.fillStyle = '#333333';
      ctx.fillText(labelText, region.x + 3, labelY);
    }

    // Draw resize handles for selected region
    if (isSelected) {
      const handleSize = 4; // Smaller handles
      ctx.fillStyle = '#FF0000';
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 1;

      // Corner handles with white border for better visibility
      const handles = [
        [region.x - handleSize/2, region.y - handleSize/2],
        [region.x + region.width - handleSize/2, region.y - handleSize/2],
        [region.x - handleSize/2, region.y + region.height - handleSize/2],
        [region.x + region.width - handleSize/2, region.y + region.height - handleSize/2]
      ];

      handles.forEach(([x, y]) => {
        ctx.fillRect(x, y, handleSize, handleSize);
        ctx.strokeRect(x, y, handleSize, handleSize);
      });
    }
  };

  const deleteRegion = (regionId) => {
    setRegions(prev => prev.filter(r => r.id !== regionId));
    redrawCanvas();
  };

  const saveTemplate = async () => {
    console.log('Save template called with:', { templateName, regionsCount: regions.length });

    if (!templateName.trim()) {
      alert('Please enter a template name');
      return;
    }

    if (regions.length === 0) {
      alert('Please add at least one region');
      return;
    }

    if (!originalImageData) {
      const saveWithoutImage = window.confirm(
        'No image uploaded. Do you want to save the template without the image?\n\n' +
        'Note: Templates without images cannot be fully restored when loaded.'
      );
      if (!saveWithoutImage) {
        return;
      }
    }

    const template = {
      name: templateName,
      description: templateDescription,
      regions: regions.map(region => ({
        fieldType: region.fieldType,
        x: region.originalX,
        y: region.originalY,
        width: region.originalWidth,
        height: region.originalHeight
      })),
      originalImageData: originalImageData,
      originalImageName: originalImageName,
      createdAt: new Date().toISOString()
    };

    try {
      const response = await fetch('/api/admin/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': localStorage.getItem('token')
        },
        body: JSON.stringify(template)
      });

      if (response.ok) {
        alert('Template saved successfully!');
        // Reset form
        setTemplateName('');
        setTemplateDescription('');
        setRegions([]);
        setImage(null);
        setOriginalImageData(null);
        setOriginalImageName(null);
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      } else {
        const errorData = await response.json();
        console.error('🚨 FRONTEND: Template save failed!');
        console.error('🚨 FRONTEND: Response status:', response.status);
        console.error('🚨 FRONTEND: Response statusText:', response.statusText);
        console.error('🚨 FRONTEND: Error data:', JSON.stringify(errorData, null, 2));

        // If error might be due to large payload, offer to save without image
        if (response.status === 413 || response.status === 500) {
          const retryWithoutImage = window.confirm(
            `Failed to save template (possibly due to large image size): ${errorData.message || 'Unknown error'}\n\n` +
            'Would you like to retry saving without the image data?'
          );

          if (retryWithoutImage) {
            // Retry without image data
            const templateWithoutImage = {
              ...template,
              originalImageData: null,
              originalImageName: null
            };

            try {
              const retryResponse = await fetch('/api/admin/templates', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'x-auth-token': localStorage.getItem('token')
                },
                body: JSON.stringify(templateWithoutImage)
              });

              if (retryResponse.ok) {
                alert('Template saved successfully (without image)!');
                // Reset form
                setTemplateName('');
                setTemplateDescription('');
                setRegions([]);
                setImage(null);
                setOriginalImageData(null);
                setOriginalImageName(null);
                const canvas = canvasRef.current;
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
              } else {
                const retryErrorData = await retryResponse.json();
                alert(`Failed to save template even without image: ${retryErrorData.message || 'Unknown error'}`);
              }
            } catch (retryError) {
              console.error('Retry save error:', retryError);
              alert('Error during retry save');
            }
          }
        } else {
          alert(`Failed to save template: ${errorData.message || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('🚨 FRONTEND: Exception during template save!');
      console.error('🚨 FRONTEND: Error name:', error.name);
      console.error('🚨 FRONTEND: Error message:', error.message);
      console.error('🚨 FRONTEND: Error stack:', error.stack);
      console.error('🚨 FRONTEND: Full error object:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
      alert(`Error saving template: ${error.message}\n\nCheck browser console for full details.`);
    }
  };

  // Fetch saved templates
  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/admin/templates', {
        headers: {
          'x-auth-token': localStorage.getItem('token')
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSavedTemplates(data.templates || []);
      } else {
        console.error('Failed to fetch templates');
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  // Delete a template
  const deleteTemplate = async (templateId) => {
    const deleteChoice = window.confirm(
      `⚠️ PERMANENT DELETION WARNING ⚠️\n\n` +
      `This will PERMANENTLY delete the template from the database.\n` +
      `You will be able to create a new template with the same name.\n\n` +
      `Click OK to permanently delete, or Cancel to keep the template.`
    );

    if (!deleteChoice) {
      return;
    }

    try {
      console.log('🚨 FRONTEND: Starting delete request for template ID:', templateId);
      console.log('🚨 FRONTEND: Auth token:', localStorage.getItem('token') ? 'Present' : 'Missing');

      const response = await fetch(`/api/admin/templates/${templateId}`, {
        method: 'DELETE',
        headers: {
          'x-auth-token': localStorage.getItem('token'),
          'Content-Type': 'application/json'
        }
      });

      console.log('🚨 FRONTEND: Response status:', response.status);
      console.log('🚨 FRONTEND: Response ok:', response.ok);

      if (response.ok) {
        const responseData = await response.json();
        console.log('🚨 FRONTEND: Success response:', responseData);
        alert('✅ Template permanently deleted successfully!\n\nYou can now create a new template with the same name if needed.');
        fetchTemplates(); // Refresh the list
      } else {
        console.log('🚨 FRONTEND: Delete failed with status:', response.status);
        const errorData = await response.json();
        console.log('🚨 FRONTEND: Error response:', errorData);
        alert(`❌ Failed to delete template: ${errorData.message || 'Unknown error'}\n\nStatus: ${response.status}`);
      }
    } catch (error) {
      console.error('🚨 FRONTEND: Exception during delete:', error);
      console.error('🚨 FRONTEND: Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      alert(`❌ Error deleting template: ${error.message}\n\nCheck browser console for details.`);
    }
  };

  // Test template with a scorecard image
  const testTemplate = async (templateId, templateName) => {
    // Create a file input for the user to select a test image
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const formData = new FormData();
        formData.append('scorecard', file);

        console.log(`🧪 Testing template "${templateName}" with image: ${file.name}`);

        // Show loading message
        alert(`🔄 Testing template "${templateName}" with your image...\n\nThis may take a few seconds. Click OK and wait for results.`);

        const response = await fetch(`/api/admin/templates/${templateId}/test`, {
          method: 'POST',
          headers: {
            'x-auth-token': localStorage.getItem('token')
          },
          body: formData
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Template test result:', result);

          // Handle the new response format
          if (result.extractionResult?.success) {
            const data = result.extractionResult.extractedData;

            // Create a proper dialog with clickable buttons
            const dialogContent = document.createElement('div');
            dialogContent.innerHTML = `
              <div style="font-family: Arial, sans-serif; max-width: 600px;">
                <h2>🎯 Template Test Results for "${templateName}"</h2>

                <div style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                  <div style="color: #2e7d32; font-weight: bold;">✅ Extraction successful!</div>
                  <div>⏱️ Processing time: ${result.extractionResult.processingTime || 'Unknown'}</div>
                  <div>📊 Regions processed: ${result.extractionResult.template?.regionsCount || 0}</div>
                </div>

                <div style="margin: 20px 0;">
                  <button id="showExtractedData" style="
                    background: #1976d2;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-right: 10px;
                    font-size: 14px;
                  ">📄 Extracted Data</button>

                  <button id="showRawData" style="
                    background: #757575;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                  ">🔍 Raw OCR Output</button>
                </div>

                <div id="dataDisplay" style="
                  margin-top: 20px;
                  padding: 15px;
                  background: #f5f5f5;
                  border-radius: 8px;
                  max-height: 300px;
                  overflow-y: auto;
                  white-space: pre-wrap;
                  font-family: monospace;
                  display: none;
                "></div>
              </div>
            `;

            // Show the dialog
            const existingDialog = document.getElementById('templateTestDialog');
            if (existingDialog) {
              existingDialog.remove();
            }

            const dialog = document.createElement('div');
            dialog.id = 'templateTestDialog';
            dialog.style.cssText = `
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,0.5);
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 10000;
            `;

            const dialogBox = document.createElement('div');
            dialogBox.style.cssText = `
              background: white;
              padding: 30px;
              border-radius: 10px;
              max-width: 90%;
              max-height: 90%;
              overflow-y: auto;
            `;

            dialogBox.appendChild(dialogContent);

            const closeButton = document.createElement('button');
            closeButton.textContent = 'OK';
            closeButton.style.cssText = `
              background: #4caf50;
              color: white;
              border: none;
              padding: 10px 30px;
              border-radius: 5px;
              cursor: pointer;
              margin-top: 20px;
              font-size: 16px;
            `;
            closeButton.onclick = () => dialog.remove();
            dialogBox.appendChild(closeButton);

            dialog.appendChild(dialogBox);
            document.body.appendChild(dialog);

            // Add event listeners for the buttons
            document.getElementById('showExtractedData').onclick = () => {
              const display = document.getElementById('dataDisplay');
              display.style.display = 'block';
              display.textContent = JSON.stringify(data, null, 2);
            };

            document.getElementById('showRawData').onclick = () => {
              const display = document.getElementById('dataDisplay');
              display.style.display = 'block';
              display.textContent = `Raw OCR Output:\n\n${JSON.stringify(result.extractionResult, null, 2)}`;
            };

          } else {
            // Handle error case
            alert(`❌ Template test failed: ${result.extractionResult?.error || 'Unknown error'}`);
          }
        } else {
          const errorData = await response.json();
          alert(`❌ Template test failed: ${errorData.message || 'Unknown error'}`);
        }
      } catch (error) {
        console.error('Error testing template:', error);
        alert(`❌ Error testing template: ${error.message}`);
      }
    };

    // Trigger file selection
    fileInput.click();
  };

  // Load template data into the builder
  const loadTemplate = async (templateId) => {
    try {
      console.log('🔄 Loading template:', templateId);

      const response = await fetch(`/api/admin/templates/${templateId}`, {
        headers: {
          'x-auth-token': localStorage.getItem('token')
        }
      });

      if (response.ok) {
        const template = await response.json();
        console.log('📋 Template loaded:', {
          name: template.name,
          regionsCount: template.regions?.length || 0,
          hasImage: !!template.originalImageData
        });

        // Clear current state first
        setRegions([]);
        setImage(null);
        setOriginalImageData(null);
        setOriginalImageName(null);
        setSelectedRegion(null);

        // Load template metadata
        setTemplateName(template.name);
        setTemplateDescription(template.description || '');

        // Load original image if available
        if (template.originalImageData) {
          const img = new Image();
          img.onload = () => {
            setImage(img);
            setupCanvas(img);
            setOriginalImageData(template.originalImageData);
            setOriginalImageName(template.originalImageName);

            // Convert template regions back to canvas coordinates
            const canvas = canvasRef.current;
            const scaleX = canvas.width / img.width;
            const scaleY = canvas.height / img.height;

            const restoredRegions = template.regions.map((region, index) => ({
              id: Date.now() + index, // Generate new IDs
              fieldType: region.fieldType,
              x: region.x * scaleX,
              y: region.y * scaleY,
              width: region.width * scaleX,
              height: region.height * scaleY,
              originalX: region.x,
              originalY: region.y,
              originalWidth: region.width,
              originalHeight: region.height
            }));

            setRegions(restoredRegions);

            // Redraw canvas with restored regions
            setTimeout(() => {
              redrawCanvas();
            }, 100);

            alert(`Template "${template.name}" loaded successfully with ${restoredRegions.length} regions!`);
          };

          img.onerror = () => {
            alert('Failed to load template image. Loading template data only.');
            setTemplateName(template.name);
            setTemplateDescription(template.description || '');
          };

          img.src = template.originalImageData;
        } else {
          alert('Template loaded! Note: No original image was saved with this template.');
        }

        setShowTemplateList(false);
      } else {
        alert('Failed to load template');
      }
    } catch (error) {
      console.error('Error loading template:', error);
      alert('Error loading template');
    }
  };

  // Debug function to check database contents
  const debugTemplates = async () => {
    try {
      // First try to get templates normally
      const response = await fetch('/api/admin/templates', {
        headers: {
          'x-auth-token': localStorage.getItem('token')
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Template List Response:', data);

        // Also check what's in savedTemplates state
        console.log('Current savedTemplates state:', savedTemplates);

        alert(`Debug Info:\nAPI returned: ${data.templates?.length || 0} templates\nState has: ${savedTemplates.length} templates\nTotal from API: ${data.total || 0}\n\nCheck console for full details.`);
      } else {
        const errorData = await response.json();
        console.error('Failed to fetch templates:', errorData);
        alert(`Failed to fetch templates: ${errorData.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error fetching debug data:', error);
      alert(`Error fetching debug data: ${error.message}`);
    }
  };

  // Fetch templates when component mounts or when showing template list
  useEffect(() => {
    if (showTemplateList) {
      fetchTemplates();
    }
  }, [showTemplateList]);

  // Keyboard controls for fine-tuning selected region
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedRegion) return;

      const moveDistance = e.shiftKey ? 10 : 1; // Shift for faster movement
      let newX = selectedRegion.x;
      let newY = selectedRegion.y;
      let newWidth = selectedRegion.width;
      let newHeight = selectedRegion.height;

      switch (e.key) {
        case 'ArrowLeft':
          newX -= moveDistance;
          e.preventDefault();
          break;
        case 'ArrowRight':
          newX += moveDistance;
          e.preventDefault();
          break;
        case 'ArrowUp':
          newY -= moveDistance;
          e.preventDefault();
          break;
        case 'ArrowDown':
          newY += moveDistance;
          e.preventDefault();
          break;
        case 'w':
        case 'W':
          if (e.ctrlKey) {
            newHeight -= moveDistance;
            e.preventDefault();
          }
          break;
        case 's':
        case 'S':
          if (e.ctrlKey) {
            newHeight += moveDistance;
            e.preventDefault();
          }
          break;
        case 'a':
        case 'A':
          if (e.ctrlKey) {
            newWidth -= moveDistance;
            e.preventDefault();
          }
          break;
        case 'd':
        case 'D':
          if (e.ctrlKey) {
            newWidth += moveDistance;
            e.preventDefault();
          }
          break;
        case 'Escape':
          setSelectedRegion(null);
          e.preventDefault();
          break;
      }

      if (newX !== selectedRegion.x || newY !== selectedRegion.y ||
          newWidth !== selectedRegion.width || newHeight !== selectedRegion.height) {

        // Update the region
        setRegions(prev => prev.map(region =>
          region.id === selectedRegion.id
            ? {
                ...region,
                x: newX,
                y: newY,
                width: Math.max(10, newWidth),
                height: Math.max(10, newHeight),
                originalX: Math.round(newX * canvasRef.current.scaleX),
                originalY: Math.round(newY * canvasRef.current.scaleY),
                originalWidth: Math.round(Math.max(10, newWidth) * canvasRef.current.scaleX),
                originalHeight: Math.round(Math.max(10, newHeight) * canvasRef.current.scaleY)
              }
            : region
        ));

        // Update selected region
        setSelectedRegion(prev => ({
          ...prev,
          x: newX,
          y: newY,
          width: Math.max(10, newWidth),
          height: Math.max(10, newHeight)
        }));
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedRegion]);

  useEffect(() => {
    redrawCanvas();
  }, [regions, selectedRegion]);

  return (
    <div className="template-builder">
      <div className="template-builder-header">
        <h2>Cricket Scorecard Template Builder</h2>
        <p>Upload a scorecard image and draw regions to map data fields</p>


      </div>

      <div className="template-form">
        <div className="form-group">
          <label>Template Name:</label>
          <input
            type="text"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            placeholder="e.g., Standard Layout A"
          />
        </div>

        <div className="form-group">
          <label>Description:</label>
          <textarea
            value={templateDescription}
            onChange={(e) => setTemplateDescription(e.target.value)}
            placeholder="Describe this scorecard layout..."
          />
        </div>

        <div className="form-group">
          <label>Upload Scorecard Sample:</label>
          <input type="file" accept="image/*" onChange={handleImageUpload} />
        </div>
      </div>

      <div className="template-builder-content">
        <div className="canvas-container">
          <div className="canvas-controls">
            <div className="mapping-mode">
              <label>Mapping Mode:</label>
              <div className="mode-buttons">
                <button
                  className={`mode-btn ${mappingMode === 'draw' ? 'active' : ''}`}
                  onClick={() => setMappingMode('draw')}
                >
                  🖱️ Draw Mode
                </button>
                <button
                  className={`mode-btn ${mappingMode === 'click' ? 'active' : ''}`}
                  onClick={() => setMappingMode('click')}
                >
                  👆 Click Mode
                </button>
              </div>
            </div>

            <div className="zoom-controls">
              <label>Zoom:</label>
              <div className="zoom-buttons">
                <button onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.25))}>🔍-</button>
                <span>{Math.round(zoomLevel * 100)}%</span>
                <button onClick={() => setZoomLevel(prev => Math.min(3, prev + 0.25))}>🔍+</button>
                <button onClick={() => setZoomLevel(1)}>Reset</button>
              </div>
            </div>
          </div>

          <div className="canvas-wrapper" style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}>
            <canvas
              ref={canvasRef}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              style={{
                border: '1px solid #ccc',
                cursor: mappingMode === 'click' ? 'pointer' : 'crosshair'
              }}
            />
          </div>

          <div className="mapping-help">
            <p><strong>Current Mode:</strong> {mappingMode === 'draw' ? 'Draw regions by dragging' : 'Click to place small regions'}</p>
            <p><strong>Click:</strong> Select region | <strong>Click & Drag:</strong> Move region | <strong>Delete:</strong> Click × in region list</p>
            {selectedRegion && (
              <div className="selected-region-info">
                <h4>🎯 Selected: {fieldTypes.find(ft => ft.value === selectedRegion.fieldType)?.label}</h4>
                <p><strong>Position:</strong> ({selectedRegion.originalX}, {selectedRegion.originalY}) | <strong>Size:</strong> {selectedRegion.originalWidth}×{selectedRegion.originalHeight}</p>
                <p><strong>Keyboard Controls:</strong> Arrow keys to move | Ctrl+WASD to resize | Shift for faster | Esc to deselect</p>
              </div>
            )}
          </div>
        </div>

        <div className="controls-panel">
          <div className="field-types">
            <h3>Select Field Type:</h3>
            <div className="field-type-grid">
              {fieldTypes.map(fieldType => (
                <button
                  key={fieldType.value}
                  className={`field-type-btn ${currentFieldType === fieldType.value ? 'active' : ''}`}
                  style={{ borderColor: fieldType.color }}
                  onClick={() => setCurrentFieldType(fieldType.value)}
                >
                  <div className="color-indicator" style={{ backgroundColor: fieldType.color }}></div>
                  {fieldType.label}
                </button>
              ))}
            </div>
          </div>

          <div className="regions-list">
            <h3>Mapped Regions ({regions.length}):</h3>
            <div className="regions-container">
              {regions.map(region => {
                const fieldType = fieldTypes.find(ft => ft.value === region.fieldType);
                return (
                  <div key={region.id} className="region-item">
                    <div className="region-info">
                      <div className="color-indicator" style={{ backgroundColor: fieldType?.color }}></div>
                      <span>{fieldType?.label}</span>
                      <small>({region.originalX}, {region.originalY}) {region.originalWidth}×{region.originalHeight}</small>
                    </div>
                    <button onClick={() => deleteRegion(region.id)} className="delete-btn">×</button>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="template-status">
            <div className="status-indicators">
              <span className={`status-indicator ${templateName.trim() ? 'valid' : 'invalid'}`}>
                {templateName.trim() ? '✅' : '❌'} Template Name
              </span>
              <span className={`status-indicator ${regions.length > 0 ? 'valid' : 'invalid'}`}>
                {regions.length > 0 ? '✅' : '❌'} Regions ({regions.length})
              </span>
              <span className={`status-indicator ${image ? 'valid' : 'invalid'}`}>
                {image ? '✅' : '❌'} Image
              </span>
            </div>
          </div>

          <div className="template-actions">
            <button onClick={saveTemplate} className="save-btn" disabled={!templateName.trim() || regions.length === 0}>
              💾 Save Template
            </button>
            <button onClick={() => setShowTemplateList(!showTemplateList)} className="manage-btn">
              {showTemplateList ? '📋 Hide Templates' : '📋 Manage Templates'}
            </button>
            <button onClick={() => {
              if (window.confirm('Are you sure you want to clear all regions? This cannot be undone.')) {
                setRegions([]);
                setSelectedRegion(null);
                redrawCanvas();
              }
            }} className="clear-btn">
              🗑️ Clear All Regions
            </button>
            <button onClick={() => {
              if (window.confirm('Are you sure you want to start over? This will clear everything including the image.')) {
                setRegions([]);
                setSelectedRegion(null);
                setImage(null);
                setOriginalImageData(null);
                setOriginalImageName(null);
                setTemplateName('');
                setTemplateDescription('');
                const canvas = canvasRef.current;
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
              }
            }} className="reset-btn">
              🔄 Reset All
            </button>
          </div>

          {showTemplateList && (
            <div className="template-list">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                <h3>📋 Saved Templates ({savedTemplates.length})</h3>
                <button
                  onClick={debugTemplates}
                  style={{
                    padding: '5px 10px',
                    fontSize: '12px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  🔍 Debug DB
                </button>
              </div>
              {savedTemplates.length === 0 ? (
                <p>No templates saved yet. <button onClick={debugTemplates} style={{textDecoration: 'underline', background: 'none', border: 'none', color: 'blue', cursor: 'pointer'}}>Check database</button></p>
              ) : (
                <div className="templates-grid">
                  {savedTemplates.map(template => (
                    <div key={template.id} className="template-card">
                      <div className="template-info">
                        <h4>{template.name}</h4>
                        <p>{template.description || 'No description'}</p>
                        <small>
                          {template.regionsCount} regions •
                          {template.hasImage ? '🖼️ Image saved' : '📄 No image'} •
                          Created: {new Date(template.createdAt).toLocaleDateString()}
                        </small>
                      </div>
                      <div className="template-actions-small">
                        <button
                          onClick={() => {
                            if (window.confirm(`Load template "${template.name}"?\n\nThis will replace your current work. Make sure to save any changes first.`)) {
                              loadTemplate(template.id);
                            }
                          }}
                          className="load-btn"
                          title="Load template for editing"
                        >
                          📂 Load
                        </button>
                        <button
                          onClick={() => testTemplate(template.id, template.name)}
                          className="test-btn"
                          title="Test template with a scorecard image"
                        >
                          🧪 Test
                        </button>
                        <button
                          onClick={() => {
                            if (window.confirm(`Delete template "${template.name}"?\n\nThis action cannot be undone.`)) {
                              deleteTemplate(template.id);
                            }
                          }}
                          className="delete-btn-small"
                          title="Permanently delete template"
                        >
                          🗑️ Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TemplateBuilder;
