const Tournament = require('../models/Tournament');
const User = require('../models/User');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Configure multer storage for scorecard images
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/scorecards';
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueFilename = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

// Configure file filter to only allow image files
const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

// Configure multer upload
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

/**
 * Process scorecard image without a match (for OCR processing only)
 * @route POST /api/tournaments/:tournamentId/scorecard
 * @access Private (Team Owner only)
 */
exports.processScorecard = async (req, res) => {
  // Create debug file to track if this function is called
  const debugDir = require('path').join(__dirname, '..', 'ocr-output', 'extracted');
  const debugFile = require('path').join(debugDir, `debug_processScorecard_${Date.now()}.json`);

  try {
    const { tournamentId } = req.params;

    console.log('=== SCORECARD UPLOAD REQUEST RECEIVED ===');
    console.log(`Processing scorecard for tournament: ${tournamentId}`);
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);
    console.log('Request headers:', req.headers);

    // Save debug info immediately (create directory if it doesn't exist)
    const fs = require('fs');
    const debugDir = path.dirname(debugFile);
    if (!fs.existsSync(debugDir)) {
      fs.mkdirSync(debugDir, { recursive: true });
    }

    fs.writeFileSync(debugFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      step: 'processScorecard_called',
      tournamentId: tournamentId,
      method: req.method,
      url: req.url,
      hasFile: !!req.file,
      bodyKeys: Object.keys(req.body || {}),
      paramsKeys: Object.keys(req.params || {})
    }, null, 2));
    console.log('Saved scorecard debug file:', debugFile);

    // Validate tournament ID format
    if (!tournamentId || !tournamentId.match(/^[0-9a-fA-F]{24}$/)) {
      console.log('Invalid tournament ID format:', tournamentId);
      return res.status(400).json({ msg: 'Invalid tournament ID format' });
    }

    // Find tournament
    const tournament = await Tournament.findById(tournamentId);
    if (!tournament) {
      console.log('Tournament not found for ID:', tournamentId);
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    console.log('Tournament found:', tournament.name);

    // Process file upload
    upload.single('scorecard')(req, res, async function (err) {
      if (err) {
        return res.status(400).json({ msg: err.message });
      }

      if (!req.file) {
        return res.status(400).json({ msg: 'No file uploaded' });
      }

      // Get file path
      const filePath = `/${req.file.path.replace(/\\/g, '/')}`;
      const absoluteFilePath = path.join(process.cwd(), req.file.path);

      // Create a scorecard image object (not saved to any match yet)
      const scorecardImage = {
        url: filePath,
        uploadedBy: req.user ? req.user.id : 'test-user',
        uploadedAt: new Date(),
        isVerified: false
      };

      // Process the image with fresh OCR service
      try {
        console.log('=== STARTING OCR PROCESSING ===');
        console.log('Processing image with fresh OCR service...');
        console.log('File path for OCR:', absoluteFilePath);
        console.log('File exists:', require('fs').existsSync(absoluteFilePath));

        // Import the new OCR controller
        const ocrController = require('./ocrController');
        console.log('OCR controller imported successfully');

        // Process the image with OCR.Space
        console.log('Calling ocrController.processImageFile with OCR.Space...');
        const ocrResult = await ocrController.processImageFile(absoluteFilePath, {
          source: 'scorecard-upload',
          language: 'en',
          // Allow fallback to PaddleOCR if OCR.Space fails
          allowFallback: true
        });

        console.log('=== OCR PROCESSING COMPLETED ===');
        console.log('OCR processing completed:', {
          success: ocrResult.success,
          ocrStatus: ocrResult.data?.ocrStatus,
          hasData: !!ocrResult.data,
          dataKeys: ocrResult.data ? Object.keys(ocrResult.data) : []
        });

        if (ocrResult.data?.error) {
          console.log('OCR Error details:', ocrResult.data.error);
        }

        // Check if OCR was successful
        const ocrSuccess = ocrResult.data && ocrResult.data.success;
        
        // Return the OCR result along with the scorecard
        res.json({
          msg: ocrSuccess 
            ? 'Scorecard processed successfully with fresh OCR' 
            : 'Scorecard uploaded successfully, but OCR processing failed. You can enter match details manually.',
          scorecard: scorecardImage,
          ocrData: ocrResult.data
        });

      } catch (ocrError) {
        console.error('Error processing image with OCR:', ocrError);

        // Determine if this was a complete failure or just a partial failure
        const errorMessage = ocrError.message || 'Unknown error';
        const isCompleteFailure = errorMessage.includes('All OCR methods failed');
        
        // Return basic scorecard data for manual entry
        res.json({
          msg: 'Scorecard uploaded successfully, but OCR processing failed. You can enter match details manually.',
          scorecard: scorecardImage,
          ocrData: {
            success: false,
            error: errorMessage,
            team1: 'Team 1',
            team2: 'Team 2',
            venue: 'Unknown Venue',
            team1Score: { runs: 0, wickets: 0, overs: 0 },
            team2Score: { runs: 0, wickets: 0, overs: 0 },
            playerOfMatch: '',
            resultText: '',
            team1Batsmen: [],
            team1Bowlers: [],
            team2Batsmen: [],
            team2Bowlers: [],
            rawText: '',
            ocrStatus: 'failed',
            ocrMethod: 'manual-entry-required',
            ocrMessage: `OCR processing failed: ${errorMessage}. Please enter match details manually.`
          }
        });
      }
    });
  } catch (err) {
    console.error('Error processing scorecard:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Upload scorecard image for a match
 * @route POST /api/tournaments/:tournamentId/matches/:matchId/scorecard
 * @access Private (Team Owner only)
 */
exports.uploadScorecard = async (req, res) => {
  try {
    const { tournamentId, matchId } = req.params;

    console.log(`Uploading scorecard for tournament: ${tournamentId}, match: ${matchId}`);

    // Validate tournament ID format
    if (!tournamentId || !tournamentId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ msg: 'Invalid tournament ID format' });
    }

    // Validate match ID format
    if (!matchId || !matchId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ msg: 'Invalid match ID format' });
    }

    // Find tournament
    const tournament = await Tournament.findById(tournamentId);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Find match in tournament phases
    let matchFound = false;
    let match = null;
    let phaseIndex = -1;
    let matchIndex = -1;

    for (let i = 0; i < tournament.phases.length; i++) {
      const phase = tournament.phases[i];
      for (let j = 0; j < phase.matches.length; j++) {
        if (phase.matches[j]._id.toString() === matchId) {
          match = phase.matches[j];
          phaseIndex = i;
          matchIndex = j;
          matchFound = true;
          break;
        }
      }
      if (matchFound) break;
    }

    if (!matchFound) {
      return res.status(404).json({ msg: 'Match not found' });
    }

    // Check if user is part of the match (team owner of one of the teams)
    const user = await User.findById(req.user.id).populate('team');
    if (!user.team) {
      return res.status(400).json({ msg: 'You do not have a team' });
    }

    const isUserTeamInMatch =
      match.homeTeam.toString() === user.team._id.toString() ||
      match.awayTeam.toString() === user.team._id.toString();

    if (!isUserTeamInMatch && req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'You are not authorized to upload scorecards for this match' });
    }

    // Process file upload
    upload.single('scorecard')(req, res, async function (err) {
      if (err) {
        return res.status(400).json({ msg: err.message });
      }

      if (!req.file) {
        return res.status(400).json({ msg: 'No file uploaded' });
      }

      // Get file path
      const filePath = `/${req.file.path.replace(/\\/g, '/')}`;
      const absoluteFilePath = path.join(process.cwd(), req.file.path);

      // Add scorecard image to match
      if (!match.result.scorecardImages) {
        match.result.scorecardImages = [];
      }

      const scorecardImage = {
        url: filePath,
        uploadedBy: req.user.id,
        uploadedAt: new Date(),
        isVerified: false
      };

      match.result.scorecardImages.push(scorecardImage);

      // Update match verification status if it's not already verified
      if (match.result.verificationStatus === 'pending') {
        match.result.verificationStatus = 'pending';
      }

      // Save tournament
      await tournament.save();

      // Process the image with fresh OCR service
      try {
        console.log('Processing match scorecard with fresh OCR service...');

        // Import the new OCR controller
        const ocrController = require('./ocrController');

        // Process the image with OCR.Space
        const ocrResult = await ocrController.processImageFile(absoluteFilePath, {
          source: 'match-scorecard-upload',
          language: 'en',
          useOCRSpace: true  // Use OCR.Space instead of PaddleOCR
        });

        console.log('Match scorecard OCR processing completed:', {
          success: ocrResult.success,
          ocrStatus: ocrResult.data?.ocrStatus
        });

        // Check if OCR was successful
        const ocrSuccess = ocrResult.data && ocrResult.data.success;
        
        // Return the OCR result along with the scorecard
        res.json({
          msg: ocrSuccess 
            ? 'Match scorecard processed successfully with fresh OCR' 
            : 'Match scorecard uploaded successfully, but OCR processing failed. You can enter match details manually.',
          scorecard: scorecardImage,
          ocrData: ocrResult.data
        });

      } catch (ocrError) {
        console.error('Error processing match scorecard with OCR:', ocrError);

        // Determine if this was a complete failure or just a partial failure
        const errorMessage = ocrError.message || 'Unknown error';
        
        // Return basic scorecard data for manual entry
        res.json({
          msg: 'Match scorecard uploaded successfully, but OCR processing failed. You can enter match details manually.',
          scorecard: scorecardImage,
          ocrData: {
            success: false,
            error: errorMessage,
            team1: 'Team 1',
            team2: 'Team 2',
            venue: 'Unknown Venue',
            team1Score: { runs: 0, wickets: 0, overs: 0 },
            team2Score: { runs: 0, wickets: 0, overs: 0 },
            playerOfMatch: '',
            resultText: '',
            team1Batsmen: [],
            team1Bowlers: [],
            team2Batsmen: [],
            team2Bowlers: [],
            rawText: '',
            ocrStatus: 'failed',
            ocrMethod: 'manual-entry-required',
            ocrMessage: `OCR processing failed: ${errorMessage}. Please enter match details manually.`
          }
        });
      }
    });
  } catch (err) {
    console.error('Error uploading scorecard:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Verify a match result
 * @route POST /api/tournaments/:tournamentId/matches/:matchId/verify
 * @access Private (Admin only)
 */
exports.verifyMatch = async (req, res) => {
  try {
    const { tournamentId, matchId } = req.params;

    // Find tournament
    const tournament = await Tournament.findById(tournamentId);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Find match in tournament phases
    let matchFound = false;
    let match = null;
    let phaseIndex = -1;
    let matchIndex = -1;

    for (let i = 0; i < tournament.phases.length; i++) {
      const phase = tournament.phases[i];
      for (let j = 0; j < phase.matches.length; j++) {
        if (phase.matches[j]._id.toString() === matchId) {
          match = phase.matches[j];
          phaseIndex = i;
          matchIndex = j;
          matchFound = true;
          break;
        }
      }
      if (matchFound) break;
    }

    if (!matchFound) {
      return res.status(404).json({ msg: 'Match not found' });
    }

    // Update match verification status
    match.result.verificationStatus = 'verified';
    match.result.verifiedBy = req.user.id;
    match.result.verifiedAt = new Date();

    // Save tournament
    await tournament.save();

    res.json({
      msg: 'Match verified successfully',
      match
    });
  } catch (err) {
    console.error('Error verifying match:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Dispute a match result
 * @route POST /api/tournaments/:tournamentId/matches/:matchId/dispute
 * @access Private (Team Owner only)
 */
exports.disputeMatch = async (req, res) => {
  try {
    const { tournamentId, matchId } = req.params;
    const { reason } = req.body;

    if (!reason) {
      return res.status(400).json({ msg: 'Dispute reason is required' });
    }

    // Find tournament
    const tournament = await Tournament.findById(tournamentId);
    if (!tournament) {
      return res.status(404).json({ msg: 'Tournament not found' });
    }

    // Find match in tournament phases
    let matchFound = false;
    let match = null;
    let phaseIndex = -1;
    let matchIndex = -1;

    for (let i = 0; i < tournament.phases.length; i++) {
      const phase = tournament.phases[i];
      for (let j = 0; j < phase.matches.length; j++) {
        if (phase.matches[j]._id.toString() === matchId) {
          match = phase.matches[j];
          phaseIndex = i;
          matchIndex = j;
          matchFound = true;
          break;
        }
      }
      if (matchFound) break;
    }

    if (!matchFound) {
      return res.status(404).json({ msg: 'Match not found' });
    }

    // Check if user is part of the match (team owner of one of the teams)
    const user = await User.findById(req.user.id).populate('team');
    if (!user.team) {
      return res.status(400).json({ msg: 'You do not have a team' });
    }

    const isUserTeamInMatch =
      match.homeTeam.toString() === user.team._id.toString() ||
      match.awayTeam.toString() === user.team._id.toString();

    if (!isUserTeamInMatch) {
      return res.status(403).json({ msg: 'You are not authorized to dispute this match' });
    }

    // Update match dispute status
    match.result.dispute = {
      isDisputed: true,
      disputedBy: req.user.id,
      disputeReason: reason,
      disputeStatus: 'open',
      disputeResolution: ''
    };

    match.result.verificationStatus = 'disputed';

    // Save tournament
    await tournament.save();

    res.json({
      msg: 'Match disputed successfully',
      match
    });
  } catch (err) {
    console.error('Error disputing match:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

module.exports = exports;
