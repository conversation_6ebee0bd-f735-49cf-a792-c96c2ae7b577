# Simple Automated RPL Cricket Project Board Setup
# Creates GitHub project board without special characters

param(
    [string]$RepoOwner = "rhing<PERSON><PERSON>",
    [string]$RepoName = "rplwebapp",
    [string]$ProjectTitle = "RPL Cricket - Big Ant Cricket 24 System",
    [switch]$DryRun = $false
)

Write-Host "RPL Cricket Project Board Setup" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host ""

# Check GitHub CLI
try {
    $ghVersion = gh --version 2>$null
    if ($LASTEXITCODE -ne 0) { throw "GitHub CLI not found" }
    Write-Host "GitHub CLI ready: $($ghVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "GitHub CLI not found. Please install: winget install --id GitHub.cli" -ForegroundColor Red
    exit 1
}

# Check authentication
try {
    gh auth status 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) { throw "Not authenticated" }
    Write-Host "GitHub authentication verified" -ForegroundColor Green
} catch {
    Write-Host "Not authenticated. Please run: gh auth login" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Function to create project
function New-GitHubProject {
    param($Title, $Owner, $Repo)
    
    Write-Host "Creating GitHub Project: $Title" -ForegroundColor Cyan
    
    if ($DryRun) {
        Write-Host "   [DRY RUN] Would create project" -ForegroundColor Yellow
        return "https://github.com/$Owner/$Repo/projects/1"
    }
    
    try {
        # Try to create project using GitHub CLI
        $result = gh project create --title $Title --owner $Owner 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   Project created successfully" -ForegroundColor Green
            # Extract project URL from result
            $projectUrl = $result | Select-String -Pattern "https://github.com/.*/projects/\d+" | ForEach-Object { $_.Matches[0].Value }
            return $projectUrl
        } else {
            throw "Failed to create project: $result"
        }
    } catch {
        Write-Host "   Failed to create project: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

# Function to create issues that will be added to project
function New-ProjectIssues {
    Write-Host "Creating key project issues..." -ForegroundColor Cyan
    
    # Define critical issues
    $issues = @(
        @{
            Title = "2.6 Transfer Market System - Complete Implementation"
            Body = "Status: IN PROGRESS (80% Complete). Complete the player trading system between teams. What needs completion: Trading Logic, Market Value Algorithms, Transfer History, Transaction Validation, Notification System, Transfer Deadlines. Priority: CRITICAL - This is 80% complete and blocking the player economy system."
        },
        @{
            Title = "7.1 Skill Points and Rating System - Core Big Ant Cricket 24 Feature"
            Body = "Status: TODO (0% Complete). Implement the core Big Ant Cricket 24 vision: automatic rating increases based on skill points. Requirements: 1 run = 1 skill point, 1 wicket = 10 skill points, 5000 skill points = +1 rating increase, Admin configurable thresholds, Automatic rating updates after each match. Priority: CRITICAL - This is THE core feature that makes this a Big Ant Cricket 24 style system."
        },
        @{
            Title = "7.2 Performance Milestone Bonuses - Reward Exceptional Performances"
            Body = "Status: TODO (0% Complete). Implement milestone bonus system for exceptional cricket performances. Batting Milestones: 30 runs = +60 bonus points, 50 runs = +90 bonus points, 100 runs = +150 bonus points. Bowling Milestones: 3 wickets = +60 bonus points, 5 wickets = +90 bonus points. Priority: CRITICAL - Essential for the Big Ant Cricket 24 vision."
        },
        @{
            Title = "7.3 Comprehensive Leaderboards - Competitive Gaming Experience"
            Body = "Status: TODO (0% Complete). Create comprehensive leaderboards for competitive cricket gaming. Categories: Most Runs, Most Wickets, Most 30s/50s/100s, Most 3W/5W Hauls, Man of the Match Awards. Format-wise filtering: T10, T20, ODI, Test. Tournament-wise and overall statistics. Priority: HIGH - Critical for competitive gaming experience and player engagement."
        },
        @{
            Title = "3.1 Tournament Management System - Complete Automation"
            Body = "Status: IN PROGRESS (75% Complete). Complete the tournament automation system. What's implemented: Tournament creation, team registration, basic match addition. What's missing: Automated progression, fixture generation, knockout brackets, standings automation. Priority: HIGH - Essential for seamless tournament management."
        }
    )
    
    $createdIssues = @()
    
    foreach ($issue in $issues) {
        Write-Host "   Creating: $($issue.Title)" -ForegroundColor White
        
        if ($DryRun) {
            Write-Host "      [DRY RUN] Would create issue" -ForegroundColor Yellow
            $createdIssues += "https://github.com/$RepoOwner/$RepoName/issues/999"
            continue
        }
        
        try {
            $issueUrl = gh issue create --title $issue.Title --body $issue.Body --repo "$RepoOwner/$RepoName"
            Write-Host "      Created: $issueUrl" -ForegroundColor Green
            $createdIssues += $issueUrl
            
            # Small delay to avoid rate limiting
            Start-Sleep -Milliseconds 500
            
        } catch {
            Write-Host "      Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $createdIssues
}

# Function to display setup instructions
function Show-SetupInstructions {
    param($ProjectUrl, $IssueCount)
    
    Write-Host ""
    Write-Host "Project Board Setup Complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Project Summary:" -ForegroundColor Cyan
    Write-Host "• Project: $ProjectTitle" -ForegroundColor White
    Write-Host "• Issues Created: $IssueCount" -ForegroundColor White
    Write-Host "• Repository: $RepoOwner/$RepoName" -ForegroundColor White
    Write-Host ""
    Write-Host "Manual Steps Needed:" -ForegroundColor Yellow
    Write-Host "1. Go to your project board: $ProjectUrl" -ForegroundColor White
    Write-Host "2. Create columns: Backlog | Ready | In Progress | Review | Complete" -ForegroundColor White
    Write-Host "3. Add the created issues to your project board" -ForegroundColor White
    Write-Host "4. Organize issues into appropriate columns based on status" -ForegroundColor White
    Write-Host "5. Set up automation rules (optional)" -ForegroundColor White
    Write-Host ""
    Write-Host "Phase Status Summary:" -ForegroundColor Cyan
    Write-Host "• Phase 1.0: Core Infrastructure (100% complete)" -ForegroundColor Green
    Write-Host "• Phase 2.0: Player and Team Management (95% complete)" -ForegroundColor Yellow
    Write-Host "• Phase 3.0: Tournament and Match Management (75% complete)" -ForegroundColor Yellow
    Write-Host "• Phase 4.0: Auction System (90% complete)" -ForegroundColor Yellow
    Write-Host "• Phase 5.0: Advanced Features and Analytics (20% complete)" -ForegroundColor Red
    Write-Host "• Phase 6.0: Production and Deployment (70% complete)" -ForegroundColor Yellow
    Write-Host "• Phase 7.0: Big Ant Cricket 24 Integration (0% complete)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Next Priorities:" -ForegroundColor Yellow
    Write-Host "1. Complete Transfer Market System (2.6) - 80% done" -ForegroundColor White
    Write-Host "2. Implement Skill Points and Rating System (7.1) - CRITICAL" -ForegroundColor White
    Write-Host "3. Add Performance Milestone Bonuses (7.2) - CRITICAL" -ForegroundColor White
    Write-Host "4. Create Comprehensive Leaderboards (7.3) - HIGH" -ForegroundColor White
    Write-Host ""
    Write-Host "Quick Links:" -ForegroundColor Blue
    Write-Host "Repository: https://github.com/$RepoOwner/$RepoName" -ForegroundColor Blue
    Write-Host "Issues: https://github.com/$RepoOwner/$RepoName/issues" -ForegroundColor Blue
    Write-Host "Projects: https://github.com/$RepoOwner/$RepoName/projects" -ForegroundColor Blue
}

# Main execution
try {
    Write-Host "Repository: $RepoOwner/$RepoName" -ForegroundColor Cyan
    Write-Host "Project: $ProjectTitle" -ForegroundColor Cyan
    if ($DryRun) {
        Write-Host "Mode: DRY RUN (no changes will be made)" -ForegroundColor Yellow
    }
    Write-Host ""
    
    # Step 1: Create the project
    $projectUrl = New-GitHubProject -Title $ProjectTitle -Owner $RepoOwner -Repo $RepoName
    
    # Step 2: Create key issues
    $createdIssues = New-ProjectIssues
    
    # Step 3: Show setup instructions
    Show-SetupInstructions -ProjectUrl $projectUrl -IssueCount $createdIssues.Count
    
} catch {
    Write-Host ""
    Write-Host "Setup failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Ensure you have admin access to the repository" -ForegroundColor White
    Write-Host "2. Check that GitHub CLI is properly authenticated" -ForegroundColor White
    Write-Host "3. Try running with -DryRun first to test" -ForegroundColor White
    exit 1
}
