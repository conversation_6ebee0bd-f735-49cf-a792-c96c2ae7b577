import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper
} from '@mui/material';
import CardGallery from '../../components/players/CardGallery';
import { getMyTeamPlayers } from '../../services/playerService';

const MyTeam = () => {
  const navigate = useNavigate();
  const [players, setPlayers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    rarity: '',
    sort: 'ratings.overall:desc'
  });

  useEffect(() => {
    fetchPlayers();
  }, [currentPage, filters]);

  const fetchPlayers = async () => {
    try {
      setLoading(true);
      const data = await getMyTeamPlayers({
        page: currentPage,
        limit: 12,
        ...filters
      });
      
      setPlayers(data.players);
      setTotalPages(data.totalPages);
    } catch (err) {
      console.error('Error fetching players:', err);
      setError('Failed to load players. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setCurrentPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setCurrentPage(1);
  };

  const handleSearchChange = (event) => {
    setFilters(prev => ({ ...prev, search: event.target.value }));
    setCurrentPage(1);
  };

  const handleCardClick = (playerId) => {
    navigate(`/players/${playerId}`);
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          My Team
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage your player cards and view your collection
        </Typography>
      </Paper>

      <CardGallery
        players={players}
        loading={loading}
        error={error}
        totalPages={totalPages}
        currentPage={currentPage}
        filters={filters}
        onPageChange={handlePageChange}
        onFilterChange={handleFilterChange}
        onSearchChange={handleSearchChange}
        onCardClick={handleCardClick}
      />
    </Container>
  );
};

export default MyTeam;