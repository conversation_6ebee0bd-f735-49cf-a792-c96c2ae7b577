const sharp = require('sharp');
const path = require('path');

async function removeBackground(inputPath) {
    try {
        const outputPath = inputPath.replace(/\.\w+$/, '-nobg.png');
        
        // First convert to PNG and extract alpha channel
        await sharp(inputPath)
            .toColorspace('srgb')
            .png()
            .toFile(outputPath);

        // Process the image to remove background
        const { width, height, channels, data } = await sharp(outputPath)
            .raw()
            .toBuffer({ resolveWithObject: true });

        // Create a new buffer for the processed image
        const processedBuffer = Buffer.alloc(width * height * 4); // 4 channels (RGBA)

        // Background removal parameters
        const tolerance = 30; // Color difference tolerance
        const edgeRadius = 2; // Pixels to check around edges

        // Sample background color from corners (assuming corners are background)
        const getPixel = (x, y) => {
            const i = (y * width + x) * channels;
            return [data[i], data[i + 1], data[i + 2]];
        };

        const bgSamples = [
            getPixel(0, 0), // Top-left
            getPixel(width - 1, 0), // Top-right
            getPixel(0, height - 1), // Bottom-left
            getPixel(width - 1, height - 1) // Bottom-right
        ];

        // Calculate average background color
        const avgBg = bgSamples.reduce((acc, pixel) => {
            return acc.map((v, i) => v + pixel[i]);
        }, [0, 0, 0]).map(v => v / bgSamples.length);

        // Process each pixel
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const i = (y * width + x) * channels;
                const o = (y * width + x) * 4;

                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                // Calculate color difference from background
                const colorDiff = Math.sqrt(
                    Math.pow(r - avgBg[0], 2) +
                    Math.pow(g - avgBg[1], 2) +
                    Math.pow(b - avgBg[2], 2)
                );

                // Set RGB values
                processedBuffer[o] = r;
                processedBuffer[o + 1] = g;
                processedBuffer[o + 2] = b;

                // Calculate alpha based on color difference
                let alpha = colorDiff < tolerance ? 0 : 255;

                // Edge detection for smoother transitions
                if (alpha === 0 && x > edgeRadius && x < width - edgeRadius && 
                    y > edgeRadius && y < height - edgeRadius) {
                    // Check surrounding pixels
                    let surroundingDiff = 0;
                    for (let sy = -edgeRadius; sy <= edgeRadius; sy++) {
                        for (let sx = -edgeRadius; sx <= edgeRadius; sx++) {
                            if (sx === 0 && sy === 0) continue;
                            const si = ((y + sy) * width + (x + sx)) * channels;
                            const sr = data[si];
                            const sg = data[si + 1];
                            const sb = data[si + 2];
                            surroundingDiff += Math.sqrt(
                                Math.pow(sr - avgBg[0], 2) +
                                Math.pow(sg - avgBg[1], 2) +
                                Math.pow(sb - avgBg[2], 2)
                            );
                        }
                    }
                    surroundingDiff /= (Math.pow(edgeRadius * 2 + 1, 2) - 1);
                    if (surroundingDiff > tolerance) {
                        alpha = Math.min(255, surroundingDiff);
                    }
                }

                processedBuffer[o + 3] = alpha;
            }
        }

        // Create final image with transparency
        await sharp(processedBuffer, {
            raw: {
                width,
                height,
                channels: 4
            }
        })
        .png()
        .toFile(outputPath);

        return outputPath;
    } catch (error) {
        console.error('Error removing background:', error);
        return inputPath; // Return original path if processing fails
    }
}

module.exports = {
    removeBackground
};