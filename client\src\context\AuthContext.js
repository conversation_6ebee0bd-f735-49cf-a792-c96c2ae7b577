import React, { createContext, useState, useEffect } from 'react';
import * as authService from '../services/authService';

// Create auth context
export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user on initial render if token exists
  useEffect(() => {
    const loadUser = async () => {
      if (token) {
        try {
          // Set token in axios headers
          authService.setAuthToken(token);
          
          // Get current user data
          const userData = await authService.getCurrentUser();
          setUser(userData);
          setError(null);
        } catch (err) {
          console.error('Error loading user:', err);
          setToken(null);
          setUser(null);
          localStorage.removeItem('token');
          setError('Session expired. Please login again.');
        }
      }
      setLoading(false);
    };

    loadUser();
  }, [token]);

  // Register user
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const res = await authService.register(userData);
      
      // Set token
      localStorage.setItem('token', res.token);
      setToken(res.token);
      
      // Load user data
      authService.setAuthToken(res.token);
      const userRes = await authService.getCurrentUser();
      setUser(userRes);
      
      setLoading(false);
      return userRes;
    } catch (err) {
      setLoading(false);
      const errorMessage = err.response?.data?.msg || 'Registration failed. Please try again.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Login user
  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const res = await authService.login(email, password);
      
      // Set token
      localStorage.setItem('token', res.token);
      setToken(res.token);
      
      // Load user data
      authService.setAuthToken(res.token);
      const userRes = await authService.getCurrentUser();
      setUser(userRes);
      
      setLoading(false);
      return userRes;
    } catch (err) {
      setLoading(false);
      const errorMessage = err.response?.data?.msg || 'Invalid credentials';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Logout user
  const logout = () => {
    localStorage.removeItem('token');
    authService.setAuthToken(null);
    setToken(null);
    setUser(null);
    setError(null);
  };

  // Update user profile
  const updateProfile = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authService.updateProfile(userData);
      setUser(response.user);
      
      setLoading(false);
      return response;
    } catch (err) {
      setLoading(false);
      const errorMessage = err.response?.data?.msg || 'Failed to update profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        token,
        user,
        loading,
        error,
        register,
        login,
        logout,
        updateProfile,
        isAuthenticated: !!user
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;