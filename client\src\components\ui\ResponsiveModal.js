import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  IconButton,
  Box,
  useMediaQuery,
  useTheme,
  Divider
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

/**
 * ResponsiveModal - A reusable modal component with responsive design
 * 
 * This component provides consistent styling and behavior for modals across the application,
 * with special focus on mobile responsiveness and clean visual design.
 */
const ResponsiveModal = ({
  open,
  onClose,
  title,
  children,
  actions,
  maxWidth = 'sm',
  fullWidth = true,
  hideCloseButton = false,
  titleComponent = null,
  contentSx = {},
  disablePadding = false,
  fullScreen = false,
  showDividers = true
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isSmall = useMediaQuery(theme.breakpoints.down('md'));
  
  // Determine if fullScreen should be applied based on props or screen size
  const useFullScreen = fullScreen || (isMobile && maxWidth !== 'xs');

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      fullScreen={useFullScreen}
      PaperProps={{
        elevation: 3,
        sx: {
          borderRadius: useFullScreen ? 0 : 2,
          height: useFullScreen ? '100%' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          // Remove the overflow hidden to prevent dropdown menus from being cut off
          overflow: 'visible'
        }
      }}
    >
      {/* Title section */}
      <DialogTitle 
        sx={{ 
          py: isSmall ? 1.5 : 2,
          px: isSmall ? 2 : 3,
          borderBottom: showDividers ? 1 : 0,
          borderColor: 'divider',
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between'
        }}
      >
        {titleComponent || (
          <Typography 
            variant={isSmall ? "h6" : "h5"} 
            component="div" 
            sx={{ 
              fontWeight: 'medium',
              flex: 1
            }}
          >
            {title}
          </Typography>
        )}
        
        {!hideCloseButton && (
          <IconButton 
            edge="end" 
            color="inherit" 
            onClick={onClose} 
            aria-label="close"
            size={isSmall ? "small" : "medium"}
          >
            <CloseIcon />
          </IconButton>
        )}
      </DialogTitle>
      
      {/* Content section */}
      <DialogContent 
        sx={{
          p: disablePadding ? 0 : (isSmall ? 2 : 3),
          // Allow content to overflow for dropdowns
          overflow: 'visible',
          flex: useFullScreen ? 1 : 'none',
          ...contentSx
        }}
      >
        {children}
      </DialogContent>
      
      {/* Actions section */}
      {actions && (
        <>
          {showDividers && <Divider />}
          <DialogActions 
            sx={{ 
              p: isSmall ? 1.5 : 2,
              px: isSmall ? 2 : 3,
              bgcolor: theme => theme.palette.mode === 'light' ? 'grey.50' : 'grey.900',
              justifyContent: 'flex-end',
              gap: 1,
              // Give action buttons some z-index to ensure they're above any overflowing content
              position: 'relative',
              zIndex: 1
            }}
          >
            {actions}
          </DialogActions>
        </>
      )}
    </Dialog>
  );
};

ResponsiveModal.propTypes = {
  /**
   * Whether the modal is open
   */
  open: PropTypes.bool.isRequired,
  
  /**
   * Callback when modal is closed
   */
  onClose: PropTypes.func.isRequired,
  
  /**
   * Modal title text
   */
  title: PropTypes.string,
  
  /**
   * Modal content
   */
  children: PropTypes.node,
  
  /**
   * Action buttons for the modal footer
   */
  actions: PropTypes.node,
  
  /**
   * MUI maxWidth prop ('xs', 'sm', 'md', 'lg', 'xl')
   */
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  
  /**
   * Whether the modal should take up full width of its container
   */
  fullWidth: PropTypes.bool,
  
  /**
   * Hide the close button in the title bar
   */
  hideCloseButton: PropTypes.bool,
  
  /**
   * Custom component to use for the title (instead of text title)
   */
  titleComponent: PropTypes.node,
  
  /**
   * Additional styles to apply to the dialog content
   */
  contentSx: PropTypes.object,
  
  /**
   * Whether to disable default padding in the content area
   */
  disablePadding: PropTypes.bool,
  
  /**
   * Force fullscreen mode regardless of screen size
   */
  fullScreen: PropTypes.bool,
  
  /**
   * Whether to show dividers between sections
   */
  showDividers: PropTypes.bool
};

export default ResponsiveModal;