#!/usr/bin/env python
"""
Test script to verify if the trained model can correctly identify elements in a scorecard.
"""

import os
import sys
import json
import argparse
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import pickle

# Add the parent directory to the path so we can import from the trainer module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ml_training.scorecard_trainer import ScorecardTrainer

def load_image(image_path):
    """Load an image from a path"""
    if not os.path.exists(image_path):
        print(f"Error: Image not found at {image_path}")
        return None
    
    return Image.open(image_path)

def draw_predictions(image, predictions, output_path):
    """Draw predictions on the image and save it"""
    draw = ImageDraw.Draw(image)
    
    # Try to load a font, use default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 14)
    except IOError:
        font = ImageFont.load_default()
    
    colors = {
        'team1_name': (255, 0, 0),      # Red
        'team2_name': (0, 0, 255),      # Blue
        'team1_score': (255, 100, 0),   # Orange
        'team2_score': (0, 100, 255),   # Light Blue
        'team1_overs': (255, 150, 0),   # Light Orange
        'team2_overs': (0, 150, 255),   # Sky Blue
        'match_result': (0, 255, 0),    # Green
        'venue': (128, 0, 128),         # Purple
        'player_of_match': (255, 0, 255), # Magenta
        'batsman_name': (0, 128, 0),    # Dark Green
        'batsman_runs': (0, 200, 0),    # Light Green
        'batsman_balls': (0, 255, 128), # Mint
        'bowler_name': (128, 64, 0),    # Brown
        'bowler_overs': (200, 100, 0),  # Light Brown
        'bowler_wickets': (255, 128, 0), # Dark Orange
        None: (128, 128, 128)           # Gray for unknown
    }
    
    for pred in predictions:
        category = pred.get('category')
        text = pred.get('text', '')
        box = pred.get('box', [[0, 0], [100, 0], [100, 30], [0, 30]])
        confidence = pred.get('confidence', 0)
        
        color = colors.get(category, colors[None])
        
        # Draw bounding box
        draw.polygon([(p[0], p[1]) for p in box], outline=color, width=2)
        
        # Draw label
        label = f"{category}: {text} ({confidence:.2f})"
        draw.text((box[0][0], box[0][1] - 15), label, fill=color, font=font)
    
    # Save the image
    image.save(output_path)
    print(f"Saved annotated image to {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Test the trained model on a scorecard image')
    parser.add_argument('--image', required=True, help='Path to the scorecard image to test')
    parser.add_argument('--model', default='./models/scorecard_classifier.pkl', help='Path to the trained model')
    parser.add_argument('--output', default='./test_output.jpg', help='Path to save the output image with predictions')
    
    args = parser.parse_args()
    
    # Load the image
    image = load_image(args.image)
    if image is None:
        return
    
    # Initialize the trainer
    trainer = ScorecardTrainer()
    
    # Check if model exists
    if not os.path.exists(args.model):
        print(f"Error: Model not found at {args.model}")
        print("Please train the model first.")
        return
    
    # Load the model
    try:
        with open(args.model, 'rb') as f:
            model = pickle.load(f)
        print(f"Successfully loaded model from {args.model}")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Get predictions
    try:
        # This is a simplified version - in a real implementation, 
        # you would use the model to predict elements
        predictions = trainer.predict_elements(image)
        print(f"Made {len(predictions)} predictions")
        
        # Print predictions
        for i, pred in enumerate(predictions):
            print(f"Prediction {i+1}:")
            print(f"  Category: {pred.get('category')}")
            print(f"  Text: {pred.get('text')}")
            print(f"  Confidence: {pred.get('confidence', 0):.2f}")
    except Exception as e:
        print(f"Error making predictions: {e}")
        return
    
    # Draw predictions on the image
    draw_predictions(image, predictions, args.output)

if __name__ == "__main__":
    main()
