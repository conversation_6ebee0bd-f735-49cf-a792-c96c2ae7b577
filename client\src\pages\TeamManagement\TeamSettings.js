import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  TextField,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Card,
  CardMedia,
  CardContent,
  useTheme,
  useMediaQuery,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PhotoCamera from '@mui/icons-material/PhotoCamera';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import EditIcon from '@mui/icons-material/Edit';
import { useAuth } from '../../hooks/useAuth';
import {
  updateTeamSettings,
  getTeamSettings,
  uploadTeamLogo
} from '../../services/teamService';

// Available team colors
const teamColors = [
  { name: 'Red', primary: '#e53935', secondary: '#ffcdd2' },
  { name: 'Blue', primary: '#1e88e5', secondary: '#bbdefb' },
  { name: '<PERSON>', primary: '#43a047', secondary: '#c8e6c9' },
  { name: '<PERSON>', primary: '#8e24aa', secondary: '#e1bee7' },
  { name: 'Orange', primary: '#fb8c00', secondary: '#ffe0b2' },
  { name: 'Teal', primary: '#00897b', secondary: '#b2dfdb' },
  { name: 'Pink', primary: '#d81b60', secondary: '#f8bbd0' },
  { name: 'Indigo', primary: '#3949ab', secondary: '#c5cae9' },
  { name: 'Yellow', primary: '#fdd835', secondary: '#fff9c4' },
  { name: 'Cyan', primary: '#00acc1', secondary: '#b2ebf2' },
];

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const TeamSettings = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user, setUser } = useAuth();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [teamData, setTeamData] = useState({
    teamName: user?.teamName || '',
    description: '',
    primaryColor: teamColors[0].primary,
    secondaryColor: teamColors[0].secondary,
    colorScheme: 'Red',
    logo: null,
    logoUrl: '/uploads/teams/default-logo.png',
    homeGround: '',
    foundedYear: new Date().getFullYear(),
    slogan: ''
  });

  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);

  // Load team settings from server
  useEffect(() => {
    if (!user || user.role !== 'team_owner') return;

    setLoading(true);

    const fetchTeamSettings = async () => {
      try {
        // Fetch team settings from server
        let data;
        try {
          data = await getTeamSettings();
          console.log('Loaded team settings from server:', data);
        } catch (settingsErr) {
          console.error('Error fetching team settings from server:', settingsErr);

          // Fallback to localStorage if server request fails
          const savedSettings = localStorage.getItem(`teamSettings_${user.id}`);
          if (savedSettings) {
            data = JSON.parse(savedSettings);
            console.log('Fallback: Loaded saved team settings from localStorage:', data);
          } else {
            // Use default settings if nothing is saved
            data = {
              teamName: user.teamName || 'My Team',
              description: 'This is a placeholder for your team description.',
              logo: '/uploads/teams/default-logo.png',
              primaryColor: '#1e88e5',
              secondaryColor: '#bbdefb',
              colorScheme: 'Blue',
              homeGround: '',
              foundedYear: new Date().getFullYear(),
              slogan: ''
            };
            console.log('Using default team settings');
          }
        }

        // Ensure teamName is set correctly from user data if not in settings
        if (!data.teamName) {
          data.teamName = user.teamName || 'My Team';
        }

        setTeamData(prev => ({
          ...prev,
          ...data,
          teamName: data.teamName || user.teamName || '',
          logoUrl: data.logo || '/uploads/teams/default-logo.png'
        }));
      } catch (err) {
        console.error('Error loading team settings:', err);
        setError('Failed to load team settings. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTeamSettings();
  }, [user]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setTeamData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleColorChange = (colorName) => {
    const selectedColor = teamColors.find(color => color.name === colorName);
    if (selectedColor) {
      setTeamData(prev => ({
        ...prev,
        colorScheme: colorName,
        primaryColor: selectedColor.primary,
        secondaryColor: selectedColor.secondary
      }));
    }
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const handleRemoveLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
    setTeamData(prev => ({
      ...prev,
      logo: null,
      logoUrl: '/uploads/teams/default-logo.png'
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Create updated team data object
      let updatedTeamData = { ...teamData };

      // If a logo was uploaded, update the logo URL
      if (logoFile) {
        try {
          // Create a FormData object to upload the logo
          const formData = new FormData();
          formData.append('teamLogo', logoFile);

          // Upload the logo to the server
          const uploadResult = await uploadTeamLogo(formData);
          console.log('Logo upload result:', uploadResult);

          // Update the logo URL in the team data
          updatedTeamData = {
            ...updatedTeamData,
            logo: uploadResult.logoUrl || logoPreview,
            logoUrl: uploadResult.logoUrl || logoPreview
          };
        } catch (uploadErr) {
          console.error('Error uploading logo:', uploadErr);
          // Continue with the local preview URL if upload fails
          updatedTeamData = {
            ...updatedTeamData,
            logo: logoPreview,
            logoUrl: logoPreview
          };
        }
      }

      // Update team settings on the server
      try {
        const result = await updateTeamSettings(updatedTeamData);
        console.log('Team settings updated on server:', result);
      } catch (updateErr) {
        console.error('Error updating team settings on server:', updateErr);
        // Save to localStorage as fallback if server update fails
        localStorage.setItem(`teamSettings_${user.id}`, JSON.stringify(updatedTeamData));
        console.log('Fallback: Saved team settings to localStorage:', updatedTeamData);
      }

      // Update user context if team name changed
      if (updatedTeamData.teamName !== user.teamName) {
        setUser({
          ...user,
          teamName: updatedTeamData.teamName
        });
      }

      // Update state with the new data
      setTeamData(updatedTeamData);

      setSuccess('Team settings updated successfully');

      // Clear logo file state after successful upload
      setLogoFile(null);
    } catch (err) {
      console.error('Error updating team settings:', err);
      setError('Failed to update team settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Team Settings
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          Customize your team's appearance and information
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Team Logo Section */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Team Logo
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Card sx={{ width: '100%', mb: 2, maxWidth: 250 }}>
                  {(logoPreview || teamData.logoUrl !== '/uploads/teams/default-logo.png') ? (
                    <CardMedia
                      component="img"
                      image={logoPreview || teamData.logoUrl}
                      alt="Team Logo"
                      sx={{
                        height: 200,
                        objectFit: 'contain',
                        bgcolor: 'background.default'
                      }}
                    />
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: 200,
                      bgcolor: teamData.secondaryColor
                    }}>
                      <Typography
                        variant="h1"
                        sx={{
                          color: teamData.primaryColor,
                          fontWeight: 'bold'
                        }}
                      >
                        {teamData.teamName.charAt(0).toUpperCase()}
                      </Typography>
                    </Box>
                  )}
                </Card>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    component="label"
                    variant="contained"
                    startIcon={<PhotoCamera />}
                    size={isMobile ? "small" : "medium"}
                  >
                    Upload Logo
                    <VisuallyHiddenInput
                      type="file"
                      accept="image/*"
                      onChange={handleLogoChange}
                    />
                  </Button>

                  <IconButton
                    color="error"
                    onClick={handleRemoveLogo}
                    disabled={!logoPreview && teamData.logoUrl === '/uploads/teams/default-logo.png'}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>

                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                  Recommended size: 500x500 pixels. PNG or JPG format.
                </Typography>
              </Box>
            </Paper>
          </Grid>

          {/* Team Information Section */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Team Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Team Name"
                    name="teamName"
                    value={teamData.teamName}
                    onChange={handleChange}
                    required
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Team Description"
                    name="description"
                    value={teamData.description}
                    onChange={handleChange}
                    multiline
                    rows={3}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Home Ground"
                    name="homeGround"
                    value={teamData.homeGround}
                    onChange={handleChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Founded Year"
                    name="foundedYear"
                    type="number"
                    value={teamData.foundedYear}
                    onChange={handleChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 1800, max: new Date().getFullYear() } }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Team Slogan"
                    name="slogan"
                    value={teamData.slogan}
                    onChange={handleChange}
                    variant="outlined"
                  />
                </Grid>
              </Grid>
            </Paper>

            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Team Colors
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                <Select
                  labelId="color-scheme-label"
                  id="color-scheme"
                  name="colorScheme"
                  value={teamData.colorScheme}
                  onChange={(e) => handleColorChange(e.target.value)}
                  label="Color Scheme"
                >
                  {teamColors.map((color) => (
                    <MenuItem key={color.name} value={color.name}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: color.primary,
                            border: '1px solid rgba(0,0,0,0.1)'
                          }}
                        />
                        {color.name}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Primary Color
                  </Typography>
                  <Box
                    sx={{
                      width: 100,
                      height: 40,
                      bgcolor: teamData.primaryColor,
                      borderRadius: 1,
                      border: '1px solid rgba(0,0,0,0.1)'
                    }}
                  />
                </Box>

                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Secondary Color
                  </Typography>
                  <Box
                    sx={{
                      width: 100,
                      height: 40,
                      bgcolor: teamData.secondaryColor,
                      borderRadius: 1,
                      border: '1px solid rgba(0,0,0,0.1)'
                    }}
                  />
                </Box>
              </Box>

              <Typography variant="caption" color="text.secondary">
                These colors will be used for your team's branding throughout the application.
              </Typography>
            </Paper>
          </Grid>

          {/* Submit Button */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                disabled={saving}
                sx={{ minWidth: 150 }}
              >
                {saving ? <CircularProgress size={24} /> : 'Save Changes'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default TeamSettings;
