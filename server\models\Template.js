const mongoose = require('mongoose');

const RegionSchema = new mongoose.Schema({
  fieldType: {
    type: String,
    required: true,
    enum: [
      // Match Information
      'match_venue', 'match_time', 'player_of_match',

      // Team Names
      'team1_name', 'team2_name',

      // Team Scores
      'team1_runs_scored', 'team2_runs_scored',

      // Team Overs
      'team1_overs_played', 'team2_overs_played',

      // Team Wickets
      'team1_wickets_lost', 'team2_wickets_lost',

      // Team 1 Batsmen
      'team1_batsman_name', 'team1_batsman_runs_scored', 'team1_batsman_balls_faced',

      // Team 1 Bowlers
      'team1_bowler_name', 'team1_bowler_figure', 'team1_bowler_wickets_taken', 'team1_bowler_runs_conceded',

      // Team 2 Batsmen
      'team2_batsman_name', 'team2_batsman_runs_scored', 'team2_batsman_balls_faced',

      // Team 2 Bowlers
      'team2_bowler_name', 'team2_bowler_figure', 'team2_bowler_wickets_taken', 'team2_bowler_runs_conceded'
    ]
  },
  x: {
    type: Number,
    required: true,
    min: 0
  },
  y: {
    type: Number,
    required: true,
    min: 0
  },
  width: {
    type: Number,
    required: true,
    min: 1
  },
  height: {
    type: Number,
    required: true,
    min: 1
  },
  validation: {
    pattern: String,
    maxLength: Number,
    minLength: Number,
    min: Number,
    max: Number,
    required: {
      type: Boolean,
      default: false
    }
  },
  preprocessing: {
    enhance: {
      type: Boolean,
      default: true
    },
    threshold: {
      type: Number,
      default: 0.8
    },
    denoise: {
      type: Boolean,
      default: true
    }
  }
});

const TemplateSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxLength: 100
  },
  description: {
    type: String,
    trim: true,
    maxLength: 500
  },
  regions: [RegionSchema],
  sampleImagePath: {
    type: String,
    default: null
  },
  originalImageData: {
    type: String, // Base64 encoded image data
    default: null
  },
  originalImageName: {
    type: String,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  usageCount: {
    type: Number,
    default: 0
  },
  successRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  lastUsed: {
    type: Date,
    default: null
  },
  version: {
    type: Number,
    default: 1
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    imageWidth: Number,
    imageHeight: Number,
    averageConfidence: Number,
    fieldTypeCounts: {
      type: Map,
      of: Number
    }
  }
}, {
  timestamps: true
});

// Indexes for better query performance (name index already created by unique: true)
TemplateSchema.index({ isActive: 1 });
TemplateSchema.index({ createdBy: 1 });
TemplateSchema.index({ usageCount: -1 });
TemplateSchema.index({ successRate: -1 });

// Virtual for region count
TemplateSchema.virtual('regionCount').get(function() {
  return this.regions.length;
});

// Virtual for field types
TemplateSchema.virtual('fieldTypes').get(function() {
  return [...new Set(this.regions.map(region => region.fieldType))];
});

// Method to update usage statistics
TemplateSchema.methods.updateUsageStats = function(success, confidence) {
  this.usageCount += 1;
  this.lastUsed = new Date();

  if (success) {
    // Update success rate using weighted average
    const totalAttempts = this.usageCount;
    const previousSuccesses = Math.round((this.successRate / 100) * (totalAttempts - 1));
    const newSuccesses = previousSuccesses + 1;
    this.successRate = (newSuccesses / totalAttempts) * 100;
  }

  if (confidence && this.metadata) {
    // Update average confidence
    const previousAvg = this.metadata.averageConfidence || 0;
    const newAvg = ((previousAvg * (this.usageCount - 1)) + confidence) / this.usageCount;
    this.metadata.averageConfidence = newAvg;
  }

  return this.save();
};

// Method to validate template regions
TemplateSchema.methods.validateRegions = function() {
  const errors = [];
  const fieldTypeCounts = {};

  this.regions.forEach((region, index) => {
    // Count field types
    fieldTypeCounts[region.fieldType] = (fieldTypeCounts[region.fieldType] || 0) + 1;

    // Check for overlapping regions
    this.regions.forEach((otherRegion, otherIndex) => {
      if (index !== otherIndex) {
        if (this.regionsOverlap(region, otherRegion)) {
          errors.push(`Region ${index} overlaps with region ${otherIndex}`);
        }
      }
    });

    // Validate region bounds
    if (region.x < 0 || region.y < 0 || region.width <= 0 || region.height <= 0) {
      errors.push(`Region ${index} has invalid bounds`);
    }
  });

  // Check for required field types
  const requiredFields = ['team1_name', 'team2_name', 'team1_score', 'team2_score'];
  requiredFields.forEach(field => {
    if (!fieldTypeCounts[field]) {
      errors.push(`Missing required field type: ${field}`);
    }
  });

  // Warn about duplicate field types (except player fields)
  const allowMultiple = [
    'team1_batsman_name', 'team1_batsman_runs', 'team1_batsman_balls',
    'team2_batsman_name', 'team2_batsman_runs', 'team2_batsman_balls',
    'team1_bowler_name', 'team1_bowler_figures',
    'team2_bowler_name', 'team2_bowler_figures'
  ];

  Object.entries(fieldTypeCounts).forEach(([fieldType, count]) => {
    if (count > 1 && !allowMultiple.includes(fieldType)) {
      errors.push(`Duplicate field type: ${fieldType} (${count} instances)`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    fieldTypeCounts
  };
};

// Helper method to check if two regions overlap
TemplateSchema.methods.regionsOverlap = function(region1, region2) {
  return !(region1.x + region1.width <= region2.x ||
           region2.x + region2.width <= region1.x ||
           region1.y + region1.height <= region2.y ||
           region2.y + region2.height <= region1.y);
};

// Method to get regions by field type
TemplateSchema.methods.getRegionsByType = function(fieldType) {
  return this.regions.filter(region => region.fieldType === fieldType);
};

// Method to get regions for a specific team
TemplateSchema.methods.getTeamRegions = function(teamNumber) {
  const teamPrefix = `team${teamNumber}_`;
  return this.regions.filter(region => region.fieldType.startsWith(teamPrefix));
};

// Static method to find templates by field type
TemplateSchema.statics.findByFieldType = function(fieldType) {
  return this.find({
    'regions.fieldType': fieldType,
    isActive: true
  });
};

// Static method to get template usage statistics
TemplateSchema.statics.getUsageStats = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: null,
        totalTemplates: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        averageSuccessRate: { $avg: '$successRate' },
        mostUsed: { $max: '$usageCount' }
      }
    }
  ]);
};

// Pre-save middleware to update metadata
TemplateSchema.pre('save', function(next) {
  if (this.isModified('regions')) {
    // Update field type counts
    const fieldTypeCounts = {};
    this.regions.forEach(region => {
      fieldTypeCounts[region.fieldType] = (fieldTypeCounts[region.fieldType] || 0) + 1;
    });

    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata.fieldTypeCounts = fieldTypeCounts;
  }

  next();
});

// Export the model
module.exports = mongoose.model('Template', TemplateSchema);
