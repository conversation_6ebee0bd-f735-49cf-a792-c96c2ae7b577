const mongoose = require('mongoose');

// Define bid schema for tracking auction bids
const bidSchema = new mongoose.Schema({
  bidder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  bidderName: {
    type: String,
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  time: {
    type: Date,
    default: Date.now
  }
});

// Define auction schema
const auctionSchema = new mongoose.Schema({
  player: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Player',
    required: true
  },
  startingPrice: {
    type: Number,
    required: true,
    min: 0,
    default: 1000
  },
  currentBid: {
    type: Number,
    min: 0,
    default: function() {
      return this.startingPrice;
    }
  },
  currentBidder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  minimumBidIncrement: {
    type: Number,
    required: true,
    min: 10,
    default: 100
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true,
    validate: {
      validator: function(value) {
        return value > this.startTime;
      },
      message: 'End time must be after start time'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  reservePrice: {
    type: Number,
    min: 0,
    default: 0
  },
  description: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    enum: ['scheduled', 'live', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  bids: [bidSchema],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // When tournament functionality is implemented, this field will be used
  tournament: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Tournament',
    default: null
  }
}, {
  timestamps: true
});

// Pre-save middleware to update auction status based on time
auctionSchema.pre('save', function(next) {
  const now = new Date();

  if (!this.isActive) {
    this.status = 'cancelled';
  } else if (now < this.startTime) {
    this.status = 'scheduled';
  } else if (now >= this.startTime && now < this.endTime) {
    this.status = 'live';
  } else if (now >= this.endTime) {
    this.status = 'completed';
  }

  next();
});

// Method to check if an auction is active and live
auctionSchema.methods.isLive = function() {
  const now = new Date();
  console.log('isLive check:');
  console.log('- isActive:', this.isActive);
  console.log('- now:', now);
  console.log('- startTime:', this.startTime);
  console.log('- endTime:', this.endTime);
  console.log('- now >= startTime:', now >= this.startTime);
  console.log('- now < endTime:', now < this.endTime);

  const result = this.isActive && now >= this.startTime && now < this.endTime;
  console.log('isLive result:', result);
  return result;
};

// Method to check if a bid is valid
auctionSchema.methods.isValidBid = function(amount, bidderId) {
  console.log('isValidBid called with amount:', amount, 'bidderId:', bidderId);
  console.log('Auction status:', this.status);
  console.log('Auction isActive:', this.isActive);
  console.log('Auction startTime:', this.startTime);
  console.log('Auction endTime:', this.endTime);
  console.log('Current time:', new Date());

  // Check if auction is live
  const isLiveResult = this.isLive();
  console.log('isLive result:', isLiveResult);
  if (!isLiveResult) {
    console.log('Auction is not live');
    return { valid: false, reason: 'Auction is not live' };
  }

  // Check if bid amount is greater than current bid + minimum increment
  console.log('Current bid:', this.currentBid);
  console.log('Minimum increment:', this.minimumBidIncrement);
  console.log('Required minimum bid:', this.currentBid + this.minimumBidIncrement);
  if (amount < this.currentBid + this.minimumBidIncrement) {
    console.log('Bid amount too low');
    return {
      valid: false,
      reason: `Bid must be at least ${this.currentBid + this.minimumBidIncrement} credits`
    };
  }

  // Check if bidder is not already the highest bidder
  console.log('Current bidder:', this.currentBidder);
  if (this.currentBidder && this.currentBidder.toString() === bidderId.toString()) {
    console.log('Bidder is already highest bidder');
    return { valid: false, reason: 'You are already the highest bidder' };
  }

  console.log('Bid is valid');
  return { valid: true };
};

const Auction = mongoose.model('Auction', auctionSchema);

module.exports = Auction;
