import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  TextField
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ContentCopy as CopyIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

/**
 * OCR Data Viewer Component
 * 
 * Displays extracted OCR data in a structured, readable format
 */
const OCRDataViewer = ({ data, onDataChange }) => {
  const [copiedField, setCopiedField] = useState(null);

  const copyToClipboard = (text, fieldName) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    });
  };

  const renderValue = (value, fieldName) => {
    if (value === null || value === undefined) {
      return <Typography variant="body2" color="text.secondary">Not detected</Typography>;
    }

    if (typeof value === 'object' && !Array.isArray(value)) {
      return (
        <Box component="pre" sx={{ fontSize: '0.75rem', whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(value, null, 2)}
        </Box>
      );
    }

    if (Array.isArray(value)) {
      return (
        <Box>
          {value.length === 0 ? (
            <Typography variant="body2" color="text.secondary">No data</Typography>
          ) : (
            <TableContainer component={Paper} variant="outlined" sx={{ mt: 1 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    {Object.keys(value[0] || {}).filter(key => key !== 'economy' && key !== 'verified' && key !== 'originalRuns' && key !== 'originalBalls').map(key => (
                      <TableCell key={key}>{key}</TableCell>
                    ))}
                    {value.some(item => item.verified) && (
                      <TableCell>Status</TableCell>
                    )}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {value.map((item, index) => (
                    <TableRow key={index} sx={item.verified ? { bgcolor: 'success.50' } : {}}>
                      {Object.entries(item).map(([key, val], i) => (
                        key !== 'verified' && key !== 'originalRuns' && key !== 'originalBalls' && key !== 'economy' ? (
                          <TableCell key={i}>{val}
                          </TableCell>
                        ) : null
                      ))}
                      {value.some(item => item.verified) && (
                        <TableCell>
                          {item.verified && (
                            <Chip 
                              icon={<SuccessIcon fontSize="small" />}
                              label="Verified" 
                              color="success" 
                              size="small"
                              variant="outlined"
                            />
                          )}
                          {item.originalRuns && item.originalRuns !== item.runs && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              Original: {item.originalRuns}({item.originalBalls})
                            </Typography>
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Box>
      );
    }

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography variant="body2">{String(value)}</Typography>
        <Tooltip title={copiedField === fieldName ? 'Copied!' : 'Copy to clipboard'}>
          <IconButton 
            size="small" 
            onClick={() => copyToClipboard(String(value), fieldName)}
            color={copiedField === fieldName ? 'success' : 'default'}
          >
            {copiedField === fieldName ? <SuccessIcon fontSize="small" /> : <CopyIcon fontSize="small" />}
          </IconButton>
        </Tooltip>
      </Box>
    );
  };

  const renderSection = (title, fields, defaultExpanded = false) => (
    <Accordion defaultExpanded={defaultExpanded}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle1" fontWeight="bold">{title}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {fields.map(({ label, value, key }) => (
            <Box key={key}>
              <Typography variant="subtitle2" color="primary" gutterBottom>
                {label}
              </Typography>
              {renderValue(value, key)}
            </Box>
          ))}
        </Box>
      </AccordionDetails>
    </Accordion>
  );

  if (!data) {
    return (
      <Paper sx={{ p: 2, textAlign: 'center' }}>
        <ErrorIcon color="error" sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="h6" color="error">No OCR Data Available</Typography>
      </Paper>
    );
  }
  
  // Check if this is a failed OCR result
  const isOcrFailed = !data.success;

  return (
    <Box>
      {/* Status Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <Chip 
          icon={data.success ? <SuccessIcon /> : <ErrorIcon />}
          label={data.success ? 'OCR Success' : 'OCR Failed'} 
          color={data.success ? 'success' : 'error'} 
          variant="outlined"
        />
        {data.ocrStatus && (
          <Chip label={data.ocrStatus} size="small" />
        )}
        {data.ocrMethod && (
          <Chip 
            label={data.ocrMethod === 'ocrspace-primary' ? 'OCR.Space' : 
                  data.ocrMethod === 'paddleocr-fallback' ? 'PaddleOCR (Fallback)' : 
                  'Manual Entry Required'} 
            size="small"
            color={data.ocrMethod === 'manual-entry-required' ? 'error' : 'default'}
          />
        )}
        {data.verification && data.verification.method === 'google-vision' && (
          <Chip 
            icon={<SuccessIcon />}
            label={`${data.verification.playersVerified} Players Verified`} 
            color="success" 
            variant="outlined"
            size="small"
          />
        )}
      </Box>

      {/* OCR Message */}
      {data.ocrMessage && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: isOcrFailed ? 'error.50' : 'info.50' }}>
          <Typography variant="body2" color={isOcrFailed ? 'error.main' : 'info.main'}>
            {data.ocrMessage}
          </Typography>
          {isOcrFailed && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              You can still proceed by manually entering the match details below.
            </Typography>
          )}
        </Paper>
      )}

      {/* Match Information */}
      {renderSection('Match Information', [
        { label: 'Team 1', value: data.team1, key: 'team1' },
        { label: 'Team 2', value: data.team2, key: 'team2' },
        { label: 'Venue', value: data.venue, key: 'venue' },
        { label: 'Result', value: data.resultText, key: 'resultText' },
        { label: 'Player of Match', value: data.playerOfMatch, key: 'playerOfMatch' }
      ], true)}

      {/* Scores */}
      {renderSection('Scores', [
        { label: 'Team 1 Score', value: data.team1Score, key: 'team1Score' },
        { label: 'Team 2 Score', value: data.team2Score, key: 'team2Score' }
      ])}

      {/* Verification Information */}
      {data.verification && renderSection('Verification Information', [
        { label: 'Method', value: data.verification.method, key: 'verificationMethod' },
        { label: 'Timestamp', value: data.verification.timestamp, key: 'verificationTimestamp' },
        { label: 'Players Verified', value: data.verification.playersVerified, key: 'playersVerified' }
      ])}

      {/* Player Statistics */}
      {renderSection('Player Statistics', [
        { label: 'Team 1 Batsmen', value: data.team1Batsmen, key: 'team1Batsmen' },
        { label: 'Team 1 Bowlers', value: data.team1Bowlers, key: 'team1Bowlers' },
        { label: 'Team 2 Batsmen', value: data.team2Batsmen, key: 'team2Batsmen' },
        { label: 'Team 2 Bowlers', value: data.team2Bowlers, key: 'team2Bowlers' }
      ])}

      {/* Raw Text */}
      {data.rawText && renderSection('Raw Extracted Text', [
        { label: 'Full Text', value: data.rawText, key: 'rawText' }
      ])}

      {/* Technical Details */}
      {data.texts && renderSection('Technical Details', [
        { label: 'Text Regions', value: `${data.texts.length} regions detected`, key: 'textCount' },
        { label: 'Extraction Method', value: data.extractionMethod || 'Advanced Parser', key: 'extractionMethod' }
      ])}

      {/* Raw JSON */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1" fontWeight="bold">Raw JSON Data</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Box 
              component="pre" 
              sx={{ 
                fontSize: '0.75rem', 
                whiteSpace: 'pre-wrap',
                maxHeight: 300,
                overflow: 'auto'
              }}
            >
              {JSON.stringify(data, null, 2)}
            </Box>
          </Paper>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default OCRDataViewer;
