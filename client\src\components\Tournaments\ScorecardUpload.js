import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Paper,
  IconButton,
  Grid,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  Image as ImageIcon,
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { uploadScorecard } from '../../services/scorecardService';
import { useAuth } from '../../hooks/useAuth';
import EnhancedMatchForm from './EnhancedMatchForm';

const ScorecardUpload = ({ open, onClose, tournamentId, matchId, onUploadSuccess, tournament }) => {
  useAuth(); // Called for its side effects if any, or if other hooks depend on it.
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [serverImageUrl, setServerImageUrl] = useState(null); // Store the server-returned image URL
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [ocrData, setOcrData] = useState(null);
  const [ocrError, setOcrError] = useState(null);
  const [showMatchForm, setShowMatchForm] = useState(false);

  // Log props on component mount
  useEffect(() => {
    console.log('ScorecardUpload component mounted with props:', { tournamentId, matchId, tournament });
  }, [tournamentId, matchId, tournament]);

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Check file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
    setError(null);

    // Create preview URL
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewUrl(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a file to upload');
      return;
    }

    // Validate tournament ID
    if (!tournamentId) {
      setError('Invalid tournament ID. Please try again later.');
      return;
    }

    try {
      setUploading(true);
      setError(null);
      setSuccess(null);
      setOcrData(null);
      setOcrError(null);

      console.log(`Uploading scorecard for tournament: ${tournamentId}, match: ${matchId || 'none'}`);

      const result = await uploadScorecard(tournamentId, matchId, selectedFile);

      // Debug: Log the complete result to understand the structure
      console.log('Upload result:', result);

      // Store the server-returned image URL
      if (result.scorecard?.url) {
        setServerImageUrl(result.scorecard.url);
        console.log('Server image URL stored:', result.scorecard.url);
      } else {
        console.log('No scorecard.url found in result. Result structure:', Object.keys(result));
      }

      // Check if OCR data is available
      if (result.ocrData) {
        setOcrData(result.ocrData);
        setSuccess('Scorecard uploaded and processed successfully');
      } else if (result.ocrError) {
        setOcrError(result.ocrError);
        setSuccess('Scorecard uploaded successfully, but OCR processing failed');
      } else {
        setSuccess('Scorecard uploaded successfully');
      }

      // Notify parent component
      if (onUploadSuccess) {
        onUploadSuccess(result);
      }

      // Don't close the dialog automatically if we have OCR data to show
      if (!result.ocrData) {
        // Close dialog after a short delay
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (err) {
      console.error('Error uploading scorecard:', err);

      // Check if we should suggest using the guided capture method
      if (err.fallbackRequired) {
        setError(`${err.message || 'OCR processing failed.'} Please try using the guided capture method for better results.`);
      } else if (err.response?.status === 404) {
        setError('The server endpoint for processing scorecards was not found. Please contact the administrator.');
      } else {
        setError(err.message || 'Failed to upload scorecard. Please try again.');
      }
    } finally {
      setUploading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!uploading) {
      setSelectedFile(null);
      setPreviewUrl(null);
      setError(null);
      setSuccess(null);
      setOcrData(null);
      setOcrError(null);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Upload Scorecard</Typography>
          <IconButton onClick={handleClose} disabled={uploading}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {ocrError && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            OCR Processing Error: {ocrError}
          </Alert>
        )}

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body2" color="text.secondary" paragraph>
              Upload a screenshot or photo of the match scorecard. This will be used to verify the match result.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              <strong>Requirements:</strong>
            </Typography>
            <Box component="ul" sx={{ mt: 0, mb: 2, pl: 4 }}>
              <li>
                <Typography variant="body2" color="text.secondary">
                  Image must be clear and readable
                </Typography>
              </li>
              <li>
                <Typography variant="body2" color="text.secondary">
                  Must show the final score of both teams
                </Typography>
              </li>
              <li>
                <Typography variant="body2" color="text.secondary">
                  Maximum file size: 5MB
                </Typography>
              </li>
              <li>
                <Typography variant="body2" color="text.secondary">
                  Supported formats: JPG, PNG, GIF
                </Typography>
              </li>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Box
              sx={{
                border: '2px dashed #ccc',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                bgcolor: 'background.paper',
                cursor: 'pointer',
                '&:hover': {
                  bgcolor: 'action.hover'
                }
              }}
              component="label"
            >
              <input
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                onChange={handleFileSelect}
                disabled={uploading}
              />

              {!previewUrl ? (
                <Box sx={{ py: 3 }}>
                  <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                  <Typography variant="body1" gutterBottom>
                    Click or drag to upload scorecard image
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    JPG, PNG or GIF (max 5MB)
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ position: 'relative' }}>
                  <img
                    src={previewUrl}
                    alt="Scorecard preview"
                    style={{
                      maxWidth: '100%',
                      maxHeight: '300px',
                      objectFit: 'contain'
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      bgcolor: 'rgba(0,0,0,0.5)',
                      borderRadius: '0 0 0 8px',
                      p: 0.5
                    }}
                  >
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedFile(null);
                        setPreviewUrl(null);
                      }}
                      sx={{ color: 'white' }}
                      disabled={uploading}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              )}
            </Box>
          </Grid>

          {selectedFile && (
            <Grid item xs={12}>
              <Paper sx={{ p: 2, bgcolor: 'background.paper' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ImageIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          )}

          {/* OCR Data Display */}
          {ocrData && (
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Extracted Scorecard Data
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<EditIcon />}
                  onClick={() => {
                    console.log('Create Match button clicked, tournament:', tournament);
                    setShowMatchForm(true);
                  }}
                >
                  Create Match
                </Button>
              </Box>

              <Grid container spacing={2}>
                {/* Team Scores */}
                <Grid item xs={12} md={6}>
                  <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {ocrData.team1 || 'Team 1'}
                    </Typography>
                    <Typography variant="h5">
                      {ocrData.team1Score?.runs || 0}-{ocrData.team1Score?.wickets || 0}
                    </Typography>
                    <Typography variant="body2">
                      Overs: {ocrData.team1Score?.overs || '0.0'}
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {ocrData.team2 || 'Team 2'}
                    </Typography>
                    <Typography variant="h5">
                      {ocrData.team2Score?.runs || 0}-{ocrData.team2Score?.wickets || 0}
                    </Typography>
                    <Typography variant="body2">
                      Overs: {ocrData.team2Score?.overs || '0.0'}
                    </Typography>
                  </Paper>
                </Grid>

                {/* Match Result */}
                <Grid item xs={12}>
                  <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Match Result
                    </Typography>
                    <Typography variant="body1">
                      {ocrData.resultText || 'Result not available'}
                    </Typography>
                    <Typography variant="body2">
                      Player of the Match: {ocrData.playerOfMatch || 'Not specified'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Venue: {ocrData.venue || 'Unknown Venue'}
                    </Typography>
                  </Paper>
                </Grid>

                {/* Player Statistics */}
                <Grid item xs={12}>
                  <Accordion defaultExpanded>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="subtitle1">Player Statistics</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      {/* Team 1 Batting */}
                      <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 1 }}>
                        {ocrData.team1 || 'Team 1'} - Batting
                      </Typography>
                      <Box sx={{ overflowX: 'auto' }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Batsman</TableCell>
                              <TableCell align="right">Runs</TableCell>
                              <TableCell align="right">Balls</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(ocrData.team1Batsmen || []).map((batsman, index) => (
                              <TableRow key={index}>
                                <TableCell>{batsman.name}</TableCell>
                                <TableCell align="right">{batsman.runs}</TableCell>
                                <TableCell align="right">{batsman.balls}</TableCell>
                              </TableRow>
                            ))}
                            {(ocrData.team1Batsmen || []).length === 0 && (
                              <TableRow>
                                <TableCell colSpan={3} align="center">No batsmen data available</TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </Box>

                      {/* Team 1 Bowling */}
                      <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {ocrData.team1 || 'Team 1'} - Bowling
                      </Typography>
                      <Box sx={{ overflowX: 'auto' }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Bowler</TableCell>
                              <TableCell align="right">W</TableCell>
                              <TableCell align="right">R</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(ocrData.team1Bowlers || []).map((bowler, index) => (
                              <TableRow key={index}>
                                <TableCell>{bowler.name}</TableCell>
                                <TableCell align="right">{bowler.wickets}</TableCell>
                                <TableCell align="right">{bowler.runs}</TableCell>
                              </TableRow>
                            ))}
                            {(ocrData.team1Bowlers || []).length === 0 && (
                              <TableRow>
                                <TableCell colSpan={3} align="center">No bowlers data available</TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </Box>

                      {/* Team 2 Batting */}
                      <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {ocrData.team2 || 'Team 2'} - Batting
                      </Typography>
                      <Box sx={{ overflowX: 'auto' }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Batsman</TableCell>
                              <TableCell align="right">Runs</TableCell>
                              <TableCell align="right">Balls</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(ocrData.team2Batsmen || []).map((batsman, index) => (
                              <TableRow key={index}>
                                <TableCell>{batsman.name}</TableCell>
                                <TableCell align="right">{batsman.runs}</TableCell>
                                <TableCell align="right">{batsman.balls}</TableCell>
                              </TableRow>
                            ))}
                            {(ocrData.team2Batsmen || []).length === 0 && (
                              <TableRow>
                                <TableCell colSpan={3} align="center">No batsmen data available</TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </Box>

                      {/* Team 2 Bowling */}
                      <Typography variant="body2" sx={{ fontWeight: 'bold', mt: 2 }}>
                        {ocrData.team2 || 'Team 2'} - Bowling
                      </Typography>
                      <Box sx={{ overflowX: 'auto' }}>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Bowler</TableCell>
                              <TableCell align="right">W</TableCell>
                              <TableCell align="right">R</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {(ocrData.team2Bowlers || []).map((bowler, index) => (
                              <TableRow key={index}>
                                <TableCell>{bowler.name}</TableCell>
                                <TableCell align="right">{bowler.wickets}</TableCell>
                                <TableCell align="right">{bowler.runs}</TableCell>
                              </TableRow>
                            ))}
                            {(ocrData.team2Bowlers || []).length === 0 && (
                              <TableRow>
                                <TableCell colSpan={3} align="center">No bowlers data available</TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
                        Extraction method: {ocrData.extractionMethod}
                      </Typography>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              </Grid>
            </Grid>
          )}

          {/* Enhanced Match Form */}
          <EnhancedMatchForm
            open={showMatchForm}
            onClose={() => setShowMatchForm(false)}
            tournament={tournament || { _id: tournamentId, registeredTeams: [] }}
            onMatchAdded={(result) => {
              setShowMatchForm(false);
              if (onUploadSuccess) {
                onUploadSuccess(result);
              }
              // Close the upload dialog after a short delay
              setTimeout(() => {
                onClose();
              }, 1500);
            }}
            initialData={ocrData}
            capturedImage={serverImageUrl || previewUrl} // Use server URL if available, fallback to preview
          />
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={uploading}>
          Cancel
        </Button>
        <Button
          onClick={handleUpload}
          variant="contained"
          color="primary"
          disabled={uploading || !selectedFile}
          startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
        >
          {uploading ? 'Uploading...' : 'Upload Scorecard'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ScorecardUpload;
