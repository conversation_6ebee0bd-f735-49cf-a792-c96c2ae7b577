<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cricket Scorecard OCR Comparison</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    .image-preview {
      max-height: 300px;
      max-width: 100%;
      margin-top: 10px;
    }
    .result-card {
      margin-bottom: 20px;
    }
    .method-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .completeness-bar {
      height: 20px;
      margin-top: 5px;
    }
    .team-card {
      margin-bottom: 15px;
    }
    .raw-text {
      max-height: 300px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .loading-spinner {
      display: inline-block;
      width: 1rem;
      height: 1rem;
      border-radius: 50%;
      border: 0.15rem solid currentColor;
      border-right-color: transparent;
      animation: spinner-border .75s linear infinite;
    }
    @keyframes spinner-border {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container py-4">
    <h1 class="mb-4">Cricket Scorecard OCR Comparison</h1>
    
    <div class="card mb-4">
      <div class="card-body">
        <form id="comparison-form">
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="scorecard-select" class="form-label">Select Stored Scorecard</label>
                <select class="form-select" id="scorecard-select">
                  <option value="">-- Select a scorecard --</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="image-upload" class="form-label">Or Upload New Image</label>
                <input class="form-control" type="file" id="image-upload" accept="image/*">
              </div>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Select OCR Methods to Compare</label>
            <div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="paddle-check" checked>
                <label class="form-check-label" for="paddle-check">PaddleOCR</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="google-check" checked>
                <label class="form-check-label" for="google-check">Google Vision API</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="tesseract-check" checked>
                <label class="form-check-label" for="tesseract-check">Tesseract.js</label>
              </div>
            </div>
          </div>
          
          <div id="error-alert" class="alert alert-danger d-none"></div>
          
          <button type="submit" class="btn btn-primary" id="compare-btn">
            Compare OCR Methods
          </button>
        </form>
      </div>
    </div>
    
    <div id="image-preview-card" class="card mb-4 d-none">
      <div class="card-header">Selected Image</div>
      <div class="card-body text-center">
        <img id="image-preview" class="image-preview" alt="Selected Scorecard">
      </div>
    </div>
    
    <div id="results-card" class="card d-none">
      <div class="card-header">Comparison Results</div>
      <div class="card-body">
        <ul class="nav nav-tabs" id="results-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">Summary</button>
          </li>
          <li class="nav-item d-none" role="presentation" id="paddle-tab-item">
            <button class="nav-link" id="paddle-tab" data-bs-toggle="tab" data-bs-target="#paddle" type="button" role="tab">PaddleOCR</button>
          </li>
          <li class="nav-item d-none" role="presentation" id="google-tab-item">
            <button class="nav-link" id="google-tab" data-bs-toggle="tab" data-bs-target="#google" type="button" role="tab">Google Vision</button>
          </li>
          <li class="nav-item d-none" role="presentation" id="tesseract-tab-item">
            <button class="nav-link" id="tesseract-tab" data-bs-toggle="tab" data-bs-target="#tesseract" type="button" role="tab">Tesseract.js</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="raw-tab" data-bs-toggle="tab" data-bs-target="#raw" type="button" role="tab">Raw Text</button>
          </li>
        </ul>
        
        <div class="tab-content" id="results-tab-content">
          <div class="tab-pane fade show active" id="summary" role="tabpanel">
            <div class="table-responsive mt-3">
              <table class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>Method</th>
                    <th>Team 1</th>
                    <th>Team 2</th>
                    <th>Score 1</th>
                    <th>Score 2</th>
                    <th>Processing Time</th>
                    <th>Completeness</th>
                  </tr>
                </thead>
                <tbody id="summary-table-body">
                  <!-- Results will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
          
          <div class="tab-pane fade" id="paddle" role="tabpanel"></div>
          <div class="tab-pane fade" id="google" role="tabpanel"></div>
          <div class="tab-pane fade" id="tesseract" role="tabpanel"></div>
          
          <div class="tab-pane fade" id="raw" role="tabpanel">
            <div class="mt-3">
              <ul class="nav nav-tabs" id="raw-tabs" role="tablist">
                <li class="nav-item d-none" role="presentation" id="paddle-raw-tab-item">
                  <button class="nav-link active" id="paddle-raw-tab" data-bs-toggle="tab" data-bs-target="#paddle-raw" type="button" role="tab">PaddleOCR</button>
                </li>
                <li class="nav-item d-none" role="presentation" id="google-raw-tab-item">
                  <button class="nav-link" id="google-raw-tab" data-bs-toggle="tab" data-bs-target="#google-raw" type="button" role="tab">Google Vision</button>
                </li>
                <li class="nav-item d-none" role="presentation" id="tesseract-raw-tab-item">
                  <button class="nav-link" id="tesseract-raw-tab" data-bs-toggle="tab" data-bs-target="#tesseract-raw" type="button" role="tab">Tesseract.js</button>
                </li>
              </ul>
              
              <div class="tab-content" id="raw-tab-content">
                <div class="tab-pane fade show active" id="paddle-raw" role="tabpanel">
                  <pre id="paddle-raw-text" class="raw-text mt-3"></pre>
                </div>
                <div class="tab-pane fade" id="google-raw" role="tabpanel">
                  <pre id="google-raw-text" class="raw-text mt-3"></pre>
                </div>
                <div class="tab-pane fade" id="tesseract-raw" role="tabpanel">
                  <pre id="tesseract-raw-text" class="raw-text mt-3"></pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const form = document.getElementById('comparison-form');
      const scorecardSelect = document.getElementById('scorecard-select');
      const imageUpload = document.getElementById('image-upload');
      const paddleCheck = document.getElementById('paddle-check');
      const googleCheck = document.getElementById('google-check');
      const tesseractCheck = document.getElementById('tesseract-check');
      const compareBtn = document.getElementById('compare-btn');
      const errorAlert = document.getElementById('error-alert');
      const imagePreviewCard = document.getElementById('image-preview-card');
      const imagePreview = document.getElementById('image-preview');
      const resultsCard = document.getElementById('results-card');
      
      // Fetch available scorecards
      fetch('/api/ocr-comparison/scorecards')
        .then(response => response.json())
        .then(data => {
          data.scorecards.forEach(scorecard => {
            const option = document.createElement('option');
            option.value = scorecard.path;
            option.textContent = scorecard.name;
            scorecardSelect.appendChild(option);
          });
        })
        .catch(error => {
          console.error('Error fetching scorecards:', error);
          showError('Failed to fetch scorecards');
        });
      
      // Handle scorecard selection
      scorecardSelect.addEventListener('change', function() {
        if (this.value) {
          imageUpload.value = '';
          imagePreviewCard.classList.remove('d-none');
          imagePreview.src = this.value;
        } else {
          imagePreviewCard.classList.add('d-none');
        }
      });
      
      // Handle image upload
      imageUpload.addEventListener('change', function() {
        if (this.files && this.files[0]) {
          scorecardSelect.value = '';
          imagePreviewCard.classList.remove('d-none');
          imagePreview.src = URL.createObjectURL(this.files[0]);
        } else {
          imagePreviewCard.classList.add('d-none');
        }
      });
      
      // Handle form submission
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate form
        if (!scorecardSelect.value && !imageUpload.files[0]) {
          showError('Please select a scorecard or upload an image');
          return;
        }
        
        const selectedMethods = [];
        if (paddleCheck.checked) selectedMethods.push('paddle');
        if (googleCheck.checked) selectedMethods.push('google');
        if (tesseractCheck.checked) selectedMethods.push('tesseract');
        
        if (selectedMethods.length === 0) {
          showError('Please select at least one OCR method');
          return;
        }
        
        // Hide error and show loading state
        errorAlert.classList.add('d-none');
        compareBtn.disabled = true;
        compareBtn.innerHTML = '<span class="loading-spinner"></span> Processing...';
        
        // Prepare form data
        const formData = new FormData();
        
        if (imageUpload.files[0]) {
          formData.append('image', imageUpload.files[0]);
        } else {
          formData.append('scorecardPath', scorecardSelect.value);
        }
        
        formData.append('methods', selectedMethods.join(','));
        
        // Send request
        fetch('/api/ocr-comparison/compare', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to compare OCR methods');
            }
            return response.json();
          })
          .then(data => {
            displayResults(data);
          })
          .catch(error => {
            console.error('Error comparing OCR methods:', error);
            showError(error.message || 'Failed to compare OCR methods');
          })
          .finally(() => {
            // Reset loading state
            compareBtn.disabled = false;
            compareBtn.innerHTML = 'Compare OCR Methods';
          });
      });
      
      // Show error message
      function showError(message) {
        errorAlert.textContent = message;
        errorAlert.classList.remove('d-none');
      }
      
      // Display results
      function displayResults(data) {
        // Show results card
        resultsCard.classList.remove('d-none');
        
        // Clear previous results
        document.getElementById('summary-table-body').innerHTML = '';
        document.getElementById('paddle').innerHTML = '';
        document.getElementById('google').innerHTML = '';
        document.getElementById('tesseract').innerHTML = '';
        document.getElementById('paddle-raw-text').textContent = '';
        document.getElementById('google-raw-text').textContent = '';
        document.getElementById('tesseract-raw-text').textContent = '';
        
        // Hide all method tabs
        document.getElementById('paddle-tab-item').classList.add('d-none');
        document.getElementById('google-tab-item').classList.add('d-none');
        document.getElementById('tesseract-tab-item').classList.add('d-none');
        document.getElementById('paddle-raw-tab-item').classList.add('d-none');
        document.getElementById('google-raw-tab-item').classList.add('d-none');
        document.getElementById('tesseract-raw-tab-item').classList.add('d-none');
        
        // Process each method's results
        Object.entries(data.results).forEach(([method, result], index) => {
          // Add to summary table
          addToSummaryTable(method, result);
          
          // Show method tab
          document.getElementById(`${method}-tab-item`).classList.remove('d-none');
          document.getElementById(`${method}-raw-tab-item`).classList.remove('d-none');
          
          // Set raw text
          document.getElementById(`${method}-raw-text`).textContent = result.rawText || 'No raw text available';
          
          // Add detailed results
          if (!result.error) {
            addDetailedResults(method, result);
          } else {
            document.getElementById(method).innerHTML = `
              <div class="alert alert-danger mt-3">
                ${result.error}
              </div>
            `;
          }
          
          // Set first tab as active in raw text tabs
          if (index === 0) {
            document.getElementById(`${method}-raw-tab`).classList.add('active');
            document.getElementById(`${method}-raw`).classList.add('show', 'active');
          } else {
            document.getElementById(`${method}-raw-tab`).classList.remove('active');
            document.getElementById(`${method}-raw`).classList.remove('show', 'active');
          }
        });
      }
      
      // Add result to summary table
      function addToSummaryTable(method, result) {
        const methodNames = {
          paddle: 'PaddleOCR',
          google: 'Google Vision',
          tesseract: 'Tesseract.js'
        };
        
        const row = document.createElement('tr');
        
        if (result.error) {
          row.innerHTML = `
            <td>${methodNames[method]}</td>
            <td class="text-danger">Error</td>
            <td class="text-danger">Error</td>
            <td class="text-danger">Error</td>
            <td class="text-danger">Error</td>
            <td>N/A</td>
            <td>0%</td>
          `;
        } else {
          const team1 = result.team1 || 'N/A';
          const team2 = result.team2 || 'N/A';
          const score1 = renderTeamScore(result.team1Score);
          const score2 = renderTeamScore(result.team2Score);
          const processingTime = `${result.processingTime || 0} ms`;
          const completenessScore = result.completenessScore || 0;
          
          row.innerHTML = `
            <td>${methodNames[method]}</td>
            <td>${team1}</td>
            <td>${team2}</td>
            <td>${score1}</td>
            <td>${score2}</td>
            <td>${processingTime}</td>
            <td>${renderCompletenessScore(completenessScore)}</td>
          `;
        }
        
        document.getElementById('summary-table-body').appendChild(row);
      }
      
      // Add detailed results
      function addDetailedResults(method, result) {
        const container = document.getElementById(method);
        
        container.innerHTML = `
          <div class="mt-3">
            <h4>Extracted Data</h4>
            <div class="row">
              <div class="col-md-6">
                <div class="card team-card">
                  <div class="card-header">Team 1: ${result.team1 || 'N/A'}</div>
                  <div class="card-body">
                    <p><strong>Score:</strong> ${renderTeamScore(result.team1Score)}</p>
                    <p><strong>Batsmen:</strong></p>
                    ${renderPlayerList(result.team1Batsmen)}
                    <p><strong>Bowlers:</strong></p>
                    ${renderPlayerList(result.team1Bowlers)}
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card team-card">
                  <div class="card-header">Team 2: ${result.team2 || 'N/A'}</div>
                  <div class="card-body">
                    <p><strong>Score:</strong> ${renderTeamScore(result.team2Score)}</p>
                    <p><strong>Batsmen:</strong></p>
                    ${renderPlayerList(result.team2Batsmen)}
                    <p><strong>Bowlers:</strong></p>
                    ${renderPlayerList(result.team2Bowlers)}
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <div class="card">
                  <div class="card-header">Additional Information</div>
                  <div class="card-body">
                    <p><strong>Venue:</strong> ${result.venue || 'N/A'}</p>
                    <p><strong>Result:</strong> ${result.resultText || 'N/A'}</p>
                    <p><strong>Player of the Match:</strong> ${result.playerOfMatch || 'N/A'}</p>
                    <p><strong>Processing Time:</strong> ${result.processingTime} ms</p>
                    <p><strong>Completeness Score:</strong> ${renderCompletenessScore(result.completenessScore)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      }
      
      // Render team score
      function renderTeamScore(score) {
        if (!score) return 'N/A';
        return `${score.runs}-${score.wickets} (${score.overs} overs)`;
      }
      
      // Render player list
      function renderPlayerList(players) {
        if (!players || players.length === 0) return '<p>None</p>';
        
        let html = '<ul class="list-unstyled">';
        
        players.slice(0, 4).forEach(player => {
          html += '<li>';
          html += player.name;
          
          if (player.runs !== undefined) {
            html += ` - ${player.runs} runs`;
          }
          
          if (player.wickets !== undefined) {
            html += ` - ${player.wickets} wickets`;
          }
          
          html += '</li>';
        });
        
        if (players.length > 4) {
          html += `<li>+ ${players.length - 4} more</li>`;
        }
        
        html += '</ul>';
        
        return html;
      }
      
      // Render completeness score
      function renderCompletenessScore(score) {
        let variant = 'danger';
        if (score > 75) variant = 'success';
        else if (score > 50) variant = 'warning';
        
        return `
          <div class="progress completeness-bar">
            <div class="progress-bar bg-${variant}" role="progressbar" style="width: ${score}%" aria-valuenow="${score}" aria-valuemin="0" aria-valuemax="100">${score}%</div>
          </div>
        `;
      }
    });
  </script>
</body>
</html>
