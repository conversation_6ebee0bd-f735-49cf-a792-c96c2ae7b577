import React from 'react';

/**
 * Cricket Ball Icon Component
 * 
 * A simple SVG icon of a cricket ball to use when the cricket-ball.png image is not available
 */
const CricketBallIcon = ({ size = 24, color = '#c62828' }) => {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Cricket ball circle */}
      <circle cx="12" cy="12" r="10" fill={color} />
      
      {/* Seam lines */}
      <path 
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" 
        fill="#8b0000" 
      />
      
      {/* Stitch lines */}
      <path 
        d="M7 12c0-2.76 2.24-5 5-5s5 2.24 5 5-2.24 5-5 5-5-2.24-5-5zm8 0c0-1.65-1.35-3-3-3s-3 1.35-3 3 1.35 3 3 3 3-1.35 3-3z" 
        fill="#ffffff" 
        fillOpacity="0.3" 
      />
      
      {/* Highlight */}
      <circle cx="9" cy="9" r="2" fill="#ffffff" fillOpacity="0.2" />
    </svg>
  );
};

export default CricketBallIcon;
