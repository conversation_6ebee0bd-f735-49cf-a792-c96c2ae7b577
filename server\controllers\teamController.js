const Team = require('../models/Team');
const User = require('../models/User');
const Player = require('../models/Player');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

/**
 * Get all teams
 * @route GET /api/teams
 * @access Private (Admin only)
 */
exports.getAllTeams = async (req, res) => {
  try {
    const teams = await Team.find({})
      .populate('owner', 'username email')
      .select('teamName description logo primaryColor secondaryColor homeGround foundedYear slogan owner')
      .sort({ teamName: 1 });

    res.json({
      success: true,
      data: teams
    });
  } catch (error) {
    console.error('Error fetching teams:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching teams'
    });
  }
};

/**
 * Get team settings
 * @route GET /api/teams/settings
 * @access Private (Team Owner only)
 */
exports.getTeamSettings = async (req, res) => {
  try {
    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
      console.log('Team found via user.team reference:', team._id);
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      console.log('Team found via owner lookup:', !!team);

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
        console.log('Updated user with team reference');
      }
    }

    // If team doesn't exist yet, get basic info from user
    if (!team) {
      return res.json({
        teamName: user.teamName,
        description: '',
        logo: '/uploads/teams/default-logo.png',
        primaryColor: '#1e88e5',
        secondaryColor: '#bbdefb',
        colorScheme: 'Blue',
        homeGround: '',
        foundedYear: new Date().getFullYear(),
        slogan: ''
      });
    }

    // Return team settings
    res.json({
      teamName: team.teamName,
      description: team.description,
      logo: team.logo,
      primaryColor: team.primaryColor,
      secondaryColor: team.secondaryColor,
      colorScheme: team.colorScheme,
      homeGround: team.homeGround,
      foundedYear: team.foundedYear,
      slogan: team.slogan
    });
  } catch (err) {
    console.error('Error getting team settings:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Update team settings
 * @route PUT /api/teams/settings
 * @access Private (Team Owner only)
 */
exports.updateTeamSettings = async (req, res) => {
  try {
    const {
      teamName,
      description,
      logo,
      primaryColor,
      secondaryColor,
      colorScheme,
      homeGround,
      foundedYear,
      slogan
    } = req.body;

    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
      console.log('Team found via user.team reference:', team._id);
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      console.log('Team found via owner lookup:', !!team);

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
        console.log('Updated user with team reference');
      }
    }

    // If team doesn't exist yet, create it
    if (!team) {
      team = new Team({
        owner: req.user.id,
        teamName: teamName || user.teamName,
        description,
        logo,
        primaryColor,
        secondaryColor,
        colorScheme,
        homeGround,
        foundedYear,
        slogan
      });

      await team.save();

      // Update user with team reference
      await User.findByIdAndUpdate(req.user.id, { team: team._id });
      console.log('Created new team and updated user reference');
    } else {
      // Update team settings
      team.teamName = teamName || team.teamName;
      team.description = description !== undefined ? description : team.description;
      team.logo = logo || team.logo;
      team.primaryColor = primaryColor || team.primaryColor;
      team.secondaryColor = secondaryColor || team.secondaryColor;
      team.colorScheme = colorScheme || team.colorScheme;
      team.homeGround = homeGround !== undefined ? homeGround : team.homeGround;
      team.foundedYear = foundedYear || team.foundedYear;
      team.slogan = slogan !== undefined ? slogan : team.slogan;
    }

    await team.save();

    // Update user's team name if it changed
    if (teamName && teamName !== req.user.teamName) {
      await User.findByIdAndUpdate(req.user.id, { teamName });
    }

    res.json({
      msg: 'Team settings updated successfully',
      team: {
        teamName: team.teamName,
        description: team.description,
        logo: team.logo,
        primaryColor: team.primaryColor,
        secondaryColor: team.secondaryColor,
        colorScheme: team.colorScheme,
        homeGround: team.homeGround,
        foundedYear: team.foundedYear,
        slogan: team.slogan
      }
    });
  } catch (err) {
    console.error('Error updating team settings:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Upload team logo
 * @route POST /api/teams/logo
 * @access Private (Team Owner only)
 */
exports.uploadTeamLogo = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ msg: 'No file uploaded' });
    }

    // Create uploads/teams directory if it doesn't exist
    const teamUploadsDir = path.join('uploads', 'teams');
    if (!fs.existsSync(teamUploadsDir)) {
      fs.mkdirSync(teamUploadsDir, { recursive: true });
    }

    // Get file path
    const filePath = req.file.path.replace(/\\/g, '/');

    res.json({
      msg: 'Team logo uploaded successfully',
      filePath: `/${filePath}`
    });
  } catch (err) {
    console.error('Error uploading team logo:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get team roster
 * @route GET /api/teams/roster
 * @access Private (Team Owner only)
 */
exports.getTeamRoster = async (req, res) => {
  try {
    const { page = 1, limit = 12 } = req.query;

    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
      console.log('Team found via user.team reference:', team._id);
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      console.log('Team found via owner lookup:', !!team);

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
        console.log('Updated user with team reference');
      }
    }

    // If no team found, return empty roster
    if (!team) {
      return res.json({
        players: [],
        totalPages: 0,
        currentPage: Number(page),
        totalPlayers: 0
      });
    }

    // Count total players in the team
    const totalPlayers = await Player.countDocuments({ owner: req.user.id });

    // Get paginated players with auction win information
    const players = await Player.find({ owner: req.user.id })
      .sort({ 'ratings.overall': -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('auctionWin.auctionId');

    res.json({
      players,
      totalPages: Math.ceil(totalPlayers / limit),
      currentPage: Number(page),
      totalPlayers
    });
  } catch (err) {
    console.error('Error getting team roster:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Add player to team
 * @route POST /api/teams/roster/add
 * @access Private (Team Owner only)
 */
exports.addPlayerToTeam = async (req, res) => {
  try {
    const { playerId } = req.body;

    if (!playerId) {
      return res.status(400).json({ msg: 'Player ID is required' });
    }

    // Check if player exists
    const player = await Player.findById(playerId);
    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Check if player is already owned
    if (player.owner) {
      return res.status(400).json({ msg: 'Player is already owned by a team' });
    }

    // Update player owner
    player.owner = req.user.id;
    player.isAvailableOnMarket = false;
    await player.save();

    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
      console.log('Team found via user.team reference:', team._id);
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      console.log('Team found via owner lookup:', !!team);

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
        console.log('Updated user with team reference');
      }
    }

    if (!team) {
      return res.status(404).json({ msg: 'Team not found. Please create a team first.' });
    }

    // Add transaction to team budget
    team.budget.transactions.push({
      description: `Acquired player: ${player.name}`,
      amount: -player.marketValue,
      category: 'playerAcquisition'
    });

    // Update total budget
    team.budget.totalBudget -= player.marketValue;

    // Recalculate budget allocations
    Object.keys(team.budget.allocations).forEach(key => {
      team.budget.allocations[key].amount = (team.budget.totalBudget * team.budget.allocations[key].percentage) / 100;
    });

    await team.save();

    res.json({
      msg: 'Player added to team successfully',
      player
    });
  } catch (err) {
    console.error('Error adding player to team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Remove player from team
 * @route POST /api/teams/roster/remove
 * @access Private (Team Owner only)
 */
exports.removePlayerFromTeam = async (req, res) => {
  try {
    const { playerId } = req.body;

    if (!playerId) {
      return res.status(400).json({ msg: 'Player ID is required' });
    }

    // Check if player exists
    const player = await Player.findById(playerId);
    if (!player) {
      return res.status(404).json({ msg: 'Player not found' });
    }

    // Check if player is owned by the current user
    if (!player.owner || player.owner.toString() !== req.user.id) {
      return res.status(400).json({ msg: 'Player is not in your team' });
    }

    // Update player owner
    player.owner = null;
    await player.save();

    res.json({
      msg: 'Player removed from team successfully',
      player
    });
  } catch (err) {
    console.error('Error removing player from team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get team statistics
 * @route GET /api/teams/statistics
 * @access Private (Team Owner only)
 */
exports.getTeamStatistics = async (req, res) => {
  try {
    // Find team by owner ID
    let team = await Team.findOne({ owner: req.user.id });

    // If team doesn't exist yet, return default statistics
    if (!team) {
      return res.json({
        totalMatches: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        winPercentage: 0
      });
    }

    // Return team statistics
    res.json(team.statistics);
  } catch (err) {
    console.error('Error getting team statistics:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Get team budget
 * @route GET /api/teams/budget
 * @access Private (Team Owner only)
 */
exports.getTeamBudget = async (req, res) => {
  try {
    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
      console.log('Team found via user.team reference:', team._id);
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });
      console.log('Team found via owner lookup:', !!team);

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
        console.log('Updated user with team reference');
      }
    }

    // If team doesn't exist yet, return default budget
    if (!team) {
      return res.json({
        totalBudget: 10000,
        allocations: {
          playerAcquisition: { percentage: 60, amount: 6000 },
          playerDevelopment: { percentage: 20, amount: 2000 },
          teamOperations: { percentage: 10, amount: 1000 },
          marketing: { percentage: 5, amount: 500 },
          reserve: { percentage: 5, amount: 500 }
        },
        transactions: []
      });
    }

    // Return team budget
    res.json({
      totalBudget: team.budget.totalBudget,
      allocations: team.budget.allocations,
      transactions: team.budget.transactions
    });
  } catch (err) {
    console.error('Error getting team budget:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Update team budget
 * @route PUT /api/teams/budget
 * @access Private (Team Owner only)
 */
exports.updateTeamBudget = async (req, res) => {
  try {
    const { allocations } = req.body;

    if (!allocations) {
      return res.status(400).json({ msg: 'Budget allocations are required' });
    }

    // Get user with team reference
    const user = await User.findById(req.user.id).populate('team');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user has a team reference
    let team;
    if (user.team) {
      team = user.team;
    } else {
      // Fallback to finding by owner
      team = await Team.findOne({ owner: req.user.id });

      // If team found, update user with reference
      if (team) {
        await User.findByIdAndUpdate(req.user.id, { team: team._id });
      }
    }

    // If team doesn't exist yet, create it
    if (!team) {
      team = new Team({
        owner: req.user.id,
        teamName: user.teamName || 'My Team'
      });

      await team.save();

      // Update user with team reference
      await User.findByIdAndUpdate(req.user.id, { team: team._id });
    }

    // Update budget allocations
    Object.keys(allocations).forEach(key => {
      if (team.budget.allocations[key]) {
        team.budget.allocations[key].percentage = allocations[key].percentage;
        team.budget.allocations[key].amount = (team.budget.totalBudget * allocations[key].percentage) / 100;
      }
    });

    await team.save();

    res.json({
      msg: 'Budget allocations updated successfully',
      budget: {
        totalBudget: team.budget.totalBudget,
        allocations: team.budget.allocations,
        transactions: team.budget.transactions
      }
    });
  } catch (err) {
    console.error('Error updating team budget:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};

/**
 * Create a team for the user
 * @route POST /api/teams/create
 * @access Private (Team Owner only)
 */
exports.createTeam = async (req, res) => {
  try {
    // Get user with team reference
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user already has a team reference
    if (user.team) {
      // Verify the team exists
      const existingTeam = await Team.findById(user.team);
      if (existingTeam) {
        return res.json({
          success: true,
          msg: 'User already has a team',
          team: {
            id: existingTeam._id,
            teamName: existingTeam.teamName,
            description: existingTeam.description || '',
            logo: existingTeam.logo || '/uploads/teams/default-logo.png',
            primaryColor: existingTeam.primaryColor || '#1e88e5',
            secondaryColor: existingTeam.secondaryColor || '#bbdefb',
            colorScheme: existingTeam.colorScheme || 'Blue',
            homeGround: existingTeam.homeGround || '',
            foundedYear: existingTeam.foundedYear || new Date().getFullYear(),
            slogan: existingTeam.slogan || '',
            budget: existingTeam.budget
          }
        });
      }
      // If team reference exists but team doesn't, continue to create a new team
      console.log('Team reference exists but team not found. Creating new team.');
    }

    // Also check by owner field as a fallback
    const existingTeamByOwner = await Team.findOne({ owner: req.user.id });

    if (existingTeamByOwner) {
      // Update user with team reference if it's missing
      if (!user.team) {
        await User.findByIdAndUpdate(req.user.id, { team: existingTeamByOwner._id });
        console.log('Updated user with existing team reference');
      }

      return res.json({
        success: true,
        msg: 'User already has a team',
        team: {
          id: existingTeamByOwner._id,
          teamName: existingTeamByOwner.teamName,
          description: existingTeamByOwner.description || '',
          logo: existingTeamByOwner.logo || '/uploads/teams/default-logo.png',
          primaryColor: existingTeamByOwner.primaryColor || '#1e88e5',
          secondaryColor: existingTeamByOwner.secondaryColor || '#bbdefb',
          colorScheme: existingTeamByOwner.colorScheme || 'Blue',
          homeGround: existingTeamByOwner.homeGround || '',
          foundedYear: existingTeamByOwner.foundedYear || new Date().getFullYear(),
          slogan: existingTeamByOwner.slogan || '',
          budget: existingTeamByOwner.budget
        }
      });
    }

    // Get team name from request or user
    const teamName = req.body.teamName || user.teamName || 'My Team';
    console.log('Creating new team with name:', teamName);

    // Create new team
    const team = new Team({
      owner: req.user.id,
      teamName: teamName,
      description: req.body.description || '',
      logo: req.body.logo || '/uploads/teams/default-logo.png',
      primaryColor: req.body.primaryColor || '#1e88e5',
      secondaryColor: req.body.secondaryColor || '#bbdefb',
      colorScheme: req.body.colorScheme || 'Blue',
      homeGround: req.body.homeGround || '',
      foundedYear: req.body.foundedYear || new Date().getFullYear(),
      slogan: req.body.slogan || '',
      budget: {
        totalBudget: 10000, // Default budget
        allocations: {
          playerAcquisition: { percentage: 60, amount: 6000 },
          playerDevelopment: { percentage: 20, amount: 2000 },
          teamOperations: { percentage: 10, amount: 1000 },
          marketing: { percentage: 5, amount: 500 },
          reserve: { percentage: 5, amount: 500 }
        },
        transactions: []
      }
    });

    await team.save();

    // Update user with team reference
    await User.findByIdAndUpdate(req.user.id, { team: team._id });

    res.json({
      success: true,
      msg: 'Team created successfully',
      team: {
        id: team._id,
        teamName: team.teamName,
        description: team.description,
        logo: team.logo,
        primaryColor: team.primaryColor,
        secondaryColor: team.secondaryColor,
        colorScheme: team.colorScheme,
        homeGround: team.homeGround,
        foundedYear: team.foundedYear,
        slogan: team.slogan,
        budget: team.budget
      }
    });
  } catch (err) {
    console.error('Error creating team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
};
