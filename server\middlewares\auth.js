const jwt = require('jsonwebtoken');

/**
 * Authentication middleware to verify JWT token
 */
const auth = (req, res, next) => {
  try {
    // Get token from header
    const token = req.header('x-auth-token');

    // Log headers for debugging
    console.log('Request headers:', req.headers);

    // Check if no token
    if (!token) {
      console.log('No token provided in request');
      return res.status(401).json({ msg: 'No token, authorization denied' });
    }

    console.log('Token found, verifying...');
    console.log('JWT_SECRET exists:', !!process.env.JWT_SECRET);

    if (!process.env.JWT_SECRET) {
      console.error('JWT_SECRET is not defined in environment variables');
      return res.status(500).json({ msg: 'Server configuration error' });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Add user from payload
      req.user = decoded.user;
      console.log('Token verified successfully, user:', req.user);
      next();
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);
      return res.status(401).json({
        msg: 'Token is not valid',
        error: jwtError.message
      });
    }
  } catch (err) {
    console.error('Auth middleware error:', err);
    res.status(500).json({
      msg: 'Server error in authentication',
      error: err.message
    });
  }
};

module.exports = { authenticateToken: auth };