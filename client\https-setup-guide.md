# Setting Up HTTPS for Local Development

This guide provides multiple options for setting up HTTPS in your local development environment, which is required for camera access on mobile devices.

## Option 1: Using React's Built-in HTTPS Support

React has built-in support for HTTPS in development mode. You just need to:

1. **Create a `.env` file** in your client directory with:
   ```
   HTTPS=true
   SSL_CRT_FILE=cert.pem
   SSL_KEY_FILE=key.pem
   ```

2. **Generate self-signed certificates**:
   ```
   openssl req -x509 -newkey rsa:2048 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/CN=localhost"
   ```

3. **Start your React app** as usual:
   ```
   npm start
   ```

4. **Access your app** at `https://localhost:3000`

5. **Accept the self-signed certificate warning** in your browser

### Using the Setup Script

We've provided a setup script to automate this process:

1. Run `setup-https.bat` (Windows) or `setup-https.sh` (Mac/Linux)
2. Start your React app with `npm start`

### Limitations

- This approach only works for accessing the app on the same device (localhost)
- For mobile testing, you'll need to use a different approach (see Option 2)

## Option 2: Using ngrok for Mobile Testing

ngrok creates a secure tunnel to your local development server, making it accessible over HTTPS from any device.

1. **Install ngrok** from https://ngrok.com/download

2. **Start your React development server**:
   ```
   npm start
   ```

3. **In a new terminal, start ngrok**:
   ```
   ngrok http 3000
   ```

4. **Use the HTTPS URL provided by ngrok** on your mobile device

See `ngrok-setup.md` for detailed instructions.

## Option 3: Using mkcert (Recommended for Development Teams)

mkcert creates locally-trusted development certificates that browsers will trust.

1. **Install mkcert**:
   - Windows: `choco install mkcert` (requires Chocolatey)
   - Mac: `brew install mkcert` (requires Homebrew)
   - Linux: Follow instructions at https://github.com/FiloSottile/mkcert

2. **Install the local CA**:
   ```
   mkcert -install
   ```

3. **Generate certificates for localhost**:
   ```
   mkcert localhost 127.0.0.1 ::1
   ```

4. **Create a `.env` file** in your client directory:
   ```
   HTTPS=true
   SSL_CRT_FILE=localhost+2.pem
   SSL_KEY_FILE=localhost+2-key.pem
   ```

5. **Start your React app**:
   ```
   npm start
   ```

## Option 4: Using https-localhost Package

The https-localhost package provides a simple way to serve your app over HTTPS:

1. **Install the package**:
   ```
   npm install -g https-localhost
   ```

2. **Build your React app**:
   ```
   npm run build
   ```

3. **Serve the build folder**:
   ```
   serve build
   ```

## Troubleshooting

### Certificate Warnings

- Self-signed certificates will show warnings in browsers
- You'll need to click "Advanced" and "Proceed" (Chrome) or "Accept the Risk" (Firefox)
- On mobile devices, you may need to add security exceptions in browser settings

### Mobile Device Access

- To access your local server from a mobile device, both devices must be on the same network
- Use your computer's local IP address (e.g., ************) instead of localhost
- For ngrok, use the provided ngrok.io URL

### CORS Issues

- If your app makes API calls to a local backend, you may encounter CORS issues
- Make sure your backend allows requests from your HTTPS origin
- For React's development server, update the proxy setting in package.json
