const path = require('path');
const OCRService = require('./services/ocrService');

/**
 * Test to show both raw OCR extraction and verified results
 */
async function testVerifiedExtraction(scorecardName = 'scorecard9.png') {
  try {
    console.log('🧪 OCR EXTRACTION WITH VERIFICATION TEST');
    console.log('======================================');
    console.log(`Testing extraction with Google Cloud Vision verification`);
    console.log(`Scorecard: ${scorecardName}\n`);

    const ocrService = new OCRService();
    // Ensure verification is enabled
    ocrService.useGoogleVisionVerification = true;
    
    const imagePath = path.join(__dirname, 'uploads', 'scorecards', scorecardName);

    console.log(`📸 Processing: ${path.basename(imagePath)}`);
    
    // Step 1: Get the raw OCR.space results (without verification)
    console.log('\n⏳ Step 1: Getting raw OCR.space results...');
    const rawResult = await ocrService.processImageWithOCRSpace(imagePath);

    if (!rawResult.success) {
      console.log('❌ Raw extraction failed:', rawResult.error);
      return;
    }

    // Step 2: Process with verification
    console.log('\n⏳ Step 2: Processing with Google Vision verification...');
    const verifiedResult = await ocrService.processImage(imagePath);

    // Display RAW results
    console.log('\n📊 RAW OCR.SPACE RESULTS (BEFORE VERIFICATION):');
    console.log('==============================================');
    
    // Find JOHN MORTIMORE in raw results
    const johnInRawTeam1 = rawResult.team1Batsmen?.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    const johnInRawTeam2 = rawResult.team2Batsmen?.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    if (johnInRawTeam1) {
      console.log(`JOHN MORTIMORE found in Team 1: ${johnInRawTeam1.runs}(${johnInRawTeam1.balls})`);
    } else if (johnInRawTeam2) {
      console.log(`JOHN MORTIMORE found in Team 2: ${johnInRawTeam2.runs}(${johnInRawTeam2.balls})`);
    } else {
      console.log('JOHN MORTIMORE not found in raw results');
    }
    
    // Display Team 1 Batsmen from raw results
    console.log(`\n🏏 RAW ${rawResult.team1.toUpperCase()} BATTING:`);
    if (rawResult.team1Batsmen && rawResult.team1Batsmen.length > 0) {
      rawResult.team1Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls})`);
      });
    } else {
      console.log('  No batting data found');
    }
    
    // Display Team 2 Batsmen from raw results
    console.log(`\n🏏 RAW ${rawResult.team2.toUpperCase()} BATTING:`);
    if (rawResult.team2Batsmen && rawResult.team2Batsmen.length > 0) {
      rawResult.team2Batsmen.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls})`);
      });
    } else {
      console.log('  No batting data found');
    }

    // Display VERIFIED results
    console.log('\n📊 VERIFIED RESULTS (AFTER GOOGLE VISION VERIFICATION):');
    console.log('====================================================');
    
    // Check if verification was performed
    if (verifiedResult.verification) {
      console.log(`Verification performed using: ${verifiedResult.verification.method}`);
      console.log(`Players verified: ${verifiedResult.verification.playersVerified}`);
    } else {
      console.log('No verification was performed');
    }
    
    // Find JOHN MORTIMORE in verified results
    const johnInVerifiedTeam1 = verifiedResult.team1Batsmen?.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    const johnInVerifiedTeam2 = verifiedResult.team2Batsmen?.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    if (johnInVerifiedTeam1) {
      console.log(`JOHN MORTIMORE found in Team 1: ${johnInVerifiedTeam1.runs}(${johnInVerifiedTeam1.balls})`);
      if (johnInVerifiedTeam1.verified) {
        console.log(`Original score: ${johnInVerifiedTeam1.originalRuns}(${johnInVerifiedTeam1.originalBalls})`);
        console.log(`✅ Score was verified and corrected by Google Vision`);
      }
    } else if (johnInVerifiedTeam2) {
      console.log(`JOHN MORTIMORE found in Team 2: ${johnInVerifiedTeam2.runs}(${johnInVerifiedTeam2.balls})`);
      if (johnInVerifiedTeam2.verified) {
        console.log(`Original score: ${johnInVerifiedTeam2.originalRuns}(${johnInVerifiedTeam2.originalBalls})`);
        console.log(`✅ Score was verified and corrected by Google Vision`);
      }
    } else {
      console.log('JOHN MORTIMORE not found in verified results');
    }
    
    // Display Team 1 Batsmen from verified results
    console.log(`\n🏏 VERIFIED ${verifiedResult.team1.toUpperCase()} BATTING:`);
    if (verifiedResult.team1Batsmen && verifiedResult.team1Batsmen.length > 0) {
      verifiedResult.team1Batsmen.forEach((player, i) => {
        const verifiedMark = player.verified ? ' ✓' : '';
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls})${verifiedMark}`);
        if (player.verified) {
          console.log(`     Original: ${player.originalRuns}(${player.originalBalls})`);
        }
      });
    } else {
      console.log('  No batting data found');
    }
    
    // Display Team 2 Batsmen from verified results
    console.log(`\n🏏 VERIFIED ${verifiedResult.team2.toUpperCase()} BATTING:`);
    if (verifiedResult.team2Batsmen && verifiedResult.team2Batsmen.length > 0) {
      verifiedResult.team2Batsmen.forEach((player, i) => {
        const verifiedMark = player.verified ? ' ✓' : '';
        console.log(`  ${i+1}. ${player.name}: ${player.runs}(${player.balls})${verifiedMark}`);
        if (player.verified) {
          console.log(`     Original: ${player.originalRuns}(${player.originalBalls})`);
        }
      });
    } else {
      console.log('  No batting data found');
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📋 VERIFICATION SUMMARY:');
    
    // Count verified players
    const verifiedPlayers = [
      ...(verifiedResult.team1Batsmen || []), 
      ...(verifiedResult.team2Batsmen || [])
    ].filter(p => p.verified);
    
    console.log(`✅ Total players verified: ${verifiedPlayers.length}`);
    
    if (verifiedPlayers.length > 0) {
      console.log('\n🔍 VERIFIED PLAYERS:');
      verifiedPlayers.forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name}: ${player.originalRuns}(${player.originalBalls}) → ${player.runs}(${player.balls})`);
      });
    }
    
    console.log('\n🎯 CONCLUSION:');
    console.log('This test shows both the raw OCR.space results and the verified results after Google Vision verification.');
    console.log('The verification step corrects 6/9 confusion in player scores.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test with optional scorecard parameter
const scorecardName = process.argv[2] || 'scorecard9.png';
testVerifiedExtraction(scorecardName);