/**
 * Production Verification Test
 * 
 * This script tests the OCR verification system with known problem scorecards
 * to ensure the 6/9 confusion detection and correction is working properly.
 */

const OCRService = require('./services/ocrService');
const path = require('path');
const fs = require('fs');

// List of test cases with known 6/9 confusion issues
const testCases = [
  {
    name: 'JOHN MORTIMORE 9(6) Case',
    imagePath: path.join(__dirname, 'uploads/scorecards/scorecard9.png'),
    playerName: 'JOHN MORTIMORE',
    expectedRuns: '9',
    expectedBalls: 6,
    description: 'This scorecard has JOHN MORTIMORE with 9(6) that was incorrectly read as 6(6)'
  }
  // Add more test cases here as they are identified
];

async function runVerificationTests() {
  console.log('🚀 Testing OCR Verification System in Production Mode\n');
  
  // Initialize OCR service
  const ocrService = new OCRService();
  
  // Ensure verification is enabled
  ocrService.useGoogleVisionVerification = true;
  
  // Track test results
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  // Process each test case
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`Description: ${testCase.description}`);
    console.log(`Image: ${testCase.imagePath}`);
    
    try {
      // Process the image
      console.log('\nProcessing image...');
      const result = await ocrService.processImage(testCase.imagePath);
      
      // Check if verification was performed
      if (!result.verification) {
        console.log('❌ No verification was performed');
        results.failed++;
        results.details.push({
          testCase: testCase.name,
          status: 'FAILED',
          reason: 'No verification was performed'
        });
        continue;
      }
      
      console.log(`\n✅ Verification performed using ${result.verification.method}`);
      console.log(`Players verified: ${result.verification.playersVerified}`);
      
      // Find the player in the results
      const player = [...result.team1Batsmen, ...result.team2Batsmen].find(p => 
        p.name.toUpperCase().includes(testCase.playerName.toUpperCase())
      );
      
      if (!player) {
        console.log(`❌ Player ${testCase.playerName} not found in results`);
        results.failed++;
        results.details.push({
          testCase: testCase.name,
          status: 'FAILED',
          reason: `Player ${testCase.playerName} not found in results`
        });
        continue;
      }
      
      console.log(`\nFound player: ${player.name} with score ${player.runs}(${player.balls})`);
      
      // Check if the player was verified
      if (!player.verified) {
        console.log('❌ Player was not verified');
        results.failed++;
        results.details.push({
          testCase: testCase.name,
          status: 'FAILED',
          reason: 'Player was found but not verified'
        });
        continue;
      }
      
      console.log(`Original score: ${player.originalRuns}(${player.originalBalls})`);
      console.log(`Verified score: ${player.runs}(${player.balls})`);
      
      // Check if the verification corrected the score properly
      const runsMatch = player.runs === testCase.expectedRuns;
      const ballsMatch = parseInt(player.balls, 10) === testCase.expectedBalls;
      
      if (runsMatch && ballsMatch) {
        console.log('✅ TEST PASSED: Score was correctly verified and matches expected values');
        results.passed++;
        results.details.push({
          testCase: testCase.name,
          status: 'PASSED',
          originalScore: `${player.originalRuns}(${player.originalBalls})`,
          verifiedScore: `${player.runs}(${player.balls})`,
          expectedScore: `${testCase.expectedRuns}(${testCase.expectedBalls})`
        });
      } else {
        console.log('❌ TEST FAILED: Score does not match expected values');
        console.log(`Expected: ${testCase.expectedRuns}(${testCase.expectedBalls})`);
        console.log(`Actual: ${player.runs}(${player.balls})`);
        results.failed++;
        results.details.push({
          testCase: testCase.name,
          status: 'FAILED',
          reason: 'Score does not match expected values',
          originalScore: `${player.originalRuns}(${player.originalBalls})`,
          verifiedScore: `${player.runs}(${player.balls})`,
          expectedScore: `${testCase.expectedRuns}(${testCase.expectedBalls})`
        });
      }
      
    } catch (error) {
      console.error(`❌ Test failed with error: ${error.message}`);
      results.failed++;
      results.details.push({
        testCase: testCase.name,
        status: 'ERROR',
        error: error.message
      });
    }
  }
  
  // Print summary
  console.log('\n📊 TEST SUMMARY');
  console.log('==============');
  console.log(`Total tests: ${testCases.length}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success rate: ${(results.passed / testCases.length * 100).toFixed(2)}%`);
  
  // Save results to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const resultsPath = path.join(__dirname, `verification-test-results-${timestamp}.json`);
  fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
  console.log(`\nDetailed results saved to: ${resultsPath}`);
}

// Run the tests
runVerificationTests();