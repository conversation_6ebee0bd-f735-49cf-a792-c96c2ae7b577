# Multi-stage Dockerfile for React + Node.js application

# Stage 1: Build React client
FROM node:18-alpine AS client-build
WORKDIR /app/client

# Copy client package files
COPY client/package*.json ./

# Install client dependencies
RUN npm ci

# Copy client source code
COPY client/ ./

# Set environment variables for React build
ENV CI=false
ENV GENERATE_SOURCEMAP=false
ENV REACT_APP_API_URL=https://rpl.xendekweb.com/api
ENV NODE_OPTIONS=--max-old-space-size=4096

# Build React application
RUN npm run build

# Stage 2: Setup Node.js server
FROM node:18-alpine AS server
WORKDIR /app

# Install system dependencies for OCR and image processing
RUN apk add --no-cache \
    python3 \
    py3-pip \
    tesseract-ocr \
    tesseract-ocr-data-eng \
    imagemagick \
    && rm -rf /var/cache/apk/*

# Copy server package files
COPY server/package*.json ./

# Install Sharp with correct platform binaries for Alpine Linux
RUN npm install --platform=linux --arch=x64 --libc=musl sharp

# Install server dependencies (production only)
RUN npm ci --only=production

# Copy server source code
COPY server/ ./

# Copy built React app from client-build stage to match server expectation
COPY --from=client-build /app/client/build ./client/build

# Create uploads directory
RUN mkdir -p uploads/scorecards

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start the server
CMD ["node", "index.js"]