import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Alert,
  Stack
} from '@mui/material';
import axios from 'axios';
import { API_URL } from '../../config';

// Get auth token from localStorage
const getAuthToken = () => localStorage.getItem('token');

// Configure axios defaults
const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

const OCRSettings = () => {
  const [settings, setSettings] = useState({
    primaryMethod: 'ocrspace',
    fallbackMethod: 'googlevision',
    ocrSpaceTimeout: 60000
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }
      const response = await axiosInstance.get('/ocr-settings', {
        headers: { 'x-auth-token': token }
      });
      setSettings(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to load OCR settings');
      console.error('Error loading OCR settings:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('No authentication token found');
      }
      await axiosInstance.put('/ocr-settings', settings, {
        headers: { 'x-auth-token': token }
      });
      setSuccess(true);
      setError(null);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to save OCR settings');
      console.error('Error saving OCR settings:', err);
    }
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setSettings(prev => ({
      ...prev,
      [name]: name === 'ocrSpaceTimeout' ? Number(value) : value
    }));
  };

  if (loading) {
    return <Typography>Loading OCR settings...</Typography>;
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          OCR Settings
        </Typography>
        <Box component="form" sx={{ mt: 2 }}>
          <Stack spacing={3}>
            <FormControl fullWidth>
              <InputLabel>Primary OCR Method</InputLabel>
              <Select
                name="primaryMethod"
                value={settings.primaryMethod}
                label="Primary OCR Method"
                onChange={handleChange}
              >
                <MenuItem value="ocrspace">OCR.space</MenuItem>
                <MenuItem value="googlevision">Google Vision</MenuItem>
                <MenuItem value="paddleocr">PaddleOCR</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Fallback OCR Method</InputLabel>
              <Select
                name="fallbackMethod"
                value={settings.fallbackMethod}
                label="Fallback OCR Method"
                onChange={handleChange}
              >
                <MenuItem value="googlevision">Google Vision</MenuItem>
                <MenuItem value="paddleocr">PaddleOCR</MenuItem>
                <MenuItem value="none">None</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              name="ocrSpaceTimeout"
              label="OCR.space Timeout (ms)"
              type="number"
              value={settings.ocrSpaceTimeout}
              onChange={handleChange}
              inputProps={{
                min: 1000,
                max: 300000,
                step: 1000
              }}
              helperText="Timeout in milliseconds (1-300 seconds)"
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mt: 2 }}>
                Settings saved successfully!
              </Alert>
            )}

            <Button
              variant="contained"
              color="primary"
              onClick={handleSave}
              sx={{ mt: 2 }}
            >
              Save Settings
            </Button>
          </Stack>
        </Box>
      </CardContent>
    </Card>
  );
};

export default OCRSettings;