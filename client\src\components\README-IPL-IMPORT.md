# IPL Player Import Feature

This feature allows you to import player data directly from the IPL website into your cricket management system.

## Features

- Scrape player data from any IPL team page
- Preview players with FIFA-style cricket cards
- Select which players to import
- Automatically generates player statistics and ratings

## How to Use

1. Navigate to the Player Management section
2. Click on "Import Players" button
3. Enter the URL of an IPL team page (e.g., https://www.iplt20.com/teams/delhi-capitals/squad/2025)
4. Click "Fetch Players" to retrieve player data
5. Select the players you want to import
6. Click "Import Selected Players" to add them to your database

## Implementation Details

### Components

- **ImportIplPlayers.js**: Main component for the import interface
- **scrapeIplPlayers.js**: Utility function that handles the web scraping

### How It Works

1. The scraper uses axios to fetch the HTML content of the IPL team page
2. It parses the HTML to extract player names, roles, and image URLs
3. It generates default values for player statistics based on their roles
4. The data is displayed using the CricketPlayerCard component
5. Selected players are sent to your database when you click "Import"

### Customization

You can customize the player data generation by modifying the following functions in `scrapeIplPlayers.js`:

- `mapPlayerRole`: Maps IPL roles to your application's player types
- `mapBowlingHand`: Determines bowling hand based on player role
- `generateRating`: Creates player ratings
- `generateRandomStats`: Generates statistics based on player role
- `generateRandomHeight`: Creates random height values for players

## Integration

To integrate this feature into your player management page:

```jsx
import ImportIplPlayers from '../components/ImportIplPlayers';

// In your component:
const handleImportPlayers = (players) => {
  // Add your logic to save players to the database
  console.log('Importing players:', players);
};

// In your render method:
<ImportIplPlayers onImportPlayers={handleImportPlayers} />
```

## Troubleshooting

- If no players are found, check that the URL is correct and points to a valid IPL team page
- If images don't load, the IPL website may have changed its structure
- The scraper may need updates if the IPL website changes its HTML structure

## Future Improvements

- Add support for other cricket leagues
- Improve accuracy of player statistics
- Add ability to match players with existing database entries to prevent duplicates
- Enhance player role detection
