# test_paddle_init.py
import os
import sys

# Add the parent directory of 'server' to sys.path to allow imports from 'server.utils' if needed by PaddleOCR indirectly
# Or ensure your PYTHONPATH is set up correctly if PaddleOCR relies on other project modules.
# For a direct PaddleOCR test, this might not be strictly necessary but can help if there are complex dependencies.

try:
    from paddleocr import PaddleOCR
    print("PaddleOCR imported successfully.")

    # Attempt to initialize PaddleOCR (this is where it might download models or fail)
    print("Attempting to initialize PaddleOCR (lang='en')...")
    # The 'lang' parameter is crucial.
    # Ensure the English models are expected to be available.
    ocr_instance = PaddleOCR(use_angle_cls=True, lang='en', show_log=True) # Added show_log for more details
    print("PaddleOCR initialized successfully.")

    if ocr_instance:
        print("ocr_instance created and seems valid.")
    else:
        print("ocr_instance was created but is None or evaluates to False. This is unexpected.")

except ImportError:
    print("Critical Error: PaddleOCR library is not installed or not found in Python path.")
    print("Please ensure Paddle<PERSON><PERSON> is installed in the correct Python environment.")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
except Exception as e:
    print(f"An error occurred during PaddleOCR initialization or use: {e}")
    import traceback
    traceback.print_exc()

print("\nScript execution finished.")