#!/usr/bin/env python
"""
Cricket Scorecard Labeling Server

This script provides a Flask API for:
1. Uploading scorecard images
2. Processing images with OCR
3. Saving and loading annotations
4. Training ML models
"""

import os
import sys
import json
import uuid
import argparse
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import cv2
import numpy as np
from werkzeug.utils import secure_filename

# Try to import PaddleOCR
try:
    from paddleocr import PaddleOCR
except ImportError:
    print("Error: PaddleOCR is not installed. Please install it with:")
    print("pip install paddleocr")
    sys.exit(1)

# Import the trainer
from scorecard_trainer import ScorecardTrainer

# Initialize Flask app
app = Flask(__name__, static_folder='../../client/build')
CORS(app)

# Configuration
DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
MODEL_DIR = os.path.join(os.path.dirname(__file__), 'models')
UPLOAD_FOLDER = os.path.join(DATA_DIR, 'images')
ANNOTATION_FOLDER = os.path.join(DATA_DIR, 'annotations')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(ANNOTATION_FOLDER, exist_ok=True)
os.makedirs(MODEL_DIR, exist_ok=True)

# Initialize PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='en')

# Initialize the trainer
trainer = ScorecardTrainer(DATA_DIR, MODEL_DIR)

def allowed_file(filename: str) -> bool:
    """Check if a file has an allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def preprocess_image(image_path: str) -> np.ndarray:
    """Preprocess an image for OCR"""
    # Read the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply adaptive thresholding
    thresh = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY, 11, 2
    )

    return thresh

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Upload a scorecard image"""
    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if file and allowed_file(file.filename):
        # Generate a unique filename
        filename = str(uuid.uuid4()) + '.' + file.filename.rsplit('.', 1)[1].lower()
        file_path = os.path.join(UPLOAD_FOLDER, filename)

        # Save the file
        file.save(file_path)

        # Process the image with OCR
        try:
            # Preprocess the image
            preprocessed = preprocess_image(file_path)

            # Run OCR
            result = ocr.ocr(preprocessed, cls=True)

            # Extract text elements
            text_elements = []
            for line in result:
                for item in line:
                    box = item[0]
                    text = item[1][0]
                    confidence = item[1][1]

                    # Calculate center of the box
                    center_x = sum(p[0] for p in box) / 4
                    center_y = sum(p[1] for p in box) / 4

                    # Calculate width and height
                    width = max(p[0] for p in box) - min(p[0] for p in box)
                    height = max(p[1] for p in box) - min(p[1] for p in box)

                    text_elements.append({
                        'id': len(text_elements),
                        'text': text,
                        'confidence': float(confidence),
                        'x': float(center_x),
                        'y': float(center_y),
                        'width': float(width),
                        'height': float(height),
                        'box': [[float(p[0]), float(p[1])] for p in box],
                        'category': None  # To be filled by user
                    })

            return jsonify({
                'success': True,
                'filename': filename,
                'text_elements': text_elements
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/api/images', methods=['GET'])
def get_images():
    """Get all uploaded images"""
    images = os.listdir(UPLOAD_FOLDER)
    return jsonify({'images': images})

@app.route('/api/image/<filename>', methods=['GET'])
def get_image(filename):
    """Get a specific image"""
    return send_from_directory(UPLOAD_FOLDER, filename)

@app.route('/api/annotations/<filename>', methods=['GET'])
def get_annotation(filename):
    """Get annotation for a specific image"""
    annotation_path = os.path.join(ANNOTATION_FOLDER, filename + '.json')

    if os.path.exists(annotation_path):
        with open(annotation_path, 'r') as f:
            annotation = json.load(f)
        return jsonify({'annotation': annotation})

    return jsonify({'annotation': None})

@app.route('/api/save-annotation', methods=['POST'])
def save_annotation():
    """Save annotation for an image"""
    data = request.json
    filename = data.get('filename')
    annotation = data.get('annotation')

    if not filename or not annotation:
        return jsonify({'error': 'Missing filename or annotation'}), 400

    # Save the annotation
    annotation_path = os.path.join(ANNOTATION_FOLDER, filename + '.json')
    with open(annotation_path, 'w') as f:
        json.dump(annotation, f)

    return jsonify({'success': True})

@app.route('/api/categories', methods=['GET', 'POST', 'DELETE'])
def get_categories():
    """Get, add, or delete categories"""
    if request.method == 'GET':
        # Get all available categories
        return jsonify({'categories': trainer.categories})
    elif request.method == 'POST':
        # Add a new custom category
        data = request.json
        new_category = data.get('category')

        if not new_category:
            return jsonify({'error': 'Missing category name'}), 400

        # Check if category already exists
        if new_category in trainer.categories:
            return jsonify({'error': 'Category already exists'}), 400

        # Add the new category
        trainer.categories.append(new_category)

        # Save the updated categories
        categories_path = os.path.join(MODEL_DIR, 'categories.json')
        with open(categories_path, 'w') as f:
            json.dump(trainer.categories, f)

        return jsonify({
            'success': True,
            'message': f'Added new category: {new_category}',
            'categories': trainer.categories
        })
    elif request.method == 'DELETE':
        # Delete a category
        data = request.json
        category_to_delete = data.get('category')

        if not category_to_delete:
            return jsonify({'error': 'Missing category name'}), 400

        # Check if category exists
        if category_to_delete not in trainer.categories:
            return jsonify({'error': 'Category does not exist'}), 400

        # Check if it's a default category (optional protection)
        default_categories = [
            'team1_name', 'team2_name',
            'team1_score', 'team2_score',
            'team1_overs', 'team2_overs',
            'team1_batsman_name', 'team2_batsman_name',
            'team1_batsman_runs', 'team2_batsman_runs',
            'team1_batsman_balls', 'team2_batsman_balls',
            'team1_bowler_name', 'team2_bowler_name',
            'team1_bowler_figures', 'team2_bowler_figures',
            'venue', 'player_of_match', 'match_time'
        ]

        if category_to_delete in default_categories:
            return jsonify({'error': 'Cannot delete default category'}), 400

        # Remove the category
        trainer.categories.remove(category_to_delete)

        # Save the updated categories
        categories_path = os.path.join(MODEL_DIR, 'categories.json')
        with open(categories_path, 'w') as f:
            json.dump(trainer.categories, f)

        # Update annotations to remove this category
        updated_annotations = 0
        for filename in os.listdir(ANNOTATION_FOLDER):
            annotation_path = os.path.join(ANNOTATION_FOLDER, filename)
            if os.path.isfile(annotation_path):
                try:
                    with open(annotation_path, 'r') as f:
                        annotation = json.load(f)

                    # Check if any elements use this category
                    modified = False
                    for item in annotation:
                        if item.get('category') == category_to_delete:
                            item['category'] = None
                            modified = True

                    # Save the updated annotation if modified
                    if modified:
                        with open(annotation_path, 'w') as f:
                            json.dump(annotation, f)
                        updated_annotations += 1
                except Exception as e:
                    print(f"Error updating annotation {filename}: {e}")

        return jsonify({
            'success': True,
            'message': f'Deleted category: {category_to_delete}',
            'categories': trainer.categories,
            'updated_annotations': updated_annotations
        })

@app.route('/api/train', methods=['POST', 'OPTIONS'])
def train_model():
    """Train the ML model"""
    # Handle OPTIONS request for CORS
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        print("Training model...")
        trainer.train_model()
        print("Model training completed successfully")
        return jsonify({'success': True, 'message': 'Model trained successfully'})
    except Exception as e:
        print(f"Error training model: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/model-status', methods=['GET'])
def get_model_status():
    """Get the status of the trained model"""
    model_path = os.path.join(MODEL_DIR, 'scorecard_classifier.pkl')

    if os.path.exists(model_path):
        # Get the modification time
        mod_time = os.path.getmtime(model_path)

        # Get the number of labeled images
        labeled_images = len(os.listdir(ANNOTATION_FOLDER))

        return jsonify({
            'trained': True,
            'last_trained': mod_time,
            'labeled_images': labeled_images
        })

    return jsonify({
        'trained': False,
        'labeled_images': len(os.listdir(ANNOTATION_FOLDER))
    })

@app.route('/api/ping', methods=['GET'])
def ping():
    """Simple ping endpoint to check if the server is running"""
    return jsonify({
        'status': 'ok',
        'message': 'Cricket Scorecard Labeling Server is running'
    })

@app.route('/api/clear-annotations', methods=['POST', 'OPTIONS'])
def clear_annotations():
    """Clear all annotations"""
    # Handle OPTIONS request for CORS
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        # Check if specific filenames were provided
        data = request.json or {}
        filenames = data.get('filenames', [])

        if filenames:
            # Clear only specific annotations
            cleared_count = 0
            for filename in filenames:
                annotation_path = os.path.join(ANNOTATION_FOLDER, filename + '.json')
                if os.path.isfile(annotation_path):
                    os.remove(annotation_path)
                    cleared_count += 1

            return jsonify({
                'success': True,
                'message': f'Successfully cleared {cleared_count} annotations',
                'cleared_count': cleared_count,
                'selective': True
            })
        else:
            # Count annotations before deletion
            annotation_count = len(os.listdir(ANNOTATION_FOLDER))

            # Delete all annotation files
            for filename in os.listdir(ANNOTATION_FOLDER):
                file_path = os.path.join(ANNOTATION_FOLDER, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)

            return jsonify({
                'success': True,
                'message': f'Successfully cleared all {annotation_count} annotations',
                'cleared_count': annotation_count,
                'selective': False
            })
    except Exception as e:
        print(f"Error clearing annotations: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/labeled-images', methods=['GET'])
def get_labeled_images():
    """Get all labeled images"""
    try:
        # Get all annotation files
        annotation_files = os.listdir(ANNOTATION_FOLDER)

        # Extract image filenames (remove .json extension)
        labeled_images = [os.path.splitext(filename)[0] for filename in annotation_files]

        # Get all images
        all_images = os.listdir(UPLOAD_FOLDER)

        # Create a list of image objects with labeled status
        image_list = []
        for image in all_images:
            image_list.append({
                'filename': image,
                'labeled': image in labeled_images,
                'url': f'/api/image/{image}'
            })

        return jsonify({
            'success': True,
            'labeled_images': labeled_images,
            'all_images': image_list
        })
    except Exception as e:
        print(f"Error getting labeled images: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/api/predict', methods=['POST', 'OPTIONS'])
def predict():
    """Predict elements in a scorecard image"""
    # Handle OPTIONS request for CORS
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response

    try:
        # Check if a file was uploaded
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Save the file temporarily
        temp_path = os.path.join(UPLOAD_FOLDER, 'temp_predict.jpg')
        file.save(temp_path)

        # Predict elements
        predictions = trainer.predict_elements(temp_path)

        # Return the predictions
        return jsonify({
            'success': True,
            'predictions': predictions
        })
    except Exception as e:
        print(f"Error predicting elements: {str(e)}")
        return jsonify({'error': str(e), 'success': False}), 500

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    """Serve the React app"""
    if path != "" and os.path.exists(app.static_folder + '/' + path):
        return send_from_directory(app.static_folder, path)
    else:
        return send_from_directory(app.static_folder, 'index.html')

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Cricket Scorecard Labeling Server')
    parser.add_argument('--host', default='0.0.0.0', help='Host to run the server on')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the server on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')

    args = parser.parse_args()

    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == '__main__':
    main()
