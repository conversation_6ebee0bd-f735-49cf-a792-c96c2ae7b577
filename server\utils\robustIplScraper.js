/**
 * Robust utility to extract IPL player names using multiple approaches
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Mock data as fallback with roles
const MOCK_PLAYER_DATA = [
  // Delhi Capitals
  { name: "<PERSON><PERSON><PERSON><PERSON>", role: "Wicket Keeper" },
  { name: "<PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON>", role: "<PERSON>rou<PERSON>" },
  { name: "<PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON>", role: "<PERSON><PERSON>" },

  // Mumbai Indians
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON><PERSON>", role: "<PERSON>rou<PERSON>" },
  { name: "<PERSON><PERSON> <PERSON><PERSON>", role: "<PERSON><PERSON><PERSON> Keeper" },
  { name: "<PERSON><PERSON>k <PERSON><PERSON>", role: "<PERSON>sman" },
  { name: "<PERSON> <PERSON>", role: "<PERSON><PERSON>nder" },
  { name: "<PERSON><PERSON>sh <PERSON>wl<PERSON>", role: "<PERSON><PERSON>" },
  { name: "<PERSON> <PERSON><PERSON>zee", role: "<PERSON>er" },

  // <PERSON> <PERSON> <PERSON>
  { name: "MS <PERSON><PERSON><PERSON>", role: "<PERSON>ick<PERSON> Keeper" },
  { name: "Ravindra Jadeja", role: "Allrounder" },
  { name: "Deepak Chahar", role: "Bowler" },
  { name: "Ruturaj Gaikwad", role: "Batsman" },
  { name: "Shivam Dube", role: "Allrounder" },
  { name: "Mitchell Santner", role: "Allrounder" },
  { name: "Moeen Ali", role: "Allrounder" },
  { name: "Ajinkya Rahane", role: "Batsman" },
  { name: "Shardul Thakur", role: "Bowler" },
  { name: "Mustafizur Rahman", role: "Bowler" },

  // Royal Challengers Bangalore
  { name: "Virat Kohli", role: "Batsman" },
  { name: "Faf du Plessis", role: "Batsman" },
  { name: "Glenn Maxwell", role: "Allrounder" },
  { name: "Mohammed Siraj", role: "Bowler" },
  { name: "Dinesh Karthik", role: "Wicket Keeper" },
  { name: "Cameron Green", role: "Allrounder" },
  { name: "Rajat Patidar", role: "Batsman" },

  // Kolkata Knight Riders
  { name: "Shreyas Iyer", role: "Batsman" },
  { name: "Nitish Rana", role: "Batsman" },
  { name: "Rinku Singh", role: "Batsman" },
  { name: "Sunil Narine", role: "Allrounder" },
  { name: "Andre Russell", role: "Allrounder" },
  { name: "Venkatesh Iyer", role: "Allrounder" },
  { name: "Mitchell Starc", role: "Bowler" },
  { name: "Varun Chakaravarthy", role: "Bowler" },

  // Sunrisers Hyderabad
  { name: "Pat Cummins", role: "Bowler" },
  { name: "Travis Head", role: "Batsman" },
  { name: "Aiden Markram", role: "Batsman" },
  { name: "Heinrich Klaasen", role: "Wicket Keeper" },
  { name: "Bhuvneshwar Kumar", role: "Bowler" },
  { name: "T Natarajan", role: "Bowler" },
  { name: "Washington Sundar", role: "Allrounder" },

  // Punjab Kings
  { name: "Shikhar Dhawan", role: "Batsman" },
  { name: "Kagiso Rabada", role: "Bowler" },
  { name: "Jonny Bairstow", role: "Wicket Keeper" },
  { name: "Liam Livingstone", role: "Allrounder" },
  { name: "Arshdeep Singh", role: "Bowler" },
  { name: "Sam Curran", role: "Allrounder" },
  { name: "Harshal Patel", role: "Bowler" },

  // Rajasthan Royals
  { name: "Sanju Samson", role: "Wicket Keeper" },
  { name: "Jos Buttler", role: "Wicket Keeper" },
  { name: "Yashasvi Jaiswal", role: "Batsman" },
  { name: "Shimron Hetmyer", role: "Batsman" },
  { name: "Ravichandran Ashwin", role: "Allrounder" },
  { name: "Trent Boult", role: "Bowler" },
  { name: "Yuzvendra Chahal", role: "Bowler" },

  // Gujarat Titans
  { name: "Hardik Pandya", role: "Allrounder" },
  { name: "Rashid Khan", role: "Bowler" },
  { name: "Shubman Gill", role: "Batsman" },
  { name: "Mohammed Shami", role: "Bowler" },
  { name: "Kane Williamson", role: "Batsman" },
  { name: "Rahul Tewatia", role: "Allrounder" },
  { name: "Wriddhiman Saha", role: "Wicket Keeper" },

  // Lucknow Super Giants
  { name: "KL Rahul", role: "Wicket Keeper" },
  { name: "Quinton de Kock", role: "Wicket Keeper" },
  { name: "Nicholas Pooran", role: "Wicket Keeper" },
  { name: "Marcus Stoinis", role: "Allrounder" },
  { name: "Krunal Pandya", role: "Allrounder" },
  { name: "Ravi Bishnoi", role: "Bowler" },
  { name: "Mark Wood", role: "Bowler" }
];

// List of IPL team URLs
const IPL_TEAM_URLS = [
  'https://www.iplt20.com/teams/chennai-super-kings/squad/2025',
  'https://www.iplt20.com/teams/delhi-capitals/squad/2025',
  'https://www.iplt20.com/teams/gujarat-titans/squad/2025',
  'https://www.iplt20.com/teams/kolkata-knight-riders/squad/2025',
  'https://www.iplt20.com/teams/lucknow-super-giants/squad/2025',
  'https://www.iplt20.com/teams/mumbai-indians/squad/2025',
  'https://www.iplt20.com/teams/punjab-kings/squad/2025',
  'https://www.iplt20.com/teams/rajasthan-royals/squad/2025',
  'https://www.iplt20.com/teams/royal-challengers-bengaluru/squad/2025',
  'https://www.iplt20.com/teams/sunrisers-hyderabad/squad/2025'
];

/**
 * Extract player data (names and roles) using multiple approaches
 * @param {string} url - Team URL
 * @returns {Promise<Array>} - Array of player data objects with name and role
 */
async function extractPlayerData(url) {
  console.log(`Extracting player data from ${url}`);
  let browser = null;

  try {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
    });

    const page = await browser.newPage();

    // Set a user agent to mimic a real browser
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    // Navigate to the page
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });

    // Take a screenshot for debugging
    const teamName = url.split('/').slice(-2)[0];
    const screenshotPath = path.join(__dirname, `../debug/${teamName}-screenshot.png`);

    // Create debug directory if it doesn't exist
    const debugDir = path.join(__dirname, '../debug');
    if (!fs.existsSync(debugDir)) {
      fs.mkdirSync(debugDir, { recursive: true });
    }

    await page.screenshot({ path: screenshotPath, fullPage: true });
    console.log(`Saved screenshot to ${screenshotPath}`);

    // Try multiple approaches to extract player data
    let playerData = [];

    // Approach 1: Try specific selectors for IPL website
    try {
      console.log('Trying approach 1: Specific selectors for player cards');

      // List of card selectors to try
      const cardSelectors = [
        '.ih-pcard1',
        '.player-card',
        '[class*="player-card"]',
        '.squad-player'
      ];

      for (const cardSelector of cardSelectors) {
        console.log(`Trying card selector: ${cardSelector}`);

        // Check if selector exists
        const exists = await page.evaluate((sel) => {
          return document.querySelector(sel) !== null;
        }, cardSelector);

        if (exists) {
          console.log(`Card selector ${cardSelector} found, extracting player data`);

          // Extract player data using this selector
          const players = await page.evaluate((cardSel) => {
            const cards = document.querySelectorAll(cardSel);
            console.log(`Found ${cards.length} player cards`);

            return Array.from(cards).map(card => {
              // Try to extract player name
              let name = '';
              const nameSelectors = ['.ih-p-name h2', 'h2', 'h3', '[class*="name"]', '.name'];
              for (const sel of nameSelectors) {
                const el = card.querySelector(sel);
                if (el && el.textContent.trim()) {
                  name = el.textContent.trim();
                  break;
                }
              }

              // Try to extract player role
              let role = '';
              const roleSelectors = ['.ih-p-img span.d-block', '.player-role', '[class*="role"]', '.role'];
              for (const sel of roleSelectors) {
                const el = card.querySelector(sel);
                if (el && el.textContent.trim()) {
                  role = el.textContent.trim();
                  break;
                }
              }

              // Map role to standardized format
              let standardRole = 'Unknown';
              if (role) {
                const roleLower = role.toLowerCase();
                if (roleLower.includes('keeper') || roleLower.includes('wicket') || roleLower.includes('wk')) {
                  standardRole = 'Wicket Keeper';
                } else if (roleLower.includes('bat')) {
                  standardRole = 'Batsman';
                } else if (roleLower.includes('bowl')) {
                  standardRole = 'Bowler';
                } else if (roleLower.includes('all')) {
                  standardRole = 'Allrounder';
                }
              }

              // Additional check for known wicket keepers
              const playerNameLower = name.toLowerCase();
              const knownKeepers = [
                'ms dhoni', 'rishabh pant', 'kl rahul', 'ishan kishan', 'sanju samson',
                'jos buttler', 'quinton de kock', 'nicholas pooran', 'dinesh karthik',
                'jitesh sharma', 'wriddhiman saha', 'jonny bairstow', 'heinrich klaasen',
                'phil salt', 'josh inglis', 'kumar kushagra', 'dhruv jurel'
              ];

              if (knownKeepers.includes(playerNameLower)) {
                standardRole = 'Wicket Keeper';
              }

              return { name, role: standardRole };
            }).filter(player => player.name); // Filter out players without names
          }, cardSelector);

          console.log(`Extracted ${players.length} players with card selector ${cardSelector}`);

          if (players.length > 0) {
            playerData = players;
            break;
          }
        }
      }
    } catch (error) {
      console.error('Error in approach 1:', error);
    }

    // Approach 2: If approach 1 failed, try a more generic approach
    if (playerData.length === 0) {
      console.log('Trying approach 2: Generic text extraction');

      try {
        // Save the HTML for debugging
        const html = await page.content();
        const htmlPath = path.join(__dirname, `../debug/${teamName}-page.html`);
        fs.writeFileSync(htmlPath, html);
        console.log(`Saved HTML to ${htmlPath}`);

        // Look for text that might be player names
        const names = await page.evaluate(() => {
          // Get all text elements
          const textElements = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div'))
            .filter(el => {
              // Filter elements that might contain player names
              const text = el.textContent.trim();
              // Player names typically have 2+ words, 5-30 chars, and no numbers
              return text &&
                     text.includes(' ') &&
                     text.length > 5 &&
                     text.length < 30 &&
                     !/\d/.test(text) &&
                     !/^\s*$/.test(text);
            })
            .map(el => el.textContent.trim());

          // Remove duplicates
          return [...new Set(textElements)];
        });

        console.log(`Found ${names.length} possible player names with generic approach`);

        // Convert names to player data objects with unknown roles
        playerData = names.map(name => ({ name, role: 'Unknown' }));
      } catch (error) {
        console.error('Error in approach 2:', error);
      }
    }

    // If both approaches failed, use mock data
    if (playerData.length === 0) {
      console.log('Both approaches failed, using mock data');

      // Use all mock data
      playerData = MOCK_PLAYER_DATA;

      console.log(`Using ${playerData.length} mock player data entries`);
    }

    // Log the first few entries for verification
    if (playerData.length > 0) {
      console.log('Sample player data:');
      playerData.slice(0, 5).forEach((player, i) => {
        console.log(`  ${i+1}. ${player.name} (${player.role})`);
      });
    } else {
      console.log('No player data found');
    }

    return playerData;
  } catch (error) {
    console.error('Error extracting player data:', error);
    return [];
  } finally {
    if (browser) await browser.close();
  }
}

/**
 * Export player data to CSV
 * @param {Array} playerData - Array of player data objects with name and role
 * @param {string} outputPath - Path to save the CSV
 */
function exportToCsv(playerData, outputPath) {
  // Create CSV content with player names and roles
  const csvHeader = 'Player Name,Role\n';
  const csvRows = playerData.map(player => `"${player.name}","${player.role}"`).join('\n');
  const csvContent = csvHeader + csvRows;

  // Write to file
  fs.writeFileSync(outputPath, csvContent);
  console.log(`Exported ${playerData.length} player entries to ${outputPath}`);
}

/**
 * Main function to extract and export all IPL player data
 */
async function exportAllPlayerNames() {
  console.log('Starting robust IPL player data export');

  // Create array to hold all player data
  let allPlayerData = [];

  // Process each team URL
  for (const url of IPL_TEAM_URLS) {
    const teamPlayerData = await extractPlayerData(url);
    allPlayerData = [...allPlayerData, ...teamPlayerData];
  }

  // Remove duplicates by name
  const nameMap = new Map();
  allPlayerData.forEach(player => {
    // If we already have this player, only update if the current role is more specific
    if (nameMap.has(player.name)) {
      const existingPlayer = nameMap.get(player.name);
      if (existingPlayer.role === 'Unknown' && player.role !== 'Unknown') {
        nameMap.set(player.name, player);
      }
    } else {
      nameMap.set(player.name, player);
    }
  });

  const uniquePlayerData = Array.from(nameMap.values());
  console.log(`Found ${uniquePlayerData.length} unique players across all teams`);

  // Create output directory if it doesn't exist
  const outputDir = path.join(__dirname, '../exports');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Export to CSV
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputPath = path.join(outputDir, `ipl-player-data-${timestamp}.csv`);
  exportToCsv(uniquePlayerData, outputPath);

  return outputPath;
}

// Export for use in other modules
module.exports = {
  exportAllPlayerNames
};

// If script is run directly, execute the export
if (require.main === module) {
  exportAllPlayerNames()
    .then(outputPath => {
      console.log(`Export completed. File saved to: ${outputPath}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error during export:', error);
      process.exit(1);
    });
}
