import axios from 'axios';
import { API_URL } from '../config';

// Create API instance for scorecard operations
const API = axios.create({
  baseURL: `${API_URL}/scorecards`
});

// Add auth token to requests
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * Upload a scorecard image for a match
 * @param {string} tournamentId - Tournament ID
 * @param {string} matchId - Match ID
 * @param {File} file - Scorecard image file
 * @returns {Promise} - Promise with upload result
 */
export const uploadScorecard = async (tournamentId, matchId, file) => {
  try {
    const formData = new FormData();
    formData.append('scorecard', file);

    // Add a flag to indicate this is a direct upload (not from guided capture)
    formData.append('source', 'direct-upload');

    // Use different endpoint based on whether we have a matchId or not
    const endpoint = matchId
      ? `/${tournamentId}/matches/${matchId}`
      : `/${tournamentId}`;

    console.log('Uploading scorecard to endpoint:', endpoint);

    const response = await API.post(
      endpoint,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error in uploadScorecard:', error);

    // Provide a more user-friendly error message
    if (error.response?.data?.error) {
      throw {
        message: error.response.data.error,
        fallbackRequired: error.response.data.fallbackRequired || false
      };
    } else if (error.response?.status === 413) {
      throw { message: 'The image file is too large. Please use a smaller image (max 5MB).' };
    } else if (error.response?.status === 415) {
      throw { message: 'Invalid file type. Please upload a valid image file (JPG, PNG, etc).' };
    } else if (error.message) {
      throw { message: error.message };
    } else {
      throw { message: 'Failed to upload scorecard. Please try again or use the guided capture method.' };
    }
  }
};

/**
 * Verify a match result (admin only)
 * @param {string} tournamentId - Tournament ID
 * @param {string} matchId - Match ID
 * @returns {Promise} - Promise with verification result
 */
export const verifyMatch = async (tournamentId, matchId) => {
  try {
    const response = await API.post(
      `/${tournamentId}/matches/${matchId}/verify`
    );
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

/**
 * Dispute a match result
 * @param {string} tournamentId - Tournament ID
 * @param {string} matchId - Match ID
 * @param {string} reason - Dispute reason
 * @returns {Promise} - Promise with dispute result
 */
export const disputeMatch = async (tournamentId, matchId, reason) => {
  try {
    const response = await API.post(
      `/${tournamentId}/matches/${matchId}/dispute`,
      { reason }
    );
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export default {
  uploadScorecard,
  verifyMatch,
  disputeMatch
};
