import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Box,
  TextField,
  MenuItem,
  Pagination,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';

import PlayerCard from '../../components/players/PlayerCard';
import { getPlayers } from '../../services/playerService';

const Players = () => {
  const navigate = useNavigate();
  const [players, setPlayers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Filter and sort state
  const [filters, setFilters] = useState({
    search: '',
    type: '',
    rarity: '',
    nationality: '',
    owner: '',
    sort: 'ratings.overall:desc'
  });

  // Player types
  const playerTypes = [
    'Batsman',
    'Bowler',
    'Batting Allrounder',
    'Bowling Allrounder',
    'Allrounder',
    'Wicket Keeper'
  ];

  // Player rarities
  const playerRarities = [
    'Common',
    'Rare',
    'Epic',
    'Legendary'
  ];

  // Sort options
  const sortOptions = [
    { value: 'ratings.overall:desc', label: 'Rating (High to Low)' },
    { value: 'ratings.overall:asc', label: 'Rating (Low to High)' },
    { value: 'marketValue:desc', label: 'Value (High to Low)' },
    { value: 'marketValue:asc', label: 'Value (Low to High)' },
    { value: 'name:asc', label: 'Name (A to Z)' },
    { value: 'name:desc', label: 'Name (Z to A)' }
  ];

  // Owner filter options
  const ownerOptions = [
    { value: '', label: 'All Players' },
    { value: 'market', label: 'Available on Market' },
    { value: 'free', label: 'Free Agents' }
  ];

  // Fetch players with filters and pagination
  useEffect(() => {
    const fetchPlayers = async () => {
      setLoading(true);
      try {
        const data = await getPlayers({
          page: currentPage,
          limit: 12,
          ...filters
        });

        setPlayers(data.players);
        setTotalPages(data.totalPages);
      } catch (err) {
        console.error('Error fetching players:', err);
        setError('Failed to load players. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPlayers();
  }, [currentPage, filters]);

  // Handle page change
  const handlePageChange = (event, value) => {
    setCurrentPage(value);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle filter changes
  const handleFilterChange = (event) => {
    const { name, value } = event.target;

    setFilters(prev => ({
      ...prev,
      [name]: value
    }));

    // Reset to first page when filters change
    setCurrentPage(1);
  };

  // Handle search input change with debounce
  const handleSearchChange = (event) => {
    const { value } = event.target;

    // Update search term immediately in the input
    setFilters(prev => ({
      ...prev,
      search: value
    }));

    // Debounce the actual filter application
    const timeoutId = setTimeout(() => {
      setCurrentPage(1);
    }, 500);

    // Clear timeout when component unmounts or when search changes again
    return () => clearTimeout(timeoutId);
  };

  // Handle click on player card to view details
  const handlePlayerClick = (playerId) => {
    navigate(`/players/${playerId}`);
  };

  // Clear all filters
  const handleClearFilters = () => {
    setFilters({
      search: '',
      type: '',
      rarity: '',
      nationality: '',
      owner: '',
      sort: 'ratings.overall:desc'
    });
    setCurrentPage(1);
  };

  // Render active filters as chips
  const renderActiveFilters = () => {
    const activeFilters = Object.entries(filters).filter(([key, value]) => {
      return value !== '' && key !== 'sort';
    });

    if (activeFilters.length === 0) {
      return null;
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2, mt: 1 }}>
        {activeFilters.map(([key, value]) => {
          let label = `${key}: ${value}`;

          // Format special filter labels
          if (key === 'type' && playerTypes.includes(value)) {
            label = `Type: ${value}`;
          } else if (key === 'rarity' && playerRarities.includes(value)) {
            label = `Rarity: ${value}`;
          } else if (key === 'owner') {
            const ownerOption = ownerOptions.find(opt => opt.value === value);
            label = `Status: ${ownerOption ? ownerOption.label : value}`;
          } else if (key === 'search') {
            label = `Search: ${value}`;
          } else if (key === 'nationality') {
            label = `Nationality: ${value}`;
          }

          return (
            <Chip
              key={key}
              label={label}
              onDelete={() => {
                setFilters(prev => ({
                  ...prev,
                  [key]: ''
                }));
                setCurrentPage(1);
              }}
              color="primary"
              variant="outlined"
              size="small"
            />
          );
        })}

        <Chip
          label="Clear All"
          onClick={handleClearFilters}
          color="secondary"
          size="small"
        />
      </Box>
    );
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Page title */}
      <Typography variant="h4" component="h1" gutterBottom>
        Player Cards
      </Typography>

      {/* Filters section */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          {/* Search */}
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              name="search"
              label="Search Players"
              variant="outlined"
              fullWidth
              value={filters.search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
              }}
            />
          </Grid>

          {/* Type filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Player Type</InputLabel>
              <Select
                name="type"
                value={filters.type}
                onChange={handleFilterChange}
                label="Player Type"
              >
                <MenuItem value="">All Types</MenuItem>
                {playerTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Rarity filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Rarity</InputLabel>
              <Select
                name="rarity"
                value={filters.rarity}
                onChange={handleFilterChange}
                label="Rarity"
              >
                <MenuItem value="">All Rarities</MenuItem>
                {playerRarities.map((rarity) => (
                  <MenuItem key={rarity} value={rarity}>
                    {rarity}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Owner/Status filter */}
          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Status</InputLabel>
              <Select
                name="owner"
                value={filters.owner}
                onChange={handleFilterChange}
                label="Status"
              >
                {ownerOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Sort options */}
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Sort By</InputLabel>
              <Select
                name="sort"
                value={filters.sort}
                onChange={handleFilterChange}
                label="Sort By"
              >
                {sortOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Active filters */}
        {renderActiveFilters()}
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading spinner */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Player cards grid */}
      {!loading && players.length === 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          No players found matching the current filters.
        </Alert>
      )}

      {!loading && players.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {players.map((player) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={player._id}>
              <PlayerCard
                player={player}
                onClick={() => handlePlayerClick(player._id)}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mt: 8, 
          mb: 3, 
          pt: 2,
          position: 'relative',
          zIndex: 1
        }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            size="large"
          />
        </Box>
      )}
    </Container>
  );
};

export default Players;