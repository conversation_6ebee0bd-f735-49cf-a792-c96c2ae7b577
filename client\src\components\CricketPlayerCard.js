import React from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import './CricketPlayerCard.css';

const CricketPlayerCard = ({ player, selected, onSelect, onEdit, onDelete }) => {
  const handleCardClick = (e) => {
    // Don't toggle selection if clicking on buttons
    if (e.target.closest('button') || e.target.closest('.MuiIconButton-root')) {
      return;
    }
    onSelect(player._id);
  };

  // Get player type abbreviation
  const getPlayerTypeAbbr = (type) => {
    switch (type.toLowerCase()) {
      case 'batsman':
        return 'BAT';
      case 'bowler':
        return 'BWL';
      case 'batting allrounder':
        return 'BAR';
      case 'bowling allrounder':
        return 'BAR';
      case 'allrounder':
        return 'ARD';
      case 'wicket keeper':
        return 'WKT';
      default:
        return 'BAT';
    }
  };

  // Format player rating with color
  const getRatingColor = (rating) => {
    if (rating >= 90) return '#00c853'; // Green for 90+
    if (rating >= 80) return '#ffab00'; // Amber for 80-89
    if (rating >= 70) return '#2196f3'; // Blue for 70-79
    if (rating >= 60) return '#e040fb'; // Purple for 60-69
    return '#ffffff'; // White for below 60
  };

  // Get player nationality code
  const getNationalityCode = (nationality) => {
    const countryMap = {
      'India': 'IN',
      'Australia': 'AU',
      'England': 'GB',
      'South Africa': 'ZA',
      'New Zealand': 'NZ',
      'West Indies': 'BB',
      'Pakistan': 'PK',
      'Sri Lanka': 'LK',
      'Bangladesh': 'BD',
      'Afghanistan': 'AF',
      'Zimbabwe': 'ZW',
      'Ireland': 'IE',
    };
    return countryMap[nationality] || nationality.substring(0, 2).toUpperCase();
  };

  return (
    <div 
      className="cricket-player-card" 
      onClick={handleCardClick}
      style={{
        backgroundImage: `url(${process.env.PUBLIC_URL}/card-backgrounds/cricket-card-bg.png)`
      }}
    >
      {/* Selection Checkbox */}
      <Box
        className="player-selection"
        onClick={(e) => {
          e.stopPropagation();
          onSelect(player._id);
        }}
      >
        {selected ? (
          <CheckBoxIcon sx={{ color: 'white', fontSize: 22 }} />
        ) : (
          <CheckBoxOutlineBlankIcon sx={{ color: 'white', fontSize: 22 }} />
        )}
      </Box>

      <div className="player-card-top">
        <div className="player-master-info">
          <div className="player-rating">
            <span style={{ color: getRatingColor(player.ratings.overall) }}>
              {player.ratings.overall}
            </span>
          </div>
          <div className="player-position">
            <span>{getPlayerTypeAbbr(player.type)}</span>
          </div>
          <div className="player-nation">
            <img
              src={`${process.env.PUBLIC_URL}/flags/${player.nationality.toLowerCase().replace(/\s+/g, '-')}.svg`}
              alt={player.nationality}
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = `${process.env.PUBLIC_URL}/flags/default-flag.svg`;
              }}
            />
          </div>
          <div className="player-team">
            <img
              src={player.teamLogo || `${process.env.PUBLIC_URL}/teams/default-team.svg`}
              alt="Team"
              onError={(e) => {
                e.target.onerror = null;
                e.target.src = `${process.env.PUBLIC_URL}/teams/default-team.svg`;
              }}
            />
          </div>
        </div>
        <div className="player-picture">
          <img
            src={player.image || `${process.env.PUBLIC_URL}/uploads/players/default.png`}
            alt={player.name}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = `${process.env.PUBLIC_URL}/uploads/players/default.png`;
            }}
          />
          <div className="player-extra">
            <span>{player.battingHand}</span>
            <span>{player.bowlingHand !== 'None' ? player.bowlingHand : 'DNB'}</span>
          </div>
        </div>
      </div>
      <div className="player-card-bottom">
        <div className="player-info">
          <div className="player-name">
            <span>{player.name.toUpperCase()}</span>
          </div>
          <div className="player-features">
            <div className="player-features-col">
              <span>
                <div className="player-feature-value">{player.height || '-'}</div>
                <div className="player-feature-title">HGT</div>
              </span>
              <span>
                <div className="player-feature-value">{player.stats?.battingAverage || '-'}</div>
                <div className="player-feature-title">AVG</div>
              </span>
              <span>
                <div className="player-feature-value">{player.stats?.strikeRate || '-'}</div>
                <div className="player-feature-title">SR</div>
              </span>
            </div>
            <div className="player-features-col">
              <span>
                <div className="player-feature-value">{player.stats?.wickets || '-'}</div>
                <div className="player-feature-title">WKT</div>
              </span>
              <span>
                <div className="player-feature-value">{player.stats?.economy || '-'}</div>
                <div className="player-feature-title">ECO</div>
              </span>
              <span>
                <div className="player-feature-value">{player.stats?.highScore || '-'}</div>
                <div className="player-feature-title">HS</div>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="player-actions">
        <IconButton
          onClick={(e) => {
            e.stopPropagation();
            onEdit(player);
          }}
          size="small"
          className="edit-button"
        >
          <EditIcon fontSize="small" />
        </IconButton>
        <IconButton
          onClick={(e) => {
            e.stopPropagation();
            onDelete(player._id);
          }}
          size="small"
          className="delete-button"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      </div>
    </div>
  );
};

export default CricketPlayerCard;
