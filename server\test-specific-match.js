const MatchOutcomeService = require('./services/matchOutcomeService');

async function testSpecificMatch() {
  try {
    console.log('Testing specific match outcome calculation...');
    
    const matchOutcomeService = new MatchOutcomeService();
    await matchOutcomeService.initialize();
    
    // Create a match object that matches the screenshot
    const match = {
      homeTeam: '681a8a001c449f2f8180e70b', // Chennai Super Kings
      awayTeam: '681a68fcd145d1e6c9fe510b', // Some other team
      date: new Date(),
      venue: 'CHENNAI CRICKET GROUND',
      format: 'T20',
      status: 'completed',
      homeTeamBattedFirst: false,
      team1IsHomeTeam: false,
      result: {
        homeTeamScore: {
          runs: 164,
          wickets: 2,
          overs: 18.2,
          extras: 0,
          ballsFaced: 0
        },
        awayTeamScore: {
          runs: 162,
          wickets: 5,
          overs: 20,
          extras: 0,
          ballsFaced: 0
        }
      }
    };
    
    console.log('\nMatch Object:');
    console.log('homeTeamBattedFirst:', match.homeTeamBattedFirst);
    console.log('team1IsHomeTeam:', match.team1IsHomeTeam);
    
    // Create OCR data
    const ocrData = {
      team1Score: {
        runs: 164,
        wickets: 2,
        overs: 18.2,
        extras: 0,
        ballsFaced: 110
      },
      team2Score: {
        runs: 162,
        wickets: 5,
        overs: 20,
        extras: 0,
        ballsFaced: 120
      },
      resultText: ''
    };
    
    // Directly test the determineWinner function
    console.log('\nDirect test of determineWinner function:');
    const directResult = matchOutcomeService.determineWinner(
      ocrData.team1Score,
      ocrData.team2Score,
      '',
      match
    );
    console.log('Direct result:', directResult);
    
    // Calculate match outcome
    const outcome = await matchOutcomeService.calculateMatchOutcome(ocrData, null, match);
    
    console.log('\nMatch Outcome:');
    console.log('Winner:', outcome.winner);
    console.log('Is Tie:', outcome.isTie);
    console.log('Team 1 Score:', outcome.team1Score);
    console.log('Team 2 Score:', outcome.team2Score);
    console.log('Result Description:', outcome.resultDescription);
    
    // Check if the result description is correct
    const expectedMarginType = 'wickets';
    const hasExpectedMarginType = outcome.resultDescription.toLowerCase().includes(expectedMarginType);
    
    if (!hasExpectedMarginType) {
      console.log(`\n❌ FAILED: Expected description to include '${expectedMarginType}', got: ${outcome.resultDescription}`);
    } else {
      console.log(`\n✅ PASSED: Result description correctly includes '${expectedMarginType}'`);
    }
    
    // Now try with the opposite batting order
    console.log('\n\nTesting with opposite batting order...');
    match.homeTeamBattedFirst = true;
    
    console.log('\nUpdated Match Object:');
    console.log('homeTeamBattedFirst:', match.homeTeamBattedFirst);
    console.log('team1IsHomeTeam:', match.team1IsHomeTeam);
    
    // Directly test the determineWinner function again
    console.log('\nDirect test of determineWinner function with updated match:');
    const directResult2 = matchOutcomeService.determineWinner(
      ocrData.team1Score,
      ocrData.team2Score,
      '',
      match
    );
    console.log('Direct result with updated match:', directResult2);
    
    // Calculate match outcome again
    const outcome2 = await matchOutcomeService.calculateMatchOutcome(ocrData, null, match);
    
    console.log('\nMatch Outcome (opposite batting order):');
    console.log('Winner:', outcome2.winner);
    console.log('Is Tie:', outcome2.isTie);
    console.log('Team 1 Score:', outcome2.team1Score);
    console.log('Team 2 Score:', outcome2.team2Score);
    console.log('Result Description:', outcome2.resultDescription);
    
    // Check if the result description is correct
    const expectedMarginType2 = 'runs';
    const hasExpectedMarginType2 = outcome2.resultDescription.toLowerCase().includes(expectedMarginType2);
    
    if (!hasExpectedMarginType2) {
      console.log(`\n❌ FAILED: Expected description to include '${expectedMarginType2}', got: ${outcome2.resultDescription}`);
    } else {
      console.log(`\n✅ PASSED: Result description correctly includes '${expectedMarginType2}'`);
    }
    
  } catch (error) {
    console.error('Error testing match outcome:', error);
  }
}

testSpecificMatch();