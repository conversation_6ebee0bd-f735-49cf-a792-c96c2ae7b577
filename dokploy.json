{"name": "rpl-cricket-app-fullstack", "type": "application", "buildpack": "hero<PERSON>/nodejs", "source": {"type": "github", "repository": "rhingonekar/rplwebapp", "branch": "main"}, "build": {"buildCommand": "chmod +x .dokploy/build.sh && .dokploy/build.sh"}, "deploy": {"startCommand": "node index.js", "port": 5000, "healthCheck": "/api/health", "domains": [{"host": "your-domain.com", "port": 5000, "https": false}], "network": {"publishAllPorts": true, "portBindings": {"5000/tcp": [{"hostPort": "5000"}]}}, "env": {"NODE_ENV": "production", "PORT": "5000"}}}