<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Match Form - Visual Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .form-header {
            background: #1976d2;
            color: white;
            padding: 15px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .section {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin: 20px 0;
            padding: 15px;
        }
        .section-title {
            background: #2196f3;
            color: white;
            padding: 8px 15px;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
        }
        .ocr-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            color: #856404;
        }
        .field-row {
            display: flex;
            gap: 15px;
            margin: 10px 0;
            align-items: center;
        }
        .field {
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
            background: #f9f9f9;
            min-width: 120px;
            text-align: center;
        }
        .field-label {
            font-weight: bold;
            min-width: 100px;
        }
        .team-section {
            display: flex;
            gap: 20px;
        }
        .team-column {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #fafafa;
        }
        .team-header {
            background: #4caf50;
            color: white;
            padding: 8px;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            text-align: center;
            font-weight: bold;
        }
        .player-row {
            display: flex;
            gap: 10px;
            margin: 8px 0;
            align-items: center;
        }
        .player-name {
            flex: 2;
            border: 1px solid #ddd;
            padding: 6px;
            border-radius: 3px;
            background: white;
        }
        .player-stat {
            flex: 1;
            border: 1px solid #ddd;
            padding: 6px;
            border-radius: 3px;
            background: white;
            text-align: center;
        }
        .add-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 10px;
        }
        .remove-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .auto-populated {
            background: #e8f5e8 !important;
            border-color: #4caf50 !important;
        }
        .legend {
            display: flex;
            gap: 20px;
            margin: 10px 0;
            font-size: 12px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-box {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-header">
            <h2>Enhanced Match Result Form - Auto-Populated from Scorecard OCR</h2>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-box auto-populated"></div>
                <span>Auto-populated from OCR (editable)</span>
            </div>
            <div class="legend-item">
                <div class="legend-box" style="background: #f9f9f9; border: 1px solid #ddd;"></div>
                <span>Manual input fields</span>
            </div>
        </div>

        <!-- OCR Validation Warning -->
        <div class="ocr-warning">
            <strong>⚠️ Please verify the following fields - OCR may have made errors:</strong>
            <ul>
                <li><strong>team1:</strong> Team name seems too short</li>
                <li><strong>venue:</strong> Venue information may be incomplete</li>
                <li><strong>player_3:</strong> Player name "ALI" seems incomplete</li>
            </ul>
            <em>All fields below are editable. Please review and correct any inaccuracies.</em>
        </div>

        <!-- Match Details Section -->
        <div class="section">
            <div class="section-title">📋 Match Details</div>
            <div class="field-row">
                <span class="field-label">Team 1:</span>
                <div class="field auto-populated">RS13</div>
                <span class="field-label">Team 2:</span>
                <div class="field auto-populated">TBA</div>
                <span class="field-label">Your Team:</span>
                <div class="field">[Select from dropdown]</div>
            </div>
            <div class="field-row">
                <span class="field-label">Match Date:</span>
                <div class="field">2025-01-23</div>
                <span class="field-label">Venue:</span>
                <div class="field auto-populated">T20 AT JUNCTION OVAL</div>
                <span class="field-label">Match Time:</span>
                <div class="field auto-populated">9:17 PM</div>
            </div>
        </div>

        <!-- Match Scores Section -->
        <div class="section">
            <div class="section-title">🏏 Match Scores</div>
            <div class="team-section">
                <div class="team-column">
                    <div class="team-header">Team 1 (RS13) Score</div>
                    <div class="field-row">
                        <span class="field-label">Runs:</span>
                        <div class="field auto-populated">113</div>
                        <span class="field-label">Wickets:</span>
                        <div class="field auto-populated">4</div>
                        <span class="field-label">Overs:</span>
                        <div class="field auto-populated">15.4</div>
                    </div>
                </div>
                <div class="team-column">
                    <div class="team-header">Team 2 (TBA) Score</div>
                    <div class="field-row">
                        <span class="field-label">Runs:</span>
                        <div class="field auto-populated">114</div>
                        <span class="field-label">Wickets:</span>
                        <div class="field auto-populated">3</div>
                        <span class="field-label">Overs:</span>
                        <div class="field auto-populated">7.5</div>
                    </div>
                </div>
            </div>
            <div class="field-row">
                <span class="field-label">Match Result:</span>
                <div class="field auto-populated">TBA won by 7 wickets (Auto-calculated)</div>
                <span class="field-label">Man of the Match:</span>
                <div class="field auto-populated">Guneet Wadhwa</div>
            </div>
        </div>

        <!-- Player Statistics Section -->
        <div class="section">
            <div class="section-title">👥 Player Statistics</div>
            <div class="team-section">
                <!-- Team 1 Players -->
                <div class="team-column">
                    <div class="team-header">Team 1 (RS13) Batsmen</div>
                    <div class="player-row">
                        <div class="player-name auto-populated">MOEEN ALI</div>
                        <div class="player-stat auto-populated">30 runs</div>
                        <div class="player-stat auto-populated">26 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">DENNIS AMISS</div>
                        <div class="player-stat auto-populated">29 runs</div>
                        <div class="player-stat auto-populated">22 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">YOUNIS KHAN</div>
                        <div class="player-stat auto-populated">21 runs</div>
                        <div class="player-stat auto-populated">19 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">DANIEL MARTYN</div>
                        <div class="player-stat auto-populated">11 runs</div>
                        <div class="player-stat auto-populated">6 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <button class="add-btn">+ Add Batsman</button>

                    <div style="margin-top: 20px;">
                        <div class="team-header">Team 1 (RS13) Bowlers</div>
                        <div class="player-row">
                            <div class="player-name auto-populated">DARREN SAMMY</div>
                            <div class="player-stat auto-populated">2 wickets</div>
                            <div class="player-stat auto-populated">49 runs</div>
                            <button class="remove-btn">Remove</button>
                        </div>
                        <div class="player-row">
                            <div class="player-name auto-populated">R SRAVAN</div>
                            <div class="player-stat auto-populated">1 wicket</div>
                            <div class="player-stat auto-populated">34 runs</div>
                            <button class="remove-btn">Remove</button>
                        </div>
                        <button class="add-btn">+ Add Bowler</button>
                    </div>
                </div>

                <!-- Team 2 Players -->
                <div class="team-column">
                    <div class="team-header">Team 2 (TBA) Batsmen</div>
                    <div class="player-row">
                        <div class="player-name auto-populated">GUNEET WADHWA</div>
                        <div class="player-stat auto-populated">60 runs</div>
                        <div class="player-stat auto-populated">26 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">LARRY GOMES</div>
                        <div class="player-stat auto-populated">31 runs</div>
                        <div class="player-stat auto-populated">11 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">JAMES NEESHAM</div>
                        <div class="player-stat auto-populated">14 runs</div>
                        <div class="player-stat auto-populated">6 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <div class="player-row">
                        <div class="player-name auto-populated">MICHAEL PEPPER</div>
                        <div class="player-stat auto-populated">6 runs</div>
                        <div class="player-stat auto-populated">3 balls</div>
                        <button class="remove-btn">Remove</button>
                    </div>
                    <button class="add-btn">+ Add Batsman</button>

                    <div style="margin-top: 20px;">
                        <div class="team-header">Team 2 (TBA) Bowlers</div>
                        <div class="player-row">
                            <div class="player-name auto-populated">GUNEET WADHWA</div>
                            <div class="player-stat auto-populated">3 wickets</div>
                            <div class="player-stat auto-populated">43 runs</div>
                            <button class="remove-btn">Remove</button>
                        </div>
                        <div class="player-row">
                            <div class="player-name auto-populated">IAN BISHOP</div>
                            <div class="player-stat auto-populated">1 wicket</div>
                            <div class="player-stat auto-populated">17 runs</div>
                            <button class="remove-btn">Remove</button>
                        </div>
                        <button class="add-btn">+ Add Bowler</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Match Notes Section -->
        <div class="section">
            <div class="section-title">📝 Match Notes</div>
            <div class="field-row">
                <textarea style="width: 100%; height: 60px; border: 1px solid #ddd; border-radius: 4px; padding: 8px;" 
                          class="auto-populated">Match result extracted from scorecard: TBA won by 7 wickets</textarea>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin-top: 30px;">
            <button style="background: #f44336; color: white; border: none; padding: 12px 24px; border-radius: 4px; margin-right: 10px;">Cancel</button>
            <button style="background: #4caf50; color: white; border: none; padding: 12px 24px; border-radius: 4px;">Save Match</button>
        </div>
    </div>
</body>
</html>
