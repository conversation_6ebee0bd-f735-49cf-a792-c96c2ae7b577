/* Mobile Dashboard Styles */

/* Common card styles for mobile dashboard */
.mobile-card {
  border-radius: 8px;
  background-color: #1e1e1e;
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px 16px;
  height: 220px; /* Fixed height for all cards */
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mobile-card-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.mobile-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #ffffff;
}

.mobile-card-subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 16px;
}

.mobile-card-button {
  margin-top: auto;
  width: 100%;
  padding: 10px 0;
  border-radius: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: none;
}

/* Activity card specific styles */
.mobile-activity-card {
  height: auto;
  min-height: 220px;
  padding: 16px;
}

.mobile-activity-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #ffffff;
  width: 100%;
  text-align: left;
}

.mobile-activity-list {
  width: 100%;
  padding: 0;
}

.mobile-activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-activity-item:last-child {
  border-bottom: none;
}

.mobile-activity-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.mobile-activity-content {
  flex: 1;
}

.mobile-activity-text {
  font-size: 0.875rem;
  color: #ffffff;
}

.mobile-activity-subtext {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}

/* Light mode overrides */
.light-mode .mobile-card {
  background-color: #ffffff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.light-mode .mobile-card-title {
  color: #333333;
}

.light-mode .mobile-card-subtitle {
  color: rgba(0, 0, 0, 0.6);
}

.light-mode .mobile-activity-title {
  color: #333333;
}

.light-mode .mobile-activity-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.light-mode .mobile-activity-text {
  color: #333333;
}

.light-mode .mobile-activity-subtext {
  color: rgba(0, 0, 0, 0.6);
}

/* Color variations for different card types */
.mobile-card.leaderboard .mobile-card-icon {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.mobile-card.game .mobile-card-icon {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.mobile-card.team .mobile-card-icon {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.mobile-card.tournament .mobile-card-icon {
  background-color: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

/* Button color variations */
.mobile-card.leaderboard .mobile-card-button {
  background-color: #2196f3;
  color: white;
}

.mobile-card.game .mobile-card-button {
  background-color: #ff9800;
  color: white;
}

.mobile-card.team .mobile-card-button {
  background-color: #4caf50;
  color: white;
}

.mobile-card.tournament .mobile-card-button {
  background-color: #9c27b0;
  color: white;
}
