/* Auction Player Card Styles - FIFA Inspired */
.cricket-player-card.auction-card {
  position: relative;
  width: 100%;
  max-width: 300px;
  height: 470px; /* Increased height to accommodate bid button */
  /* Using a gradient background instead of an image for better compatibility */
  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 2.5rem 0;
  z-index: 2;
  transition: 200ms ease-in;
  margin: 0 auto;
  margin-bottom: 25px; /* Increased bottom margin to prevent overlap */
  cursor: pointer;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  font-family: 'Saira Semi Condensed', sans-serif;
}

/* Enhanced gold card background with shine effect */
.cricket-player-card.auction-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e6c656 0%, #a17c32 100%);
  border-radius: 15px;
  z-index: -1;
}

/* Add shine effect */
.cricket-player-card.auction-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  z-index: 1;
  pointer-events: none;
}

.cricket-player-card.auction-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* Player card top section */
.cricket-player-card.auction-card .player-card-top {
  height: 50%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  position: relative;
}

/* Player master info (rating, position, nation) */
.cricket-player-card.auction-card .player-card-top .player-master-info {
  width: 100%;
  display: flex;
  flex-direction: column; /* Changed to vertical layout */
  align-items: flex-start; /* Align items to the left */
  padding: 0;
  margin-bottom: 0.3rem;
  position: relative;
  z-index: 5; /* Higher z-index to ensure it's above the player image */
  position: absolute; /* Position absolutely */
  top: 10px; /* Position from top */
  left: 25px; /* Adjusted to better align with player type and flag */
}

/* Removed vertical line effect */

.cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 2rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 0.5rem;
  padding-left: 0; /* Removed padding to better align with player type and flag */
  position: relative;
  z-index: 2; /* Ensure it's above the vertical line */
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); /* Add glow effect */
}

.cricket-player-card.auction-card .player-card-top .player-master-info .player-position {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 3; /* Higher z-index to ensure text is above the image */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 5px rgba(255, 255, 255, 0.5); /* Enhanced text shadow */
  margin-bottom: 0.5rem;
  padding-left: 0; /* Ensure alignment with rating */
}

.cricket-player-card.auction-card .player-card-top .player-master-info .player-nation {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 0; /* Ensure alignment with rating and position */
}

.cricket-player-card.auction-card .player-card-top .player-master-info .player-nation img {
  width: 1.8rem;
  height: 1.8rem;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); /* Add glow effect */
  z-index: 3;
}

/* Player picture */
.cricket-player-card.auction-card .player-card-top .player-picture {
  width: 180px;
  height: 160px;
  position: relative;
  margin: 20px auto 0; /* Added top margin to move image down */
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1; /* Lower z-index to prevent overlapping with text */
}

.cricket-player-card.auction-card .player-card-top .player-picture img {
  width: 160px;
  height: 160px;
  object-fit: contain; /* Changed to contain to prevent cropping */
  object-position: center center; /* Center the image */
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Player name directly on card background */
.cricket-player-card.auction-card .player-name-direct {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 3px;
  z-index: 5;
  position: relative;
  margin-top: -8px; /* Move up more */
}

.cricket-player-card.auction-card .player-name-direct span {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.5px;
  white-space: nowrap;
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 95%;
  text-align: center;
}

/* Player card bottom section */
.cricket-player-card.auction-card .player-card-bottom {
  height: 50%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  position: relative;
  padding-top: 0; /* Remove top padding */
}

/* Player info */
.cricket-player-card.auction-card .player-card-bottom .player-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.3rem 0; /* Further reduced padding */
}

/* Player name */
.cricket-player-card.auction-card .player-card-bottom .player-info .player-name {
  width: 100%;
  text-align: center;
  margin-bottom: 0.5rem;
}

/* Player name at the top of player-card-bottom */
.cricket-player-card.auction-card .player-card-bottom .player-info .player-name-top {
  width: 100%;
  text-align: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
}

.cricket-player-card.auction-card .player-card-bottom .player-info .player-name span {
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  text-shadow: 1px 1px #000;
}

/* Player info grid */
.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0.2rem 0;
  padding: 0 1rem;
}

.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.2rem;
}

.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-cell {
  display: flex;
  align-items: center;
  width: 48%;
}

.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label {
  font-size: 0.8rem;
  font-weight: 700;
  color: #fff;
  margin-right: 0.3rem;
}

.cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {
  font-size: 0.8rem;
  color: #fff;
}

/* Auction specific styles */
.cricket-player-card.auction-card .auction-status {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.cricket-player-card.auction-card .your-bid-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.cricket-player-card.auction-card .admin-controls {
  position: absolute;
  top: 40px;
  right: 10px;
  z-index: 100; /* Increased z-index to ensure it's above other elements */
  display: flex;
  pointer-events: auto; /* Ensure pointer events work */
}

.cricket-player-card.auction-card .auction-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 0.2rem;
  padding: 0 1rem;
  margin-bottom: 0.3rem; /* Reduced bottom margin */
}

.cricket-player-card.auction-card .auction-info .current-bid {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.3rem;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.3rem;
  border-radius: 8px;
}

.cricket-player-card.auction-card .auction-info .current-bid .bid-label {
  font-size: 0.8rem;
  color: #fff;
  font-weight: 600;
}

.cricket-player-card.auction-card .auction-info .current-bid .bid-amount {
  font-size: 1.3rem;
  font-weight: 700;
  color: #fff;
}

.cricket-player-card.auction-card .auction-info .current-bid .bidder-info {
  font-size: 0.7rem;
  color: #fff;
  opacity: 0.9;
}

.cricket-player-card.auction-card .auction-info .time-left {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  color: #fff;
  font-size: 0.8rem;
  margin-top: 0.3rem;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.3rem 0.5rem;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cricket-player-card.auction-card .auction-actions {
  position: relative;
  width: 100%;
  padding: 0 1rem;
  z-index: 10;
  margin-top: auto;
  margin-bottom: 15px; /* Increased bottom margin */
  padding-top: 5px; /* Added top padding */
}

.cricket-player-card.auction-card .auction-actions button {
  background: linear-gradient(135deg, #3a8dff 0%, #0d47a1 100%);
  transition: all 0.2s ease;
  font-weight: 600;
}

.cricket-player-card.auction-card .auction-actions button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.cricket-player-card.auction-card .auction-actions button:disabled {
  background: linear-gradient(135deg, #9e9e9e 0%, #616161 100%);
  opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cricket-player-card.auction-card {
    width: 100%;
    max-width: 280px;
    height: 440px; /* Increased height for medium screens */
    margin-bottom: 25px; /* Maintain bottom margin on medium screens */
  }

  .cricket-player-card.auction-card .player-card-top .player-master-info {
    left: 20px; /* Adjust left position for medium screens */
  }

  .cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {
    font-size: 1.8rem;
  }

  .cricket-player-card.auction-card .player-card-top .player-picture {
    width: 180px;
    height: 160px;
    margin: 20px auto 0; /* Maintain top margin in responsive view */
  }

  .cricket-player-card.auction-card .player-card-top .player-picture img {
    width: 160px;
    height: 160px;
    object-fit: contain; /* Maintain contain to prevent cropping */
  }

  /* Responsive player name */
  .cricket-player-card.auction-card .player-name-direct span {
    font-size: 1.3rem;
    letter-spacing: 0.3px;
  }

  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label,
  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  .cricket-player-card.auction-card {
    width: 100%;
    max-width: 260px;
    height: 420px; /* Increased height for small screens */
    margin-bottom: 25px; /* Maintain bottom margin on small screens */
  }

  .cricket-player-card.auction-card .player-card-top .player-master-info {
    left: 15px; /* Adjust left position for small screens */
  }

  .cricket-player-card.auction-card .player-card-top .player-master-info .player-rating {
    font-size: 1.6rem;
  }

  .cricket-player-card.auction-card .player-card-top .player-picture {
    width: 160px;
    height: 140px;
    margin: 20px auto 0; /* Maintain top margin in responsive view */
  }

  .cricket-player-card.auction-card .player-card-top .player-picture img {
    width: 140px;
    height: 140px;
    object-fit: contain; /* Maintain contain to prevent cropping */
  }

  /* Responsive player name for small screens */
  .cricket-player-card.auction-card .player-name-direct {
    margin-bottom: 8px;
  }

  .cricket-player-card.auction-card .player-name-direct span {
    font-size: 1.1rem;
    letter-spacing: 0px; /* Remove letter spacing on small screens */
    max-width: 95%;
  }

  .cricket-player-card.auction-card .player-card-bottom .player-info .player-name span {
    font-size: 1.2rem;
  }

  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid {
    margin: 0.3rem 0;
  }

  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-row {
    margin-bottom: 0.3rem;
  }

  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-label,
  .cricket-player-card.auction-card .player-card-bottom .player-info .player-info-grid .info-value {
    font-size: 0.7rem;
  }

  .cricket-player-card.auction-card .auction-info .current-bid .bid-amount {
    font-size: 1.1rem;
  }
}
