/**
 * Player Form Fix
 * 
 * This script fixes the layout issues with the player creation form
 * by targeting the problematic elements and applying fixes.
 */

// Function to fix the player form layout
export const fixPlayerFormLayout = () => {
  // Run this after the component has mounted
  setTimeout(() => {
    // Fix the Player Type and Batting Hand buttons
    const playerTypeButton = document.querySelector('.playing-style button:first-child');
    const battingHandButton = document.querySelector('.playing-style button:nth-child(2)');
    
    if (playerTypeButton) {
      playerTypeButton.style.minWidth = '120px';
      playerTypeButton.style.width = 'auto';
      playerTypeButton.style.whiteSpace = 'nowrap';
      playerTypeButton.style.overflow = 'visible';
      playerTypeButton.style.textOverflow = 'clip';
      playerTypeButton.style.padding = '8px 16px';
      
      // Replace "P..." with "Player Type"
      if (playerTypeButton.textContent.includes('P')) {
        playerTypeButton.textContent = 'Player Type';
      }
    }
    
    if (battingHandButton) {
      battingHandButton.style.minWidth = '120px';
      battingHandButton.style.width = 'auto';
      battingHandButton.style.whiteSpace = 'nowrap';
      battingHandButton.style.overflow = 'visible';
      battingHandButton.style.textOverflow = 'clip';
      battingHandButton.style.padding = '8px 16px';
      
      // Replace "B..." with "Batting Hand"
      if (battingHandButton.textContent.includes('B')) {
        battingHandButton.textContent = 'Batting Hand';
      }
    }
    
    // Fix the bowling hand dropdown
    const bowlingHandSelect = document.querySelector('.bowling-hand select');
    if (bowlingHandSelect) {
      bowlingHandSelect.style.minWidth = '180px';
    }
    
    // Add classes to help with CSS targeting
    const playingStyleContainer = document.querySelector('.playing-style');
    if (playingStyleContainer) {
      playingStyleContainer.classList.add('playing-style-container');
    }
    
    // Fix the form container
    const formContainer = document.querySelector('form');
    if (formContainer) {
      formContainer.classList.add('player-form-container');
    }
    
    // Fix form sections
    const formSections = document.querySelectorAll('.MuiBox-root');
    formSections.forEach(section => {
      section.classList.add('player-form-section');
    });
    
    console.log('Player form layout fixed');
  }, 500); // Wait for the DOM to be fully rendered
};

// Function to observe DOM changes and fix the form when it appears
export const observePlayerForm = () => {
  // Create a MutationObserver to watch for the form being added to the DOM
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        // Check if the player form has been added
        const playerForm = document.querySelector('.playing-style');
        if (playerForm) {
          fixPlayerFormLayout();
        }
      }
    });
  });
  
  // Start observing the document body for DOM changes
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  return observer;
};

// Export default function to initialize the fix
export default function initPlayerFormFix() {
  // Fix the form if it's already in the DOM
  fixPlayerFormLayout();
  
  // Observe for future form appearances
  const observer = observePlayerForm();
  
  // Return a cleanup function
  return () => {
    observer.disconnect();
  };
}
