/**
 * Authentication utility functions
 */

// Get the authentication token from localStorage
export const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Set the authentication token in localStorage
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
  } else {
    localStorage.removeItem('token');
  }
};

// Remove the authentication token from localStorage
export const removeAuthToken = () => {
  localStorage.removeItem('token');
};

// Check if the user is authenticated
export const isAuthenticated = () => {
  return !!getAuthToken();
};

// Parse the JWT token to get user information
export const parseToken = (token) => {
  if (!token) return null;
  
  try {
    // JWT tokens are in the format: header.payload.signature
    // We only need the payload part
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing token:', error);
    return null;
  }
};

// Get user information from the token
export const getUserFromToken = () => {
  const token = getAuthToken();
  if (!token) return null;
  
  return parseToken(token);
};

export default {
  getAuthToken,
  setAuthToken,
  removeAuthToken,
  isAuthenticated,
  parseToken,
  getUserFromToken
};
