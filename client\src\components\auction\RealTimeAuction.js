import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Alert,
  Chip,
  LinearProgress,
  Grid,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider
} from '@mui/material';
import {
  Timer as TimerIcon,
  Gavel as GavelIcon,
  Person as PersonIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { io } from 'socket.io-client';

const RealTimeAuction = ({ auctionId, onBidPlaced }) => {
  const { user, token } = useAuth();
  const [auction, setAuction] = useState(null);
  const [bidAmount, setBidAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [timeLeft, setTimeLeft] = useState('');
  const [recentBids, setRecentBids] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  
  const socketRef = useRef(null);
  const timerRef = useRef(null);

  // Initialize Socket.io connection
  useEffect(() => {
    if (!token || !auctionId) return;

    const socket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5000', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('Connected to server');
      setIsConnected(true);
      socket.emit('join_auction', auctionId);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from server');
      setIsConnected(false);
    });

    socket.on('auction_update', (data) => {
      console.log('Auction update received:', data);
      if (data.auctionId === auctionId) {
        setAuction(prev => ({
          ...prev,
          currentBid: data.currentBid,
          currentBidder: data.currentBidder,
          bidsCount: data.bidsCount
        }));
        
        if (data.type === 'bid_placed') {
          setRecentBids(prev => [data.bid, ...prev.slice(0, 9)]); // Keep last 10 bids
          setSuccess(`New bid placed: ${data.currentBid} credits`);
          setTimeout(() => setSuccess(''), 3000);
        }
      }
    });

    socket.on('bid_error', (data) => {
      console.log('Bid error:', data);
      setError(data.message);
      setLoading(false);
      setTimeout(() => setError(''), 5000);
    });

    socket.on('auction_ended', (data) => {
      console.log('Auction ended:', data);
      if (data.auctionId === auctionId) {
        setAuction(prev => ({ ...prev, status: 'completed' }));
        setSuccess('Auction has ended!');
      }
    });

    return () => {
      socket.disconnect();
    };
  }, [token, auctionId]);

  // Timer for countdown
  useEffect(() => {
    if (!auction?.endTime) return;

    const updateTimer = () => {
      const now = new Date();
      const end = new Date(auction.endTime);
      const diff = end - now;

      if (diff <= 0) {
        setTimeLeft('Auction Ended');
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        return;
      }

      const minutes = Math.floor(diff / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);
      setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
    };

    updateTimer();
    timerRef.current = setInterval(updateTimer, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [auction?.endTime]);

  // Fetch initial auction data
  useEffect(() => {
    const fetchAuction = async () => {
      try {
        const response = await fetch(`/api/auctions/${auctionId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          setAuction(data);
          setRecentBids(data.bids?.slice(-10).reverse() || []);
        }
      } catch (err) {
        console.error('Error fetching auction:', err);
        setError('Failed to load auction data');
      }
    };

    if (auctionId && token) {
      fetchAuction();
    }
  }, [auctionId, token]);

  const handlePlaceBid = async () => {
    if (!bidAmount || !auction) return;

    const amount = parseInt(bidAmount);
    if (amount <= auction.currentBid) {
      setError('Bid must be higher than current bid');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Emit bid through Socket.io for real-time processing
      socketRef.current?.emit('place_bid', {
        auctionId,
        amount
      });

      setBidAmount('');
      if (onBidPlaced) {
        onBidPlaced(auction, amount);
      }
    } catch (err) {
      console.error('Error placing bid:', err);
      setError('Failed to place bid');
      setLoading(false);
    }
  };

  const getMinimumBid = () => {
    if (!auction) return 0;
    return auction.currentBid + (auction.minimumBidIncrement || 100);
  };

  const getTimeProgress = () => {
    if (!auction?.startTime || !auction?.endTime) return 0;
    
    const now = new Date();
    const start = new Date(auction.startTime);
    const end = new Date(auction.endTime);
    const total = end - start;
    const elapsed = now - start;
    
    return Math.min(100, Math.max(0, (elapsed / total) * 100));
  };

  if (!auction) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading auction...</Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 800, mx: 'auto', mt: 2 }}>
      <CardContent>
        {/* Connection Status */}
        <Box sx={{ mb: 2 }}>
          <Chip 
            label={isConnected ? 'Connected' : 'Disconnected'} 
            color={isConnected ? 'success' : 'error'}
            size="small"
          />
        </Box>

        {/* Player Info */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" gutterBottom>
            {auction.player?.name || 'Unknown Player'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {auction.player?.type} • {auction.player?.nationality}
          </Typography>
        </Box>

        {/* Current Bid Info */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {auction.currentBid?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2">Current Bid (Credits)</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="secondary">
                <TimerIcon sx={{ mr: 1 }} />
                {timeLeft}
              </Typography>
              <Typography variant="body2">Time Remaining</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Time Progress */}
        <Box sx={{ mb: 3 }}>
          <LinearProgress 
            variant="determinate" 
            value={getTimeProgress()} 
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Current Bidder */}
        {auction.currentBidder && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Current Highest Bidder:
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                <PersonIcon fontSize="small" />
              </Avatar>
              <Typography variant="body1">
                {auction.currentBidder.username || 'Anonymous'}
              </Typography>
            </Box>
          </Box>
        )}

        {/* Bid Input */}
        {auction.status === 'live' && user && (
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={8}>
                <TextField
                  fullWidth
                  type="number"
                  label="Bid Amount"
                  value={bidAmount}
                  onChange={(e) => setBidAmount(e.target.value)}
                  placeholder={`Minimum: ${getMinimumBid().toLocaleString()}`}
                  disabled={loading}
                  InputProps={{
                    endAdornment: <Typography variant="body2">Credits</Typography>
                  }}
                />
              </Grid>
              <Grid item xs={4}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handlePlaceBid}
                  disabled={loading || !bidAmount || parseInt(bidAmount) <= auction.currentBid}
                  startIcon={<GavelIcon />}
                >
                  {loading ? 'Placing...' : 'Place Bid'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Recent Bids */}
        {recentBids.length > 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              <TrendingUpIcon sx={{ mr: 1 }} />
              Recent Bids
            </Typography>
            <List dense>
              {recentBids.slice(0, 5).map((bid, index) => (
                <React.Fragment key={bid._id || index}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ width: 32, height: 32 }}>
                        <PersonIcon fontSize="small" />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${bid.amount?.toLocaleString()} Credits`}
                      secondary={`${bid.bidderName} • ${new Date(bid.time).toLocaleTimeString()}`}
                    />
                  </ListItem>
                  {index < recentBids.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default RealTimeAuction;