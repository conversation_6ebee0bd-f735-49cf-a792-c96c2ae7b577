/**
 * Mock IPL player data for testing the IPL player import feature
 */

const mockIplTeams = {
  'delhi-capitals': {
    teamName: 'Delhi Capitals',
    players: [
      {
        name: '<PERSON><PERSON>',
        type: 'Wicket Keeper',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/19.png',
        nationality: 'India',
        battingHand: 'RHB',
        bowlingHand: 'None',
        height: '5\'11"',
        ratings: {
          overall: 88,
          batting: 90,
          bowling: 30,
          fielding: 85
        },
        stats: {
          matches: 125,
          runs: 4500,
          highestScore: 132,
          battingAverage: 45.5,
          strikeRate: 138.7,
          wickets: 0,
          bowlingAverage: 0,
          economy: 0
        }
      },
      {
        name: '<PERSON>',
        type: 'Batsman',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/3115.png',
        nationality: 'Australia',
        battingHand: 'RHB',
        bowlingHand: 'None',
        height: '5\'10"',
        ratings: {
          overall: 82,
          batting: 85,
          bowling: 20,
          fielding: 80
        },
        stats: {
          matches: 8,
          runs: 330,
          highestScore: 84,
          battingAverage: 41.25,
          strikeRate: 233.33,
          wickets: 0,
          bowlingAverage: 0,
          economy: 0
        }
      },
      {
        name: 'Axar <PERSON>',
        type: 'Allrounder',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/110.png',
        nationality: 'India',
        battingHand: 'LHB',
        bowlingHand: 'SLA',
        height: '6\'0"',
        ratings: {
          overall: 86,
          batting: 75,
          bowling: 88,
          fielding: 82
        },
        stats: {
          matches: 142,
          runs: 1500,
          highestScore: 65,
          battingAverage: 28.5,
          strikeRate: 138.2,
          wickets: 120,
          bowlingAverage: 29.8,
          economy: 7.2
        }
      },
      {
        name: 'Mitchell Starc',
        type: 'Bowler',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/31.png',
        nationality: 'Australia',
        battingHand: 'LHB',
        bowlingHand: 'LFM',
        height: '6\'5"',
        ratings: {
          overall: 89,
          batting: 45,
          bowling: 92,
          fielding: 75
        },
        stats: {
          matches: 58,
          runs: 200,
          highestScore: 29,
          battingAverage: 10.5,
          strikeRate: 120.5,
          wickets: 73,
          bowlingAverage: 20.5,
          economy: 8.2
        }
      },
      {
        name: 'Kuldeep Yadav',
        type: 'Bowler',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/14.png',
        nationality: 'India',
        battingHand: 'LHB',
        bowlingHand: 'LCG',
        height: '5\'8"',
        ratings: {
          overall: 84,
          batting: 30,
          bowling: 88,
          fielding: 70
        },
        stats: {
          matches: 82,
          runs: 120,
          highestScore: 19,
          battingAverage: 8.5,
          strikeRate: 100.5,
          wickets: 95,
          bowlingAverage: 25.8,
          economy: 8.4
        }
      }
    ]
  },
  'mumbai-indians': {
    teamName: 'Mumbai Indians',
    players: [
      {
        name: 'Rohit Sharma',
        type: 'Batsman',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/107.png',
        nationality: 'India',
        battingHand: 'RHB',
        bowlingHand: 'ROB',
        height: '5\'9"',
        ratings: {
          overall: 90,
          batting: 92,
          bowling: 40,
          fielding: 82
        },
        stats: {
          matches: 243,
          runs: 6211,
          highestScore: 109,
          battingAverage: 29.4,
          strikeRate: 130.5,
          wickets: 15,
          bowlingAverage: 30.5,
          economy: 8.7
        }
      },
      {
        name: 'Jasprit Bumrah',
        type: 'Bowler',
        imageUrl: 'https://documents.iplt20.com/ipl/IPLHeadshot2025/1124.png',
        nationality: 'India',
        battingHand: 'RHB',
        bowlingHand: 'RFM',
        height: '5\'10"',
        ratings: {
          overall: 93,
          batting: 25,
          bowling: 95,
          fielding: 75
        },
        stats: {
          matches: 120,
          runs: 56,
          highestScore: 10,
          battingAverage: 5.6,
          strikeRate: 84.8,
          wickets: 145,
          bowlingAverage: 21.2,
          economy: 7.4
        }
      }
    ]
  }
};

// Export the Delhi Capitals players by default for fallback
module.exports = mockIplTeams['delhi-capitals'].players;
