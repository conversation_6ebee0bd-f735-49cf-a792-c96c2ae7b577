const OCRService = require('./services/ocrService');
const path = require('path');

async function testOCRVerification() {
  console.log('🚀 Testing OCR with Google Vision Verification...\n');

  try {
    // Test image path
    const imagePath = path.join(__dirname, 'uploads/scorecards/scorecard9.png');
    
    console.log(`📸 Testing with image: ${imagePath}`);

    // Initialize OCR service
    const ocrService = new OCRService();
    
    // Enable Google Vision verification
    ocrService.useGoogleVisionVerification = true;

    // Process the image with verification
    console.log('\n🔍 Processing image with verification:');
    const result = await ocrService.processImage(imagePath);
    
    // Check if verification was performed
    if (result.verification) {
      console.log(`\n✅ Verification performed using ${result.verification.method}`);
      console.log(`Players verified: ${result.verification.playersVerified}`);
    } else {
      console.log('\n❌ No verification was performed');
    }
    
    // Look for JOHN MORTIMORE in the results
    console.log('\n👥 Looking for JOHN MORTIMORE:');
    
    const johnInTeam1 = result.team1Batsmen.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    const johnInTeam2 = result.team2Batsmen.find(player => 
      player.name.toUpperCase().includes('JOHN') && 
      player.name.toUpperCase().includes('MORTIMORE')
    );
    
    if (johnInTeam1) {
      console.log(`Found in Team 1: ${johnInTeam1.name} -> ${johnInTeam1.runs}(${johnInTeam1.balls})`);
      if (johnInTeam1.verified) {
        console.log(`Original score: ${johnInTeam1.originalRuns}(${johnInTeam1.originalBalls})`);
        console.log(`✅ Score was verified and corrected by Google Vision`);
      }
    } else if (johnInTeam2) {
      console.log(`Found in Team 2: ${johnInTeam2.name} -> ${johnInTeam2.runs}(${johnInTeam2.balls})`);
      if (johnInTeam2.verified) {
        console.log(`Original score: ${johnInTeam2.originalRuns}(${johnInTeam2.originalBalls})`);
        console.log(`✅ Score was verified and corrected by Google Vision`);
      }
    } else {
      console.log('❌ JOHN MORTIMORE not found in either team');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testOCRVerification();