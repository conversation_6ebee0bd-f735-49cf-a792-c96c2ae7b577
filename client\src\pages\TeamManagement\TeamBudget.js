import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Grid,
  Paper,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  TextField,
  Slider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Snackbar,
  useTheme,
  useMediaQuery,
  LinearProgress
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import InfoIcon from '@mui/icons-material/Info';
// Replace PieChart with a custom implementation using regular MUI components
// import { PieChart } from '@mui/x-charts/PieChart';
import { useAuth } from '../../hooks/useAuth';
import { getTeamBudget, updateTeamBudget } from '../../services/teamService';

// Budget categories
const budgetCategories = [
  { id: 'playerAcquisition', name: 'Player Acquisition', color: '#1e88e5', defaultPercentage: 60 },
  { id: 'playerDevelopment', name: 'Player Development', color: '#43a047', defaultPercentage: 20 },
  { id: 'teamOperations', name: 'Team Operations', color: '#fb8c00', defaultPercentage: 10 },
  { id: 'marketing', name: 'Marketing', color: '#8e24aa', defaultPercentage: 5 },
  { id: 'reserve', name: 'Reserve Fund', color: '#e53935', defaultPercentage: 5 }
];

const TeamBudget = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [budgetData, setBudgetData] = useState({
    totalBudget: 10000,
    allocations: budgetCategories.reduce((acc, category) => {
      acc[category.id] = {
        percentage: category.defaultPercentage,
        amount: (10000 * category.defaultPercentage) / 100
      };
      return acc;
    }, {}),
    transactions: []
  });

  // Fetch team budget on component mount
  useEffect(() => {
    const fetchTeamBudget = async () => {
      if (!user || user.role !== 'team_owner') return;

      setLoading(true);
      try {
        const data = await getTeamBudget();
        setBudgetData(data);
      } catch (err) {
        console.error('Error fetching team budget:', err);
        setError('Failed to load team budget. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTeamBudget();
  }, [user]);

  const handleSliderChange = (categoryId, newValue) => {
    // Calculate the total percentage of other categories
    const otherCategoriesTotal = Object.entries(budgetData.allocations)
      .filter(([id]) => id !== categoryId)
      .reduce((sum, [, allocation]) => sum + allocation.percentage, 0);

    // Check if the new total would exceed 100%
    if (newValue + otherCategoriesTotal > 100) {
      return; // Don't allow changes that would make the total exceed 100%
    }

    // Update the allocation for the changed category
    setBudgetData(prev => {
      const newAllocations = {
        ...prev.allocations,
        [categoryId]: {
          percentage: newValue,
          amount: (prev.totalBudget * newValue) / 100
        }
      };

      return {
        ...prev,
        allocations: newAllocations
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      await updateTeamBudget(budgetData);
      setSuccess('Budget allocations updated successfully');
    } catch (err) {
      console.error('Error updating team budget:', err);
      setError('Failed to update budget allocations. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Calculate total percentage to ensure it's 100%
  const totalPercentage = Object.values(budgetData.allocations)
    .reduce((sum, allocation) => sum + allocation.percentage, 0);

  // Prepare data for pie chart
  const pieChartData = Object.entries(budgetData.allocations).map(([id, allocation]) => {
    const category = budgetCategories.find(cat => cat.id === id);
    return {
      id,
      value: allocation.percentage,
      label: category.name,
      color: category.color
    };
  });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Team Budget
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          Manage your team's budget allocations and financial resources
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      <Grid container spacing={3}>
        {/* Budget Overview */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Budget Overview
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Total Budget
              </Typography>
              <Typography variant="h4" color="primary">
                {budgetData.totalBudget.toLocaleString()} Credits
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Budget Allocation
              </Typography>

              {totalPercentage !== 100 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Total allocation must equal 100% (currently {totalPercentage}%)
                </Alert>
              )}

              <Box sx={{ height: 250, width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                {/* Custom pie chart visualization using Box components */}
                <Box sx={{
                  position: 'relative',
                  width: 200,
                  height: 200,
                  borderRadius: '50%',
                  overflow: 'hidden',
                  boxShadow: 2
                }}>
                  {pieChartData.map((segment, index) => {
                    // Calculate the segment's position in the circle
                    const startPercent = pieChartData
                      .slice(0, index)
                      .reduce((sum, item) => sum + item.value, 0);

                    const endPercent = startPercent + segment.value;

                    // Convert percentages to degrees for the conic gradient
                    const startAngle = (startPercent / 100) * 360;
                    const endAngle = (endPercent / 100) * 360;

                    return (
                      <Box
                        key={segment.id}
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          background: `conic-gradient(
                            ${segment.color} ${startAngle}deg,
                            ${segment.color} ${endAngle}deg,
                            transparent ${endAngle}deg,
                            transparent ${startAngle + 360}deg
                          )`
                        }}
                      />
                    );
                  })}

                  {/* Center circle for donut chart effect */}
                  <Box sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '60%',
                    height: '60%',
                    borderRadius: '50%',
                    bgcolor: 'background.paper',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column'
                  }}>
                    <Typography variant="caption" color="text.secondary">
                      Total
                    </Typography>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {totalPercentage}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            <List dense>
              {budgetCategories.map((category) => (
                <ListItem key={category.id}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: category.color,
                      mr: 1
                    }}
                  />
                  <ListItemText
                    primary={category.name}
                    secondary={`${budgetData.allocations[category.id]?.percentage || 0}% (${(budgetData.allocations[category.id]?.amount || 0).toLocaleString()} Credits)`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Budget Allocation Form */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Budget Allocation
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {budgetCategories.map((category) => (
                  <Grid item xs={12} key={category.id}>
                    <Box sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2">
                          {category.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {budgetData.allocations[category.id]?.percentage || 0}%
                        </Typography>
                      </Box>
                      <Slider
                        value={budgetData.allocations[category.id]?.percentage || 0}
                        onChange={(e, newValue) => handleSliderChange(category.id, newValue)}
                        aria-labelledby={`${category.id}-slider`}
                        valueLabelDisplay="auto"
                        step={1}
                        min={0}
                        max={100}
                        sx={{
                          color: category.color,
                          '& .MuiSlider-thumb': {
                            '&:hover, &.Mui-focusVisible': {
                              boxShadow: `0px 0px 0px 8px ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.16)' : 'rgba(0, 0, 0, 0.16)'}`
                            }
                          }
                        }}
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Amount: {(budgetData.allocations[category.id]?.amount || 0).toLocaleString()} Credits
                      </Typography>
                      <Tooltip title={`Recommended: ${category.defaultPercentage}%`}>
                        <IconButton size="small">
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <Divider />
                  </Grid>
                ))}

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      startIcon={<SaveIcon />}
                      disabled={saving || totalPercentage !== 100}
                      sx={{ minWidth: 150 }}
                    >
                      {saving ? <CircularProgress size={24} /> : 'Save Allocations'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>

          {/* Recent Transactions */}
          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Transactions
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {budgetData.transactions.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  No recent transactions to display.
                </Typography>
              </Box>
            ) : (
              <List>
                {budgetData.transactions.map((transaction, index) => (
                  <ListItem key={index} divider={index < budgetData.transactions.length - 1}>
                    <ListItemText
                      primary={transaction.description}
                      secondary={new Date(transaction.date).toLocaleDateString()}
                    />
                    <ListItemSecondaryAction>
                      <Typography
                        variant="body2"
                        color={transaction.amount > 0 ? 'success.main' : 'error.main'}
                      >
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount.toLocaleString()} Credits
                      </Typography>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default TeamBudget;
