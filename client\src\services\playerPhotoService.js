import axios from 'axios';
import { API_URL } from '../config';

const API = axios.create({
  baseURL: `${API_URL}/player-photos`,
});

// Add auth token to requests if available
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  
  if (token) {
    config.headers['x-auth-token'] = token;
  }
  
  return config;
});

/**
 * Match players with IPL data and update their photos
 * @param {Array} playerIds - Array of player IDs to match
 * @param {string} iplTeamUrl - IPL team URL to scrape player data from
 * @returns {Promise} - Promise resolving to match results
 */
export const matchPlayerPhotos = async (playerIds, iplTeamUrl) => {
  try {
    console.log(`Matching photos for ${playerIds.length} players using IPL team URL: ${iplTeamUrl}`);
    
    const response = await API.post('/match', {
      playerIds,
      iplTeamUrl
    });
    
    console.log('Match response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error matching player photos:', error);
    throw error.response?.data || error.message;
  }
};

export default {
  matchPlayerPhotos
};
