# RPL Cricket Project Board Setup Script
# Creates GitHub Project Board with proper phase structure and historical tracking
# Requires GitHub CLI (gh) to be installed and authenticated

param(
    [string]$RepoOwner = "rhingonekar",
    [string]$RepoName = "rpl<PERSON><PERSON><PERSON>",
    [string]$ProjectTitle = "RPL Cricket - Big Ant Cricket 24 System",
    [switch]$DryRun = $false
)

Write-Host "🚀 RPL Cricket Project Board Setup" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# Check GitHub CLI
try {
    $ghVersion = gh --version 2>$null
    if ($LASTEXITCODE -ne 0) { throw "GitHub CLI not found" }
    Write-Host "✅ GitHub CLI ready: $($ghVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "   winget install --id GitHub.cli" -ForegroundColor Yellow
    exit 1
}

# Check authentication
try {
    gh auth status 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) { throw "Not authenticated" }
    Write-Host "✅ GitHub authentication verified" -ForegroundColor Green
} catch {
    Write-Host "❌ Not authenticated. Please run: gh auth login" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Project Board Configuration
$ProjectConfig = @{
    Title = $ProjectTitle
    Body = @"
Systematic tracking of RPL Cricket Application development from Phase 1.0 to 7.0.

## Project Overview
- **Phase 1.0**: Core Infrastructure & Authentication ✅ COMPLETE
- **Phase 2.0**: Player & Team Management 🔄 95% COMPLETE  
- **Phase 3.0**: Tournament & Match Management 🔄 75% COMPLETE
- **Phase 4.0**: Auction System 🔄 90% COMPLETE
- **Phase 5.0**: Advanced Features & Analytics 📋 20% COMPLETE
- **Phase 6.0**: Production & Deployment 🔄 70% COMPLETE
- **Phase 7.0**: Big Ant Cricket 24 Integration 📋 0% COMPLETE

## Key Features
- Automatic updates from git commits
- Phase-based task organization
- Historical work tracking
- Big Ant Cricket 24 vision alignment
"@
}

# Define project board columns/fields
$BoardColumns = @(
    @{ Name = "📋 Backlog"; Description = "Planned tasks not yet started" }
    @{ Name = "🎯 Ready"; Description = "Tasks ready to be worked on" }
    @{ Name = "🔄 In Progress"; Description = "Currently being worked on" }
    @{ Name = "👀 Review"; Description = "Completed work under review" }
    @{ Name = "✅ Complete"; Description = "Finished and verified tasks" }
)

# Define phase-based views
$PhaseViews = @(
    @{ Name = "Phase 1.0 - Infrastructure"; Filter = "label:phase-1" }
    @{ Name = "Phase 2.0 - Player Management"; Filter = "label:phase-2" }
    @{ Name = "Phase 3.0 - Tournaments"; Filter = "label:phase-3" }
    @{ Name = "Phase 4.0 - Auctions"; Filter = "label:phase-4" }
    @{ Name = "Phase 5.0 - Analytics"; Filter = "label:phase-5" }
    @{ Name = "Phase 6.0 - Production"; Filter = "label:phase-6" }
    @{ Name = "Phase 7.0 - Big Ant Cricket 24"; Filter = "label:phase-7" }
)

# Function to create project board
function New-ProjectBoard {
    param($Config)
    
    Write-Host "📋 Creating Project Board: $($Config.Title)" -ForegroundColor Cyan
    
    if ($DryRun) {
        Write-Host "   [DRY RUN] Would create project board" -ForegroundColor Yellow
        return "dry-run-project-id"
    }
    
    try {
        # Create the project using GitHub CLI
        $repositoryId = gh api repos/$RepoOwner/$RepoName --jq .node_id
        $projectJson = gh api graphql -f query='
            mutation($repositoryId: ID!, $title: String!) {
                createProjectV2(input: {
                    ownerId: $repositoryId
                    title: $title
                }) {
                    projectV2 {
                        id
                        number
                        title
                        url
                    }
                }
            }
        ' -f repositoryId="$repositoryId" -f title="$($Config.Title)"
        
        $project = ($projectJson | ConvertFrom-Json).data.createProjectV2.projectV2
        
        Write-Host "   ✅ Created project: $($project.title)" -ForegroundColor Green
        Write-Host "   🔗 URL: $($project.url)" -ForegroundColor Blue
        
        return $project.id
    } catch {
        Write-Host "   ❌ Failed to create project: $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

# Function to add project fields
function Add-ProjectFields {
    param($ProjectId)
    
    Write-Host "🏗️  Setting up project fields..." -ForegroundColor Cyan
    
    # Add Phase field
    if ($DryRun) {
        Write-Host "   [DRY RUN] Would add Phase field" -ForegroundColor Yellow
    } else {
        try {
            $phaseField = gh api graphql -f query='
                mutation($projectId: ID!) {
                    createProjectV2Field(input: {
                        projectId: $projectId
                        dataType: SINGLE_SELECT
                        name: "Phase"
                        singleSelectOptions: [
                            {name: "Phase 1.0", description: "Core Infrastructure", color: GREEN}
                            {name: "Phase 2.0", description: "Player Management", color: BLUE}
                            {name: "Phase 3.0", description: "Tournaments", color: PURPLE}
                            {name: "Phase 4.0", description: "Auctions", color: YELLOW}
                            {name: "Phase 5.0", description: "Analytics", color: ORANGE}
                            {name: "Phase 6.0", description: "Production", color: RED}
                            {name: "Phase 7.0", description: "Big Ant Cricket 24", color: PINK}
                        ]
                    }) {
                        projectV2Field {
                            ... on ProjectV2SingleSelectField {
                                id
                                name
                            }
                        }
                    }
                }
            ' -f projectId="$ProjectId"
            
            Write-Host "   ✅ Added Phase field" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️  Could not add Phase field: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # Add Priority field
    if ($DryRun) {
        Write-Host "   [DRY RUN] Would add Priority field" -ForegroundColor Yellow
    } else {
        try {
            $priorityField = gh api graphql -f query='
                mutation($projectId: ID!) {
                    createProjectV2Field(input: {
                        projectId: $projectId
                        dataType: SINGLE_SELECT
                        name: "Priority"
                        singleSelectOptions: [
                            {name: "🔴 Critical", description: "Critical priority", color: RED}
                            {name: "🟠 High", description: "High priority", color: ORANGE}
                            {name: "🟡 Medium", description: "Medium priority", color: YELLOW}
                            {name: "🟢 Low", description: "Low priority", color: GREEN}
                        ]
                    }) {
                        projectV2Field {
                            ... on ProjectV2SingleSelectField {
                                id
                                name
                            }
                        }
                    }
                }
            ' -f projectId="$ProjectId"
            
            Write-Host "   ✅ Added Priority field" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️  Could not add Priority field: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # Add Completion field
    if ($DryRun) {
        Write-Host "   [DRY RUN] Would add Completion field" -ForegroundColor Yellow
    } else {
        try {
            $completionField = gh api graphql -f query='
                mutation($projectId: ID!) {
                    createProjectV2Field(input: {
                        projectId: $projectId
                        dataType: NUMBER
                        name: "Completion %"
                    }) {
                        projectV2Field {
                            ... on ProjectV2Field {
                                id
                                name
                            }
                        }
                    }
                }
            ' -f projectId="$ProjectId"
            
            Write-Host "   ✅ Added Completion % field" -ForegroundColor Green
        } catch {
            Write-Host "   ⚠️  Could not add Completion field: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# Main execution
try {
    Write-Host "Repository: $RepoOwner/$RepoName" -ForegroundColor Cyan
    Write-Host "Project: $ProjectTitle" -ForegroundColor Cyan
    if ($DryRun) {
        Write-Host "Mode: DRY RUN (no changes will be made)" -ForegroundColor Yellow
    }
    Write-Host ""
    
    # Create the project board
    $projectId = New-ProjectBoard -Config $ProjectConfig
    
    # Add custom fields
    Add-ProjectFields -ProjectId $projectId
    
    Write-Host ""
    Write-Host "🎉 Project Board Setup Complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📝 Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Visit your project board in GitHub" -ForegroundColor White
    Write-Host "2. Create issues for each task using the issue templates" -ForegroundColor White
    Write-Host "3. Add issues to the project board" -ForegroundColor White
    Write-Host "4. Set up automation rules for status updates" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 Project URL: https://github.com/$RepoOwner/$RepoName/projects" -ForegroundColor Blue
    
} catch {
    Write-Host ""
    Write-Host "❌ Setup failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Ensure you have admin access to the repository" -ForegroundColor White
    Write-Host "2. Check that GitHub CLI is properly authenticated" -ForegroundColor White
    Write-Host "3. Verify the repository exists and is accessible" -ForegroundColor White
    exit 1
}
