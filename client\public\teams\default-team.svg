<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 2048 2048" width="550" height="550" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(1012,133)" d="m0 0h56l50 3 39 4 34 5 37 7 30 7 40 11 31 10 25 9 20 8 36 15 19 9 13 6 27 14 24 14 28 17 21 14 19 14 14 10 17 13 16 13 14 12 11 9 10 9 8 7 11 11 8 7 23 23 7 8 10 11 7 8 9 10 12 14 11 14 16 21 14 20 21 32 11 17 14 24 15 28 14 27 13 29 12 29 15 42 11 36 12 46 7 36 5 31 4 31 3 36 2 33 1 35v24l-1 31-3 44-4 33-7 43-7 35-15 57-12 36-13 37-11 27-12 26-18 36-9 16-17 29-10 15-11 18-14 20-13 18-10 13-13 16-9 10-9 11-11 13-10 11-1 2h-2l-2 4-37 37-8 7-11 10-11 9-15 13-11 9-16 13-28 20-20 14-17 11-20 12-26 15-22 12-31 15-33 15-27 11-28 10-27 9-31 9-36 9-59 12-33 5-40 4-33 2h-69l-51-3-39-4-37-6-47-9-28-7-32-9-47-16-32-12-16-7-20-9-50-25-18-10-26-15-22-14-14-10-15-10-14-10-16-12-17-13-17-14-14-12-17-16-10-10-2-1v-2l-4-2-23-23-7-8-11-12-9-11-12-14-11-14-14-18-14-19-10-14-11-16-16-26-14-24-13-24-12-22-15-32-14-34-16-45-10-31-13-48-10-50-7-46-5-41-2-25-1-35v-19l1-37 3-40 4-35 6-41 7-35 8-32 13-45 11-35 15-38 8-19 7-16 10-21 10-19 9-17 14-24 16-26 14-21 11-16 14-19 13-18 10-13 10-11 9-11 13-15 14-15 41-41 8-7 14-12 17-14 16-13 17-13 16-12 35-24 25-15 24-14 28-15 25-13 25-11 36-15 35-13 39-12 36-10 25-6 45-8 36-5 46-4z" fill="#02AFFC"/>
<path transform="translate(639,476)" d="m0 0h39l21 2 21 4 21 7 9 4 12 6 15 10 15 13 12 13 12 18 10 19 9 27 5 21 3 21 1 8v53l-3 30-6 32-7 29-12 37-11 26-14 29-11 20-13 19-12 16-15 16-19 19v2l11 7 5 7 1 7 7 9 10 11 9 11 8 8 8 7 7 7 11 16 15 28 15 29 10 21v2l13-6 3-1h12l8 4 5 6 1 8-3 7-14 18-10 14-11 13-8 5-15 5-14 7-13 10-7 8h-2l-2 4-15 15-8 7-15 15h-2v2l-8 7-10 10-8 7-15 15-16 14-10 4-6 1-4 18-2 19-3 10-2 2h-7l-10-4-8-7-10-14-4-6v-2l-4 2-7 8-15 15-9 11-11 13-8 13-4 9-1 9-4 4-19 3h-25l-10-3-6-4-4-5-4-15-1-6v-15l1-3 1-11 4-8 7-6 9-7 15-15 11-16 7-9 10-13 10-18 12-24 16-13 13-13 8-11 8-14 1-12 1-3 1-28-3-35-3-19-4-18-8-26-4-8-5-1-17 7-23 8-8 2h-5l3-3 20-9 16-7 19-9 14-9 17-12 10-9 11-9 10-8 5-5 4-5h2l2-4 12-12 7-8 9-10 14-19 15-22 13-20 11-19 8-16 11-24 7-19 8-24 6-27 3-22 1-10v-27l-4-31-5-22-7-21-11-22-12-18-11-14-16-16-19-14-25-13-19-7-21-5-11-2-15-1h-14l-21 2-22 4-19 5-20 7-27 12-20 10-18 11-14 10-16 12-11 9-10 8-13 13-4 1h-12l-2-1 1-4 12-14 11-11 7-8 20-20 8-7 11-10 13-10 10-8 18-13 21-13 32-17 25-10 21-7 16-4 18-3z" fill="#FCFDFD"/>
<path transform="translate(950,859)" d="m0 0h125l21 1 22 4 16 5 16 7 13 8 11 9 10 13 8 16 4 14 3 19v17l-4 22-6 15-6 10-9 10-12 12-17 10-7 3 5 4 11 9 8 7 11 14 12 19 12 23 13 29 26 60v6h-94l-3-4-15-34-18-43-11-25-10-19-10-14-9-9-10-5-9-2-9-1v43l-1 112-1 1h-87l-1-1 1-364z" fill="#FCFDFD"/>
<path transform="translate(611,606)" d="m0 0 5 3 3 5-1 11 2 14 4 4 13 6 1 3-3 6 3 1-1 4-5 4-1 2v6l8 3h15l6 4-1 6-4 8-9 39-5 19-7 16-15 28-6 10-7 14-7 12-1 3 4 2 36 33 15 15 13 21 13 20 11 17 2 11 9 3-3 5-9 11-11 13-11 12-17 17-8 7-14 12-17 13-18 12-4-1-2-9v-13l1-11-4-13-5-17-5-8-11-9-7-7-11-9-15-14-8-7-13-11-7-7-8-7-10-10-8-7-10-9-4-5-4-11-1-24-8-6-2-5-2-10-4-10 1-4 2-1h9l13 7h3l-14-9-14-7-5-8-4-10-10-19-4-10-1-5v-20l8-17 7-9 10-9 15-7 6-1 17 1 13 5 9 5 9 9 8 14 3 4 15 6 3 3-2 6-6 5-6 2 1 5 3 6 5 1 2-1h7l2 2v8l5-2 12-11 18-18 4-11 8-21-3-27-1-3v-8l3-3 17-6 10-1-1-5-6-8-6-4-7 1-8 6-5 6-2 11h-9l-8-3-4-5-7-19-3-1-9 4-4 4-11 3-61 12-59 10-42 6-8 1-9 10-11 16-7 10-11 16-6 8-1-3 7-16 8-14 9-16 5-7 1-3-10 1-25 1-28-2-9-2-4-4-3-10v-12l1-4 2-1h7v3l-6 1 1 4h73l23-2 24-4 10-3 53-9 37-7 14-3 23-4 10-2h4l-4-5-20 4-82 12-13 1h-10l2-2 32-5 33-6 17-2 19-3 13-2h10l5 3v4l8-1 4 5 8 14 4 6 10-2 33-11 1-1v-7l-28 8-13 4h-8v-2l8-3 29-8 10-2-1-12 1-3z" fill="#FCFDFD"/>
<path transform="translate(1316,859)" d="m0 0h85l21 2 22 5 18 7 12 6 11 8 9 8 10 13 9 17 4 14 2 10 1 16v17l-2 16-7 21-9 16-12 14-12 10-15 9-16 7-17 5-19 3-26 2-25 1-1 137-2 2h-84l-2-1-1-7v-356l1-1z" fill="#FCFDFD"/>
<path transform="translate(639,476)" d="m0 0h39l21 2 21 4 21 7 9 4 12 6 15 10 15 13 12 13 12 18 10 19 9 27 5 21 3 21 1 8v53l-3 30-6 32-7 29-12 37-11 26-14 29-11 20-13 19-12 16-15 16-19 19v2l11 7 5 7 1 7 7 9 10 11 9 11 7 7h-3l-11-11-8-7-13-11-6-8-3-9-7-2-7 3-26 4-3 3-6 9-6 5-10 3-4 2h-3l6-5 5-5 4-5h2l2-4 12-12 7-8 9-10 14-19 15-22 13-20 11-19 8-16 11-24 7-19 8-24 6-27 3-22 1-10v-27l-4-31-5-22-7-21-11-22-12-18-11-14-16-16-19-14-25-13-19-7-21-5-11-2-15-1h-14l-21 2-22 4-19 5-20 7-27 12-20 10-18 11-14 10-16 12-11 9-10 8-13 13-4 1h-12l-2-1 1-4 12-14 11-11 7-8 20-20 8-7 11-10 13-10 10-8 18-13 21-13 32-17 25-10 21-7 16-4 18-3z" fill="#FCFDFD"/>
<path transform="translate(1563,860)" d="m0 0h88l1 2-1 297h123l2 2v61l-1 2-211 1-1-1-1-51v-279z" fill="#FDFDFD"/>
<path transform="translate(611,606)" d="m0 0 5 3 3 5-1 11 2 14 4 4 13 6 1 3-3 6 3 1-1 4-5 4-1 2v6l8 3h15v1l-9 2-7-1h-17l-11 5h-7l-5-1-3 7-1 1v7l2 10-3 4v-8l-1-4-2-18-1-3v-8l3-3 17-6 10-1-1-5-6-8-6-4-7 1-8 6-5 6-2 11h-9l-8-3-4-5-7-19-3-1-9 4-4 4-11 3-61 12-59 10-42 6-8 1-9 10-11 16-7 10-11 16-6 8-1-3 7-16 8-14 9-16 5-7 1-3-10 1-25 1-28-2-9-2-4-4-3-10v-12l1-4 2-1h7v3l-6 1 1 4h73l23-2 24-4 10-3 53-9 37-7 14-3 23-4 10-2h4l-4-5-20 4-82 12-13 1h-10l2-2 32-5 33-6 17-2 19-3 13-2h10l5 3v4l8-1 4 5 8 14 4 6 10-2 33-11 1-1v-7l-28 8-13 4h-8v-2l8-3 29-8 10-2-1-12 1-3z" fill="#ECFFFF"/>
<path transform="translate(1377,917)" d="m0 0 13 1 10 3 9 6 7 8 8 16 2 5 1 10v15l-2 13-8 16-7 7-10 6-9 3-31 1-1-1v-106l4-2z" fill="#02AFFC"/>
<path transform="translate(793,673)" d="m0 0 2 3 4 22 2 18v34l-2 19-1 4-1 20-3 4v10l-4 4-2 10-1 4-1 8h2l9-19 7-20 3-5 4-1 1 5-3 9-3 15-3 14-6 23-3 7v4l5-3 7-14 2-1-3 9-14 29-11 20-13 19-12 16-15 16-19 19v2l11 7 5 7 1 7 7 9 10 11 9 11 7 7h-3l-11-11-8-7-13-11-6-8-3-9-7-2-7 3-26 4-3 3-6 9-6 5-10 3-4 2h-3l6-5 5-5 4-5h2l2-4 12-12 7-8 9-10 14-19 15-22 13-20 11-19 8-16 11-24 7-19 8-24 6-27 3-22 1-10v-27l-4-31-2-10z" fill="#ECFFFF"/>
<path transform="translate(1038,917)" d="m0 0h30l13 4 8 5 7 7 7 14 2 7 1 8v12l-4 14-6 11-7 6-16 8-10 2h-25l-1-1v-66z" fill="#02AFFC"/>
<path transform="translate(639,476)" d="m0 0h39l21 2 21 4 21 7 9 4 6 3-2 1-10-4-7-1-12-5-4-2h-8l-9-3-8-1-8-2-15-1h-28l-11 1-8 4h-11l-20 4-6 1-9 3-11 2v2l-16 9-5 4-1 2h18l19 3 2 2-16 6-10 6-12 5-16 8-15 8-6 4v2l4-1 9-4 8-1-2 2-26 11-24 12-18 11-14 10-16 12-11 9-10 8-13 13-4 1h-12l-2-1 1-4 12-14 11-11 7-8 20-20 8-7 11-10 13-10 10-8 18-13 21-13 32-17 25-10 21-7 16-4 18-3z" fill="#ECFEFE"/>
<path transform="translate(1563,860)" d="m0 0h88l1 2-1 297h123l2 2v61l-1 2-211 1-1-1-1-51v-279zm1 1v68l1 10v104h-1v180l2 1 123-1 2-1 52-1h19l10-2 1-1v-52l-5-3-21 1h-26l-49-2-10-1-1 1h-5l-2-2-3-1-1-155v-143z" fill="#ECFFFF"/>
<path transform="translate(1316,859)" d="m0 0h85l21 2 10 2v1h-9l-10-2v2l-5-2-45-1h-44l-42 1-3 3-1 5v351l9 1h26l50 1-1 2h-84l-2-1-1-7v-356l1-1z" fill="#ECFFFF"/>
<path transform="translate(597,1250)" d="m0 0 2 1-12 12h-2l-1 5-5 6h-2l-2 12-4 12-7 10-5 12-5 10-4 5h-2v2l-12 8-9 11-5 6-10 6-4-2 15-15 11-16 7-9 10-13 10-18 12-24 16-13z" fill="#ECFEFE"/>
<path transform="translate(628,590)" d="m0 0h13l13 2 7 4 3 8 2 8 1 1 11 1 2 1 1 3v7l-5 10-4 3-1 4-1 13h-3l-5-13-1-3-12-1-12-6-1-2 3-1h10l-1-6 1-1 11-1 1-7 1-3v-3h-2l-2-11-23-2-7-3z" fill="#ECFFFF"/>
<path transform="translate(597,834)" d="m0 0 1 3 4 2 36 33 15 15 13 21 13 20 11 17 2 11 9 3-3 5-9 11-11 13-11 12-17 17-2-1 12-12 1-2h2l2-4 4-4 1-3 4-1 1-3 5-5 3-4h2l2-5 1-7-2-7v-9l-8-11-2-2-1-7-7-10-1-4-4-2-7-9-8-16-9-9-7-6-4-6-11-8-10-9v-2h-2l-4-7v-2l-4-2z" fill="#ECFFFF"/>
<path transform="translate(609,532)" d="m0 0h14l15 1 29 6 17 6 9 4 3 3 10 5-1 2-11-4-13-4-5-2-9-3-2-1v-3h-13l-15-2-14-2h-21l-20 1-10 4-15 4-23 5-26 10-3-1 4-1v-2l23-10 25-8 24-5 13-2z" fill="#05A0E9"/>
<path transform="translate(949,860)" d="m0 0h1v360l2 2h66l12-1 4-2 1-21v-114l-1-2v-10l2-3h2v43l-1 112-1 1h-87l-1-1z" fill="#ECFFFF"/>
<path transform="translate(791,1058)" d="m0 0 4 4 9 16 12 23 10 19 12 25v2l13-6 3-1h12l8 4 2 3-4-2-7-3h-9l-2 6 9 1 4 5-2 7-13 13-4-2-5-13 2-9-4-1-2 1-4-4-4-10-7-14-5-8-5-9-6-10-5-8-10-19-2-6z" fill="#ECFFFF"/>
<path transform="translate(780,513)" d="m0 0 4 2 12 11 11 13 12 19 8 17 8 24 5 21 3 21 1 8v53l-3 30-6 32-4 17h-1v-10l2-3 2-13 2-7 4-26 1-21v-20l-1-39-2-15-3-12-4-14-8-17-5-18-8-15-6-7-6-9-11-13v-3l-4-2z" fill="#ECFFFF"/>
<path transform="translate(1038,1069)" d="m0 0h9l11 3 9 5 11 11 10 16 11 21 13 31 21 49 8 18 1 3h90v1l-89 1-5-4-8-15-6-16-5-10-6-17-7-15v-4h-2l-3-9-6-15-9-17-6-9v-2h-2l-2-5h-2l-1-4h-2v-2h-2v-2l-5-2-7-3-10-1h-6v2h-2v-6l-2-1z" fill="#059FE8"/>
<path transform="translate(459,815)" d="m0 0 5 2 8 6-1 4h-3l-2 4-4 9-4-1-4-3-6-1v6h-1l-2-10-4-10 1-4 2-1h9l13 7h3l-10-7z" fill="#ECFFFF"/>
<path transform="translate(1158,882)" d="m0 0 5 2 11 9 10 13 8 16 4 14 3 19v17l-4 22-6 15-6 10-6 7v-3l5-10 8-15 4-14 2-16v-18l-3-13-7-13-4-8-9-21-12-10z" fill="#ECFFFF"/>
<path transform="translate(463,692)" d="m0 0h8v1l-13 2-3 5-8 7-9 6-4 4h-2l-9 18-3 13-1 1v11h-1l-1-5v-20l8-17 7-9 10-9 15-7z" fill="#ECFFFF"/>
<path transform="translate(1471,1062)" d="m0 0 4 1-10 7-17 8-13 4-17 3-26 3h-24v2h-3l-3 13-2-3v-13l1-1 23-1 1-1 26-2 23-4 21-7 15-8z" fill="#059FE8"/>
<path transform="translate(725,931)" d="m0 0 2 1-3 7-8 11-13 18-7 7-7 8-9 10-4 2-1-2 6-7 2-5 11-13 5-6 3-2v-3l6-5h2l2-4 10-14 3-1z" fill="#04A2EB"/>
<path transform="translate(615,1362)" d="m0 0h3l12 18 1 4-7-6-5-8h-6l-8 9-6 7h-2l-2 4-9 11h-2l-2 4-8 9-9 14-2-1 2-7 8-11 11-13 9-11 19-19z" fill="#05A0E9"/>
<path transform="translate(535,617)" d="m0 0 4 2 2 4-10 2h-13l-11 3h-11l-5 2-4 1h-16v2h-7v2h-6l-1-3h4v-2l14-3 42-6z" fill="#02B0FD"/>
<path transform="translate(1377,917)" d="m0 0 13 1 10 3 9 6 7 8 9 18v6l-2-2-7-14-8-12v-2h-2l-3-3-5-3-17-2h-10l-7 2-1 7-1 54-2 2v-67l3-1z" fill="#059FE8"/>
<path transform="translate(596,1060)" d="m0 0 3 1-2 1 1 4 3 4-1 2-2-2-7 1-14 6-23 8-8 2h-5l3-3 20-9 16-7z" fill="#ECFFFF"/>
<path transform="translate(681,984)" d="m0 0v3l-5 6 1 3-5 6h-2l-2 4-5 4-5 5-10 9-22 18-3 2h-3l2-4 10-9 11-9 16-15 15-15z" fill="#02AEFB"/>
<path transform="translate(832,783)" d="m0 0 1 3-1 5-10 31-6 18-5 12-8 16-3 5h-2l1-6 9-20 9-23 10-31h2l1-6z" fill="#04A2EB"/>
<path transform="translate(1037,917)" d="m0 0h1v97h25l13-3 15-8 5-4-2 4-5 4-3 5-8 3h-8l-6 1h-27l-3-4 1-8v-50l-1-33z" fill="#ECFFFF"/>
<path transform="translate(533,1331)" d="m0 0h2l-2 7-11 15-17 17-7 5-4-1 2-4 7-8 15-14 7-8 4-4z" fill="#059FE8"/>
<path transform="translate(1363,916)" d="m0 0h23l13 3 5 3-2 1-7-3-13-2-19 1-3 1v106h31l-3 2-20 1-10-1-2-2 1-9v-36l1-62z" fill="#ECFFFF"/>
<path transform="translate(1142,1051)" d="m0 0h6l1 5 7 8 3 5 8 7 9 12 11 18 10 19 1 5h-2l-10-20-12-19-10-13-4-5-8-7-12-9-3-3z" fill="#04A1EA"/>
<path transform="translate(1235,1219)" d="m0 0h1v6h-94l-1-2 9-1 85-1z" fill="#ECFFFF"/>
<path transform="translate(1168,1083)" d="m0 0 5 5 12 19 12 23 13 29-1 3-3-6-5-5-9-19-2-3-3-10-7-14-2-5-4-4-3-5v-3h-2z" fill="#ECFFFF"/>
<path transform="translate(1114,1167)" d="m0 0 3 1 16 37 8 18 1 3h90v1l-89 1-5-4-8-15-6-16-5-10-5-14z" fill="#059EE7"/>
<path transform="translate(746,1282)" d="m0 0 4 1-16 16-16 14-1 2h-2l-2 4h-2l-2 4-3 1v2l-6 5-5 5-5 4-6 2 2-4 8-7 15-14 12-12 8-7 10-10h2v-2z" fill="#04A2EC"/>
<path transform="translate(510,656)" d="m0 0 1 2-16 4-7 2-28 4-16 4-15 3h-13l3-2 17-3 10-2h5v-2l48-9z" fill="#05A0E9"/>
<path transform="translate(1045,917)" d="m0 0h23l13 4 8 5 7 7 6 11 1 9h-2l-3-8v-3h-2l-6-8v-2l-4-2v-2l-5-2-9-5-5-1-19 1-6 2-1 5-2-2v-8z" fill="#05A0E9"/>
<path transform="translate(880,1150)" d="m0 0 2 2 1 6-3 9-8 10-9 12-6 9-7 9-7 6-3-1 11-14 10-13 12-16 5-7z" fill="#059FE8"/>
<path transform="translate(744,588)" d="m0 0 5 2 10 12 10 15 8 14 5 10v5l-2-1-7-14-8-12-8-11-12-16z" fill="#05A0E9"/>
<path transform="translate(1294,1225)" d="m0 0h65v2l-3 1h-78l-5-1v-1z" fill="#059DE5"/>
<path transform="translate(1200,970)" d="m0 0h1v18l-5 15-8 16-7 8-4 1 2-5 7-10 6-13h2l4-20z" fill="#059EE7"/>
<path transform="translate(665,1350)" d="m0 0h1l-1 9-3 24-3 10-2 2h-7l-10-4 3-3h12l3-9 3-5 1-18z" fill="#ECFFFF"/>
<path transform="translate(1516,1006)" d="m0 0 2 1-2 10-6 12-7 11-6 7-11 9h-3l10-11 10-13 8-15 2-6h2z" fill="#05A0E9"/>
<path transform="translate(678,1343)" d="m0 0 4 1-10 8h-2l-3 21-2 13-2 1v-23l4-18 7-2z" fill="#059FE8"/>
<path transform="translate(505,1369)" d="m0 0h1v5l-9 12-5 9-4 3v-11l4-8 7-6z" fill="#ECFFFF"/>
<path transform="translate(515,938)" d="m0 0 5 2 8 8 8 7 12 11 11 9 6 7h-3l-11-8-7-8-8-6-5-6-11-8-5-6z" fill="#05A0EA"/>
<path transform="translate(1081,1094)" d="m0 0 4 5 10 18 9 20 12 29h-3l-7-15v-4h-2l-3-9-6-15-9-17-6-10z" fill="#05A0EA"/>
<path transform="translate(525,621)" d="m0 0h5l-1 3-15 2-7 2h-11l-5 2-4 1h-16v2h-7v2h-6l-1-3 19-3 10-2 35-5z" fill="#059CE4"/>
<path transform="translate(506,708)" d="m0 0 5 4 8 14 3 4 15 6 3 3-2 6-4 4-2-1 3-4-1-5-11-2-3-6-6-7-5-8-3-4z" fill="#ECFFFF"/>
<path transform="translate(664,540)" d="m0 0 13 4 12 3 4 2 3 3 10 5-1 2-11-4-13-4-5-2-9-3-2-1v-3l-2-1z" fill="#059DE5"/>
<path transform="translate(660,688)" d="m0 0h2l-1 7-1 2h-2l-2 9-7 28-2 2v-9l7-31 3-6z" fill="#04A1EB"/>
<path transform="translate(748,1008)" d="m0 0 7 6 4 5 5 4 9 11 4 4v2l4 2 10 11v2l-4-2-7-8-17-16-13-15-2-2z" fill="#04A3EC"/>
<path transform="translate(656,890)" d="m0 0 5 5 7 10 14 22 5 9-1 2-5-5-10-16-11-17-4-7z" fill="#04A1EB"/>
<path transform="translate(990,1225)" d="m0 0 45 1-2 2-14 3-26-3-24-1v-1z" fill="#069CE4"/>
<path transform="translate(617,1211)" d="m0 0h1v11l-10 17-9 11-8 7-4 1 2-1v-2h2l2-4h2l2-4 3-7 8-8 6-10z" fill="#04A2EB"/>
<path transform="translate(561,1288)" d="m0 0h2l-2 9-6 10-5 9-7 5 2-5 3-7 4-6 3-8 2-4h2v-2z" fill="#05A0E9"/>
<path transform="translate(867,1175)" d="m0 0v3l-14 19-11 13-4 3h-2l2-4 15-20 4-5 8-7z" fill="#ECFFFF"/>
<path transform="translate(1385,1084)" d="m0 0h14l-1 3-6 1h-24v2h-3l-3 13-2-3v-13l1-1 23-1z" fill="#059DE6"/>
<path transform="translate(825,1217)" d="m0 0h6l-3 2-17 9-12 8-7 5v-3l6-7 14-9z" fill="#05A0E9"/>
<path transform="translate(642,1370)" d="m0 0h6l4 4v9l-2 2-5-1-5-6v-6z" fill="#ECFFFF"/>
<path transform="translate(499,700)" d="m0 0h6l7 6 1 4h2l8 17v2l-4-1-7-13-9-10-4-3z" fill="#059FE8"/>
<path transform="translate(635,766)" d="m0 0h2l-1 6-6 15-8 12-3 2 2-9z" fill="#05A0E9"/>
<path transform="translate(766,500)" d="m0 0 6 2 5 4 5 3 5 4 6 7 5 4 7 10h-3l-14-15-11-9-7-5z" fill="#04A2EC"/>
<path transform="translate(770,921)" d="m0 0 1 3-14 19-12 12-2-1 5-5 2-5 9-10 8-11z" fill="#04A1EA"/>
<path transform="translate(1405,926)" d="m0 0 4 1 7 8 9 18v6l-2-2-7-14-8-12v-2h-2z" fill="#04A1EA"/>
<path transform="translate(724,567)" d="m0 0 5 2 13 12 13 13 9 12 7 12-1 2-10-15-11-14-16-16-9-7z" fill="#ECFFFF"/>
<path transform="translate(1045,917)" d="m0 0h23l4 3h-12l-12 1-6 2-1 5-2-2v-8z" fill="#059DE5"/>
<path transform="translate(457,666)" d="m0 0 3 1-20 6-11 2h-13l3-2 17-3z" fill="#069CE4"/>
<path transform="translate(545,1447)" d="m0 0h7l-1 2-10 2h-31l-3-2 29-1z" fill="#05A0E9"/>
<path transform="translate(1166,883)" d="m0 0 7 6 5 4 9 13 4 8h-2l-2-4-4-4-10-13-7-6-1-3z" fill="#04A1EB"/>
<path transform="translate(632,770)" d="m0 0 1 2-14 26-2 1 2-9 4-10z" fill="#ECFFFF"/>
<path transform="translate(597,591)" d="m0 0 2 2-4 4-10-1-5 1-10-2 4-3h11z" fill="#ECFFFF"/>
<path transform="translate(1089,998)" d="m0 0h5l-1 4-9 6-6 1v-5z" fill="#059FE8"/>
<path transform="translate(1081,1094)" d="m0 0 4 5 10 18 6 13v2l-3-1-4-9-8-16-6-10z" fill="#04A4EF"/>
<path transform="translate(420,768)" d="m0 0 4 4 11 21v6l-4-5-8-16z" fill="#05A1EA"/>
<path transform="translate(746,1282)" d="m0 0 4 1-16 16-8 7-5 5-2-1 21-21 1-3z" fill="#059DE6"/>
<path transform="translate(1425,974)" d="m0 0h1l-1 20-4 9-3 1 4-19z" fill="#059FE8"/>
<path transform="translate(535,617)" d="m0 0 4 2 2 4-10 2-4-1h2v-2h-8l2-2z" fill="#02ADFA"/>
<path transform="translate(1179,1097)" d="m0 0 3 1 6 9 8 16 2 7h-2l-10-20-7-11z" fill="#04A4EE"/>
<path transform="translate(566,984)" d="m0 0 4 1 7 6 5 12 1 3v7l-2-4-5-13v-2l-3-1-7-8z" fill="#04A5EF"/>
<path transform="translate(436,796)" d="m0 0 1 2 6 2 4 8 9 4 3 3-9-3-10-6-4-7z" fill="#ECFFFF"/>
<path transform="translate(656,680)" d="m0 0 4 2-1 6-4 8-3 13h-1l-1-13 4-4 2-8z" fill="#ECFFFF"/>
<path transform="translate(612,1364)" d="m0 0v3l-20 20-6 7v-3l9-11 3-3h2l2-4z" fill="#ECFFFF"/>
<path transform="translate(638,1026)" d="m0 0m-1 1m-1 1m-1 1m-1 1m-2 1 4 1-1 3-12 9h-3l2-4z" fill="#02AEFA"/>
<path transform="translate(646,738)" d="m0 0h1l-1 8-2 9-4 9h-2l1-8 4-12h2z" fill="#04A1EA"/>
<path transform="translate(538,746)" d="m0 0 2 1-5 5-4 3 1 3-3 1-4-4 1-3z" fill="#04A1EA"/>
<path transform="translate(650,709)" d="m0 0 1 4-6 27h-1v-13l1-2h2v-13z" fill="#ECFFFF"/>
<path transform="translate(617,1211)" d="m0 0h1v11l-6 10h-3l2-4 4-9z" fill="#059FE8"/>
<path transform="translate(646,1017)" d="m0 0 2 1-8 8-11 9-6 5-2-1 13-12 11-9z" fill="#ECFFFF"/>
<path transform="translate(1460,871)" d="m0 0 7 1 15 10-4 1-11-7-7-3z" fill="#04A3EC"/>
<path transform="translate(468,663)" d="m0 0h6l-1 3-13 2v-2l-9 1v-1z" fill="#04A5EF"/>
<path transform="translate(1415,1011)" d="m0 0 1 2-2 4-7 4-4 3-9 1 3-2 10-5z" fill="#ECFFFF"/>
<path transform="translate(449,844)" d="m0 0 3 2 6 2 1 3v25h-1l-1-24-8-6z" fill="#ECFFFF"/>
<path transform="translate(611,811)" d="m0 0h3l-2 5-6 11-1-3 4-10z" fill="#04A2EB"/>
<path transform="translate(603,1083)" d="m0 0 3 2 4 13v5l-2-1-5-16z" fill="#04A2EB"/>
<path transform="translate(537,763)" d="m0 0h7l2 2v8l4 1-4 1-3-3v-7h-8z" fill="#ECFFFF"/>
<path transform="translate(806,536)" d="m0 0 4 4 7 11v2h-2l-9-14z" fill="#04A3ED"/>
<path transform="translate(1210,1162)" d="m0 0 3 4 5 11-1 3-7-11z" fill="#ECFFFF"/>
<path transform="translate(1099,1132)" d="m0 0 3 1 6 13v2l-4-1-3-9z" fill="#04A1EB"/>
<path transform="translate(554,765)" d="m0 0 2 1-6 7h-3v-5l2-2z" fill="#059EE6"/>
<path transform="translate(566,984)" d="m0 0 4 1 7 6v5l-1-2-3-1-7-8z" fill="#04A1EA"/>
<path transform="translate(1390,919)" d="m0 0 8 1 9 5-4 1-5-3-6-1v-2z" fill="#04A5F0"/>
<path transform="translate(648,722)" d="m0 0h3l-1 9-2 5h-1v-9z" fill="#05A0E9"/>
<path transform="translate(638,1026)" d="m0 0m-1 1m-1 1m-1 1m-1 1m-1 1 3 1-1 3-5 4-3-1z" fill="#03A8F3"/>
<path transform="translate(603,828)" d="m0 0 1 3-1 7-5-1 1-4 2-4z" fill="#04A2EB"/>
<path transform="translate(561,595)" d="m0 0h8v5l-6-1z" fill="#ECFFFF"/>
<path transform="translate(587,1056)" d="m0 0h2v7l-5 1v-5z" fill="#059EE7"/>
<path transform="translate(1405,1016)" d="m0 0h3l-2 4-4 2h-4v-3z" fill="#04A1EB"/>
<path transform="translate(1081,1094)" d="m0 0 4 5 4 8-4-2-5-9z" fill="#04A3EC"/>
<path transform="translate(1137,1052)" d="m0 0 3 1-2 2 9 6-3 1-9-7z" fill="#ECFFFF"/>
<path transform="translate(832,783)" d="m0 0 1 3-1 5-2 6h-2l1-7z" fill="#069CE4"/>
<path transform="translate(545,1447)" d="m0 0h7l-1 2-10 1-5-1v-1z" fill="#04A4EF"/>
<path transform="translate(824,810)" d="m0 0 1 3-3 9h-2l2-10z" fill="#069CE4"/>
</svg>
