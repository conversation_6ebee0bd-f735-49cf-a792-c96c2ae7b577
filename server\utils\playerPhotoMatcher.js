/**
 * Utility to match players from XLSX import with IPL website data to get profile photos
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

/**
 * Fetch player data from IPL website
 * @param {string} url - IPL team URL
 * @returns {Promise<Array>} - Array of player data with names and image URLs
 */
async function fetchIplPlayerData(url) {
  console.log(`Fetching player data from ${url}`);
  let browser = null;

  try {
    console.log('Launching puppeteer browser...');
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
      timeout: 60000 // Increase timeout to 60 seconds
    });

    console.log('Creating new page...');
    const page = await browser.newPage();

    // Set a longer navigation timeout
    page.setDefaultNavigationTimeout(60000);

    console.log('Navigating to URL...');
    await page.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });

    console.log('Waiting for player cards to load...');

    // Try to find player cards with a more flexible approach
    let players = [];

    try {
      // First try the original selector
      await page.waitForSelector('.ih-pcard1', { timeout: 20000 });

      console.log('Player cards found, extracting data...');

      // Extract player data (only name and image URL)
      players = await page.evaluate(() => {
        const playerCards = document.querySelectorAll('.ih-pcard1');
        console.log('Found player cards:', playerCards.length);

        return Array.from(playerCards).map(card => {
          // Extract player name
          const name = card.querySelector('.ih-p-name h2')?.textContent.trim() || '';

          // Extract player image URL
          const imageUrl = card.querySelector('.ih-p-img img')?.dataset.src || '';

          return { name, imageUrl };
        }).filter(player => player.name && player.imageUrl);
      });
    } catch (selectorError) {
      console.error('Error with primary selector, trying alternative approach:', selectorError);

      // If the specific selector fails, try a more generic approach
      // Take a screenshot to debug
      await page.screenshot({ path: 'debug-screenshot.png' });

      // Try to find any player cards or images on the page
      players = await page.evaluate(() => {
        // Look for any elements that might contain player data
        const possibleCards = document.querySelectorAll('[class*="player"], [class*="card"], [class*="profile"]');

        return Array.from(possibleCards).map(card => {
          // Try to find name and image with more generic selectors
          const nameElement = card.querySelector('h1, h2, h3, .name, [class*="name"]');
          const name = nameElement ? nameElement.textContent.trim() : '';

          // Look for image elements
          const imgElement = card.querySelector('img');
          const imageUrl = imgElement ? (imgElement.dataset.src || imgElement.src) : '';

          return { name, imageUrl };
        }).filter(player => player.name && player.imageUrl);
      });
    }

    // Use mock data if no players found
    if (players.length === 0) {
      console.log('No players found on the page, using mock data');
      // Import mock data
      const mockIplPlayers = require('../data/mockIplPlayers');
      players = mockIplPlayers.map(player => ({
        name: player.name,
        imageUrl: player.imageUrl
      }));
    }

    console.log(`Found ${players.length} players on IPL website`);
    return players;
  } catch (error) {
    console.error('Error fetching IPL player data:', error);
    console.error('Error stack:', error.stack);

    // Return mock data as fallback
    console.log('Using mock data as fallback due to error');
    const mockIplPlayers = require('../data/mockIplPlayers');
    return mockIplPlayers.map(player => ({
      name: player.name,
      imageUrl: player.imageUrl
    }));
  } finally {
    if (browser) {
      console.log('Closing browser...');
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }
  }
}

/**
 * Match players from XLSX import with IPL website data
 * @param {Array} importedPlayers - Players imported from XLSX
 * @param {string} iplTeamUrl - IPL team URL
 * @returns {Promise<Array>} - Matched players with image URLs
 */
async function matchPlayersWithIplData(importedPlayers, iplTeamUrl) {
  try {
    // Fetch player data from IPL website
    const iplPlayers = await fetchIplPlayerData(iplTeamUrl);

    // Match players based on name similarity
    const matchedPlayers = importedPlayers.map(importedPlayer => {
      // Normalize player name for comparison
      const normalizedImportedName = normalizePlayerName(importedPlayer.name);

      // Find best match from IPL players
      const bestMatch = findBestMatch(normalizedImportedName, iplPlayers);

      if (bestMatch) {
        console.log(`Matched ${importedPlayer.name} with ${bestMatch.name} (score: ${bestMatch.score})`);
        return {
          ...importedPlayer,
          imageUrl: bestMatch.imageUrl,
          matched: true,
          matchScore: bestMatch.score
        };
      }

      return {
        ...importedPlayer,
        matched: false
      };
    });

    return matchedPlayers;
  } catch (error) {
    console.error('Error matching players:', error);
    throw error;
  }
}

/**
 * Normalize player name for better matching
 * @param {string} name - Player name
 * @returns {string} - Normalized name
 */
function normalizePlayerName(name) {
  return name
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Find best match for a player name from IPL players
 * @param {string} playerName - Normalized player name
 * @param {Array} iplPlayers - IPL player data
 * @returns {Object|null} - Best match or null if no good match found
 */
function findBestMatch(playerName, iplPlayers) {
  let bestMatch = null;
  let bestScore = 0;

  for (const iplPlayer of iplPlayers) {
    const iplPlayerName = normalizePlayerName(iplPlayer.name);

    // Calculate similarity score
    const score = calculateSimilarity(playerName, iplPlayerName);

    // Consider it a match if score is above threshold
    if (score > 0.7 && score > bestScore) {
      bestScore = score;
      bestMatch = {
        ...iplPlayer,
        score
      };
    }
  }

  return bestMatch;
}

/**
 * Calculate similarity between two strings (0-1)
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Similarity score (0-1)
 */
function calculateSimilarity(str1, str2) {
  // Simple implementation using Levenshtein distance
  const distance = levenshteinDistance(str1, str2);
  const maxLength = Math.max(str1.length, str2.length);

  return 1 - (distance / maxLength);
}

/**
 * Calculate Levenshtein distance between two strings
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Levenshtein distance
 */
function levenshteinDistance(str1, str2) {
  const m = str1.length;
  const n = str2.length;

  // Create matrix
  const dp = Array(m + 1).fill().map(() => Array(n + 1).fill(0));

  // Initialize first row and column
  for (let i = 0; i <= m; i++) dp[i][0] = i;
  for (let j = 0; j <= n; j++) dp[0][j] = j;

  // Fill matrix
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      dp[i][j] = Math.min(
        dp[i - 1][j] + 1,      // deletion
        dp[i][j - 1] + 1,      // insertion
        dp[i - 1][j - 1] + cost // substitution
      );
    }
  }

  return dp[m][n];
}

/**
 * Download player image and save to uploads directory
 * @param {string} imageUrl - Image URL
 * @param {string} playerId - Player ID
 * @returns {Promise<string>} - Local image path
 */
async function downloadPlayerImage(imageUrl, playerId) {
  console.log(`Downloading image from ${imageUrl} for player ${playerId}`);

  try {
    // Check if the URL is valid
    if (!imageUrl || !imageUrl.startsWith('http')) {
      console.error('Invalid image URL:', imageUrl);
      return null;
    }

    console.log('Making request to download image...');
    const response = await axios({
      url: imageUrl,
      method: 'GET',
      responseType: 'arraybuffer',
      timeout: 30000, // 30 second timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    // Check if we got a valid response
    if (!response.data || response.data.length === 0) {
      console.error('Empty response when downloading image');
      return null;
    }

    console.log(`Successfully downloaded image (${response.data.length} bytes)`);

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '../../client/public/uploads/players');
    console.log('Uploads directory path:', uploadsDir);

    if (!fs.existsSync(uploadsDir)) {
      console.log('Creating uploads directory...');
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Save image to file
    const imagePath = path.join(uploadsDir, `${playerId}.jpg`);
    console.log('Saving image to:', imagePath);
    fs.writeFileSync(imagePath, response.data);

    // Return relative path for database
    const relativePath = `/uploads/players/${playerId}.jpg`;
    console.log('Image saved successfully, returning path:', relativePath);
    return relativePath;
  } catch (error) {
    console.error(`Error downloading image from ${imageUrl}:`, error);
    console.error('Error stack:', error.stack);

    // Try to create a more detailed error message
    let errorMessage = 'Unknown error';
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      errorMessage = `Server responded with status ${error.response.status}: ${error.response.statusText}`;
    } else if (error.request) {
      // The request was made but no response was received
      errorMessage = 'No response received from server';
    } else {
      // Something happened in setting up the request that triggered an Error
      errorMessage = error.message;
    }

    console.error('Detailed error:', errorMessage);
    return null;
  }
}

module.exports = {
  matchPlayersWithIplData,
  downloadPlayerImage,
  normalizePlayerName,
  calculateSimilarity
};
