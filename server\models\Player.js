const mongoose = require('mongoose');

// Define stats schema for a player's performance statistics
const statsSchema = new mongoose.Schema({
  matches: { type: Number, default: 0 },
  runs: { type: Number, default: 0 },
  battingAverage: { type: Number, default: 0 },
  strikeRate: { type: Number, default: 0 },
  wickets: { type: Number, default: 0 },
  bowlingAverage: { type: Number, default: 0 },
  economy: { type: Number, default: 0 },
  fifties: { type: Number, default: 0 },
  hundreds: { type: Number, default: 0 },
  highScore: { type: Number, default: 0 },
  bestBowling: { type: String, default: '0/0' }
});

// Define ratings schema - simplified to only overall rating
const ratingsSchema = new mongoose.Schema({
  overall: { type: Number, required: true, min: 40, max: 99 }
});

// Define player schema
const playerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  nationality: {
    type: String,
    required: true,
    trim: true
  },
  age: {
    type: Number,
    required: false, // Age is not required
    min: 16,
    max: 45,
    default: 25
  },
  height: {
    type: String,
    required: true,
    match: /^\d+\s*cm$/
  },
  type: {
    type: String,
    required: true,
    // Define the enum values as a constant for better maintainability
    validate: {
      validator: function(v) {
        return ['Batsman', 'Bowler', 'Batting Allrounder', 'Bowling Allrounder', 'Allrounder', 'Wicket Keeper'].includes(v);
      },
      message: props => `${props.value} is not a valid player type`
    }
  },
  battingHand: {
    type: String,
    required: true,
    enum: ['RHB', 'LHB']
  },
  bowlingHand: {
    type: String,
    required: true,
    enum: [
      'None',
      'RF', 'LF',
      'RFM', 'LFM',
      'RM', 'LM',
      'RHWS', 'LHWS',
      'RHFS', 'LHFS'
    ]
  },
  image: {
    type: String,
    default: '/uploads/players/default.png'
  },
  stats: {
    type: statsSchema,
    default: () => ({})
  },
  ratings: {
    type: ratingsSchema,
    default: () => ({
      overall: 70
    })
  },
  marketValue: {
    type: Number,
    default: 10000
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  isAvailableOnMarket: {
    type: Boolean,
    default: false
  },
  auctionWin: {
    auctionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Auction',
      default: null
    },
    amount: {
      type: Number,
      default: 0
    },
    date: {
      type: Date,
      default: null
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

const Player = mongoose.model('Player', playerSchema);

module.exports = Player;