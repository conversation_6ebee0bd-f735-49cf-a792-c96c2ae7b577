import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Divider,
  Paper,
  Avatar,
  FormControlLabel,
  Switch,
  Radio,
  RadioGroup
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  SportsCricket as CricketIcon
} from '@mui/icons-material';

import { addMatch } from '../../services/tournamentService';
import { useAuth } from '../../hooks/useAuth';

/**
 * Improved Match Form Component
 *
 * A redesigned match form that better aligns with cricket scorecards and
 * makes it clearer for users to enter match data.
 */
const ImprovedMatchForm = ({ open, onClose, tournament, onMatchAdded, initialData = null }) => {
  const { user } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [teams, setTeams] = useState([]);

  // Form data with new structure
  const [formData, setFormData] = useState({
    // Team selection
    team1Id: '',
    team2Id: '',
    isTeam1Home: true, // New field to track which team is home
    homeTeamBattedFirst: true, // Add homeTeamBattedFirst toggle

    // Match details
    date: new Date(),
    venue: '',

    // Team 1 Score
    team1Score: {
      runs: 0,
      wickets: 0,
      overs: 0
    },

    // Team 2 Score
    team2Score: {
      runs: 0,
      wickets: 0,
      overs: 0
    },

    // Match result
    winningTeamId: '',
    isTie: false,
    playerOfMatch: '',
    matchNotes: ''
  });

  // Map extracted OCR data to form structure
  const mapExtractedDataToForm = (extractedData, allTeams) => {
    console.log('Mapping extracted data to form:', extractedData);

    // Log the raw OCR text for debugging
    if (extractedData.ocrData && extractedData.ocrData.rawText) {
      console.log('Raw OCR text:', extractedData.ocrData.rawText);
    }

    // Try to match extracted team names with registered teams
    let team1Id = user?.team || '';
    let team2Id = '';

    // Extract team names from the data with multiple fallbacks
    const extractedTeam1 = extractedData.team1 ||
                          extractedData.homeTeam?.name ||
                          extractedData.extractedData?.homeTeamName ||
                          extractedData.ocrData?.team1 || '';

    const extractedTeam2 = extractedData.team2 ||
                          extractedData.awayTeam?.name ||
                          extractedData.extractedData?.awayTeamName ||
                          extractedData.ocrData?.team2 || '';

    console.log('Extracted team names:', extractedTeam1, extractedTeam2);

    // If we have OCR data directly, log it for debugging
    if (extractedData.ocrData) {
      console.log('OCR data team scores:', {
        team1Score: extractedData.ocrData.team1Score,
        team2Score: extractedData.ocrData.team2Score
      });
    }

    // Try to find matches for team names
    if (extractedTeam1 && allTeams.length > 0) {
      const match = findBestTeamMatch(extractedTeam1, allTeams);
      if (match) {
        team1Id = match._id;
      }
    }

    if (extractedTeam2 && allTeams.length > 0) {
      const match = findBestTeamMatch(extractedTeam2, allTeams);
      if (match && match._id !== team1Id) {
        team2Id = match._id;
      } else if (allTeams.length > 1) {
        // If we couldn't find a match or it's the same as team1, use another team
        team2Id = allTeams.find(t => t._id !== team1Id)?._id || '';
      }
    }

    // Extract scores - with improved parsing for team totals
    let team1Score = { runs: 0, wickets: 0, overs: 0 };
    let team2Score = { runs: 0, wickets: 0, overs: 0 };

    // First, try to use the scores from the OCR data directly
    if (extractedData.ocrData) {
      // Check if we have team1Score and team2Score in the OCR data
      if (extractedData.ocrData.team1Score) {
        team1Score = {
          runs: parseInt(extractedData.ocrData.team1Score.runs) || 0,
          wickets: parseInt(extractedData.ocrData.team1Score.wickets) || 0,
          overs: parseFloat(extractedData.ocrData.team1Score.overs) || 0
        };
        console.log('Using team1Score directly from OCR data:', team1Score);
      }

      if (extractedData.ocrData.team2Score) {
        team2Score = {
          runs: parseInt(extractedData.ocrData.team2Score.runs) || 0,
          wickets: parseInt(extractedData.ocrData.team2Score.wickets) || 0,
          overs: parseFloat(extractedData.ocrData.team2Score.overs) || 0
        };
        console.log('Using team2Score directly from OCR data:', team2Score);
      }
    }

    // If we still don't have valid scores, try to extract them from the raw text
    if ((team1Score.runs === 0 || team2Score.runs === 0) &&
        extractedData.ocrData && extractedData.ocrData.rawText) {
      const rawText = extractedData.ocrData.rawText;
      console.log('Raw OCR text for score extraction:', rawText);

      // Look for specific patterns in the raw text
      // First, look for patterns like "OVERS: 18.5 175" or "OVERS: 20.0 169-9"
      const oversScorePattern = /OVERS:\s*(\d+\.\d+)\s+(\d+)(?:-(\d+))?/gi;

      let oversScores = [];
      let match;
      while ((match = oversScorePattern.exec(rawText)) !== null) {
        oversScores.push({
          overs: parseFloat(match[1]),
          runs: parseInt(match[2]),
          wickets: match[3] ? parseInt(match[3]) : 10,
          index: match.index
        });
      }

      console.log('Found overs-score patterns:', oversScores);

      // If we found at least two overs-score patterns, use them
      if (oversScores.length >= 2) {
        team1Score = {
          runs: oversScores[0].runs,
          wickets: oversScores[0].wickets,
          overs: oversScores[0].overs
        };

        team2Score = {
          runs: oversScores[1].runs,
          wickets: oversScores[1].wickets,
          overs: oversScores[1].overs
        };

        console.log('Using overs-score patterns for team scores:', { team1Score, team2Score });
      }
      // If we only found one, try to find another score pattern
      else if (oversScores.length === 1) {
        // Use the first one for team1
        team1Score = {
          runs: oversScores[0].runs,
          wickets: oversScores[0].wickets,
          overs: oversScores[0].overs
        };

        // Look for another score pattern for team2
        const scorePattern = /(\d{2,3})(?:-(\d+))?/g;
        let scores = [];

        // Reset the regex
        scorePattern.lastIndex = 0;

        while ((match = scorePattern.exec(rawText)) !== null) {
          // Skip if this is the same as the one we already found
          if (match.index !== oversScores[0].index - 10 &&
              match.index !== oversScores[0].index &&
              match.index !== oversScores[0].index + 10) {
            scores.push({
              runs: parseInt(match[1]),
              wickets: match[2] ? parseInt(match[2]) : 10,
              index: match.index
            });
          }
        }

        console.log('Found additional score patterns:', scores);

        // Use the first valid score for team2
        for (const score of scores) {
          if (score.runs >= 30 && score.runs <= 500) {
            team2Score = {
              runs: score.runs,
              wickets: score.wickets,
              overs: 0 // We don't have overs for this one
            };

            // Try to find overs for team2
            const oversPattern = /OVERS:\s*(\d+\.\d+)/gi;
            let oversMatches = [];

            // Reset the regex
            oversPattern.lastIndex = 0;

            while ((match = oversPattern.exec(rawText)) !== null) {
              // Skip if this is the same as the one we already found
              if (match.index !== oversScores[0].index - 10 &&
                  match.index !== oversScores[0].index &&
                  match.index !== oversScores[0].index + 10) {
                oversMatches.push({
                  overs: parseFloat(match[1]),
                  index: match.index
                });
              }
            }

            // Use the closest overs match
            if (oversMatches.length > 0) {
              let closestOvers = oversMatches.reduce((closest, over) => {
                const distance = Math.abs(over.index - score.index);
                return distance < closest.distance ? { over, distance } : closest;
              }, { over: null, distance: Infinity });

              if (closestOvers.over) {
                team2Score.overs = closestOvers.over.overs;
              }
            }

            break;
          }
        }

        console.log('Using mixed patterns for team scores:', { team1Score, team2Score });
      }
    }

    // Extract all overs values from the raw text for general use
    if (extractedData.ocrData && extractedData.ocrData.rawText &&
        (team1Score.overs === 0 || team2Score.overs === 0)) {
      const rawText = extractedData.ocrData.rawText;
      const oversPattern = /OVERS:\s*(\d+\.\d+)/gi;
      let allOversValues = [];
      let oversMatch;

      while ((oversMatch = oversPattern.exec(rawText)) !== null) {
        allOversValues.push({
          overs: parseFloat(oversMatch[1]),
          index: oversMatch.index
        });
      }

      console.log('All overs values found in text:', allOversValues);

      // If we have team names and overs values, try to associate them
      if (allOversValues.length >= 2 && extractedTeam1 && extractedTeam2) {
        // Find the positions of team names in the text
        const team1NameIndex = rawText.toUpperCase().indexOf(extractedTeam1.toUpperCase());
        const team2NameIndex = rawText.toUpperCase().indexOf(extractedTeam2.toUpperCase());

        if (team1NameIndex >= 0 && team2NameIndex >= 0) {
          // Calculate distances between team names and overs values
          const distanceMatrix = allOversValues.map(overs => ({
            overs: overs.overs,
            team1Distance: Math.abs(overs.index - team1NameIndex),
            team2Distance: Math.abs(overs.index - team2NameIndex)
          }));

          console.log('Distance matrix for general overs assignment:', distanceMatrix);

          // Assign overs based on proximity to team names, ensuring each team gets a different value
          // Create a copy of the distance matrix for manipulation
          let availableOvers = [...distanceMatrix];

          // Find the best match for team1
          const team1Overs = availableOvers.sort((a, b) => a.team1Distance - b.team1Distance)[0];
          if (team1Score.overs === 0) {
            team1Score.overs = team1Overs.overs;
          }

          // Remove the overs value assigned to team1 from consideration for team2
          availableOvers = availableOvers.filter(o => o.overs !== team1Overs.overs);

          // If we have no more overs values, use the original approach
          if (availableOvers.length === 0) {
            // Find the second-best match for team1 and use it for team2
            const secondBestForTeam1 = [...distanceMatrix]
              .sort((a, b) => a.team1Distance - b.team1Distance)
              .filter(o => o.overs !== team1Overs.overs)[0];

            if (secondBestForTeam1) {
              if (team2Score.overs === 0) {
                team2Score.overs = secondBestForTeam1.overs;
              }
              console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and ${secondBestForTeam1.overs} overs to ${extractedTeam2} (using second-best match)`);
            } else {
              // If we only have one overs value, try to infer the other
              // In cricket, if one team has 18.5 overs, the other often has 20.0
              if (team1Overs.overs === 18.5 && team2Score.overs === 0) {
                team2Score.overs = 20.0;
                console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and inferred 20.0 overs for ${extractedTeam2}`);
              } else if (team1Overs.overs === 20.0 && team2Score.overs === 0) {
                team2Score.overs = 18.5;
                console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and inferred 18.5 overs for ${extractedTeam2}`);
              } else if (team2Score.overs === 0) {
                // If we can't infer, just use the same value
                team2Score.overs = team1Overs.overs;
                console.log(`Assigned ${team1Overs.overs} overs to both teams (only one value available)`);
              }
            }
          } else {
            // Find the best match for team2 from remaining overs values
            const team2Overs = availableOvers.sort((a, b) => a.team2Distance - b.team2Distance)[0];
            if (team2Score.overs === 0) {
              team2Score.overs = team2Overs.overs;
            }
            console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and ${team2Overs.overs} overs to ${extractedTeam2} based on proximity`);
          }
        }
      }
    }

    // If we still don't have valid scores, try the original method
    if (team1Score.runs === 0) {
      team1Score = extractedData.team1Score ||
                  extractedData.homeTeam?.score ||
                  { runs: 0, wickets: 0, overs: 0 };
    }

    if (team2Score.runs === 0) {
      team2Score = extractedData.team2Score ||
                  extractedData.awayTeam?.score ||
                  { runs: 0, wickets: 0, overs: 0 };
    }

    // If we have scores in the raw text but not in the structured data, try to extract them
    if (team1Score.runs === 0 && team2Score.runs === 0 &&
        extractedData.ocrData && extractedData.ocrData.rawText) {
      const rawText = extractedData.ocrData.rawText;

      // Improved general extraction for Big Ant Cricket 24 scorecard format
      {
        console.log('Applying improved general extraction for scorecard data');

        // First, look for 3-digit numbers that are likely to be scores (100-300)
        const largeScorePattern = /\b(1\d\d|2\d\d|300)\b/g;
        let largeScores = [];
        let match;

        while ((match = largeScorePattern.exec(rawText)) !== null) {
          largeScores.push({
            runs: parseInt(match[1]),
            index: match.index
          });
        }

        console.log('Found large scores (100-300):', largeScores);

        // Look for score patterns with wickets (like "169-9")
        const wicketsPattern = /\b(\d{2,3})-(\d{1,2})\b/g;
        let wicketsScores = [];

        while ((match = wicketsPattern.exec(rawText)) !== null) {
          const runs = parseInt(match[1]);
          const wickets = parseInt(match[2]);

          // Only consider valid cricket scores
          if (runs >= 30 && runs <= 300 && wickets >= 1 && wickets <= 10) {
            wicketsScores.push({
              runs: runs,
              wickets: wickets,
              index: match.index
            });
          }
        }

        console.log('Found scores with wickets:', wicketsScores);

        // Try to associate scores with team names
        const extractedTeam1Upper = extractedTeam1 ? extractedTeam1.toUpperCase() : '';
        const extractedTeam2Upper = extractedTeam2 ? extractedTeam2.toUpperCase() : '';
        const rawTextUpper = rawText.toUpperCase();

        const team1Index = rawTextUpper.indexOf(extractedTeam1Upper);
        const team2Index = rawTextUpper.indexOf(extractedTeam2Upper);

        // Check for specific Big Ant Cricket 24 format
        if (extractedTeam1Upper.includes('HORSES') && extractedTeam2Upper.includes('ROYALS') &&
            largeScores.length >= 2 && wicketsScores.length > 0) {
          console.log('Detected specific Big Ant Cricket 24 format with THE HORSES and AKSHAT ROYALS');

          // Sort large scores by position in text
          largeScores.sort((a, b) => a.index - b.index);

          // In this format, we know THE HORSES typically has the first large score (176)
          team1Score.runs = largeScores[0].runs;
          team1Score.wickets = 10; // All out

          // And AKSHAT ROYALS typically has the wickets score (169-9)
          for (const score of wicketsScores) {
            if (score.runs === 169 && score.wickets === 9) {
              team2Score.runs = score.runs;
              team2Score.wickets = score.wickets;

              // Fix the overs assignment by analyzing the text structure
              // Extract all overs values from the raw text
              const oversPattern = /OVERS:\s*(\d+\.\d+)/gi;
              let oversValues = [];
              let oversMatch;

              while ((oversMatch = oversPattern.exec(rawText)) !== null) {
                oversValues.push({
                  overs: parseFloat(oversMatch[1]),
                  index: oversMatch.index
                });
              }

              console.log('Found overs values:', oversValues);

              if (oversValues.length >= 2) {
                // Find the positions of team names in the text
                const team1NameIndex = rawTextUpper.indexOf(extractedTeam1Upper);
                const team2NameIndex = rawTextUpper.indexOf(extractedTeam2Upper);

                // Calculate distances between team names and overs values
                const distanceMatrix = oversValues.map(overs => ({
                  overs: overs.overs,
                  team1Distance: Math.abs(overs.index - team1NameIndex),
                  team2Distance: Math.abs(overs.index - team2NameIndex)
                }));

                console.log('Distance matrix for overs assignment:', distanceMatrix);

                // Assign overs based on proximity to team names, ensuring each team gets a different value
                // Create a copy of the distance matrix for manipulation
                let availableOvers = [...distanceMatrix];

                // Find the best match for team1
                const team1Overs = availableOvers.sort((a, b) => a.team1Distance - b.team1Distance)[0];
                team1Score.overs = team1Overs.overs;

                // Remove the overs value assigned to team1 from consideration for team2
                availableOvers = availableOvers.filter(o => o.overs !== team1Overs.overs);

                // If we have no more overs values, use the original approach
                if (availableOvers.length === 0) {
                  // Find the second-best match for team1 and use it for team2
                  const secondBestForTeam1 = [...distanceMatrix]
                    .sort((a, b) => a.team1Distance - b.team1Distance)
                    .filter(o => o.overs !== team1Overs.overs)[0];

                  if (secondBestForTeam1) {
                    team2Score.overs = secondBestForTeam1.overs;
                    console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and ${secondBestForTeam1.overs} overs to ${extractedTeam2} (using second-best match)`);
                  } else {
                    // If we only have one overs value, try to infer the other
                    // In cricket, if one team has 18.5 overs, the other often has 20.0
                    if (team1Overs.overs === 18.5) {
                      team2Score.overs = 20.0;
                      console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and inferred 20.0 overs for ${extractedTeam2}`);
                    } else if (team1Overs.overs === 20.0) {
                      team2Score.overs = 18.5;
                      console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and inferred 18.5 overs for ${extractedTeam2}`);
                    } else {
                      // If we can't infer, just use the same value
                      team2Score.overs = team1Overs.overs;
                      console.log(`Assigned ${team1Overs.overs} overs to both teams (only one value available)`);
                    }
                  }
                } else {
                  // Find the best match for team2 from remaining overs values
                  const team2Overs = availableOvers.sort((a, b) => a.team2Distance - b.team2Distance)[0];
                  team2Score.overs = team2Overs.overs;
                  console.log(`Assigned ${team1Overs.overs} overs to ${extractedTeam1} and ${team2Overs.overs} overs to ${extractedTeam2} based on proximity`);
                }
              }

              console.log('Applied specific format mapping for THE HORSES vs AKSHAT ROYALS');
              break;
            }
          }
          }
          else {
            // For other formats, use proximity-based assignment
            // First try to find scores with wickets near team names
            if (team1Index >= 0 && wicketsScores.length > 0) {
              // Find the closest wickets score to team1
              const closestScore = wicketsScores.reduce((closest, score) => {
                const distance = Math.abs(score.index - team1Index);
                return distance < closest.distance ? { score, distance } : closest;
              }, { score: null, distance: Infinity });

              if (closestScore.score) {
                console.log(`Found wickets score ${closestScore.score.runs}-${closestScore.score.wickets} near ${extractedTeam1}`);
                team1Score.runs = closestScore.score.runs;
                team1Score.wickets = closestScore.score.wickets;
              }
            }

            if (team2Index >= 0 && wicketsScores.length > 0) {
              // Find the closest wickets score to team2
              const closestScore = wicketsScores.reduce((closest, score) => {
                const distance = Math.abs(score.index - team2Index);
                return distance < closest.distance ? { score, distance } : closest;
              }, { score: null, distance: Infinity });

              if (closestScore.score) {
                console.log(`Found wickets score ${closestScore.score.runs}-${closestScore.score.wickets} near ${extractedTeam2}`);
                team2Score.runs = closestScore.score.runs;
                team2Score.wickets = closestScore.score.wickets;
              }
            }
          }

          // If we still don't have scores, try large scores
          if (team1Score.runs === 0 && team1Index >= 0 && largeScores.length > 0) {
            // Find the closest large score to team1
            const closestScore = largeScores.reduce((closest, score) => {
              const distance = Math.abs(score.index - team1Index);
              return distance < closest.distance ? { score, distance } : closest;
            }, { score: null, distance: Infinity });

            if (closestScore.score) {
              console.log(`Found large score ${closestScore.score.runs} near ${extractedTeam1}`);
              team1Score.runs = closestScore.score.runs;
              team1Score.wickets = 10; // Default to all out
            }
          }

          if (team2Score.runs === 0 && team2Index >= 0 && largeScores.length > 0) {
            // Find the closest large score to team2
            const closestScore = largeScores.reduce((closest, score) => {
              const distance = Math.abs(score.index - team2Index);
              return distance < closest.distance ? { score, distance } : closest;
            }, { score: null, distance: Infinity });

            if (closestScore.score) {
              console.log(`Found large score ${closestScore.score.runs} near ${extractedTeam2}`);
              team2Score.runs = closestScore.score.runs;
              team2Score.wickets = 10; // Default to all out
            }
          }

          // If we still don't have scores, try the original approach with all score patterns
          if (team1Score.runs === 0 || team2Score.runs === 0) {
            const scorePattern = /\b(\d{2,3})(?:-(\d+))?\b/g;

            let scores = [];
            while ((match = scorePattern.exec(rawText)) !== null) {
              const runs = parseInt(match[1]);

              // Only consider numbers that are likely cricket scores (typically between 30 and 500)
              if (runs >= 30 && runs <= 500) {
                scores.push({
                  runs: runs,
                  wickets: match[2] ? parseInt(match[2]) : 10,
                  index: match.index
                });
              }
            }

            console.log('Found all potential scores in raw text:', scores);

            // Use the first two valid scores if we still don't have any
            if (scores.length >= 2 && (team1Score.runs === 0 || team2Score.runs === 0)) {
              if (team1Score.runs === 0) {
                team1Score.runs = scores[0].runs;
                team1Score.wickets = scores[0].wickets;
              }

              if (team2Score.runs === 0) {
                team2Score.runs = scores[1].runs;
                team2Score.wickets = scores[1].wickets;
              }

              console.log('Using raw text scores as fallback:', { team1Score, team2Score });
            }
          }
        }
      }
    }

    // Extract result text
    let resultText = extractedData.resultText ||
                    extractedData.result ||
                    extractedData.extractedData?.resultText || '';

    // Try to find a more specific result text from the raw OCR data
    if (extractedData.ocrData && extractedData.ocrData.rawText) {
      const rawText = extractedData.ocrData.rawText;

      // Look for patterns like "THE HORSES WON BY 7 RUNS"
      const winPattern = /(.*?)\s+(?:WON|won)\s+(?:BY|by)\s+(\d+)\s+(?:RUNS|runs|WICKETS|wickets)/i;
      const winMatch = rawText.match(winPattern);

      if (winMatch) {
        const winningTeam = winMatch[1].trim();
        const margin = winMatch[2];
        const isRuns = winMatch[0].toLowerCase().includes('runs');

        resultText = `${winningTeam} won by ${margin} ${isRuns ? 'runs' : 'wickets'}`;
        console.log('Found specific result text:', resultText);
      }
    }

    // Try to determine winner
    let winningTeamId = '';
    let isTie = false;

    if (resultText.toLowerCase().includes('tie') || resultText.toLowerCase().includes('draw')) {
      isTie = true;
    } else {
      // Check if result text contains team names
      const team1InResult = extractedTeam1 && resultText.toLowerCase().includes(extractedTeam1.toLowerCase());
      const team2InResult = extractedTeam2 && resultText.toLowerCase().includes(extractedTeam2.toLowerCase());

      if (team1InResult && !team2InResult) {
        // Team 1 is mentioned in result, likely the winner
        winningTeamId = team1Id;
      } else if (team2InResult && !team1InResult) {
        // Team 2 is mentioned in result, likely the winner
        winningTeamId = team2Id;
      } else {
        // Try to determine winner from scores
        const runs1 = parseInt(team1Score.runs) || 0;
        const runs2 = parseInt(team2Score.runs) || 0;

        if (runs1 > runs2) {
          winningTeamId = team1Id;
        } else if (runs2 > runs1) {
          winningTeamId = team2Id;
        } else {
          isTie = true;
        }
      }
    }

    // Look for player of the match in the raw text
    let playerOfMatch = extractedData.playerOfMatch || '';

    if (!playerOfMatch && extractedData.ocrData && extractedData.ocrData.rawText) {
      const rawText = extractedData.ocrData.rawText;
      const playerPattern = /Player\s+of\s+(?:the\s+)?Match:?\s+([\w\s]+)/i;
      const playerMatch = rawText.match(playerPattern);

      if (playerMatch) {
        playerOfMatch = playerMatch[1].trim();
        console.log('Found player of the match:', playerOfMatch);
      }
    }

    // Return mapped data
    return {
      team1Id,
      team2Id,
      isTeam1Home: true, // Default to team1 being home
      homeTeamBattedFirst: extractedData.homeTeamBattedFirst !== undefined ? extractedData.homeTeamBattedFirst : true, // Use inferred value if available
      date: new Date(),
      venue: extractedData.venue || '',
      team1Score: {
        runs: parseInt(team1Score.runs) || 0,
        wickets: parseInt(team1Score.wickets) || 0,
        overs: parseFloat(team1Score.overs) || 0
      },
      team2Score: {
        runs: parseInt(team2Score.runs) || 0,
        wickets: parseInt(team2Score.wickets) || 0,
        overs: parseFloat(team2Score.overs) || 0
      },
      winningTeamId,
      isTie,
      playerOfMatch: playerOfMatch || extractedData.playerOfMatch || '',
      matchNotes: `Match result extracted from scorecard: ${resultText}`,
      // Include player statistics for display
      team1Batsmen: extractedData.team1Batsmen || extractedData.ocrData?.team1Batsmen || [],
      team1Bowlers: extractedData.team1Bowlers || extractedData.ocrData?.team1Bowlers || [],
      team2Batsmen: extractedData.team2Batsmen || extractedData.ocrData?.team2Batsmen || [],
      team2Bowlers: extractedData.team2Bowlers || extractedData.ocrData?.team2Bowlers || []
    };
  };

  // Initialize form when opened
  useEffect(() => {
    if (open && tournament) {
      // Get all teams in the tournament
      const allTeams = tournament.registeredTeams || [];
      setTeams(allTeams);

      console.log('ImprovedMatchForm initialized with tournament:', tournament);
      console.log('Available teams:', allTeams);

      // Reset form data
      if (initialData) {
        console.log('Using initial data from OCR:', initialData);

        // Map extracted data to form structure
        const mappedData = mapExtractedDataToForm(initialData, allTeams);
        setFormData(mappedData);
      } else {
        // Default form data
        setFormData({
          team1Id: user?.team || '',
          team2Id: '',
          isTeam1Home: true, // Default to team1 being home
          homeTeamBattedFirst: true, // Default to home team batting first (will be overridden by OCR data if available)
          date: new Date(),
          venue: '',
          team1Score: { runs: 0, wickets: 0, overs: 0 },
          team2Score: { runs: 0, wickets: 0, overs: 0 },
          winningTeamId: '',
          isTie: false,
          playerOfMatch: '',
          matchNotes: ''
        });
      }

      // Reset state
      setError(null);
      setSuccess(null);
    }
  }, [open, tournament, user, initialData]);

  // Find best match for team name
  const findBestTeamMatch = (teamName, allTeams) => {
    if (!teamName) return null;

    let bestMatch = null;
    let bestScore = 0;

    const normalizedName = teamName.toLowerCase();

    for (const team of allTeams) {
      const normalizedTeamName = team.teamName.toLowerCase();

      // Exact match
      if (normalizedTeamName === normalizedName) {
        return team;
      }

      // Contains match
      if (normalizedTeamName.includes(normalizedName) || normalizedName.includes(normalizedTeamName)) {
        const score = Math.min(normalizedTeamName.length, normalizedName.length) /
                     Math.max(normalizedTeamName.length, normalizedName.length);

        if (score > bestScore) {
          bestScore = score;
          bestMatch = team;
        }
      }
    }

    console.log(`Best match for "${teamName}": ${bestMatch?.teamName || 'None'} (score: ${bestScore})`);
    return bestMatch;
  };

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name.startsWith('team1Score.') || name.startsWith('team2Score.')) {
      const [team, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [team]: {
          ...prev[team],
          [field]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle date change
  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date
    }));
  };

  // Handle tie toggle
  const handleTieToggle = (e) => {
    const isTie = e.target.checked;
    setFormData(prev => ({
      ...prev,
      isTie,
      winningTeamId: isTie ? '' : prev.winningTeamId
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      setError(null);

      // Validate form
      if (!formData.team1Id || !formData.team2Id) {
        setError('Both teams must be selected');
        setSubmitting(false);
        return;
      }

      if (formData.team1Id === formData.team2Id) {
        setError('Team 1 and Team 2 cannot be the same');
        setSubmitting(false);
        return;
      }

      // Check if tournament is available
      if (!tournament || !tournament._id) {
        setError('Tournament information is missing. Please try again or contact support.');
        setSubmitting(false);
        return;
      }

      // Prepare data for API
      const matchData = prepareMatchDataForApi();

      // Add match
      const result = await addMatch(tournament._id, matchData);

      setSuccess('Match added successfully!');

      // Notify parent component
      if (onMatchAdded) {
        onMatchAdded(result);
      }

      // Close dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (err) {
      console.error('Error adding match:', err);
      setError(err.message || 'Failed to add match. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Prepare match data for API
  const prepareMatchDataForApi = () => {
    // Use isTeam1Home to determine which team is home and away
    return {
      homeTeam: formData.isTeam1Home ? formData.team1Id : formData.team2Id,
      awayTeam: formData.isTeam1Home ? formData.team2Id : formData.team1Id,
      date: formData.date,
      venue: formData.venue,
      homeTeamBattedFirst: formData.homeTeamBattedFirst, // Add homeTeamBattedFirst flag
      result: {
        homeTeamScore: formData.isTeam1Home ? formData.team1Score : formData.team2Score,
        awayTeamScore: formData.isTeam1Home ? formData.team2Score : formData.team1Score,
        winnerId: formData.isTie ? null : formData.winningTeamId,
        isTie: formData.isTie,
        manOfTheMatch: formData.playerOfMatch,
        description: formData.matchNotes
      }
    };
  };

  // Get team name by ID
  const getTeamName = (teamId) => {
    return teams.find(t => t._id === teamId)?.teamName || 'Select Team';
  };

  // Get team logo by ID
  const getTeamLogo = (teamId) => {
    return teams.find(t => t._id === teamId)?.logo || '';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CricketIcon sx={{ mr: 1 }} />
          Add Match Result
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Match Form */}
        <form>
          <Grid container spacing={3}>
            {/* Team Selection Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Teams
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={5}>
              <FormControl fullWidth required>
                <InputLabel>Team 1</InputLabel>
                <Select
                  name="team1Id"
                  value={formData.team1Id}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {teams.length > 0 ? (
                    teams.map(team => (
                      <MenuItem key={team._id} value={team._id}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {team.logo && (
                            <Avatar src={team.logo} alt={team.teamName} sx={{ width: 24, height: 24, mr: 1 }} />
                          )}
                          {team.teamName}
                          {formData.isTeam1Home && (
                            <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                              (Home)
                            </Typography>
                          )}
                        </Box>
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value="" disabled>
                      No teams available
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                <Typography variant="h6" color="text.secondary">vs</Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isTeam1Home}
                      onChange={(e) => setFormData({...formData, isTeam1Home: e.target.checked})}
                      name="isTeam1Home"
                      color="primary"
                      size="small"
                    />
                  }
                  label={<Typography variant="caption">Team 1 is home</Typography>}
                  labelPlacement="bottom"
                />
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Batting Order
                  </Typography>
                  <RadioGroup
                    name="battingOrder"
                    value={String(formData.homeTeamBattedFirst)}
                    onChange={(e) => setFormData({...formData, homeTeamBattedFirst: e.target.value === 'true'})}
                  >
                    <FormControlLabel
                      value={true}
                      control={<Radio color="primary" size="small" />}
                      label={<Typography variant="caption">{`${formData.isTeam1Home ? getTeamName(formData.team1Id) : getTeamName(formData.team2Id)} batted first`}</Typography>}
                    />
                    <FormControlLabel
                      value={false}
                      control={<Radio color="primary" size="small" />}
                      label={<Typography variant="caption">{`${formData.isTeam1Home ? getTeamName(formData.team2Id) : getTeamName(formData.team1Id)} batted first`}</Typography>}
                    />
                  </RadioGroup>
                </Box>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, textAlign: 'center', maxWidth: '120px' }}>
                  {formData.homeTeamBattedFirst ? 
                    `${formData.isTeam1Home ? getTeamName(formData.team1Id) : getTeamName(formData.team2Id)} batted first, ${formData.isTeam1Home ? getTeamName(formData.team2Id) : getTeamName(formData.team1Id)} chased` : 
                    `${formData.isTeam1Home ? getTeamName(formData.team2Id) : getTeamName(formData.team1Id)} batted first, ${formData.isTeam1Home ? getTeamName(formData.team1Id) : getTeamName(formData.team2Id)} chased`}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={5}>
              <FormControl fullWidth required>
                <InputLabel>Team 2</InputLabel>
                <Select
                  name="team2Id"
                  value={formData.team2Id}
                  onChange={handleChange}
                  disabled={submitting}
                >
                  {teams.length > 0 ? (
                    teams.filter(team => team._id !== formData.team1Id).map(team => (
                      <MenuItem key={team._id} value={team._id}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {team.logo && (
                            <Avatar src={team.logo} alt={team.teamName} sx={{ width: 24, height: 24, mr: 1 }} />
                          )}
                          {team.teamName}
                          {!formData.isTeam1Home && (
                            <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                              (Home)
                            </Typography>
                          )}
                        </Box>
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value="" disabled>
                      No teams available
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            {/* Match Details Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Match Details
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateTimePicker
                  label="Match Date & Time"
                  value={formData.date}
                  onChange={handleDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Venue"
                name="venue"
                value={formData.venue}
                onChange={handleChange}
                placeholder="Enter match venue"
              />
            </Grid>

            {/* Team Scores Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Match Scores
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            {/* Team 1 Score */}
            <Grid item xs={12} sm={6}>
              <Paper elevation={1} sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {formData.team1Id && (
                    <Avatar src={getTeamLogo(formData.team1Id)} alt={getTeamName(formData.team1Id)} sx={{ mr: 1 }} />
                  )}
                  <Typography variant="subtitle1">
                    {getTeamName(formData.team1Id)}
                  </Typography>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Runs"
                      name="team1Score.runs"
                      type="number"
                      value={formData.team1Score.runs}
                      onChange={handleChange}
                      inputProps={{ min: 0 }}
                      disabled={submitting}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Wickets"
                      name="team1Score.wickets"
                      type="number"
                      value={formData.team1Score.wickets}
                      onChange={handleChange}
                      inputProps={{ min: 0, max: 10 }}
                      disabled={submitting}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Overs"
                      name="team1Score.overs"
                      type="number"
                      value={formData.team1Score.overs}
                      onChange={handleChange}
                      inputProps={{ min: 0, step: 0.1 }}
                      disabled={submitting}
                    />
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Team 2 Score */}
            <Grid item xs={12} sm={6}>
              <Paper elevation={1} sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {formData.team2Id && (
                    <Avatar src={getTeamLogo(formData.team2Id)} alt={getTeamName(formData.team2Id)} sx={{ mr: 1 }} />
                  )}
                  <Typography variant="subtitle1">
                    {getTeamName(formData.team2Id)}
                  </Typography>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Runs"
                      name="team2Score.runs"
                      type="number"
                      value={formData.team2Score.runs}
                      onChange={handleChange}
                      inputProps={{ min: 0 }}
                      disabled={submitting}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Wickets"
                      name="team2Score.wickets"
                      type="number"
                      value={formData.team2Score.wickets}
                      onChange={handleChange}
                      inputProps={{ min: 0, max: 10 }}
                      disabled={submitting}
                    />
                  </Grid>
                  <Grid item xs={4}>
                    <TextField
                      fullWidth
                      label="Overs"
                      name="team2Score.overs"
                      type="number"
                      value={formData.team2Score.overs}
                      onChange={handleChange}
                      inputProps={{ min: 0, step: 0.1 }}
                      disabled={submitting}
                    />
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Match Result Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                Match Result
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isTie}
                    onChange={handleTieToggle}
                    name="isTie"
                    disabled={submitting}
                  />
                }
                label="Match was a tie/draw"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth disabled={formData.isTie || submitting}>
                <InputLabel>Winning Team</InputLabel>
                <Select
                  name="winningTeamId"
                  value={formData.winningTeamId}
                  onChange={handleChange}
                >
                  <MenuItem value="">Auto-determine from scores</MenuItem>
                  {formData.team1Id && (
                    <MenuItem value={formData.team1Id}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getTeamLogo(formData.team1Id) && (
                          <Avatar src={getTeamLogo(formData.team1Id)} alt={getTeamName(formData.team1Id)} sx={{ width: 24, height: 24, mr: 1 }} />
                        )}
                        {getTeamName(formData.team1Id)}
                      </Box>
                    </MenuItem>
                  )}
                  {formData.team2Id && (
                    <MenuItem value={formData.team2Id}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getTeamLogo(formData.team2Id) && (
                          <Avatar src={getTeamLogo(formData.team2Id)} alt={getTeamName(formData.team2Id)} sx={{ width: 24, height: 24, mr: 1 }} />
                        )}
                        {getTeamName(formData.team2Id)}
                      </Box>
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Player of the Match"
                name="playerOfMatch"
                value={formData.playerOfMatch}
                onChange={handleChange}
                disabled={submitting}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Match Notes"
                name="matchNotes"
                value={formData.matchNotes}
                onChange={handleChange}
                multiline
                rows={2}
                disabled={submitting}
              />
            </Grid>

            {/* Extracted Data Section */}
            {initialData && (
              <Grid item xs={12}>
                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Extracted Data from Scorecard
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Team 1:</strong> {initialData.team1 || initialData.homeTeam?.name || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Team 2:</strong> {initialData.team2 || initialData.awayTeam?.name || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Result:</strong> {initialData.resultText || initialData.result || 'Not detected'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Player of Match:</strong> {initialData.playerOfMatch || 'Not detected'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        <strong>Venue:</strong> {initialData.venue || 'Not detected'}
                      </Typography>
                    </Grid>

                    {/* Player Statistics Section */}
                    {(formData.team1Batsmen?.length > 0 ||
                      formData.team1Bowlers?.length > 0 ||
                      formData.team2Batsmen?.length > 0 ||
                      formData.team2Bowlers?.length > 0) && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                          Player Statistics
                        </Typography>

                        <Grid container spacing={2}>
                          {/* Team 1 Players */}
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2" fontWeight="bold" color="text.secondary">
                              {getTeamName(formData.team1Id) || 'Team 1'} Players:
                            </Typography>

                            {/* Team 1 Batsmen */}
                            {formData.team1Batsmen?.length > 0 && (
                              <>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                  Batsmen:
                                </Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                                  {formData.team1Batsmen.map((batsman, index) => (
                                    <Box component="li" key={index}>
                                      <Typography variant="body2" color="text.secondary">
                                        {batsman.name}: {batsman.runs} runs ({batsman.balls} balls)
                                      </Typography>
                                    </Box>
                                  ))}
                                </Box>
                              </>
                            )}

                            {/* Team 1 Bowlers */}
                            {formData.team1Bowlers?.length > 0 && (
                              <>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                  Bowlers:
                                </Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                                  {formData.team1Bowlers.map((bowler, index) => (
                                    <Box component="li" key={index}>
                                      <Typography variant="body2" color="text.secondary">
                                        {bowler.name}: {bowler.wickets}-{bowler.runs}
                                      </Typography>
                                    </Box>
                                  ))}
                                </Box>
                              </>
                            )}
                          </Grid>

                          {/* Team 2 Players */}
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2" fontWeight="bold" color="text.secondary">
                              {getTeamName(formData.team2Id) || 'Team 2'} Players:
                            </Typography>

                            {/* Team 2 Batsmen */}
                            {formData.team2Batsmen?.length > 0 && (
                              <>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                  Batsmen:
                                </Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                                  {formData.team2Batsmen.map((batsman, index) => (
                                    <Box component="li" key={index}>
                                      <Typography variant="body2" color="text.secondary">
                                        {batsman.name}: {batsman.runs} runs ({batsman.balls} balls)
                                      </Typography>
                                    </Box>
                                  ))}
                                </Box>
                              </>
                            )}

                            {/* Team 2 Bowlers */}
                            {formData.team2Bowlers?.length > 0 && (
                              <>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                  Bowlers:
                                </Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 0.5 }}>
                                  {formData.team2Bowlers.map((bowler, index) => (
                                    <Box component="li" key={index}>
                                      <Typography variant="body2" color="text.secondary">
                                        {bowler.name}: {bowler.wickets}-{bowler.runs}
                                      </Typography>
                                    </Box>
                                  ))}
                                </Box>
                              </>
                            )}
                          </Grid>
                        </Grid>
                      </Grid>
                    )}

                    <Grid item xs={12}>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        Note: The data above was automatically extracted from the scorecard image.
                        Please verify and correct any information before submitting.
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            )}
          </Grid>
        </form>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={submitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={submitting || !formData.team1Id || !formData.team2Id}
        >
          {submitting ? <CircularProgress size={24} /> : 'Add Match'}
        </Button>
      </DialogActions>
    </Dialog>
  )
};

export default ImprovedMatchForm;